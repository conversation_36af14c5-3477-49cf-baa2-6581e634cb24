.mat-step-header {
  pointer-events: all !important;

  // &:dir(rtl) {
  //   .mat-step-text-label {
  //     font-family: "Noto Kufi Arabic" !important;
  //   }
  // }
}

.showSuccessMessage {
  // visibility: hidden !important;
  display: none;
  height: 0 !important;
  overflow: hidden !important;
  transition: height 0.3s ease;
}

.result-message {
  @media (max-width: 1024px) {
    padding: 0 16px !important;
    display: flex !important;
    align-items: self-start !important;
    flex-direction: column !important;
    justify-content: center !important;
  }
}

.congratulation-message {
  // margin: 32px 16px;
  margin: 0 0 32px;

  > h1 {
    font-size: 40px;
    font-weight: 700;
    margin-bottom: 0 !important;
    gap: 24px;

    @media (max-width: 1024px) {
      gap: 12px;
      font-size: 32px !important;

      > .result-icon {
        width: 85px;
        height: 85px;
      }
    }
  }
}

.submition-message {
  line-height: 48.41px;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 24px !important;

  @media (max-width: 1024px) {
    font-size: 24px !important;
    margin-bottom: 16px !important;
    line-height: 32px !important;
  }
}

.actions-container {
  display: flex;
  gap: 24px !important;

  @media (max-width: 1024px) {
    align-items: center;
    justify-content: center;
    flex-direction: column;

    button {
      width: 100% !important;
    }
  }
}

.review-message {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;

  @media (max-width: 1024px) {
    font-size: 18px !important;
    line-height: 28px !important;
    margin-bottom: 16px !important;
  }
}

.application-reference-number {
  padding: 32px;
  border-radius: 8px;
  background-color: #f9f7ed;
  margin-bottom: 40px;

  @media (max-width: 1024px) {
    padding: 15px 16px !important;
    margin-bottom: 16px !important;
  }
}

.application-reference-number p {
  font-size: 26px;
  font-weight: 600;
  line-height: 31.47px;

  @media (max-width: 1024px) {
    font-size: 20px !important;
    line-height: 24.2px !important;
  }
}

.application-reference-number .eService_sub-title {
  font-size: 24px;
  font-weight: 400;
  line-height: 32px;
  color: #7c5e24;

  @media (max-width: 1024px) {
    font-size: 20px !important;
    line-height: 28px !important;
  }
}

.notification-headsup {
  margin-bottom: 40px !important;

  > .part1,
  > .part2 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 400;
    margin: 0 !important;
  }

  @media (max-width: 1024px) {
    margin-bottom: 24px !important;

    > .part1,
    > .part2 {
      font-size: 14px !important;
    }
  }
}
