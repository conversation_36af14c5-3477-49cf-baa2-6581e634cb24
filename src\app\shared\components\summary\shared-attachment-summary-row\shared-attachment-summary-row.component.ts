import { Attachment } from './../../../models/attachment.model';
import { Component, Input, AfterViewInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Lookup } from '../../../models/lookup.model';
import { LanguageService } from '../../../services/language.service';
import { distinctUntilChanged } from 'rxjs';
import { StepperService } from '../../../services/stepper.service';

@Component({
  selector: 'div[app-shared-attachment-summary-row]',
  templateUrl: './shared-attachment-summary-row.component.html',
  styleUrl: './shared-attachment-summary-row.component.scss'
})
export class SharedAttachmentSummaryRowComponent {
  @Input() label: string;
  @Input() value: any;
  @Input() isPlain: boolean;
  @Input() isLookup: boolean;
  @Input() isFile: boolean;
  @Input() isDate: boolean;

  valueLookup: string;
  file: File;
  fileUrl: string;
  fileName: string;

  constructor(
    protected translate: TranslateService,
    private lang: LanguageService,
    private stepper: StepperService
  ) {
    if (this.isFile) {
      this.file = this.value?.file;
    }
  }

  ngAfterViewInit() {
  }

  createDownloadLink(file: File) {
    const blob = new Blob([file], { type: file.type });
    this.fileUrl = URL.createObjectURL(blob);
  }

}
