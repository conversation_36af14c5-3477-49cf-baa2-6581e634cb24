import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { filter, tap } from 'rxjs';
import { ActionFormType } from '../../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { SERVICES_PROVID_FUND } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { letterPatternValidatorGeneral } from '../../../../../../shared/validators/letter-pattern-validator-accept-general';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-establishment-fund-services',
  templateUrl: './establishment-fund-services.component.html',
  styleUrls: ['./establishment-fund-services.component.scss']
})
export class EstablishmentFundServicesComponent extends MyEstablishmentComponentBase implements OnInit, OnChanges {


  serviesList: any[];

  internalFundServicesForm: FormGroup;
  get fundServices(): FormArray {
    return this.form.get('fundServices') as FormArray;
  }
  get fControls(): any {
    return this.internalFundServicesForm.controls;
  }


  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.FundServices.";
    this.intiForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.intiForm();
    this.initLookups();
    this.pagination();
    this.fundServices.valueChanges.subscribe(_ => this.pagination());
  }

  initLookups = (): void => {
    this.MyEstablishmentService.lookupData$
      .pipe(
        filter(data => !!data),
        tap(data => {
          this.serviesList = SERVICES_PROVID_FUND;
          if (this.MyEstablishmentService.FORM_MODE == ActionFormType.CREATE)
            this.fillPredefineConditions();

          this.StepperService.requestData$.subscribe(_ => {
            if (_ && _.isFullDetails == true) {
              this.mapData(_?.FundServiceForm);
              // Object.keys(this.form.controls).forEach((control) => {
              //   if (
              //     (this.form.get(control)?.value === null ||
              //       this.form.get(control)?.value === '' ||
              //       !this.form.get(control)?.value) &&
              //     this.isNotAllowedToEdit === false
              //   ) {
              //     this.form.get(control)?.enable();
              //   } else {
              //     this.form.get(control)?.disable();
              //   }
              // });
            } else if (_ && _.isFullDetails == false && _?.FundServiceForm) {
              this.fundServices.clear();
              this.mapData(_?.FundServiceForm);
            }
          });
        })
      )
      .subscribe();
  };

  fillPredefineConditions = (): void => {
    if (this.serviesList?.length > 0) {
      this.serviesList.forEach(item => {
        if (!this.checkMembershipConditionsExist(item?.Id)) {
          this.fundServices.push(this.FormBuilder.group({
            Id: [item.Id],
            Description: [item.NameEnglish],
            DescriptionAr: [item.NameArabic],
            MaskedServiceTitle: [item.NameEnglish],
            MaskedServiceTitleAR: [item.NameArabic],
            value: [],
            allow: [false],
            canEdit: [false],
            toggeled: [true],
            showAmount: [item.ShowAmount == "TRUE"],
            SatusReason: [item?.SatusReason?.trim() || 'draft'],
          }));
        }
      });
    }
  }

  intiForm = (): void => {
    this.internalFundServicesForm = this.FormBuilder.group({
      Id: [''],
      Description: new FormControl("", [letterPatternValidatorGeneral('en')]),
      DescriptionAr: new FormControl("", [letterPatternValidatorGeneral('ar')]),
      value: [],
      allow: [false],
      toggeled: [false],
      showAmount: [false],
      SatusReason: [''],
    });
  }

  isItemDuplicated = (item: any): boolean => {
    if (!this.fundServices?.controls) return false;

    return this.fundServices.controls.some(control => {
      const { Description, DescriptionAr, Id } = { Description: control.get('Description')?.value?.trim(), DescriptionAr: control.get('DescriptionAr')?.value?.trim(), Id: control.get('Id')?.value };
      return ((Description === item.Description?.trim() || DescriptionAr === item.DescriptionAr?.trim()) && Id !== item.Id);
    });
  };

  manageFundServices(item: any, type: GridActionTypesEnum) {
    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError('notify.error', 'notify.FundServicesIsAlearyExist');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.fundServices.push(
        this.FormBuilder.group({
          Id: [this.generateDistinctId()],
          Description: [item.Description],
          DescriptionAr: [item.DescriptionAr],
          MaskedServiceTitle: [item.MaskedServiceTitle],
          MaskedServiceTitleAR: [item.MaskedServiceTitleAR],
          value: [],
          allow: [false],
          toggeled: [true],
          showAmount: [false],
          SatusReason: [item?.SatusReason?.trim() || 'draft'],
          canEdit: [true],
        })
      );
    else
      this.fundServices
        .at(this.fundServicesEditIndex)
        .patchValue({
          Id: item.Id,
          Description: item.Description,
          DescriptionAr: item.DescriptionAr,
          SatusReason: item?.SatusReason?.trim() || 'draft',
          canEdit: item?.canEdit,
        });

    this.internalFundServicesForm.reset();
    this.modalService.dismissAll();
  }

  fundServicesEditIndex: number;
  handleEdit(data: any, idx: number) {
    this.fundServicesEditIndex = idx;
    this.internalFundServicesForm.patchValue({
      Id: data?.value?.Id,
      Description: data?.value?.Description,
      DescriptionAr: data?.value?.DescriptionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit
    });
    this.FormService.enableFields(this.internalFundServicesForm, ['Description', 'DescriptionAr'])
  }

  handleClone(data: any) {
    this.internalFundServicesForm.patchValue({
      Description: data?.value?.Description,
      DescriptionAr: data?.value?.DescriptionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit
    });
    this.FormService.enableFields(this.internalFundServicesForm, ['Description', 'DescriptionAr'])
  }

  async removeFundServices(data: any, idx: number) {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.fundServices?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.Id, this.Fund_SERVICES_GRID_IN_CRM);

      this.fundServices.removeAt(idx);
    }
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("FundServiceForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("FundServiceForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("FundServiceForm", false);
    this.handleSaveRequest(submitParams);
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.fundServices.controls).every(controlName => {
      const control = this.fundServices.get(controlName);
      return control?.valid;
    });
    // let result: boolean = Object.keys(this.fundServices.controls).every(controlName => {
    //   const control = this.fundServices.get(controlName);
    //   return control?.disabled || control?.valid;
    // });

    return result;
    // return true;
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      FundService: (this.fundServices.controls as Array<any>).map((control, index) => {
        let _$ = control.value;
        return {
          Id: (this.includeConditionIds && _$.Id) ? _$.Id : (_$.canEdit == false ? this.EMPTY_GUID : _$.Id ?? this.EMPTY_GUID),
          Description: _$.Description,
          DescriptionAr: _$.DescriptionAr,
          Priority: index + 1,
          Toggleenabled: _$.allow == true ? 1 : 0,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$?.SatusReason?.trim() || 'draft',
        };
      })

    }
  }

  includeConditionIds: boolean = false;
  mapData = (data: any): void => {
    if (!data) return;

    data.FundService = data.FundService.sort((a, b) => a.Priority - b.Priority);
    data.FundService.forEach((item: any, index$: number) => {
      this.getMaskedService(item, index$);
      this.fundServices.push(this.FormBuilder.group({
        Id: [item.Id],
        Description: [item.Description],
        DescriptionAr: [item.DescriptionAr],
        MaskedServiceTitle: [item.MaskedServiceTitle],
        MaskedServiceTitleAR: [item.MaskedServiceTitleAR],
        value: [item.value ?? ''],
        canEdit: true,
        // canEdit: [item.canEdit],
        allow: [item.allow],
        toggeled: [item.toggeled],
        showAmount: [item.showAmount],
        SatusReason: [item?.SatusReason?.trim() || 'draft'],
      }));
    });
    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.fundServices?.value || this.fundServices?.value?.length == 0)) {
      this.fillPredefineConditions();
    } else {
      this.includeConditionIds = true;
    }
  }


  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "fundServices", this.fControls);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;

  checkMembershipConditionsExist = (item: any): boolean => {
    if (!item?.Description || !item?.DescriptionAr) return false;
    const itemDescription = item.Description.trim().toLowerCase();
    const itemDescriptionAr = item.DescriptionAr.trim().toLowerCase();

    return this.fundServices.controls.some(control => {
      const Description = control.get('Description')?.value?.trim().toLowerCase();
      const DescriptionAr = control.get('DescriptionAr')?.value?.trim().toLowerCase();
      return Description === itemDescription && DescriptionAr === itemDescriptionAr;
    });
  }

  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.fundServices.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
    data$.forEach(_ => this.dataTable.push(_));
  }

  getFormControl = (ele: any, key: string): FormControl<any> | null => {
    if (ele?.get(key)) {
      const element = ele?.get(key) as FormControl;
      // element.disable();
      return element;
    }
    return null;
  }

  changeServicesValue = (ele: any): void => {
    const val = ele.get('value').value;
    if ((typeof val === 'number' && !isNaN(val))) {
      const formattedValue = val.toLocaleString();
      ele.get('Description').setValue((ele.get('MaskedServiceTitle').value as string).replace('(...)', formattedValue));
      ele.get('DescriptionAr').setValue((ele.get('MaskedServiceTitleAR').value as string).replace('(...)', formattedValue));
    }
  }

  getMaskedService = (item, index$): void => {
    let num = -1;
    if (index$ < 8) {
      if (item.Description.includes('(...)')) {
        item["MaskedServiceTitle"] = item.Description;
        item["MaskedServiceTitleAR"] = item.DescriptionAr;
      } else {
        let description = item.Description.replaceAll(',', '');
        let descriptionAr = item.DescriptionAr.replaceAll(',', '');

        item["MaskedServiceTitle"] = description.replace(/(\d+)/, '(...)');
        item["MaskedServiceTitleAR"] = descriptionAr.replace(/(\d+)/, '(...)');
        var match$ = description.match(/\d+/);
        if (match$ && match$.length > 0)
          num = description.match(/\d+/)[0] ?? -1;
      }
      const index = SERVICES_PROVID_FUND.findIndex(_ => _.NameEnglish === item["MaskedServiceTitle"])
      if (index > -1) {
        item["value"] = num != -1 ? Number(num) : '';
        item["allow"] = item?.Toggleenabled == 1;
        item["canEdit"] = false;
        item["toggeled"] = true;
        item["showAmount"] = SERVICES_PROVID_FUND[index].ShowAmount == "TRUE";
      }
    } else {
      item["MaskedServiceTitle"] = '';
      item["MaskedServiceTitleAR"] = '';
      item["value"] = '';
      item["allow"] = item?.Toggleenabled == 1;
      item["canEdit"] = true;
      item["toggeled"] = false;
      item["showAmount"] = false;
    }

  }

  getErrorMessage(control): string | null {
    if (control && control.errors) {
      for (const key in control.errors) {
        if (control.errors.hasOwnProperty(key) && control.touched) {
          return this.ValidationService.getValidatorErrorMessage(key, control.errors[key]);
        }
      }
    }
    return null;
  }

  toggle = (event, control: FormControl<any>, showAmount: boolean): void => {
    if (!showAmount)
      return;

    if (control && event.checked) {
      control.addValidators([Validators.required, Validators.min(1), Validators.pattern('^[0-9]{1,10}$')]);
    } else {
      control.clearValidators();
    }
    control.updateValueAndValidity();
  }
}
