import { Component, Input } from '@angular/core';
import { DataService } from '../../../services/data.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomerpulseService } from '../../../services/customerpulse.service';
import { LanguageService } from '../../../services/language.service';
import { ServiceCatalogue } from '../../../../shared/enums/service-catalogue.enum';

@Component({
  selector: 'app-payment-result',
  templateUrl: './payment-result.component.html',
  styleUrl: './payment-result.component.scss'
})
export class PaymentResultComponent {
  isSuccess: boolean;

  data: any;
  paymentId: string | null;

  constructor(private dataService: DataService, protected lang: LanguageService,
    private customerPulseService: CustomerpulseService, private route: ActivatedRoute, private router: Router) {
    this.getData();
  }

  goToMyApps() {
    this.router.navigate(['/user-pages/my-applications']);
  }

  getData() {

    this.paymentId = this.route.snapshot.paramMap.get('id');
    this.dataService
      .get(`Payment/GetPODPaymentDetails?paymentId=${this.paymentId}`)
      .subscribe((res) => {
        console.log('GetPODPaymentDetails= ', res);
        this.data = res?.data[0]?.PaymentDetails;
        this.isSuccess = this.data?.IsSuccessPayment;
        this.customerPulseService.postCustomerPulse(res?.data[0]?.PaymentDetails.RequestId,ServiceCatalogue.LostCard, this.lang.IsArabic ? 'ar' : 'en');
      });
  }

}
