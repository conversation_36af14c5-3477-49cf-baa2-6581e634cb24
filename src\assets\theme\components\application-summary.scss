.the_summary-box {
    background-color: $aeblack-50;
    border-radius: 6px;
    padding: 24px 32px;
    color: $aeblack-800;
    margin-bottom: 40px;

    .title_wrapper {
        // font-family: "Inter" !important;
        font-size: 32px;
        font-weight: $font-bold;
        margin-bottom: 32px;

        @media(max-width:1024px){
            font-size: 24px !important;
        }
    }

    .row {
        gap: 8px 0;
    }
}

.summary_item {
    font-size: 16px;
    // font-family: "Roboto";
}

.summary_head {
    font-weight: $font-bold;
}

.status {
    border-radius: 24px !important;
    padding: 6px 20px;
    font-size: 14px;
    line-height: 1;
    display: inline-block;
    position: relative;

    &::before {
        content: "";
        width: 6px;
        height: 6px;
        border-radius: 50%;
        position: absolute;
        left: 10px;
        top: calc(50% - 3px);
    }

    &-warning {
        background-color: $camelyellow-50 !important;
        color: $camelyellow-600;

        &::before {
            background-color: $camelyellow-600;
        }
    }

    &-primary {
        background-color: $techblue-50 !important;
        color: $techblue-600;

        &::before {
            background-color: $techblue-600;
        }
    }

    &-draft {
        background-color: $aeblack-50 !important;
        color: $aeblack-600;

        &::before {
            background-color: $aeblack-600;
        }
    }

}

.summary_alerts {
    background-color: $camelyellow-50;
    border-radius: 8px;
    gap: 8px;
    padding: 16px;
    margin-bottom: 40px;

    .alert-warning {
        background-color: transparent;
        border: 0;
        font-size: 16px;
        // font-family: "Roboto";
        color: $camelyellow-600;
        line-height: 22px;
        padding: 0;

        &::before {
            content: "";
            width: 20px;
            height: 20px;
            background: url('../../images/icons/Warning.svg') no-repeat center;
            background-size: contain;
            vertical-align: middle;
            margin-right: 10px;
        }
    }

    .btn-primary {
        background-color: $camelyellow-600 !important;
        border-color: $camelyellow-600 !important;
    }
}

.summary_review {
    .download-button {
        color: $aegold-600 !important;
        background-color: $aegold-100 !important;
        border-radius: 8px !important;
        padding: 10px 16px !important;
        // width: fit-content !important;
        height: 48px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px !important;

        @media (max-width: 1023px) {
            // width: 176px;
            height: 40px;
            font-size: 14px !important;
        }
    }
}

[ng-reflect-dir=rtl],
[dir=rtl] {
    .status::before {
        left: auto;
        right: 10px;
    }

    .summary_alerts .alert-warning::before {
        margin-left: 10px;
        margin-right: 0;
    }
}

body [ng-reflect-dir="rtl"],
body [dir="rtl"] {
    // .the_summary-box .title_wrapper {
    //     font-family: 'Noto Kufi Arabic', sans-serif !important;
    // }

    .ms-2 {
        margin-left: 0 !important;
        margin-right: 0.5rem !important;
    }
}