import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LangChangeEvent } from '@ngx-translate/core';
import { filter, tap } from 'rxjs';
import { ActionFormType } from '../../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { LegalFormTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/legal-form-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { NATIONALITY_TYPES } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { letterPatternValidatorGeneral } from '../../../../../../shared/validators/letter-pattern-validator-accept-general';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { numberValidator } from '../../../../../../shared/validators/number-validator';
import { optionalNumberValidator } from '../../../../../../shared/validators/optional-number-validator';
import { editEstablishmentNumberValidator } from '../../../../../../shared/validators/edit-establishment-number-validator';

@Component({
  selector: 'app-establishment-membership-enrollment',
  templateUrl: './establishment-membership-enrollment.component.html',
  styleUrls: ['./establishment-membership-enrollment.component.scss']
})
export class EstablishmentMembershipEnrollmentComponent extends MyEstablishmentComponentBase
  implements OnInit, OnChanges {

  membershipEditIndex: number;
  membershipConditionsForm: FormGroup;
  conditionList: any[];
  nationalityTypes: Lookup[];
  includeIds: boolean = false;

  min: number = 1;

  get fb(): any {
    return this.form.controls;
  }
  get membershipConditions(): FormArray {
    return this.form.get("MembershipConditions") as FormArray;
  }
  get mcControls(): any {
    return this.membershipConditionsForm.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.membership.';
    this.intiForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.initLookups();
    this.resetFromValidation(this.form);

    this.fb.AnnualMembershipDueDate.setValue(this.getAnnualMembershipDueDateValue());
    this.fb.AnnualMembershipDueDate.disable();
    this.Translation.onLangChange.subscribe((event: LangChangeEvent) => {
      this.fb.AnnualMembershipDueDate.setValue(this.getAnnualMembershipDueDateValue());
      this.fb.AnnualMembershipDueDate.disable();
    });

    this.pagination();
    this.membershipConditions.valueChanges.subscribe(_ => this.pagination());

    this.updateCategoryValidation();
  }

  initLookups = (): void => {
    this.nationalityTypes = NATIONALITY_TYPES;
    this.MyEstablishmentService.lookupData$
      .pipe(
        filter(data => !!data),
        tap(data => {
          this.conditionList = data.GeneralConditionMembership;
          this.filterConditionsBasedOnType();

          if (this.MyEstablishmentService.FORM_MODE == ActionFormType.CREATE)
            this.fillPredefineConditions();



          this.StepperService.requestData$.subscribe(_ => {
            if (_ && (_.isFullDetails == true)) {
              this.mapData(_?.MembershipForm);
              // Object.keys(this.form.controls).forEach((control) => {
              //   if (
              //     (this.form.get(control)?.value === null ||
              //       this.form.get(control)?.value === '' ||
              //       !this.form.get(control)?.value) &&
              //     this.isNotAllowedToEdit === false
              //   ) {
              //     this.form.get(control)?.enable();
              //   } else {
              //     this.form.get(control)?.disable();
              //   }
              // });
            }
            else if (_ && (_.isFullDetails == false) && _?.MembershipForm) {
              this.membershipConditions.clear();
              this.mapData(_?.MembershipForm);
            }
            const entityNameEn$ = _?.BasicInformationForm?.EntityName;
            const entityNameAr$ = _?.BasicInformationForm?.EntityNameAr;
            if (this.isEmptyString(entityNameEn$) && this.isEmptyString(entityNameAr$)) {
              this.checkTempleteReplace('(...)', '(...)');
            } else {
              this.checkTempleteReplace(entityNameEn$, entityNameAr$);
            }
          });

        })
      )
      .subscribe();
  };

  get countOfPredefindConditions(): number {
    return this.conditionList?.length ?? -1;
  }

  checkTempleteReplace = (nameEn: string, nameAr: string): void => {
    if (LegalFormTypesEnum.SocialSolidarityFunds != this.fb.LegalFormId?.value)
      return;

    (this.membershipConditions.controls).map(_ => {
      if (_.get("canEdit")?.value == false) {
        if ((_.get("membershipConditionMaskEn")?.value as string).includes('(...)')) {
          _.get("membershipConditionEn")?.setValue(_.get("membershipConditionMaskEn")?.value.replace('(...)', nameEn));
          _.get("membershipConditionAr")?.setValue(_.get("membershipConditionMaskAr")?.value.replace('(...)', nameAr));
          _.get("replaceMask")?.setValue(true);
        }
      }
    });
  }

  filterConditionsBasedOnType = (): void => {
    const legalFormMapping = {
      [LegalFormTypesEnum.Association]: 'Association',
      [LegalFormTypesEnum.Union]: 'Union',
      [LegalFormTypesEnum.SocialSolidarityFunds]: 'Social Solidarity Fund',
      [LegalFormTypesEnum.AssociationByDecree]: 'Association by decree',

    };

    const selectedForm = legalFormMapping[this.fb.LegalFormId?.value];

    if (selectedForm) {
      this.conditionList = this.conditionList.filter(condition => condition.LegalForm === selectedForm);
    } else {
      this.conditionList = [];
    }

    this.conditionList = this.conditionList.sort((a, b) => a.Priority - b.Priority);
  };

  isValidForm = (): boolean => {
    // let result: boolean = Object.keys(this.form.controls).every(controlName => {
    //   const control = this.form.get(controlName);
    //   return control?.disabled || control?.valid;
    // });
    
    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.valid;
    });

    // const conditionsValidLength = this.membershipConditions && this.membershipConditions.length > 0;
    // return result && conditionsValidLength;
    return result;
    // return true;
  }

  intiForm = (): void => {
    this.membershipConditionsForm = this.FormBuilder.group({
      id: [''],
      membershipConditionEn: ['', [Validators.required, letterPatternValidatorGeneral('en')]],
      membershipConditionAr: ['', [Validators.required, letterPatternValidatorGeneral('ar')]],
      canEdit: [''],

      membershipConditionMaskEn: [''],
      membershipConditionMaskAr: [''],
      replaceMask: [''],
      SatusReason: [''],
    });
  }

  isItemDuplicated = (item: any): boolean => {
    if (!this.membershipConditions?.controls) return false;

    return this.membershipConditions.controls.some(control => {
      const { conditionEn, conditionAr, id } = {
        conditionEn: control.get('membershipConditionEn')?.value,
        conditionAr: control.get('membershipConditionAr')?.value,
        id: control.get('id')?.value
      };

      return (
        (conditionEn === item.membershipConditionEn ||
          conditionAr === item.membershipConditionAr) &&
        id !== item.id
      );
    });
  };

  manageMembership = (item: any, type: GridActionTypesEnum): void => {

    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError('notify.error', 'notify.conditionExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.membershipConditions.push(this.FormBuilder.group({
        id: [this.generateDistinctId()],
        membershipConditionEn: [item.membershipConditionEn,],
        membershipConditionAr: [item.membershipConditionAr,],
        SatusReason: [item?.SatusReason?.trim() || 'draft'],
        canEdit: [true]
      }));
    else
      this.membershipConditions.at(this.membershipEditIndex).patchValue(
        {
          id: item.id,
          membershipConditionEn: item.membershipConditionEn,
          membershipConditionAr: item.membershipConditionAr,
          SatusReason: item?.SatusReason?.trim() || 'draft',
          canEdit: item?.canEdit
        }
      );

    this.membershipConditionsForm.reset();
    this.modalService.dismissAll();
  }

  handleEditMembership = (data: any, idx: number): void => {
    this.membershipEditIndex = idx;
    this.membershipConditionsForm.patchValue({
      id: data?.value?.id,
      membershipConditionEn: data?.value?.membershipConditionEn,
      membershipConditionAr: data?.value?.membershipConditionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit
    });
  }
  handleCloneMembership(data: any) {
    this.membershipConditionsForm.patchValue({
      membershipConditionEn: data?.value?.membershipConditionEn,
      membershipConditionAr: data?.value?.membershipConditionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit
    });
  }

  async removeMember(idx: number) {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.membershipConditions?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.MEMBERSHIP_CONDITION_GRID_IN_CRM);

      this.membershipConditions.removeAt(idx);
    }
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("MembershipForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("MembershipForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("MembershipForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }


  getMappingObject = (): any => {
    // this.membershipConditions?.value?.forEach(element => {
    //   if(element.id.length === 3)
    //     element.id = this.EMPTY_GUID;
    // });
    return {
      Membershipfees: this.getDecimal(this.fb?.MembershipFees?.value),
      Enrollmentfees: this.getDecimal(this.fb?.EnrollmentFees?.value),
      BeneficiaryOrMembershipFees: this.getDecimal(this.fb?.BeneficiaryMembershipFees?.value),
      // Annualmembershipduedate: this.fb?.AnnualMembershipDueDate?.value,

      MembershipConditions: (this.membershipConditions.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: (this.includeIds && _$.id) ? _$.id : (_$.canEdit == false ? this.EMPTY_GUID : _$.id ?? this.EMPTY_GUID),
          Name: _$.replaceMask == true ? _$.membershipConditionMaskEn : _$.membershipConditionEn,
          NameAr: _$.replaceMask == true ? _$.membershipConditionMaskAr : _$.membershipConditionAr,
          Establishment: this.EMPTY_GUID,
          Priority: index + 1,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$?.SatusReason?.trim() || 'draft'
        };
      })
    };
  }

  getDecimal = (value: any): number => {
    if (value !== '') {
      const parsedValue = parseFloat(value);
      return isNaN(parsedValue) ? 0 : parsedValue;
    }

    return 0;
  }

  mapData = (data: any): void => {
    if (!data) return;

    this.fb.MembershipFees.setValue(data?.Membershipfees);
    this.fb.EnrollmentFees.setValue(data?.Enrollmentfees);
    this.fb.BeneficiaryMembershipFees.setValue(data?.BeneficiaryOrMembershipFees);

    data.MembershipConditions = data.MembershipConditions.sort((a, b) => a.Priority - b.Priority);

    data.MembershipConditions.forEach((item: any) => {
      // if (!this.checkMembershipConditionsExist(item?.Id)) {
      this.membershipConditions.push(this.FormBuilder.group({
        id: [item.Id],
        membershipConditionEn: [item.Name],
        membershipConditionAr: [item.NameAr],
        // canEdit: [(this.conditionList?.find(_ => _.Name == item.Name && _.NameAr == item.NameAr)) ? false : true],
        canEdit: true,
        membershipConditionMaskEn: [item.Name],
        membershipConditionMaskAr: [item.NameAr],
        replaceMask: [''],
        SatusReason: [item?.SatusReason?.trim() || 'draft']
      }));
      // }
    });

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.membershipConditions?.value || this.membershipConditions?.value?.length == 0)) {
      this.fillPredefineConditions();
    } else {
      this.includeIds = true;
    }

  }

  fillPredefineConditions = (): void => {
    if (this.conditionList?.length > 0) {
      this.conditionList.forEach(condition => {
        if (!this.checkMembershipConditionsExist(condition?.Id)) {
          this.membershipConditions.push(this.FormBuilder.group({
            id: [condition.Id],
            membershipConditionEn: [condition.Name,],
            membershipConditionAr: [condition.NameAr,],
            canEdit: [false],
            membershipConditionMaskEn: [condition.Name],
            membershipConditionMaskAr: [condition.NameAr],
            replaceMask: [''],
            SatusReason: [condition?.SatusReason?.trim() || 'draft']
          }));
        }
      });
    }
  }

  getAnnualMembershipDueDateValue() {
    return this.LanguageService.IsArabic
      ? 'مع بداية السنة المالية (جزء من قيمة الاشتراك)'
      : 'Beginning of the fiscal year(Partial subscription Amount)';
  }

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "membership", this.fb);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  // checkMembershipConditionsExist = (id: string): boolean => this.membershipConditions.controls.findIndex(_ => _.get('id')?.value == id) > -1;

  checkMembershipConditionsExist = (item: any): boolean => {
    if (!item?.Objectives || !item?.ObjectivesAR) return false;

    const itemEn = item.membershipConditionEn.trim().toLowerCase();
    const itemAr = item.membershipConditionAr.trim().toLowerCase();

    return this.membershipConditions.controls.some(control => {
      const objEn = control.get('membershipConditionEn')?.value?.trim().toLowerCase();
      const objAr = control.get('membershipConditionAr')?.value?.trim().toLowerCase();

      return objEn === itemEn && objAr === itemAr;
    });
  }

  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.membershipConditions.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
    data$.forEach(_ => this.dataTable.push(_));
  }

  isUnionLegalForm = (): boolean => {
    return this.fb.LegalFormId?.value === LegalFormTypesEnum.Union
  }

  isAssociationLegalForm = (): boolean => {
    return this.fb.LegalFormId?.value === LegalFormTypesEnum.Association
  }

  updateCategoryValidation = (): void => {
    const isUnionForm = this.isUnionLegalForm();

    if (isUnionForm) {
      this.FormService.clearValidators(this.form, ['EnrollmentFees', 'AnnualMembershipDueDate']);
    }     
    
    if (this.isAssociationLegalForm()) {
      this.FormService.addValidators(this.form, ['BeneficiaryMembershipFees'], [editEstablishmentNumberValidator()])
    } else {
      this.FormService.clearValidators(this.form, ['BeneficiaryMembershipFees']);
    }
  }
}
