import { environment } from './../../../../environments/environment';
import { Component } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../shared/services/auth.service';
import { LanguageService } from '../../../shared/services/language.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-login-website',
  templateUrl: './login-website.component.html',
  styleUrl: './login-website.component.scss',
})
export class LoginWebsiteComponent {
  userInfo: any;

  constructor(
    private route: ActivatedRoute,
    private auth: AuthService,
    protected lang: LanguageService,
    private router: Router,
    private translate: TranslateService
  ) {

    this.route.queryParams.subscribe((params) => {
      if (params && params['code']) {
        this.auth.getUaePassToken(params['code']).subscribe({
          next: (data: any) => {
            if (
              data &&
              data.access_token &&
              data.crmUserId &&
              this.auth.isTokenValid(data.access_token)
            ) {
              var response = this.auth.setJwtToken(data.access_token);
              this.auth.setUserInfo(data);
              const redirectUrl = `${environment.websiteUrl}/o/token?code=${params['code']}`;
              window.location.href = redirectUrl;
            }
            else {
              return
            }
          },
          error: (error) => {
            console.error('Error fetching data:', error);
            // const state = this.router.routerState.snapshot;
            // const currentUrl = state.url.split('?')[0];
            // const langParam = state.url.split('?')[1];
            // if (langParam && langParam.includes('lang=')) {
            //   this.translate.currentLang = langParam.split('=')[1];
            // }
            // this.auth.logout(currentUrl);
            const state = this.router.routerState.snapshot;
            const url = new URL(state.url, window.location.origin);
            const params = new URLSearchParams(url.search);
            const lang = params.get('lang');
            if (lang) {
              this.translate.use(lang);
            }
            this.auth.logout(url.pathname);
          },
        });
      } else {
        // const state = this.router.routerState.snapshot;
        // const currentUrl = state.url.split('?')[0];
        // const langParam = state.url.split('?')[1];
        // if (langParam && langParam.includes('lang=')) {
        //   this.translate.currentLang = langParam.split('=')[1];
        // }
        // this.auth.logout(currentUrl);
        const state = this.router.routerState.snapshot;
        const url = new URL(state.url, window.location.origin);
        const params = new URLSearchParams(url.search);
        const lang = params.get('lang');
        if (lang) {
          this.translate.use(lang);
        }
        this.auth.logout(url.pathname);
      }
    });


  }
}
