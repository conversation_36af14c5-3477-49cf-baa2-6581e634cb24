.alert-info {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.alert-info img {
  margin-right: 10px;
  margin-left: 10px;
}

.alert-info div {
  margin-left: 10px;
}

.viewAll {
  margin-top: 10px;
}

.search-input-container {
  @media (max-width:1024px) {
    margin-top: 0.5rem !important;
  }
}


.mobile-only {
  display: none !important;
}

@media (max-width: 1024px) {
  .draft-service-status-card {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .draft-status-wrapper {
    align-items: center !important;
    justify-content: center !important;
    flex-direction: row !important;
  }

  .mobile-only {
    display: flex !important;
  }

  .desktop-only {
    display: none !important;
  }
}




// list


$primary: #b68a35;
$head1: #232528;
$head2: #1b1d21;
$text: #000000;
$grey-bg: rgba(0, 0, 0, 0.65);


.eService_links {
  .col {
    margin-bottom: 16px;
    padding-inline: 16px;

    @media (max-width: 991px) {
      margin-bottom: 12px;
    }
  }

  .card {
    box-shadow: 0px 4px 54px 0px #00000012;
    border: 0;
    padding: 24px;
    transition: all 0.4s ease-in-out;
    cursor: pointer;

    .go-to_lnk {
      color: $primary;
      font-size: 1.5rem;
      transition: all 0.4s ease-in-out;

      @media (max-width: 991px) {
        font-size: 1rem;
      }
    }

    &:hover,
    &:focus {
      box-shadow: 0px 4px 54px 0px transparent;
      border-left-color: $grey-bg;

      .go-to_lnk {
        color: $grey-bg;
      }
    }

    @media (max-width: 991px) {
      padding: 12px;
    }

    &-title {
      color: $head2;
      // font-family: Roboto;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
      position: relative;
      padding-left: calc(16px + 32px);

      &::before {
        content: "";
        position: absolute;
        width: 32px;
        height: 32px;
        top: calc(50% - 16px);
        background-size: contain;
        background-position: center;
        left: 0;

        /* Default for non-Arabic */
        @media (max-width: 991px) {
          width: 24px;
          height: 24px;
          top: calc(50% - 12px);
        }
      }

      @media (max-width: 991px) {
        font-size: 16px;
        padding-left: calc(12px + 24px);
        line-height: 1.2;
      }
    }
  }

  /* Apply styles when LanguageService.IsArabic === true */
  &.is-arabic {
    // font-family: "Noto Kufi Arabic" !important;

    .card {
      &-title {
        padding-right: calc(16px + 32px);
        padding-left: 0;
        /* Reset left padding for Arabic */

        &::before {
          left: auto;
          /* Reset left */
          right: 0;
          /* Set right for Arabic */
        }
      }
    }
  }
}

