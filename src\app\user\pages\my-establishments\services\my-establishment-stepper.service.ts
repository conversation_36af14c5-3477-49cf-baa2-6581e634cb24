import { ElementRef, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Lookup } from '../../../../shared/models/lookup.model';

@Injectable({
  providedIn: 'root'
})
export class MyEstablishmentStepperService {

  requestId: string = '00000000-0000-0000-0000-000000000000';
  requestCode: number = 1;
  establishmentId: string = '00000000-0000-0000-0000-000000000000';
  get IsAutoStep() {
    const autoStep = localStorage.getItem('auto.step');
    return autoStep === 'on' ? true : false;
  }

  constructor() { }

  /*
  *form Data Subject
  */
  private formDataSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  formData$: Observable<any> = this.formDataSubject.asObservable();
  getFormData = (): any => this.formDataSubject.value;
  resetFormData = (): void => this.formDataSubject.next({});
  getAttachments = (): any => this.formDataSubject.value?.attachments;
  updateFormData(key: string, formData: any, isAttachments: boolean = false) {
    if (!isAttachments) {
      const updatedData = this.formDataSubject.getValue();
      updatedData[key] = formData;
      this.formDataSubject.next(updatedData);
    }
    else {
      const updatedData = { ...this.formDataSubject.getValue(), attachments: formData };
      this.formDataSubject.next(updatedData);
    }
  }


  /*
  *request Data Subject
  */
  private requestDataSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  requestData$: Observable<any> = this.requestDataSubject.asObservable();
  getRequestData = (): any => this.requestDataSubject.value;
  resetRequestData = (): void => this.requestDataSubject.next({});
  setRequestData = (data: any): void => {
    data['isFullDetails'] = true;
    this.requestDataSubject.next(data);
  }
  setRerenderRequestData = (data: any): void => {
    data['isFullDetails'] = false;
    this.requestDataSubject.next(data);
  }

  updateRequestData(key: string, requestData: any) {
    const updatedData = this.requestDataSubject.getValue();
    updatedData[key] = requestData;
    updatedData['isFullDetails'] = false;
    this.requestDataSubject.next(updatedData);
  }

  updateRequestAllData(requestData: any): void {
    const updatedData = { ...this.requestDataSubject.getValue() };

    const formKeys = [
      'BasicInformationForm',
      'ObjectivesForm',
      'BoardOfDirectorsForm',
      'BoardOfTrusteesForm',
      'FoundingMembersForm',
      'InterimCommitteeForm',
      'MembershipForm',
      'UploadDocumentForm',
      'AllocationFundForm',
      'FundServiceForm'
    ];

    formKeys.forEach(formKey => {
      if (requestData[formKey] !== undefined) {
        updatedData[formKey] = requestData[formKey];
      }
    });

    updatedData['isFullDetails'] = false;

    this.requestDataSubject.next(updatedData);
  }



  /*
  *other Data Subject
  */
  private applicationDataSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  applicationData$: Observable<any> = this.applicationDataSubject.asObservable();
  updateApplicationData = (applicationData: any): void => {
    const updatedData = { ...this.applicationDataSubject.getValue(), ...applicationData };
    this.applicationDataSubject.next(updatedData);
  }

  private enablePay: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  enablePay$: Observable<any> = this.enablePay.asObservable();
  enableDisablePay = (enable: boolean): void => this.enablePay.next(enable);


  getLookup = (list: Lookup[], id: string): Lookup => list?.filter((x) => String(x.ID).toLowerCase() == String(id)?.toLowerCase())[0];
  setAutoStep = (isEnabled: boolean): void => localStorage.setItem('auto.step', isEnabled ? 'on' : 'off');
  scrollToError = (elementRef: ElementRef): void => {
    const firstDiv = elementRef.nativeElement.querySelector('.ng-invalid');
    if (firstDiv) {
      firstDiv.scrollIntoView({ behavior: 'smooth' });
    }
  }


  private detectedChangeSubject: BehaviorSubject<number> = new BehaviorSubject<number>(0);
  detectedChangeSubject$: Observable<any> = this.detectedChangeSubject.asObservable();
  resetDetectedChangeSubject = (): void => this.detectedChangeSubject.next(0);
  updateDetectedChangeSubject(value: number) {
    if (value != 0) {
      this.detectedChangeSubject.next(value);
    }
  }

}
