.to-conditions {
  @media(max-width:1024px) {
    // display: none !important;
    margin: 14px 0 !important;
  }

  position: relative;
  cursor: pointer;


  .info-icon {
    color: $aegold-500;
    width: 32px;
    height: 32px;
    text-align: center;
  }

  .tooltip {
    position: absolute;
    top: 125%; // Position the tooltip below the link
    right: 0;
    transform: translateX(100%); // Initial position to slide in from the right
    width: 300px;
    background-color: $aegold-50 ;
    box-shadow: 0px 10px 15px -3px rgba(27, 29, 33, 0.10), 0px 4px 6px -4px rgba(27, 29, 33, 0.10);
    border-radius: 6px;
    opacity: 0;
    // visibility: hidden;
    display: none;
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
    text-align: left;
    z-index: 1;
    color: $aeblack-800;
    font-size: 12px;
    padding: 12px 24px;

    &-title {
      font-size: 14px;
      margin: 0 0 5px;
      font-weight: $font-bold;
    }

    @media (max-width: 1024px) {
      
      &:dir(ltr){
        right: -14px !important;
      }
      
      &:dir(rtl){
        right: auto !important;
      }


    }

    ul {
      padding-left: 20px; // Adds indentation for list
      margin: 0;
      list-style-type: disc; // Adds dot before each item
      color: $aeblack-700;

      li {
        font-size: 14px;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0; // Remove margin for the last item
        }
      }
    }

    ol {
      padding-left: 20px; // Adds indentation for list
      margin: 0;
      list-style-type: decimal; // Adds dot before each item
      color: $aeblack-700;

      li {
        font-size: 14px;
        margin-bottom: 4px;
        padding: 0;

        &:last-child {
          margin-bottom: 0; // Remove margin for the last item
        }
      }
    }

    // Tooltip arrow pointing up
    &::after {
      content: '';
      position: absolute;
      top: -14px;
      right: 10%;
      transform: translateX(-50%);
      border-width: 8px;
      border-style: solid;
      border-color: transparent transparent $aegold-50 transparent;
    }
  }

  // Show tooltip on hover with sliding effect
  &:hover .tooltip {
    opacity: 1;
    display: block;
    transform: translateX(0) !important; // Slide the tooltip in from the right
  }


}

body [ng-reflect-dir=rtl],
body [dir=rtl] {
  .to-conditions .tooltip {
    left: 0;
    right: auto;
    transform: translateX(-100%);
    text-align: right;

    &::after {
      right: unset !important;
      left: 10% !important;
    }

  }
}