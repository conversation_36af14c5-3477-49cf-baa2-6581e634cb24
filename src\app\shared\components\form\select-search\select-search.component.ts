import { LanguageService } from '../../../services/language.service';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Lookup } from '../../../models/lookup.model';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { MatSelect } from '@angular/material/select';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-select-search',
  templateUrl: './select-search.component.html',
  styleUrl: './select-search.component.scss',
  host: {
    '[class]': "'col-md-' + columns",
  },
})
export class SelectSearchComponent implements OnInit, OnChanges {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() data: Lookup[] = [];
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Output() onValueChange: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild('singleSelect', { static: true }) singleSelect: MatSelect;

  searchControl = new FormControl();

  filteredLookups: Lookup[];

  constructor(
    protected lang: LanguageService,
    public translate: TranslateService,
    private validationService: ValidationService
  ) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder = '';
    });
  }

  _placeholder: string = '';

  public get Placeholder() {
      if(this._placeholder)
      {
        return this._placeholder;
      }
      this._placeholder = this.placeholder ? this.placeholder : `${this.translate.instant('Please enter')}${this.translate.instant(this.label)}`;
      return this._placeholder;
    }

  ngOnInit(): void {
    this.initialize();
    this.translate.onLangChange.subscribe(_ => this.sortData())
    this.searchControl.valueChanges.subscribe((value) => this.filteredLookups = this._filter(value));
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data']) {
      this.sortData();
      this.initialize();
    }
  }

  sortData = ():void => {
    this.data = this.data.filter(d => d.NameArabic !== '' && d.NameEnglish !== '')?.sort((a,b) => this.lang.IsArabic ?  a.NameArabic.localeCompare(b.NameArabic) : a.NameEnglish.localeCompare(b.NameEnglish));
  }

  initialize() {
    this.filteredLookups = this.data;
  }


  private _filter(value: string | Lookup): Lookup[] {
    if (!this.data) {
      return [];
    }
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    if (!this.data) {
      return [];
    } return this.data.filter(
      (lookup) =>
        lookup?.NameEnglish?.toLowerCase().includes(filterValue) ||
        lookup?.NameArabic?.toLowerCase().includes(filterValue)
    );
  }

  onSelectionChange(item: any) {
    this.control.setValue(item?.value);
     this.onValueChange.emit(item);
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(
            key,
            this.control.errors[key]
          );
        }
      }
    }
    return null;
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
