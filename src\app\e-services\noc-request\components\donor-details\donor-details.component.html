<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="col-md-12 d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center">
        {{(messageTranslationPrefix+'title') | translate}}
      </h1>
    </div>


    <div class="row section-separator">
      <div class="col-md-12">
        <app-select [label]="messageTranslationPrefix+'donorType' | translate" [control]="fb?.DonorType"
          (onValueChange)="selectedDonorType($event)" [data]="donorTypes" />
      </div>

      <!-- Outside UAE -->
      @if(selectedLegalFormType === RequestType.OutsideUae){
      <!-- Individual -->
      @if(fb?.DonorType?.value?.ID == donorTypesEnum.Individual) {
      <div class="col-md-12">
        <app-input [label]="messageTranslationPrefix+'fullName' | translate" [control]="fb?.FullName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="nationalities" [label]="messageTranslationPrefix+'nationality' | translate"
          [control]="fb?.Nationality" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'placeOfBirth' | translate"
          [control]="fb?.PlaceOfBirth" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'currentResidence' | translate"
          [control]="fb?.CurrentResidence" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.InternationalMobileWithoutMask" [label]="messageTranslationPrefix+'mobile' | translate"
          [control]="fb?.Mobile" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'passportNumber' | translate" [control]="fb?.PassportNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select [data]="passportTypes" [label]="messageTranslationPrefix+'passportType' | translate"
          [control]="fb?.PassportType" />
      </div>
      <!-- if user selected passport type other -->
      @if(fb?.PassportType?.value?.ID == 4){
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'passportType' | translate"
          [placeholder]="messageTranslationPrefix+'otherPassportType' | translate" [control]="fb?.OtherPassportType" />
      </div>
      }

      <div class="col-md-6 col-sm-12">
        <app-input-date [min]="minDate" [label]="messageTranslationPrefix+'passportExpiryDate' | translate"
          [control]="fb?.PassportExpiryDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'jobTitle' | translate" [control]="fb?.JobTitle" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'employer' | translate" [control]="fb?.Employer" />
      </div>
      }
      <!-- Individual -->

      <!-- Public Sector -->
      @if(fb?.DonorType?.value?.ID == donorTypesEnum.Government_Sector) {
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'entityName' | translate" [control]="fb?.EntityName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'country' | translate"
          [control]="fb?.Country" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'city' | translate" [control]="fb?.City" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'businessDomain' | translate" [control]="fb?.BusinessDomain" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.InternationalLandLineWithoutMask" [label]="messageTranslationPrefix+'phoneNumber' | translate"
          [control]="fb?.PhoneNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      }
      <!-- Public Sector -->

      <!-- Private Sector -->
      @if(fb?.DonorType?.value?.ID == donorTypesEnum.Private_Sector) {
      <div class="col-md-12">
        <app-input [label]="messageTranslationPrefix+'companyName' | translate" [control]="fb?.CompanyName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'country' | translate"
          [control]="fb?.Country" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'city' | translate" [control]="fb?.City" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'licensingAuthority' | translate"
          [control]="fb?.OutsideLicensingAuthority" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'commercialLicenseNumber' | translate"
          [control]="fb?.CommercialLicenseNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseIssuanceDate' | translate"
          [control]="fb?.LicenseIssuanceDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseExpiryDate' | translate"
          [control]="fb?.LicenseExpiryDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.InternationalLandLineWithoutMask" [label]="messageTranslationPrefix+'phoneNumber' | translate" [control]="fb?.PhoneNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'email' | translate" [control]="fb?.Email" />
      </div>
      }
      <!-- Private Sector -->

      <!-- NPO Sector -->
      @if(fb?.DonorType?.value?.ID == donorTypesEnum.NPO_Sector) {
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'entityName' | translate" [control]="fb?.EntityName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'country' | translate"
          [control]="fb?.Country" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'city' | translate" [control]="fb?.City" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'licensingAuthority' | translate"
          [control]="fb?.OutsideLicensingAuthority" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'licenseNumber' | translate" [control]="fb?.LicenseNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseIssuanceDate' | translate"
          [control]="fb?.LicenseIssuanceDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseExpiryDate' | translate"
          [control]="fb?.LicenseExpiryDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'npoCategory' | translate" [control]="fb?.Category" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.InternationalLandLineWithoutMask" [label]="messageTranslationPrefix+'phoneNumber' | translate"
          [control]="fb?.PhoneNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      }
      <!-- NPO Sector -->
      }

      <!-- Inside UAE -->
      @if(selectedLegalFormType === RequestType.InsideUae){
      <!-- Individual -->
      @if(fb?.DonorType?.value?.ID == 1) {
      <div class="col-md-12">
        <div class="get-info row">
          <div class="col-md-4 col-sm-12">
            <app-input [label]="messageTranslationPrefix+'emiratesId' | translate" [control]="fb?.EmiratesId" />
          </div>
          <div class="col-md-4 col-sm-12">
            <app-input-date [label]="messageTranslationPrefix+'dateOfBirth' | translate" [control]="fb?.DateOfBirth" />
          </div>
          <div class="col-md-4">
            <button [disabled]="!(fb?.EmiratesId?.value&&fb?.DateOfBirth?.value)" type="button"
              class="btn btn-sm btn-primary" (click)="getIndividualInfo()">{{messageTranslationPrefix+'getInformation' |
              translate}}</button>
          </div>
        </div>
      </div>

      @if(individualDonorDetails !== undefined){
      <div style="margin-top: 24px !important;" class="d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <!-- <th class="align-middle text-center" scope="col">
                  {{messageTranslationPrefix+'nameAr'|translate }}
                </th>
                <th class="align-middle text-center" scope="col">
                  {{messageTranslationPrefix+'nameEn'|translate }}
                </th> -->
                <th class="align-middle text-center" scope="col">
                  {{messageTranslationPrefix+'emiratesId'|translate }}
                </th>
                <th class="align-middle text-center" scope="col">
                  {{messageTranslationPrefix+'dateOfBirth'|translate }}
                </th>
                <th class="align-middle text-center text-center" scope="col">
                  {{messageTranslationPrefix+"actions" | translate }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <!-- <td class="align-middle text-center" lang="ar">
                  {{individualDonorDetails.fullArabicName}}
                </td>
                <td class="align-middle text-center" lang="en">
                  {{individualDonorDetails.fullEnglishName}}
                </td> -->
                <td class="align-middle text-center">
                  {{individualDonorDetails.emiratesId}}
                </td>
                <td class="align-middle text-center">
                  {{individualDonorDetails.dateOfBirth | date: 'dd/MM/yyyy'}}
                </td>
                <td class="align-middle text-center">
                  <button type="button" (click)="deleteIndividualDonor()" class="btn text-primary">
                    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
                        fill="#92722A" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="figma-card-container">
        <div class="figma-card">
          <div class="figma-card-content">
            <button type="button" (click)="deleteIndividualDonor()" class="btn text-primary">
              <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
                  fill="#92722A" />
              </svg>
            </button>
            <!-- <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix+"nameAr" | translate}}:
              </div>
              <div class="dynamic-value" lang="ar">
                {{individualDonorDetails.fullArabicName}}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix+"nameEn" | translate}}:
              </div>
              <div class="dynamic-value" lang="en">
                {{individualDonorDetails.fullEnglishName}}
              </div>
            </div> -->
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix+"emiratesId" | translate}}:
              </div>
              <div class="dynamic-value">
                {{individualDonorDetails.emiratesId}}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix+"dateOfBirth" | translate}}:
              </div>
              <div class="dynamic-value">
                {{individualDonorDetails.dateOfBirth | date: 'yyyy-MM-dd'}}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div style="margin-top: 24px !important;" class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'jobTitle' | translate" [control]="fb?.JobTitle" />
      </div>
      <div style="margin-top: 24px !important;" class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'employer' | translate" [control]="fb?.Employer" />
      </div>
      }

      @if (false) {
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'nameEn' | translate" [control]="fb?.NameEn" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'nameAr' | translate" [control]="fb?.NameAr" />
      </div>
      <div class="col-md-12">
        <app-select-search [data]="countries" [label]="messageTranslationPrefix+'nationality' | translate"
          [control]="fb?.Nationality" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'unifiedNumber' | translate" [control]="fb?.UnifiedNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="emirates" [label]="messageTranslationPrefix+'emirate' | translate"
          [control]="fb?.Emirate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Mobile" [label]="messageTranslationPrefix+'mobile' | translate"
          [control]="fb?.Mobile" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      }

      }
      <!-- Individual -->

      <!-- Public Sector -->
      @if(fb?.DonorType?.value?.ID == 2) {
      <div class="col-md-12">
        <app-input [label]="messageTranslationPrefix+'entityName' | translate" [control]="fb?.EntityName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="entityLegalForms" [label]="messageTranslationPrefix+'entityType' | translate"
          [control]="fb?.EntityType" [data]="entityTypes" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="emirates" [label]="messageTranslationPrefix+'emirate' | translate"
          [control]="fb?.Emirate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.LandLine" [label]="messageTranslationPrefix+'phoneNumber' | translate"
          [control]="fb?.PhoneNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      }
      <!-- Public Sector -->

      <!-- Private Sector -->
      @if(fb?.DonorType?.value?.ID == 3) {
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'entityOrCompanyName' | translate" [control]="fb?.CompanyName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="emirates" [label]="messageTranslationPrefix+'emirate' | translate" [control]="fb?.Emirate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="licensingAuthorities" [label]="messageTranslationPrefix+'licensingAuthority' | translate"
          [control]="fb?.LicensingAuthority" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'commercialLicenseNumber' | translate"
          [control]="fb?.CommercialLicenseNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseIssuanceDate' | translate" [control]="fb?.LicenseIssuanceDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseExpiryDate' | translate" [control]="fb?.LicenseExpiryDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.LandLine" [label]="messageTranslationPrefix+'phoneNumber' | translate"
          [control]="fb?.PhoneNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
          [control]="fb?.Email" />
      </div>
      }
      <!-- Private Sector -->

      <!-- NPO Sector -->
      @if(fb?.DonorType?.value?.ID == 4) {
      <div class="col-md-12">
        <div class="get-info">
          <div class="col-md-8 col-sm-12">
            <app-input [label]="messageTranslationPrefix+'unifiedNumberOrNpoName' | translate"
              [control]="fb?.UnifiedNumber" />
          </div>
          <div class="col-md-4 col-sm-12">
            <button [disabled]="!(fb?.UnifiedNumber?.value)" type="button" class="btn btn-sm btn-primary"
              (click)="getEstablishmentDonorDetails()">
              {{messageTranslationPrefix+'getInformation' | translate}}
            </button>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoNameEn' | translate" [control]="fb?.NpoNameEn" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoNameAr' | translate" [control]="fb?.NpoNameAr" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="legalForms" [label]="messageTranslationPrefix+'npoLegalForm' | translate"
          [control]="fb?.NpoLegalForm" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoDeclarationDecision' | translate"
          [control]="fb?.DeclarationDecisionLink" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="categories" [label]="messageTranslationPrefix+'mainCategory' | translate"
          [control]="fb?.Category" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="emirates" [label]="messageTranslationPrefix+'headquartersEmirate' | translate"
          [control]="fb?.HeadquartersEmirate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [data]="licensingAuthorities" [label]="messageTranslationPrefix+'licensingEntity' | translate"
          [control]="fb?.LicensingEntity" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'licenseNumber' | translate" [control]="fb?.LicenseNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseIssuanceDate' | translate"
          [control]="fb?.LicenseIssuanceDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'licenseExpiryDate' | translate"
          [control]="fb?.LicenseExpiryDate" />
      </div>
      }
      <!-- NPO Sector -->
      }

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
        (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()"> {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
