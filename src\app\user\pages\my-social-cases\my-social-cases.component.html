<div class="container dashboard-padding">
  <div *ngIf="auth.isAuthenticated()" class="application-overview">
    <!-- Header -->
    <div class="application-overview__header d-flex justify-content-between align-items-center">
      <div>
        <h2>{{ 'userPages.applications.title' | translate }}</h2>
        <p>{{ 'Keep track of your applications and tasks' | translate }}</p>
      </div>

    </div>

    <!-- Filters -->
    <div class="application-overview__filters">
      <div class="d-flex gap-3 order-lg-1">
        <div>
          <form (submit)="$event.preventDefault(); search();">
            <div class="aegov-form-control">
              <div class="form-control-input">
                <span class="control-suffix">
                  <button class="basic-filled-button" (click)="search()" type="submit">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
                      <path
                        d="M25.1191 23.8809L19.6427 18.4056C21.2299 16.5 22.0214 14.0558 21.8525 11.5814C21.6836 9.10709 20.5672 6.79313 18.7357 5.12091C16.9041 3.4487 14.4984 2.54697 12.0189 2.60332C9.53944 2.65967 7.17715 3.66976 5.42345 5.42345C3.66976 7.17715 2.65967 9.53944 2.60332 12.0189C2.54697 14.4984 3.4487 16.9041 5.12091 18.7357C6.79313 20.5672 9.10709 21.6836 11.5814 21.8525C14.0558 22.0214 16.5 21.2299 18.4056 19.6427L23.8809 25.1191C23.9622 25.2004 24.0588 25.2649 24.165 25.3088C24.2712 25.3528 24.385 25.3755 24.5 25.3755C24.615 25.3755 24.7288 25.3528 24.835 25.3088C24.9413 25.2649 25.0378 25.2004 25.1191 25.1191C25.2004 25.0378 25.2649 24.9413 25.3088 24.835C25.3528 24.7288 25.3755 24.615 25.3755 24.5C25.3755 24.385 25.3528 24.2712 25.3088 24.165C25.2649 24.0588 25.2004 23.9622 25.1191 23.8809ZM4.37501 12.25C4.37501 10.6925 4.83687 9.16993 5.70218 7.87489C6.5675 6.57985 7.79741 5.57049 9.23637 4.97445C10.6753 4.37841 12.2587 4.22246 13.7863 4.52632C15.3139 4.83018 16.7171 5.5802 17.8185 6.68154C18.9198 7.78288 19.6698 9.18607 19.9737 10.7137C20.2775 12.2413 20.1216 13.8247 19.5256 15.2636C18.9295 16.7026 17.9202 17.9325 16.6251 18.7978C15.3301 19.6631 13.8075 20.125 12.25 20.125C10.1621 20.1227 8.16044 19.2923 6.6841 17.8159C5.20775 16.3396 4.37732 14.3379 4.37501 12.25Z" />
                    </svg>
                  </button>
                </span>
                <input type="text" id="table-complete-search" name="searchTerm" [placeholder]="'Search' | translate"
                  [(ngModel)]="searchTerm">
              </div>
            </div>
          </form>
        </div>
      </div>
      <div class="overflow-x-auto application-overview__filters-tabs-parent">
        <form class="overflow-x-auto application-overview__filters-tabs">
          <div class="form-check" (click)="statusId='-1';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1"
              [checked]="statusId=='-1'">
            <label class="form-check-label" for="flexRadioDefault11">{{ 'status.all' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='100';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2"
              [checked]="statusId=='100'">
            <label class="form-check-label" for="flexRadioDefault21">{{ 'status.Completed' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='100000001';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault3"
              [checked]="statusId=='100000001'">
            <label class="form-check-label" for="flexRadioDefault31">{{ 'status.InProgress' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='2';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault5"
              [checked]="statusId=='2'">
            <label class="form-check-label" for="flexRadioDefault51">{{ 'status.OnHold' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='1';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault6"
              [checked]="statusId=='1'">
            <label class="form-check-label" for="flexRadioDefault61">{{ 'status.Draft' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='100000002';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault9"
              [checked]="statusId=='100000002'">
            <label class="form-check-label" for="flexRadioDefault91">{{ 'status.Returned' | translate }}</label>
          </div>
          <div class="form-check" (click)="statusId='100000005';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault10"
              [checked]="statusId=='100000005'">
            <label class="form-check-label" for="flexRadioDefault101">{{ 'status.PendingPayment' | translate }}</label>
          </div>
        </form>
      </div>
    </div>

    <!-- Content: Table -->
    <div class="application-overview__content mt-3">
      <div class="table-listing-full-width">
        <div>
          <table class="table">
            <thead>
              <tr>
                <th scope="col" sortable="RequestName" (sort)="onSort($event)">{{ 'applications.ApplicationNumber' |
                  translate }}</th>
                @if(lang.IsArabic){
                <th scope="col" class="text-center" sortable="ServiceCatalogueNameAR" (sort)="onSort($event)">{{
                  'applications.ServiceName' | translate }}</th>
                } @else {
                <th scope="col" class="text-center" sortable="ServiceCatalogueName" (sort)="onSort($event)">{{
                  'applications.ServiceName' | translate }}</th>
                }
                <th scope="col" class="text-center" sortable="CreatedDate" (sort)="onSort($event)">{{
                  'applications.SubmissionDate' | translate }}</th>
                <th scope="col" class="text-center">{{ 'applications.FinalComment' | translate }}</th>
                <th scope="col" class="text-center" sortable="Status" (sort)="onSort($event)">{{ 'applications.Status' |
                  translate }}</th>
                <th scope="col">{{ 'applications.Actions' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              @for (app of filteredData; track app.Id; let i = $index) {
              <tr>
                <!-- Desktop view -->
                <td class=" desktop-only">
                  <span class="table-listing-full-width__inner-title">{{ app.RequestName }}</span>
                </td>
                <td class="desktop-only text-center maxWidth">
                  {{ lang.IsArabic ? (app.ServiceCatalogueNameAR || app.Template?.TemplateNameAr) :
                  (app.ServiceCatalogueName || app.Template?.TemplateName) }}
                </td>
                <td class="desktop-only text-center">{{ app.CreatedDate | date:'dd/MM/yyyy' }}</td>
                <td class="desktop-only text-center maxWidth">{{ app.FinalComment }}</td>
                <td class="desktop-only text-center table-listing-full-width__status">
                  <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                    <circle cx="4.75" cy="4" r="4" [attr.fill]="getStatusInfo(app.Status).color" />
                  </svg>
                  <span> {{ getStatusInfo(app.Status).label }} </span>
                </td>
                <!-- Mobile view -->
                <td class="mobile-only">
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'applications.ApplicationNumber' | translate}}:
                    </div>
                    <div>
                      <span class="table-listing-full-width__inner-title">{{ app.RequestName }}</span>
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'applications.ServiceName' | translate}}:
                    </div>
                    <div>
                      {{ lang.IsArabic ? (app.ServiceCatalogueNameAR || app.Template?.TemplateNameAr) :
                      (app.ServiceCatalogueName || app.Template?.TemplateName) }}
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'applications.SubmissionDate' | translate}}:
                    </div>
                    <div>
                      {{ app.CreatedDate | date:'dd/MM/yyyy' }}
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'applications.FinalComment' | translate}}:
                    </div>
                    <div>
                      {{ app.FinalComment }}
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'applications.Status'| translate}}:
                    </div>
                    <div class="table-listing-full-width__status">
                      <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                        <circle cx="4.75" cy="4" r="4" [attr.fill]="getStatusInfo(app.Status).color" />
                      </svg>
                      <span> {{ getStatusInfo(app.Status).label }} </span>

                    </div>
                  </div>
                </td>
                <td class="text-center" (click)="$event.stopPropagation()">
                  @if(app.EligibleForEdit || isDraft(app.Status)){
                  <button mat-icon-button [matMenuTriggerFor]="actionMenu" class="action-button" type="button"
                    aria-label="Actions menu">
                    <mat-icon>more_vert</mat-icon>
                  </button>
                  <mat-menu #actionMenu="matMenu" class="action-menu">
                    @if(app.EligibleForEdit || isDraft(app.Status)){
                    <button mat-menu-item
                      (click)="isDraft(app.Status) ? onProceedClick(app) : handleEditClick($event, app)">
                      <mat-icon>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M14.85 1.54a1.25 1.25 0 0 0-1.77 0L4.56 10.05a.5.5 0 0 0-.13.23l-1.1 4.4a.25.25 0 0 0 .3.3l4.4-1.1a.5.5 0 0 0 .23-.13l8.52-8.52a1.25 1.25 0 0 0 0-1.77L14.85 1.54zM5.75 11.8l-1.1 0.28 0.28-1.1 6.54-6.54 0.82 0.82-6.54 6.54zM15.67 3.22l-0.82-0.82 1.1-1.1 0.82 0.82-1.1 1.1z"
                            fill="currentColor" />
                        </svg>
                      </mat-icon>
                      <span>{{ 'Edit' | translate }}</span>
                    </button>
                    }
                    @if(app.EligibleForEdit && !isInflationTemplate(app) && !isDraft(app.Status)){
                    <mat-divider></mat-divider>

                    <button mat-menu-item (click)="handleEditClick($event, app, false, true, false)">
                      <mat-icon>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M14.85 1.54a1.25 1.25 0 0 0-1.77 0L4.56 10.05a.5.5 0 0 0-.13.23l-1.1 4.4a.25.25 0 0 0 .3.3l4.4-1.1a.5.5 0 0 0 .23-.13l8.52-8.52a1.25 1.25 0 0 0 0-1.77L14.85 1.54zM5.75 11.8l-1.1 0.28 0.28-1.1 6.54-6.54 0.82 0.82-6.54 6.54zM15.67 3.22l-0.82-0.82 1.1-1.1 0.82 0.82-1.1 1.1z"
                            fill="currentColor" />
                        </svg>
                      </mat-icon>
                      <span>{{ 'services.swp.applyForHousingAllowance' | translate }}</span>
                    </button>
                    <button mat-menu-item (click)="handleEditClick($event, app, false, false, true)">
                      <mat-icon>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M14.85 1.54a1.25 1.25 0 0 0-1.77 0L4.56 10.05a.5.5 0 0 0-.13.23l-1.1 4.4a.25.25 0 0 0 .3.3l4.4-1.1a.5.5 0 0 0 .23-.13l8.52-8.52a1.25 1.25 0 0 0 0-1.77L14.85 1.54zM5.75 11.8l-1.1 0.28 0.28-1.1 6.54-6.54 0.82 0.82-6.54 6.54zM15.67 3.22l-0.82-0.82 1.1-1.1 0.82 0.82-1.1 1.1z"
                            fill="currentColor" />
                        </svg>
                      </mat-icon>
                      <span>{{ 'services.swp.applyForAcademicExcellenceAllowance' | translate }}</span>
                    </button>
                    <button mat-menu-item (click)="handleEditClick($event, app, true)">
                      <mat-icon>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M14.85 1.54a1.25 1.25 0 0 0-1.77 0L4.56 10.05a.5.5 0 0 0-.13.23l-1.1 4.4a.25.25 0 0 0 .3.3l4.4-1.1a.5.5 0 0 0 .23-.13l8.52-8.52a1.25 1.25 0 0 0 0-1.77L14.85 1.54zM5.75 11.8l-1.1 0.28 0.28-1.1 6.54-6.54 0.82 0.82-6.54 6.54zM15.67 3.22l-0.82-0.82 1.1-1.1 0.82 0.82-1.1 1.1z"
                            fill="currentColor" />
                        </svg>
                      </mat-icon>
                      <span>{{ ('services.swp.applyForHousingEducationTopup'
                        | translate) }}</span>
                    </button>
                    }
                  </mat-menu>
                  }
                </td>
              </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Footer: pagination and page size -->
    <div class="application-overview__footer mt-3">
      <div
        class="application-overview__footer-total-count d-flex justify-content-between align-items-center flex-sm-column">
        <span class="w-sm-100 text-start">{{ 'Total count' | translate }}: {{ filteredCount }}</span>
        <div class="mobile-only d-flex gap-2 align-items-center w-sm-100 justify-content-sm-between">
          <span>{{ 'Show per page' | translate }}:</span>
          <select class="form-select" style="width:auto" name="pageSize" [(ngModel)]="pageSize"
            (change)="onSelectChange()">
            <option [value]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [value]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [value]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>
      <div class="aegov-pagination">
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize"
          [collectionSize]="filteredCount" (pageChange)="onPageChange()"></ngb-pagination>
      </div>
    </div>


  </div>

  <!-- Edit Reasons Modal (reuses Bootstrap modal styles) -->
  <div class="modal fade show d-block edit-reasons-modal" tabindex="-1" role="dialog" *ngIf="showEditModal"
    (click)="closeEditModal()">
    <div class="modal-dialog" role="document" (click)="$event.stopPropagation()">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">{{ 'Edit' | translate }}</h5>
          <button type="button" class="btn-close" aria-label="Close" (click)="closeEditModal()"></button>
        </div>
        <div class="modal-body">
          <p class="mb-3">{{ 'Please select reason(s) for editing' | translate }}</p>
          <div class="edit-reasons-form">
            <app-select [data]="reasonsToShow" [placeholder]="'placeholder' | translate"
              [control]="selectedReasonsControl" [required]="true" [columns]="12"
              (onValueChange)="onReasonsChange($event)" [multiple]="true" [label]="'reasonToEdit'">
            </app-select>
          </div>
        </div>
        <div class="modal-footer d-flex justify-content-between flex-wrap gap-2">
          <button type="button" class="basic-button" (click)="closeEditModal()">{{ 'Cancel' | translate }}</button>
          <button type="button" class="basic-filled-button" [disabled]="selectedReasons.length===0 || isLoading"
            (click)="confirmReopen()">
            <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2" role="status"
              aria-hidden="true"></span>
            {{ 'Edit' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

</div>