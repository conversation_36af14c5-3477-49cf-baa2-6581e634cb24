import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function letterPatternValidatorNoNumbers(
  isRequired: boolean = true,
  maxLength: number = 500,
  minLength: number = 2
): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    const containsNumbers = /\d/;

    if (!value) {
      if (isRequired) {
        return { required: true };
      }
    } else {
      if (value.length < minLength) {
        return { minLengthIs2: true };
      }

      if (value.length > maxLength) {
        return { maxLength: true };
      }

      if (containsNumbers.test(value)) {
        return { containsNumbers: true };
      }
    }

    return null;
  };
}
