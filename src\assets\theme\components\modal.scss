// .modal-body .row app-input {
//   margin-bottom: 24px;
// }

.modal-body {
  // @media (max-width: 1024px) {
    .row {
      row-gap: 10px !important;
    }
  // }
}

.ar_lang .mat-mdc-form-field-input-control:not(:placeholder-shown) {
  text-align: right;
}

.modal-footer {
  margin-top: 10px !important;
  @media (max-width: 1023px) {
    justify-content: space-between;
  }
}

.location_modal {
  --bs-modal-width: 590px;

  @media (min-width: 768px) {
    .modal-dialog {
      max-width: var(--bs-modal-width);
    }
  }
}

.modal-dialog {
  display: flex;
  align-items: center;
  // min-height: 100vh;
  min-height: 90%;
  margin-top: 0;

  @media (max-width: 1024px) {
    max-width: 100% !important;
  }
}

@media (min-width: 768px) {
  .modal-content {
    padding: 32px 24px;
  }
}

button:focus:not(:focus-visible) {
  box-shadow: none;
}

button.swal2-confirm.swal2-styled.swal2-default-outline {
  // background-color: #b52520 !important;
  // border: 1px solid #b52520;
  height: 46px;
  min-width: 77px;
}

button.swal2-cancel.swal2-styled.swal2-default-outline {
  border: 2px solid #d83731 !important;
  border-radius: 8px !important;
  color: #d83731 !important;
  padding: 10px 16px !important;
  height: 46px !important;
  background: none !important;
  min-width: 77px;
}

.swal2-icon.swal2-warning.swal2-icon-show {
  display: none !important;
}

.swal2-title {
  margin-bottom: 0;
  font-size: 24px;
  font-weight: $font-bold;
  color: $aeblack-800;
  position: relative;

  //padding-left: 65px;
  &::before {
    // content: "";
    // position: absolute;
    // left: 20px;
    // top: calc(50% - 9px);
    // width: 32px;
    // height: 32px;
    // background: url("../../images/icons/WarningOctagon.svg") no-repeat center;
    // background-size: contain;
  }
}

.swal2-html-container {
  font-size: 16px !important;
  color: $aeblack-500 !important;
  padding-top: 0 !important;
  line-height: 1.5 !important;
}

.swal2-actions {
  width: 100%;
  justify-content: end;
  padding-inline: 1em;
  flex-direction: row-reverse;
}

body[dir="rtl"] {

  .swal2-title,
  .swal2-html-container,
  .swal2-actions {
    text-align: right !important;
    direction: rtl !important;
  }

  .swal2-title {
    padding-left: 1em;

    // padding-right: 65px;
    &::before {
      left: auto;
      right: 20px;
    }
  }
}

body[dir="ltr"] {

  .swal2-title,
  .swal2-html-container,
  .swal2-actions {
    text-align: left !important;
    direction: ltr !important;
  }

  .swal2-title {
    padding-left: 1em;

    // padding-right: 65px;
    &::before {
      right: auto;
      left: 20px;
    }
  }
}