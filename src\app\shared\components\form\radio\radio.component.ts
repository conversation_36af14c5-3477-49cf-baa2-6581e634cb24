import { Component, Input, OnInit } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Lookup } from '../../../models/lookup.model';
import { LanguageService } from '../../../services/language.service';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-radio',
  templateUrl: './radio.component.html',
  styleUrl: './radio.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class RadioComponent implements OnInit {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() data: Lookup[] | undefined = [];
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Input() isDisabled: boolean = false;
  @Input()  showHint: boolean = true;
  @Input() classList: string[] = [''];

  selectedItem: Lookup;

  constructor(protected lang: LanguageService, public translate: TranslateService, private validationService: ValidationService) { }
  ngOnInit(): void {
  }

  onSelectionChange(item: any) {
    this.control.setValue(item?.value)

  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }
  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
