import { AfterViewInit, Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { Feedback, FileType } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { ENTITY_SECTOR_TYPES, ASSOCIATION_CLASSIFICATION_TYPES } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { landLineValidator } from '../../../../../../shared/validators/custom.landLine.Validator';
import { letterPatternValidator } from '../../../../../../shared/validators/letter-pattern-validator';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-establishment-basic-information',
  templateUrl: './establishment-basic-information.component.html',
  styleUrls: ['./establishment-basic-information.component.scss']
})
export class EstablishmentBasicInformationComponent
  extends MyEstablishmentComponentBase
  implements OnInit, AfterViewInit, OnChanges {

  requestId: any;
  proposedNameForm: FormGroup;
  targetGroupForm: FormGroup;
  proposedNameEditIndex: number;
  targetGroupEditIndex: number;
  membershipExpiryType: { value: number; label: string }[];
  associationClassificationOptions: Lookup[] = [];
  emirates: Lookup[];
  EntitySectorTypes: Lookup[];
  licensingAuthority: Lookup[];
  filteredLicensingAuthorities: Lookup[] = [];
  intialAddress: string;
  maxDate: Date;
  exampleOfActivitiesForm: FormGroup;

  get fb(): any {
    return this.form.controls;
  }
  get proposedNames(): FormArray {
    return this.form.get('proposedNames') as FormArray;
  }

  get pnControls(): any {
    return this.proposedNameForm.controls;
  }
  get targetGroupControls(): any {
    return this.targetGroupForm.controls;
  }
  get targetGroups(): FormArray {
    return this.form.get('targetGroups') as FormArray;
  }

  get eControls(): any {
    return this.exampleOfActivitiesForm.controls;
  }

  get NpoActivityPrograms(): FormArray {
    return this.form.get('NpoActivityPrograms') as FormArray;
  }


  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.basicInformation.';
    this.initForm();

    const today = new Date();
    today.setHours(23, 59, 59, 999);
    this.maxDate = today;

    this.EntitySectorTypes = ENTITY_SECTOR_TYPES;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {

    this.removeValidations();

    this.StepperService.formData$.subscribe((res) => {
      if (res && res.establishmentId) {
        this.form.get('EstablishmentId')?.setValue(res.establishmentId);
      }
    });

    this.MyEstablishmentService.lookupData$.subscribe((data) => {
      if (data) {
        // this.emirates = data.Emirates.filter(_ => _.Npobylowornot === 0);
        this.emirates = data.Emirates;

        this.associationClassificationOptions = ASSOCIATION_CLASSIFICATION_TYPES;
        this.licensingAuthority = data.LicensingAuthority;
        this.filteredLicensingAuthorities = this.licensingAuthority;
        this.StepperService.requestData$.subscribe(_ => {
          if (_ && (_.isFullDetails == true)) {
            this.mapData(_?.BasicInformationForm);
            this.form.get('FirstProposedNameEn')?.disable();
            this.form.get('FirstProposedNameAr')?.disable();
          } else if (_ && (_.isFullDetails == false)) {
            this.proposedNames.clear();
            this.targetGroups.clear();
            this.NpoActivityPrograms.clear();
            this.mapData(_?.BasicInformationForm);
          }

          this.form.get('Emirate')?.value !== '' && this.form.get('Emirate')?.value !== null && this.form.get('Emirate')?.value !== undefined && this.form.get('Emirate')?.value !== this.EMPTY_GUID ? 
          this.form.get('Emirate')?.disable() : 
          this.form.get('Emirate')?.enable();

          if (this.fb.LegalFormId?.value === this.LEGAL_FORM_TYPES.SocialSolidarityFunds) {
            this.setInputsSocialSolidarityFund();
          } else {
            this.FormService.clearValidators(this.form, ['EntityName']);
            this.FormService.clearValidators(this.form, ['EntityNameAr']);
            this.FormService.clearValidators(this.form, ['Entitysectortype']);
            this.FormService.clearValidators(this.form, ['FundsAllocationAmount']);
            this.FormService.clearValidators(this.form, ['Numberofemployees']);
          }
        });
      }
    });

    ((this.fb?.Emirate) as FormControl).valueChanges.subscribe(_ => {
      if (_) {
        this.intialAddress = this.fb?.Emirate?.value?.NameEnglish;
        this.addLandLineValidation(_.ID);
      }
    })

    this.resetFromValidation(this.form);
    this.saveLegalForm();

    this.paginationExampleOfActivities();
    this.NpoActivityPrograms.valueChanges.subscribe((_) =>
      this.paginationExampleOfActivities()
    );
  }

  setInputsSocialSolidarityFund = (): void => {
    this.FormService.addValidators(this.form, ['EntityName'], []);
    this.FormService.addValidators(this.form, ['EntityNameAr'], []);
    this.FormService.addValidators(this.form, ['Entitysectortype'], []);
    this.form.get("Entitysectortype")?.valueChanges.subscribe(_ => {
      if (_ && _?.ID == 3) {
        this.FormService.addValidators(this.form, ['Numberofemployees'], [Validators.min(100)]);
      } else {
        this.form.get("Numberofemployees")?.setValue('');
        this.FormService.clearValidators(this.form, ['Numberofemployees']);
      }
    })
  }

  initForm = (): void => {
    this.proposedNameForm = this.FormBuilder.group({
      id: [''],
      proposedNameEn: ['', []],
      proposedNameAr: ['', []],
      SatusReason: [''],
    });

    this.targetGroupForm = this.FormBuilder.group({
      id: [''],
      targetGroupNameEn: ['', [Validators.required, letterPatternValidator('en')]],
      targetGroupNameAr: ['', [Validators.required, letterPatternValidator('ar')]],
      SatusReason: [''],
      canEdit: new FormControl(''),
    });

    this.exampleOfActivitiesForm = this.FormBuilder.group({
      id: [''],
      exampleOfActivitiesEn: ['', [Validators.required, letterPatternValidator('en')]],
      exampleOfActivitiesAr: ['', [Validators.required, letterPatternValidator('ar')]],
      SatusReason: [''],
    });
  }

  validateProposedName = (item: any): Boolean => {
    if (!this.proposedNames || !this.proposedNames.controls)
      return false;

    const matchingName = this.proposedNames.controls.find(control => {
      const proposedNameEn = control.get('proposedNameEn')?.value?.trim()?.toLowerCase();
      const proposedNameAr = control.get('proposedNameAr')?.value?.trim()?.toLowerCase();
      const id = control.get('id')?.value;
      return (proposedNameEn === item.proposedNameEn.trim().toLowerCase() || proposedNameAr === item.proposedNameAr?.trim().toLowerCase()) && id !== item.id;
    });
    return !!matchingName;
  }

  manageProposedName = (item: any, type: GridActionTypesEnum): void => {
    if (this.validateProposedName(item)) {
      this.NotifyService.showError('notify.error', 'notify.proposedNameExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT) {
      const newProposedName = this.FormBuilder.group({
        id: [this.generateDistinctId()],
        proposedNameEn: [
          item.proposedNameEn,
          [],
        ],
        proposedNameAr: [
          item.proposedNameAr,
          [

          ],
        ],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
      });

      this.proposedNames.push(newProposedName);
    } else {
      this.proposedNames
        .at(this.proposedNameEditIndex)
        .setValue({
          id: item.id,
          proposedNameEn: item.proposedNameEn,
          proposedNameAr: item.proposedNameAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
        });
    }


    this.proposedNameForm.reset();
    this.modalService.dismissAll();

    this.resetAiParamter();
  }

  handleEditProposedName = (data: any, idx: number): void => {
    this.resetAiParamter();
    this.proposedNameEditIndex = idx;
    this.proposedNameForm.patchValue({
      id: data?.value?.id,
      proposedNameEn: data?.value?.proposedNameEn,
      proposedNameAr: data?.value?.proposedNameAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
  }

  removeProposedName = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let proposedName = this.proposedNames?.value[idx];
      if (proposedName)
        this.removeFromCRM(proposedName?.id, this.PROPOSED_NAME_GRID_IN_CRM);

      this.proposedNames.removeAt(idx);
    }
  }

  removeValidations = (): void => {
    switch (this.fb.LegalFormId.value) {
      case this.LEGAL_FORM_TYPES.Association:
        this.FormService.clearFields(this.form, [
          'LocalDecreeLawNumber',
          'IssuanceDate',
          // 'AssociationClassification',
          'FirstProposedNameEn',
          'FirstProposedNameAr'
        ], true);
        this.FormService.clearValidators(this.form, ['NPOUnifiedNumbersList']);
        break;
      case this.LEGAL_FORM_TYPES.NationalSociety:
        this.FormService.clearFields(this.form, [
          'LocalDecreeLawNumber',
          'IssuanceDate',
          // 'AssociationClassification',
          'FirstProposedNameEn',
          'FirstProposedNameAr',
          'PermanentAdvance'
        ], true);
        this.FormService.clearValidators(this.form, ['NPOUnifiedNumbersList']);
        break;
      case this.LEGAL_FORM_TYPES.SocialSolidarityFunds:
        this.FormService.clearFields(this.form, [
          'LocalDecreeLawNumber',
          'IssuanceDate',
          // 'AssociationClassification',
          'FirstProposedNameEn',
          'FirstProposedNameAr',
          'NpoActivityPrograms',
        ], true);
        this.FormService.clearValidators(this.form, ['NPOUnifiedNumbersList']);
        break;
      case this.LEGAL_FORM_TYPES.Union:
        this.FormService.clearFields(this.form, [
          'LocalDecreeLawNumber',
          'IssuanceDate',
          // 'AssociationClassification',
          'FirstProposedNameEn',
          'FirstProposedNameAr',
          'NpoActivityPrograms',
        ], true);
        break;
      case this.LEGAL_FORM_TYPES.NationalSocietyByDecree:
        this.FormService.clearFields(this.form, [
          'PermanentAdvance',
        ], true);
        this.FormService.clearValidators(this.form, ['NPOUnifiedNumbersList', 'proposedNames', 'targetGroups', 'NpoActivityPrograms']);

        break;
      case this.LEGAL_FORM_TYPES.AssociationByDecree:
        this.FormService.clearFields(this.form, [
          'PermanentAdvance',
        ], true);
        this.FormService.clearValidators(this.form, ['NPOUnifiedNumbersList', 'proposedNames', 'targetGroups', 'NpoActivityPrograms']);
        break;
    }

    this.form.updateValueAndValidity();
  }

  isValidForm = (): boolean => {
    // let result: boolean = Object.keys(this.form.controls).every(controlName => {
    //   const control = this.form.get(controlName);
    //   return control?.disabled || control?.valid;
    // });

    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.disabled || control?.valid;
    });

    // const isValidTargetGroupsLength = this.targetGroups.length > 0;
    // const isValidLength = this.proposedNames.length >= 2 && this.proposedNames.length <= 3;


    // if (this.isReturnForUpdate) {
    //   result = (this.proposedNames.value as Array<any>).some(_ => _.SatusReason?.value?.trim().toLocaleLowerCase() != "rejected");
    // }

    // switch (this.fb.LegalFormId.value) {
    //   case this.LEGAL_FORM_TYPES.Association:
    //   case this.LEGAL_FORM_TYPES.NationalSociety:
    //     return result && isValidTargetGroupsLength && isValidLength;

    //   case this.LEGAL_FORM_TYPES.SocialSolidarityFunds:
    //   case this.LEGAL_FORM_TYPES.Union:
    //     return result && isValidLength;

    //   default:
    //     return result;
    // }
    // const isPoBoxValid = (this.form.get('POBox')?.value && this.form.get('POBox')?.value !== "" && this.form.get('POBox')?.value != null) ? this.form.get('POBox')?.valid : true;
    // return result && isPoBoxValid!;
    return result;
  };

  saveLegalForm = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("BasicInformationForm", true);
    this.continueToNextStep(submitParams, false);
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("BasicInformationForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("BasicInformationForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("BasicInformationForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    // this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;
    if ((this.fb.LegalFormId.value === this.LEGAL_FORM_TYPES.AssociationByDecree || this.fb.LegalFormId.value === this.LEGAL_FORM_TYPES.NationalSocietyByDecree) && this.fb.FirstProposedNameEn.value && this.fb.FirstProposedNameAr.value) {
      const id = this.proposedNames?.value[0]?.id ?? this.EMPTY_GUID;
      this.proposedNames.clear();
      this.proposedNames.push(this.FormBuilder.group({
        id: [id],
        proposedNameEn: [this.fb.FirstProposedNameEn.value],
        proposedNameAr: [this.fb.FirstProposedNameAr.value],
      }));
    }
    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      EstablishmentId: this.fb?.EstablishmentId?.value,
      LocalDecreeLawNumber: this.fb?.LocalDecreeLawNumber?.value ?? '',
      IssuanceDate: this.fb?.IssuanceDate?.value ?? '',
      // AssociationClassification: this.fb?.AssociationClassification?.value?.ID ?? '',
      landlinenumber: this.fb?.LandlineNumber?.value ?? '',
      pobox: this.fb?.POBox?.value ?? '',
      website: this.fb?.Website?.value ?? '',
      email: this.fb?.Email?.value ?? '',
      address: this.fb?.Address?.value ?? '',
      EmirateID: this.AuthService?.getUserInfo()?.userInfo?.idn,
      npoform: this.fb.LegalFormId?.value,
      emirate: this.fb?.Emirate?.value?.ID ?? '',
      permanentadvance: this.fb?.PermanentAdvance?.value ?? '',
      geographiclocation: this.fb?.GeographicLocation?.value ?? '',
      ProposedNames: (this.proposedNames.value as Array<any>).map((_, index) => {
        return {
          Id: _.id ?? this.EMPTY_GUID,
          Name: _.proposedNameEn,
          NameAr: _.proposedNameAr,
          DecisionDescription: "",
          SatusReason: _.SatusReason?.trim() || 'draft',
          Priority: index + 1,
        };
      }),

      NPOTargetgroup: (this.targetGroups.value as Array<any>).map((_, index) => {
        return {
          Id: _.id ?? this.EMPTY_GUID,
          Name: _.targetGroupNameEn,
          NameAr: _.targetGroupNameAr,
          SatusReason: _.SatusReason?.trim() || 'draft',
          Priority: index + 1,
        };
      }),

      NpoActivityPrograms: (this.NpoActivityPrograms.controls as Array<any>).map(
        (control, index) => {
          let _$ = control.value;
          return {
            Id: _$.id ?? this.EMPTY_GUID,
            Name: _$.exampleOfActivitiesEn,
            NameAr: _$.exampleOfActivitiesAr,
            SatusReason: _$.SatusReason?.trim() || 'draft',
            Priority: index + 1,
          };
        }
      ),

      FundsAllocationAmount: this.fb?.FundsAllocationAmount?.value ?? '',
      Numberofemployees: this.fb?.Numberofemployees?.value ?? '',
      EntityNameAr: this.fb?.EntityNameAr?.value ?? '',
      EntityName: this.fb?.EntityName?.value ?? '',
      Entitysectortype: this.fb?.Entitysectortype?.value?.ID ?? '',
      approvedNameAr: this.fb?.approvedNameAr?.value ?? '',
      approvedNameEn: this.fb?.approvedNameEn?.value ?? '',
      name: this.fb?.name?.value ?? '',
      namear: this.fb?.namear?.value ?? ''
    }
  }

  mapData = (data: any): void => {
    if (!data) return;

    // this.fb.FirstProposedNameEn.setValue(data?.name ?? '');
    // this.fb.FirstProposedNameAr.setValue(data?.namear ?? '');

    // this.fb.AssociationClassification.setValue(this.associationClassificationOptions?.find(_ => _.ID === data?.AssociationClassification) ?? '');

    this.fb.IssuanceDate.setValue(data?.IssuanceDate ?? '');
    this.fb.LocalDecreeLawNumber.setValue(data?.LocalDecreeLawNumber ?? '');
    this.fb.LandlineNumber.setValue(data?.landlinenumber ?? '', {
      emitEvent: true,
      onlySelf: false
    });

    this.fb.POBox.setValue(data?.pobox ?? '', {
      emitEvent: true,
      onlySelf: false
    });

    this.fb.Website.setValue(data?.website ?? '', {
      emitEvent: true,
      onlySelf: false
    });

    this.fb.Email.setValue(data?.email ?? '', {
      emitEvent: true,
      onlySelf: false
    });

    this.fb.Address.setValue(data?.address ?? '');
    this.fb.LegalFormId.setValue(data?.npoform ?? '');
    this.fb.Emirate.setValue(this.emirates?.find(_ => _.ID === data?.emirate));
    this.fb.PermanentAdvance.setValue(data?.permanentadvance ?? '');

    setTimeout(() => {
      this.fb.GeographicLocation.setValue(data?.geographiclocation ?? '');
    }, 1000);


    data.ProposedNames?.forEach((item: any) => {
      this.proposedNames.push(this.FormBuilder.group({
        id: [item.Id],
        proposedNameEn: [item.Name],
        proposedNameAr: [item.NameAr],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
      }));
    });

    if (data.NPOTargetgroup?.length > 0)
      this.targetGroups.clear();

    data.NPOTargetgroup?.forEach((item: any) => {
      this.targetGroups.push(this.FormBuilder.group({
        id: [item.Id],
        targetGroupNameEn: [item.Name],
        targetGroupNameAr: [item.NameAr],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
        canEdit: true,
        modified: [this.isReturnForUpdate],
      }));
    });

    data.NpoActivityPrograms?.forEach((item: any) => {
      this.NpoActivityPrograms.push(
        this.FormBuilder.group({
          id: [item.Id],
          exampleOfActivitiesEn: [item.Name],
          exampleOfActivitiesAr: [item.NameAr],
          SatusReason: [item.SatusReason?.trim() || 'draft'],
        })
      );
    });

    this.fb.Entitysectortype.setValue(this.EntitySectorTypes?.find(_ => _.ID === data?.Entitysectortype));

    this.fb.EntityName.setValue(data?.EntityName ?? '');
    this.fb.EntityNameAr.setValue(data?.EntityNameAr ?? '');

    this.fb.FundsAllocationAmount.setValue(data?.FundsAllocationAmount ?? '');
    this.fb.Numberofemployees.setValue(data?.Numberofemployees ?? '');

    this.form.markAllAsTouched();
    this.form.updateValueAndValidity();

    this.fb.approvedNameAr.setValue(data?.approvedNameAr ?? '');
    this.fb.approvedNameEn.setValue(data?.approvedNameEn ?? '');
    this.fb.name.setValue(data?.name ?? '');
    this.fb.namear.setValue(data?.namear ?? '');
  }

  isProposedNameExist = (item: any): boolean => {
    return this.proposedNames.value.find(_ => _.proposedNameEn.trim().toLowerCase() == item.Name.trim().toLowerCase() && _.proposedNameAr.trim().toLowerCase() == item.NameAr.trim().toLowerCase());
  }

  isTargetGroupExist = (item: any): boolean => {
    return this.targetGroups.value.find(_ => _.targetGroupNameEn.trim().toLowerCase() == item.Name.trim().toLowerCase() && _.targetGroupNameAr.trim().toLowerCase() == item.NameAr.trim().toLowerCase());
  }

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "basicinformation", this.fb);

    if (this.fb.LegalFormId.value === this.LEGAL_FORM_TYPES.AssociationByDecree || this.fb.LegalFormId.value === this.LEGAL_FORM_TYPES.NationalSocietyByDecree) {
      let tableRows = this.feedbackList?.filter(_ => _.SectionTechnicalname?.toLowerCase().trim() == "basicinformation".toLowerCase().trim() && _.Type == FileType.Grid);
      if (tableRows && tableRows.length > 0) {
        let propIndex$ = tableRows.findIndex(_ => _.FieldorGridTechnicalname?.toLowerCase().trim() == "proposedNames".toLowerCase().trim());
        // if (propIndex$ > -1) {
        //   this.fb.FirstProposedNameEn.enable();
        //   this.fb.FirstProposedNameAr.enable();
        // }
      }
    }
  }

  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;


  get disableAddProposed(): boolean {
    return this.proposedNames.length >= 3;
  }

  get allowDeleteButton(): boolean {
    if (this.isReturnForUpdate) {
      if (this.proposedNames && this.proposedNames.length > 0) {
        const status = this.proposedNames.value.map(p => p.SatusReason);
        const count = status.filter(item => item.trim().toLowerCase() === "rejected").length;
        return count >= 3
      }
      return false;
    } else
      return true;
  }

  aiMessage: string = '';
  displayAiInfoMessage: boolean = false;
  displayAiErrorMessage: boolean = false;
  confirmedProposedNames: any;

  checkProposedNameFromAI = (item: any, type: GridActionTypesEnum): void => {
    this.MyEstablishmentService.validateProposedNames({
      name_en: item.proposedNameEn,
      name_ar: item.proposedNameAr,
      language: this.LanguageService.IsArabic ? "ar" : "en"
    }).subscribe(res => {
      const _ = res.Data;
      if (_["is_valid"] == true || _["is_valid"] == "true") {
        if (_["displayMsg"] == true || _["displayMsg"] == "true") {
          this.confirmedProposedNames = item;
          this.displayAiInfoMessage = true;
          this.displayAiErrorMessage = false;
          this.aiMessage = _["message"];
        }
        if (_['displayMsg'] == false || _['displayMsg'] == "false") {
          this.manageProposedName(item, type);
        }
      } else {
        this.displayAiErrorMessage = true;
        this.displayAiInfoMessage = false;
        this.aiMessage = _["message"];
      }
    }, error => {
      console.error(error);
    });
  }

  resetAiParamter = (): void => {
    this.displayAiInfoMessage = false;
    this.displayAiErrorMessage = false;
    this.confirmedProposedNames = null;
    this.aiMessage = '';
  }


  manageTargetGroup = (item: any, type: GridActionTypesEnum): void => {
    if (this.validateTargetGroup(item)) {
      this.NotifyService.showError('notify.error', 'notify.targetGroupExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT) {
      this.targetGroups.push(this.FormBuilder.group({
        id: [this.generateDistinctId()],
        targetGroupNameEn: [item.targetGroupNameEn, [letterPatternValidator('en')]],
        targetGroupNameAr: [item.targetGroupNameAr, [letterPatternValidator('ar')]],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
        modified: [this.isReturnForUpdate],
        canEdit: true
      }));
    }
    else
      this.targetGroups.at(this.targetGroupEditIndex).setValue(
        {
          id: item.id,
          targetGroupNameEn: item.targetGroupNameEn,
          targetGroupNameAr: item.targetGroupNameAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
          modified: this.isReturnForUpdate,
          canEdit: item.canEdit
        }
      );


    this.targetGroupForm.reset();
    this.modalService.dismissAll();
  }

  handleEditTargetGroup = (data: any, idx: number): void => {
    this.targetGroupEditIndex = idx;
    this.targetGroupForm.patchValue({
      id: data?.value?.id,
      targetGroupNameEn: data?.value?.targetGroupNameEn,
      targetGroupNameAr: data?.value?.targetGroupNameAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit,
      modified: this.isReturnForUpdate,
    });
  }

  removeTargetGroup = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let targetGroup = this.targetGroups?.value[idx];
      if (targetGroup)
        this.removeFromCRM(targetGroup?.id, this.TARGET_GROUP_GRID_IN_CRM);

      this.targetGroups.removeAt(idx);
    }
  }

  validateTargetGroup = (item: any): Boolean => {
    // CRM call
    if (!this.targetGroups || !this.targetGroups.controls)
      return false;

    const matchingName = this.targetGroups.controls.find(control => {
      const targetGroupNameEn = control.get('targetGroupNameEn')?.value?.trim();
      const targetGroupNameAr = control.get('targetGroupNameAr')?.value?.trim();
      const id = control.get('id')?.value;
      return (targetGroupNameEn === item.targetGroupNameEn.trim() || targetGroupNameAr === item.targetGroupNameAr?.trim()) && id !== item.id;
    });
    return !!matchingName;
  }

  addLandLineValidation = (emirateId: string): void => {
    const emirate = this.emirates.find(({ ID }) => ID === emirateId);
    const startWith6 = (emirate?.NameEnglish?.toUpperCase() === 'UMM AL QUWAIN' || emirate?.NameEnglish?.toUpperCase() === 'AJMAN') || false;
    const length = 11;

    this.FormService.clearValidators(this.form, ['LandlineNumber']);
    this.FormService.addValidators(this.form, ['LandlineNumber'], [landLineValidator(startWith6, length)]);
  };

  exampleOfActivitiesEditIndex: number;

  manageExampleOfActivitiesForm(item: any, type: GridActionTypesEnum) {
    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError(
        'notify.error',
        'notify.exampleOfActivitiesAlreadyExists'
      );
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.NpoActivityPrograms.push(
        this.FormBuilder.group({
          Id: [this.generateDistinctId()],
          exampleOfActivitiesEn: [
            item.exampleOfActivitiesEn,
            Validators.required,
          ],
          exampleOfActivitiesAr: [
            item.exampleOfActivitiesAr,
            Validators.required,
          ],
          SatusReason: [item.SatusReason?.trim() || 'draft'],
        })
      );
    else
      this.NpoActivityPrograms.at(this.exampleOfActivitiesEditIndex).patchValue(
        {
          Id: item.Id,
          exampleOfActivitiesEn: item.exampleOfActivitiesEn,
          exampleOfActivitiesAr: item.exampleOfActivitiesAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
        }
      );

    this.exampleOfActivitiesForm.reset();
    this.modalService.dismissAll();
  }

  isItemDuplicated = (item: any): boolean => {
    if (!this.NpoActivityPrograms?.controls) return false;

    return this.NpoActivityPrograms.controls.some((control) => {
      const { exampleOfActivitiesEn, exampleOfActivitiesAr, Id } = {
        exampleOfActivitiesEn: control
          .get('exampleOfActivitiesEn')
          ?.value?.trim(),
        exampleOfActivitiesAr: control
          .get('exampleOfActivitiesAr')
          ?.value?.trim(),
        Id: control.get('Id')?.value,
      };

      return (
        (exampleOfActivitiesEn === item.exampleOfActivitiesEn?.trim() ||
          exampleOfActivitiesAr === item.exampleOfActivitiesAr?.trim())
      );
    });
  };

  handleEditExampleOfActivities(data: any, idx: number) {
    this.exampleOfActivitiesEditIndex = idx;
    this.exampleOfActivitiesForm.patchValue({
      Id: data?.value?.Id,
      exampleOfActivitiesEn: data?.value?.exampleOfActivitiesEn,
      exampleOfActivitiesAr: data?.value?.exampleOfActivitiesAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
    });
    this.FormService.enableFields(this.exampleOfActivitiesForm, [
      'exampleOfActivitiesEn',
      'exampleOfActivitiesAr',
    ]);
  }

  removeExampleOfActivity = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let example = this.NpoActivityPrograms?.value[idx];
      if (example)
        this.removeFromCRM(example?.id, 'mocd_examplesofactivitiesandprograms');

      this.NpoActivityPrograms.removeAt(idx);
    }
  };

  pageExampleOfActivities = 1;
  pageSizeExampleOfActivities = 10;
  dataTableExampleOfActivities: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTableExampleOfActivities(): FormArray {
    return this.dataTableExampleOfActivities;
  }
  get tableIndexExampleOfActivities(): number {
    return (
      (this.pageExampleOfActivities - 1) * this.pageSizeExampleOfActivities
    );
  }
  paginationExampleOfActivities = (): void => {
    this.dataTableExampleOfActivities = this.FormBuilder.array([]);
    let data$ = this.NpoActivityPrograms.controls.slice(
      (this.pageExampleOfActivities - 1) * this.pageSizeExampleOfActivities,
      (this.pageExampleOfActivities - 1) * this.pageSizeExampleOfActivities +
      this.pageSizeExampleOfActivities
    );
    data$.forEach((_) => this.dataTableExampleOfActivities.push(_));
  };

}
