import { Component, Injector, Input, OnInit } from '@angular/core';
import { CustomerpulseService } from '../../../../shared/services/customerpulse.service';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';

@Component({
  selector: 'app-noc-request-customer-pulse',
  template: ''
})
export class NocRequestCustomerPulseComponent extends NocRequestComponentBase implements OnInit {

  @Input() serviceCatalogId: string;
  @Input() requestId: string;
  constructor(injector: Injector, private customerPulseService: CustomerpulseService) {
    super(injector);
  }

  ngOnInit() {
    this.customerPulseService.postCustomerPulse(this.requestId, this.serviceCatalogId, this.LanguageService.IsArabic ? 'ar' : 'en');
  }
}
