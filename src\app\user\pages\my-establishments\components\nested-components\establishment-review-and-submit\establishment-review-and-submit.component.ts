import { sumBy } from 'lodash';
import { Component, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { SubmitionType } from '../../../../../../e-services/npo-license-declaration/enums/submition-type.enum';
import { FormGroup } from '@angular/forms';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { ENTITY_SECTOR_TYPES, FREQUENCY_OF_BOARD_MEETINGS_LIST, ELECTION_METHOD_LIST, RENOMINATION_LIST, NUMBER_OF_PERMISSIBLE_MEMBERS_LIST, BOARD_ELECTION_CYCLE_LIST, NATURE_OF_FUNDS_ALLOCATED, ALLOCATION_CYCLE, FREQUENCY_OF_MEETING, FREQUENCY_OF_MEETING_APPOINTMENT, NATIONALITY_TYPES } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';

@Component({
  selector: 'app-establishment-review-and-submit',
  templateUrl: './establishment-review-and-submit.component.html',
  styleUrls: ['./establishment-review-and-submit.component.scss']
})
export class EstablishmentReviewAndSubmitComponent
  extends MyEstablishmentComponentBase
  implements OnInit {


  NPO_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO DeclarationServiceCatalogue';
  NPO_DECREE_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO Declaration By DecreeServiceCatalogue';
  NPO_LINCEING_DELEACRTION: string = "NPO License Declaration";
  NPO_LINCEING: string = "NPO Declaration";

  panelOpenState: boolean = false;
  formData: any;
  emirates: Lookup[] = [];
  positions: Lookup[] = [];
  reportParameter: any[] = [];
  downloadButtons: any[] = [];
  EntitySectorTypes: Lookup[];
  private showMessage: boolean = false;
  private submitationType: SubmitionType = SubmitionType.SaveAsDraft;
  private referenceNumber: string = '';
  public docTypes: any[];
  public get ShowSuccessMessage(): boolean {
    return this.showMessage;
  }
  public get SubmitationType(): number {
    return this.submitationType;
  }
  public get ReferenceNumber(): string {
    return this.referenceNumber;
  }


  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes.find(_ => _.ID == this.selectedLegalFormType);
    if (type) {
      return type.NameEnglish;
    }
    return "";
  }


  @Input() migrationID: string | undefined;


  @Input() proposedNameEn: string | undefined;
  @Input() proposedNameAr: string | undefined;
  @Input() requestNumber: string | undefined;

  @Input() selectedLegalFormType: any;
  @Input() legalFromTypes: Lookup[] = [];
  @Input() form: FormGroup;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() showAction: boolean = false;
  @Input() showGetFoundingMemberButton: boolean = false;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() feedbackList: Feedback[] | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.reviewAndSubmit.';
    this.EntitySectorTypes = ENTITY_SECTOR_TYPES;
  }

  ngOnInit() {
    this.MyEstablishmentService.lookupData$.subscribe((data) => {
      if (data) {
        this.emirates = data.Emirates;
        this.positions = data.InteriimCommiteePosition;
        this.reportParameter = data.ReportParametre;
        this.StepperService.requestData$.subscribe((data: any) => {
          this.formData = data;

          this.collectionSizeProposedName = this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
          this.filteredCountProposedName = this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
          this.getPremiumDataProposedName();

          this.collectionSizeNpoNumber = this.formData?.BasicInformationForm?.npoNumbers?.length || 0;
          this.filteredCountNpoNumber = this.formData?.BasicInformationForm?.npoNumbers?.length || 0;
          this.getPremiumDataNpoNumber();

          this.collectionSizeFoundingMember = this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
          this.filteredCountFoundingMember = this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
          this.getPremiumDataFoundingMember();


          this.collectionSizeObjectives = this.formData?.ObjectivesForm?.Objectif?.length || 0;
          this.filteredCountObjectives = this.formData?.ObjectivesForm?.Objectif?.length || 0;
          this.getPremiumDataObjectives();

          this.collectionSizeInterimMembers = this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
          this.filteredCountInterimMembers = this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
          this.getPremiumDataInterimCommittee();

          this.collectionSizeMemberShip = this.formData?.MembershipForm?.MembershipConditions?.length || 0;
          this.filteredCountMemberShip = this.formData?.MembershipForm?.MembershipConditions?.length || 0;
          this.getPremiumDataMemberShip();


          this.collectionSizeBoardOfDirectorsMembershipConditions = this.formData?.BoardOfDirectorsForm?.MembershipConditions?.length || 0;
          this.filteredCountBoardOfDirectorsMembershipConditions = this.formData?.BoardOfDirectorsForm?.MembershipConditions?.length || 0;
          this.getPremiumDataBoardOfDirectorsMembershipConditions();

          this.collectionSizeBoardOfDirectorsPositions = this.formData?.BoardOfDirectorsForm?.Positions?.length || 0;
          this.filteredCountBoardOfDirectorsPositions = this.formData?.BoardOfDirectorsForm?.Positions?.length || 0;
          this.getPremiumDataBoardOfDirectorsPositions();

          this.collectionSizeBoardOfTrusteePositions = this.formData?.BoardOfTrusteesForm?.Positions?.length || 0;
          this.filteredCountBoardOfTrusteePositions = this.formData?.BoardOfTrusteesForm?.Positions?.length || 0;
          this.getPremiumDataBoardOfTrusteePositions();

          this.collectionSizeBoardOfTrusteeMembershipConditions = this.formData?.BoardOfTrusteesForm?.Conditions?.length || 0;
          this.filteredCountBoardOfTrusteeMembershipConditions = this.formData?.BoardOfTrusteesForm?.Conditions?.length || 0;
          this.getPremiumDataBoardOfTrusteeMembershipConditions();


          this.collectionSizeBoardOfTrusteeMembershipMembers = this.formData?.BoardOfTrusteesForm?.Memberlist?.length || 0;
          this.filteredCountBoardOfTrusteeMembershipMembers = this.formData?.BoardOfTrusteesForm?.Memberlist?.length || 0;
          this.getPremiumDataBoardOfTrusteeMembershipMembers();

          this.collectionSizeTargetGroup = this.formData?.BasicInformationForm?.NPOTargetgroup?.length || 0;
          this.filteredCountTargetGroup = this.formData?.BasicInformationForm?.NPOTargetgroup?.length || 0;
          this.getPremiumDataTargetGroup();


          this.collectionSizeFundServices = this.formData?.FundServicesForm?.FundServices?.length || 0;
          this.filteredCountFundServices = this.formData?.FundServiceForm?.FundService?.length || 0;
          this.getPremiumDataFundServices();

          this.collectionSizeAllocationOfFoundationFunds = this.formData?.AllocationFundForm?.AllocationFunds?.length || 0;
          this.filteredCountAllocationOfFoundationFunds = this.formData?.AllocationFundForm?.AllocationFunds?.length || 0;
          this.getPremiumDataAllocationOfFoundationFunds();

          this.checkAttachmentRequest(this.formData?.Id);

          this.collectionSizeExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.length || 0;
          this.filteredCountExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.length || 0;
          this.getPremiumDataExamplesOfActivities();
        })

        this.docTypes = data.DocumentType;
        // const serviceCatalogues = data.CrmConfiguration;
        // const legalFormType = this.formData?.BasicInformationForm?.npoform;

        // if (legalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree || legalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree) {
        //   const service = serviceCatalogues.find(_ => _.Name.toLocaleLowerCase().trim() == this.NPO_DECREE_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim());
        //   if (service) {
        //     this.docTypes = this.docTypes.filter(_ => _.ServiceCatalogue == service.Value);
        //   }
        // } else {
        //   const service = serviceCatalogues.find(_ => _.Name.toLocaleLowerCase().trim() == this.NPO_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim());
        //   if (service) {
        //     this.docTypes = this.docTypes.filter(_ => _.ServiceCatalogue == service.Value);
        //   }
        // }

        this.getDownloadButton(this.StepperService.requestCode);
        this.checkTempleteReplace();
      }
    });
  }

  getDocTypeName = (id: string): string => {
    let doctype = this.docTypes.find(_ => _.ID == id);
    return (this.LanguageService.IsArabic ? doctype?.NameArabic : doctype?.NameEnglish) ?? ''
  }

  getDownloadButton = (statusCode: number): void => {
    if (!this.legalFormTypeTitle) {
      return;
    }
    const legalFormType = this.legalFormTypeTitle.toLocaleLowerCase().trim();
    let collection: any;
    if (this.migrationID) {
      collection = this.reportParameter.filter(param => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType && param.ServiceCatlogue?.toLocaleLowerCase().trim() == this.NPO_LINCEING_DELEACRTION.toLocaleLowerCase().trim());
    } else {
      collection = this.reportParameter.filter(param => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType && param.ServiceCatlogue?.toLocaleLowerCase().trim() == this.NPO_LINCEING.toLocaleLowerCase().trim());
    }
    if (statusCode == 1) {
      // this.downloadButtons = collection.filter(_ => _.RequestStatus == 1 || _.RequestStatus == null || _.RequestStatus == 100000001);
      this.downloadButtons = collection;
    }
    // according to Adrien and business logic
    // if the status is draft, we don't show any other documents than the logo
    // else {
    //   this.downloadButtons = collection.filter(_ => _.RequestStatus == statusCode);
    // }
  };

  checkProposedNameFromAI = (proposedNameEn: string, proposedNameAr: string, namesCount: number, currentIndex: number): void => {
    this.MyEstablishmentService.validateProposedNames({
      name_en: proposedNameEn,
      name_ar: proposedNameAr,
      language: this.LanguageService.IsArabic ? "ar" : "en"
    }).subscribe(res => {
      const _ = res.Data;
      if (_["is_valid"] == true || _["is_valid"] == "true") {

        this.fundingMembers.push(1);

      } else {
        this.NotifyService.showError('notify.error', _["message"]);
        this.fundingMembers.push(0);
      }

      if (currentIndex + 1 == namesCount && this.fundingMembers.every(_ => _ == 1)) {
        let request = this.getRequestMappingObject(undefined, undefined, 100000001);
        this.getFundingMember(request);
      }

    }, error => {
      console.error(error);
      this.fundingMembers.push(0);
    });
  }

  fundingMembers: number[] = [];
  getFundingMembersConfirmation = async (): Promise<void> => {
    const translationMessage = this.Translation.instant('notify.saveTheRequestAsConfirmMessage');
    const confirmed = await this.AlertService.confirmSubmit(translationMessage);
    if (confirmed) {

      if (
        this.formData?.BasicInformationForm?.npoform == this.LEGAL_FORM_TYPES.AssociationByDecree ||
        this.formData?.BasicInformationForm?.npoform == this.LEGAL_FORM_TYPES.NationalSocietyByDecree
      ) {
        let request = this.getRequestMappingObject(undefined, undefined, 100000001);
        this.getFundingMember(request);
      } else {
        const names = this.formData?.BasicInformationForm?.ProposedNames as any[];
        names.forEach((item, index) => {
          this.checkProposedNameFromAI(item?.Name, item?.NameAr, names.length, index);
        });
      }
    }
  }

  getFundingMember = (request): void => {
    this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe((res) => {
      if (res.Status == 2000) {
        this.NotifyService.showSuccess('notify.success', 'notify.submitted');
        this.StepperService.requestId = res?.Id;
        this.StepperService.establishmentId = res?.EstablishmentId;
        this.showMessage = true;
        this.referenceNumber = res?.RequestNo;
        this.submitationType = SubmitionType.GetFoundingMemberConfirmation;
      } else {
        let errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
        this.NotifyService.showError('notify.error', errorMessage.trim());
      }
    }, error => {
      this.NotifyService.showError('notify.error', error);
    });
  }

  // submitAction = async (saveDraft: boolean = true): Promise<void> => {
  //   const translationTitle = this.Translation.instant(saveDraft ? 'alert.saveTheRequestAsDraftTitle' : 'alert.saveTheRequestAsSubmittTitle');
  //   const translationMessage = this.Translation.instant(saveDraft ? 'notify.saveTheRequestAsDraftMessage' : 'notify.saveTheRequestAsSubmitMessage');
  //   const translations = this.Translation.instant('alert');
  //   let param = {
  //     title: translationTitle,
  //     message: translationMessage,
  //     showCancelButton: true,
  //     confirmButtonColor: '#92722A',
  //     cancelButtonColor: '#d33',
  //     confirmButtonText: translations.confirmButtonText,
  //     cancelButtonText: translations.cancelButtonText
  //   };
  //   const confirmed = await this.AlertService.showAlert(param);
  //   if (confirmed) {
  //     let requestCode = this.StepperService.requestCode;
  //     let request = this.getRequestMappingObject(undefined, undefined, saveDraft === true ? requestCode : 100000000);
  //     this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe((res) => {
  //       if (res.Status == 2000) {
  //         this.NotifyService.showSuccess('notify.success', saveDraft ? 'notify.draftSaved' : 'notify.submitted');

  //         this.StepperService.requestId = res?.Id;
  //         this.StepperService.establishmentId = res?.EstablishmentId;
  //         this.Router.navigate(['/user-pages/my-establishments']);
  //       } else {
  //         let errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
  //         this.NotifyService.showError('notify.error', errorMessage.trim());
  //       }
  //     }, error => {
  //       this.NotifyService.showError('notify.error', error);
  //     });
  //   }
  // }

  submit = async (saveDraft: boolean = true): Promise<void> => {
    const translationTitle = this.Translation.instant(saveDraft ? 'alert.saveTheRequestAsDraftTitle' : 'alert.saveTheRequestAsSubmittTitle');
    const translationMessage = this.Translation.instant(saveDraft ? 'notify.saveTheRequestAsDraftMessage' : 'notify.saveTheRequestAsSubmitMessage');
    const translations = this.Translation.instant('alert');
    let param = {
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#92722A',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmButtonText,
      cancelButtonText: translations.cancelButtonText
    };
    const confirmed = await this.AlertService.showAlert(param);
    if (confirmed) {
      let requestCode = this.StepperService.requestCode;
      let request = this.getRequestMappingObject(undefined, undefined, saveDraft === true ? requestCode : 100000000);
      this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe((res) => {
        if (res.Status == 2000) {
          this.NotifyService.showSuccess('notify.success', saveDraft ? 'notify.draftSaved' : 'notify.submitted');

          this.StepperService.requestId = res?.Id;
          this.StepperService.establishmentId = res?.EstablishmentId;
          this.referenceNumber = res?.RequestNo;
          this.submitationType = SubmitionType.SaveAsDraft;
          if (!saveDraft) {
            this.submitationType = SubmitionType.Submit;
            this.showMessage = true;
          } else {
            this.showMessage = false;
          }


          if (!this.StepperService.requestId) {
            this.StepperService.setAutoStep(true);
          } else {
            this.getRouterMode();

          }


        } else {
          let errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
          this.NotifyService.showError('notify.error', errorMessage.trim());
        }
      }, error => {
        this.NotifyService.showError('notify.error', error);
      });
    }
  }

  getEmirate(emirateId: any): string {
    const emirate = this.emirates?.find(e => e.ID == emirateId);
    return this.LanguageService.IsArabic ? emirate?.NameArabic ?? '' : emirate?.NameEnglish ?? '';
  }

  getEntitySectorType = (id): string => {
    const type = this.EntitySectorTypes?.find(e => e.ID == id);
    return this.LanguageService.IsArabic ? type?.NameArabic ?? '' : type?.NameEnglish ?? '';
  }

  getPosition(positionId: any): string {
    const postion = this.positions?.find(_ => _.ID === positionId);
    return this.LanguageService.IsArabic ? postion?.NameArabic ?? '' : postion?.NameEnglish ?? '';
  }

  getFrequencyOfBoardMeetings(id: any): string {
    const frequency = FREQUENCY_OF_BOARD_MEETINGS_LIST?.find(freq => freq.ID == id);
    return this.LanguageService.IsArabic ? frequency?.NameArabic ?? '' : frequency?.NameEnglish ?? '';
  }

  getElectionMethod(id: any): string {
    const method = ELECTION_METHOD_LIST?.find(m => m.ID == id);
    return this.LanguageService.IsArabic ? method?.NameArabic ?? '' : method?.NameEnglish ?? '';
  }

  getRenominationStatus(id: any): string {
    const renomination = RENOMINATION_LIST?.find(r => r.ID == id);
    return this.LanguageService.IsArabic ? renomination?.NameArabic ?? '' : renomination?.NameEnglish ?? '';
  }

  getPermissibleTerms(id: any): string {
    const terms = NUMBER_OF_PERMISSIBLE_MEMBERS_LIST?.find(t => t.ID == id);
    return this.LanguageService.IsArabic ? terms?.NameArabic ?? '' : terms?.NameEnglish ?? '';
  }

  getBoardElectionCycle(id: any): string {
    const cycle = BOARD_ELECTION_CYCLE_LIST?.find(c => c.ID == id);
    return this.LanguageService.IsArabic ? cycle?.NameArabic ?? '' : cycle?.NameEnglish ?? '';
  }

  gettotalFundsAmount = (): number => {
    return sumBy(this.formData?.AllocationFundForm?.AllocationFunds, (control: any) => Number(control.ValueOfTheFund))
  }

  getNatureOfAllocatedFunds(id: any): string {
    const obj = NATURE_OF_FUNDS_ALLOCATED?.find(c => c.ID == id);
    return this.LanguageService.IsArabic ? obj?.NameArabic ?? '' : obj?.NameEnglish ?? '';
  }

  getAllocationCycle(id: any): string {
    const obj = ALLOCATION_CYCLE?.find(c => c.ID == id);
    return this.LanguageService.IsArabic ? obj?.NameArabic ?? '' : obj?.NameEnglish ?? '';
  }

  getAnnualMembershipDueDateValue() {
    return this.LanguageService.IsArabic
      ? 'مع بداية السنة المالية (جزء من قيمة الاشتراك)'
      : 'Beginning of the fiscal year(Partial subscription Amount)';
  }

  getFrequencyOfMeetings(id: any): string {
    const frequencyOfMeetings = FREQUENCY_OF_MEETING?.find(c => c.ID == id);
    return this.LanguageService.IsArabic ? frequencyOfMeetings?.NameArabic ?? '' : frequencyOfMeetings?.NameEnglish ?? '';
  }

  getFrequencyOfAppointments(id: any): string {
    const frequencyOfMeetings = FREQUENCY_OF_MEETING_APPOINTMENT?.find(c => c.ID == id);
    return this.LanguageService.IsArabic ? frequencyOfMeetings?.NameArabic ?? '' : frequencyOfMeetings?.NameEnglish ?? '';
  }





  generateDownloadLink = (file: any): string => `data:${file?.mimeType};base64,${file?.Base64}`;

  createDownloadLink = (file: File): string => {
    const blob = new Blob([file], { type: file.type });
    return URL.createObjectURL(blob);
  }

  pageProposedName = 1;
  pageSizeProposedName = 10;
  collectionSizeProposedName = 0;
  filteredCountProposedName = 0;
  paginateDataProposedName: any[] = [];
  getPremiumDataProposedName(): void {
    const start = (this.pageProposedName - 1) * this.pageSizeProposedName;
    const end = start + this.pageSizeProposedName;
    this.paginateDataProposedName = this.formData?.BasicInformationForm?.ProposedNames?.slice(start, end) || [];
  }

  pageNpoNumber = 1;
  pageSizeNpoNumber = 10;
  collectionSizeNpoNumber = 0;
  filteredCountNpoNumber = 0;
  paginateDataNpoNumber: any[] = [];
  getPremiumDataNpoNumber(): void {
    const start = (this.pageNpoNumber - 1) * this.pageSizeNpoNumber;
    const end = start + this.pageSizeNpoNumber;
    this.paginateDataNpoNumber = this.formData?.FoundingMembersForm?.npoNumbers?.slice(start, end) || [];
  }

  pageFoundingMember = 1;
  pageSizeFoundingMember = 10;
  collectionSizeFoundingMember = 0;
  filteredCountFoundingMember = 0;
  paginateDataFoundingMember: any[] = [];
  getPremiumDataFoundingMember(): void {
    const start = (this.pageFoundingMember - 1) * this.pageSizeFoundingMember;
    const end = start + this.pageSizeFoundingMember;
    this.paginateDataFoundingMember = this.formData?.FoundingMembersForm?.FounderMember?.slice(start, end) || [];
  }


  pageObjectives = 1;
  pageSizeObjectives = 10;
  collectionSizeObjectives = 0;
  filteredCountObjectives = 0;
  paginateDataObjectives: any[] = [];

  getPremiumDataObjectives(): void {
    const start = (this.pageObjectives - 1) * this.pageSizeObjectives;
    const end = start + this.pageSizeObjectives;
    this.paginateDataObjectives = this.formData?.ObjectivesForm?.Objectif?.slice(start, end) || [];
  }

  pageTargetGroup = 1;
  pageSizeTargetGroup = 10;
  collectionSizeTargetGroup = 0;
  filteredCountTargetGroup = 0;
  paginateDataTargetGroup: any[] = [];
  getPremiumDataTargetGroup(): void {
    const start = (this.pageTargetGroup - 1) * this.pageSizeTargetGroup;
    const end = start + this.pageSizeTargetGroup;
    this.paginateDataTargetGroup = this.formData?.BasicInformationForm?.NPOTargetgroup?.slice(start, end) || [];
  }

  pageInterimMembers = 1;
  pageSizeInterimMembers = 10;
  collectionSizeInterimMembers = 0;
  filteredCountInterimMembers = 0;
  paginateDataInterimMembers: any[] = [];

  getPremiumDataInterimCommittee(): void {
    const start = (this.pageInterimMembers - 1) * this.pageSizeInterimMembers;
    const end = start + this.pageSizeInterimMembers;
    this.paginateDataInterimMembers = this.formData?.InterimCommitteeForm?.InterimCommitee?.slice(start, end) || [];
  }

  pageMemberShip = 1;
  pageSizeMemberShip = 10;
  collectionSizeMemberShip = 0;
  filteredCountMemberShip = 0;
  paginateDataMemberShip: any[] = [];

  getPremiumDataMemberShip(): void {
    const start = (this.pageMemberShip - 1) * this.pageSizeMemberShip;
    const end = start + this.pageSizeMemberShip;
    this.paginateDataMemberShip = this.formData?.MembershipForm?.MembershipConditions?.slice(start, end) || [];
  }



  pageBoardOfDirectorsMembershipConditions = 1;
  pageSizeBoardOfDirectorsMembershipConditions = 10;
  collectionSizeBoardOfDirectorsMembershipConditions = 0;
  filteredCountBoardOfDirectorsMembershipConditions = 0;
  paginateDataBoardOfDirectorsMembershipConditions: any[] = [];
  getPremiumDataBoardOfDirectorsMembershipConditions(): void {
    const start = (this.pageBoardOfDirectorsMembershipConditions - 1) * this.pageSizeBoardOfDirectorsMembershipConditions;
    const end = start + this.pageSizeBoardOfDirectorsMembershipConditions;
    this.paginateDataBoardOfDirectorsMembershipConditions = this.formData?.BoardOfDirectorsForm?.MembershipConditions?.slice(start, end) || [];
  }


  pageBoardOfDirectorsPositions = 1;
  pageSizeBoardOfDirectorsPositions = 10;
  collectionSizeBoardOfDirectorsPositions = 0;
  filteredCountBoardOfDirectorsPositions = 0;
  paginateDataBoardOfDirectorsPositions: any[] = [];
  getPremiumDataBoardOfDirectorsPositions(): void {
    const start = (this.pageBoardOfDirectorsPositions - 1) * this.pageSizeBoardOfDirectorsPositions;
    const end = start + this.pageSizeBoardOfDirectorsPositions;
    this.paginateDataBoardOfDirectorsPositions = this.formData?.BoardOfDirectorsForm?.Positions?.slice(start, end) || [];
  }

  pageBoardOfTrusteeMembershipConditions = 1;
  pageSizeBoardOfTrusteeMembershipConditions = 10;
  collectionSizeBoardOfTrusteeMembershipConditions = 0;
  filteredCountBoardOfTrusteeMembershipConditions = 0;
  paginateDataBoardOfTrusteeMembershipConditions: any[] = [];
  getPremiumDataBoardOfTrusteeMembershipConditions(): void {
    const start = (this.pageBoardOfTrusteeMembershipConditions - 1) * this.pageSizeBoardOfTrusteeMembershipConditions;
    const end = start + this.pageSizeBoardOfTrusteeMembershipConditions;
    this.paginateDataBoardOfTrusteeMembershipConditions = this.formData?.BoardOfTrusteesForm?.Conditions?.slice(start, end) || [];
  }


  pageBoardOfTrusteeMembershipMembers = 1;
  pageSizeBoardOfTrusteeMembershipMembers = 10;
  collectionSizeBoardOfTrusteeMembershipMembers = 0;
  filteredCountBoardOfTrusteeMembershipMembers = 0;
  paginateDataBoardOfTrusteeMembershipMembers: any[] = [];
  getPremiumDataBoardOfTrusteeMembershipMembers(): void {
    const start = (this.pageBoardOfTrusteeMembershipMembers - 1) * this.pageSizeBoardOfTrusteeMembershipMembers;
    const end = start + this.pageSizeBoardOfTrusteeMembershipMembers;
    this.paginateDataBoardOfTrusteeMembershipMembers = this.formData?.BoardOfTrusteesForm?.Memberlist?.slice(start, end) || [];
  }


  pageBoardOfTrusteePositions = 1;
  pageSizeBoardOfTrusteePositions = 10;
  collectionSizeBoardOfTrusteePositions = 0;
  filteredCountBoardOfTrusteePositions = 0;
  paginateDataBoardOfTrusteePositions: any[] = [];
  getPremiumDataBoardOfTrusteePositions(): void {
    const start = (this.pageBoardOfTrusteePositions - 1) * this.pageSizeBoardOfTrusteePositions;
    const end = start + this.pageSizeBoardOfTrusteePositions;
    this.paginateDataBoardOfTrusteePositions = this.formData?.BoardOfTrusteesForm?.Positions?.slice(start, end) || [];
  }

  pageFundServices = 1;
  pageSizeFundServices = 10;
  collectionSizeFundServices = 0;
  filteredCountFundServices = 0;
  paginateDataFundServices: any[] = [];
  getPremiumDataFundServices(): void {
    const start = (this.pageFundServices - 1) * this.pageSizeFundServices;
    const end = start + this.pageSizeFundServices;
    this.paginateDataFundServices = this.formData?.FundServiceForm?.FundService?.slice(start, end) || [];
  }

  pageAllocationOfFoundationFunds = 1;
  pageSizeAllocationOfFoundationFunds = 10;
  collectionSizeAllocationOfFoundationFunds = 0;
  filteredCountAllocationOfFoundationFunds = 0;
  paginateDataAllocationOfFoundationFunds: any[] = [];
  getPremiumDataAllocationOfFoundationFunds(): void {
    const start = (this.pageAllocationOfFoundationFunds - 1) * this.pageSizeAllocationOfFoundationFunds;
    const end = start + this.pageSizeAllocationOfFoundationFunds;
    this.paginateDataAllocationOfFoundationFunds = this.formData?.AllocationFundForm?.AllocationFunds?.slice(start, end) || [];
  }

  pageExamplesOfActivities = 1;
  pageSizeExamplesOfActivities = 10;
  collectionSizeExamplesOfActivities = 0;
  filteredCountExamplesOfActivities = 0;
  paginateDataExamplesOfActivities: any[] = [];
  getPremiumDataExamplesOfActivities(): void {
    const start = (this.pageExamplesOfActivities - 1) * this.pageSizeExamplesOfActivities;
    const end = start + this.pageSizeExamplesOfActivities;
    this.paginateDataExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.slice(start, end) || [];
  }

  showSubmitButton = (): boolean => {
    if (this.formData?.BasicInformationForm?.npoform === this.LEGAL_FORM_TYPES.Union)
      return this.showAction;

    const founderMembers = this.formData?.FoundingMembersForm?.FounderMember || [];
    if (!founderMembers.length) {
      return false;
    }
    return founderMembers.every(member => member?.SatusReason?.trim() === 'Confirmed' || member?.SatusReason?.trim() === 'Approved');
  };

  showGFCButton = (): boolean => {
    const founderMembers = this.formData?.FoundingMembersForm?.FounderMember || [];
    if (!founderMembers.length) {
      return false;
    }
    return founderMembers.some(member => {
      const status = member?.SatusReason?.toLowerCase().trim();
      return status === 'rejected' || status === 'refused' || status === 'draft' || status === '';
    });
  };

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
      case 'approved':
        return 'green';
      case 'pending confirmation':
        return 'orange';
      case 'rejected':
      case 'refused':
        return 'red';
      default:
        return '';
    }
  }

  getStatusBgColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
        return '#E7F5FF'
      case 'approved':
        return '#F3FAF4';
      case 'pending confirmation':
        return '#FFFBEB';
      case 'rejected':
      case 'refused':
        return '#FEF2F2';
      default:
        return '';
    }
  }

  download = (file: any): void => {
    if (this.formData?.Id) {
      this.downloadNpoFiles('application/pdf', file.ReportName, this.formData?.Id, file.LogicalName, this.formData?.BasicInformationForm?.EstablishmentId);
    } else {
      let request = this.getRequestMappingObject(undefined, undefined, 1);
      this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe((res) => {
        if (res.Status == 2000) {
          this.StepperService.requestId = res?.Id;
          this.StepperService.establishmentId = res?.EstablishmentId;
          this.referenceNumber = res?.RequestNo;
          this.getRouterMode();
          this.downloadNpoFiles('application/pdf', file.ReportName, res?.Id, file.LogicalName, res?.BasicInformationForm?.EstablishmentId);
        }
      }, error => {
        this.NotifyService.showError('notify.error', error);
      });
    }
  }

  canSeeAgendaSection = (): boolean => {
    if ((this.collectionSizeFoundingMember > 1 && this.formData?.BasicInformationForm?.npoform === this.LEGAL_FORM_TYPES.NationalSociety)
      || this.formData?.BasicInformationForm?.npoform !== this.LEGAL_FORM_TYPES.NationalSociety)
      return true;

    return false;
  };

  getProposedNameEn = (): string => {
    return this.formData?.BasicInformationForm?.name ?? '';
  }

  getProposedNameAr = (): string => {
    return this.formData?.BasicInformationForm?.namear ?? '';
  }

  checkSubmitButtonIncaseRequestForUpdate = (): boolean => {
    // let jsonObject1 = this.getRequestMappingObject(undefined, undefined, this.StepperService.requestCode);
    // let jsonObject2 = {};

    // if (this.isReturnForUpdate) {
    //   this.feedbackList?.forEach((_) => {
    //     let sectionName = _.SectionTechnicalname?.toLowerCase().trim();
    //     let fieldName = _.FieldorGridTechnicalname?.toLowerCase().trim();
    //     const isEqual = this.compareJsonObjects(jsonObject1, jsonObject2, sectionName ?? '');
    //     if (!isEqual) {
    //       return false;
    //     }
    //   });
    // }

    return true;
  }

  compareJsonObjects(json1: any, json2: any, key: string): boolean {
    if (json1.hasOwnProperty(key) && json2.hasOwnProperty(key)) {
      const value1 = json1[key];
      const value2 = json2[key];

      if (this.isObject(value1) && this.isObject(value2)) {
        return this.deepCompareObjects(value1, value2);
      }

      return value1 === value2;
    } else {
      return false;
    }
  }

  isObject(value: any): boolean {
    return value !== null && typeof value === 'object';
  }

  deepCompareObjects(obj1: any, obj2: any): boolean {
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return false;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (let key of keys1) {
      if (!obj2.hasOwnProperty(key)) {
        return false;
      }

      if (!this.compareJsonObjects(obj1, obj2, key)) {
        return false;
      }
    }

    return true;
  }


  checkTempleteReplace = (): void => {
    if (this.formData?.BasicInformationForm?.npoform != this.LEGAL_FORM_TYPES.SocialSolidarityFunds)
      return;

    if (this.isEmptyString(this.formData?.BasicInformationForm?.EntityName) &&
      this.isEmptyString(this.formData?.BasicInformationForm?.EntityNameAr))
      return;


    (this.formData?.MembershipForm?.MembershipConditions).map(_ => {
      if ((_?.Name).includes('(...)')) {
        _.Name = _?.Name.replace('(...)', this.formData?.BasicInformationForm?.EntityName);
        _.NameAr = _?.NameAr.replace('(...)', this.formData?.BasicInformationForm?.EntityNameAr);
      }
    });

    this.getPremiumDataMemberShip();
  }

  isUnionLegalForm = (): boolean => this.formData?.BasicInformationForm?.npoform == this.LEGAL_FORM_TYPES.Union;
  getNationalType = (isLocalCondition: number): string => {
    let type = NATIONALITY_TYPES.find(type => type.ID == isLocalCondition);
    if (type) {
      return this.LanguageService.IsArabic ? type.NameArabic : type.NameEnglish;
    }
    return '';
  }

  get requestId(): string {
    return this.StepperService.requestId ?? '';
  }

  documents: any[] = [];
  checkAttachmentRequest = (requestId: string): void => {
    if (requestId && requestId != '' && requestId != undefined) {
      this.MyEstablishmentService.fetchAttachments(requestId)
        .subscribe(res => {
          this.documents = res?.data ?? [];
        });
    }
  }
}
