import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { AsyncPipe, CommonModule, DatePipe, DecimalPipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CdkStepperModule, STEPPER_GLOBAL_OPTIONS } from '@angular/cdk/stepper';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatMenuModule } from '@angular/material/menu';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RouterModule } from '@angular/router';
import { MatStepperModule } from '@angular/material/stepper';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { InputComponent } from './components/form/input/input.component';
import { SelectComponent } from './components/form/select/select.component';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { ToastrModule } from 'ngx-toastr';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { SelectSearchComponent } from './components/form/select-search/select-search.component';
import { RadioComponent } from './components/form/radio/radio.component';
import { FileUploadComponent } from './components/form/file-upload/file-upload.component';
import { SharedSummaryComponent } from './components/summary/shared-summary/shared-summary.component';
import { SharedSummaryRowComponent } from './components/summary/shared-summary-row/shared-summary-row.component';
import { NgbHighlight, NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgbdSortableHeader } from './directives/sortable.diractive';
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { TextareaComponent } from './components/form/textarea/textarea.component';
import { NgxSpinnerModule } from 'ngx-spinner';
import { ApplicationsComponent } from './components/user/applications/applications.component';
import { InputHiddenComponent } from './components/form/input-hidden/input-hidden.component';
import { InputDateComponent } from './components/form/input-date/input-date.component';
import { MAT_DATE_LOCALE, MAT_DATE_FORMATS, DateAdapter } from '@angular/material/core';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { registerLocaleData } from '@angular/common';
import localeAr from '@angular/common/locales/ar';
import localeEn from '@angular/common/locales/en';
import { HelperComponent } from './components/app/helper/helper.component';
import { ServiceCardComponent } from './components/app/service-card/service-card.component';
import { NgxMaskDirective } from 'ngx-mask';
import { MaxLengthDirective } from './directives/max-length.directive';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { CheckboxComponent } from './components/form/checkbox/checkbox.component';
import { InputWithActionComponent } from './components/form/input-with-action/input-with-action.component';
import { ExternalLinkDirective } from './directives/external-link.directive';
import { ModalComponent } from './components/app/modal/modal.component';
import { ModalInfoComponent } from './components/app/modal-info/modal-info.component';
import { PaymentResultComponent } from './components/user/payment-result/payment-result.component';
import { SubmitResultComponent } from './components/user/submit-result/submit-result.component';
import { PersonalDetailsComponent } from './components/app/personal-details/personal-details.component';
import { DocumentCardComponent } from './components/app/document-card/document-card.component';
import { InquiryTrackerComponent } from './components/user/inquiry-tracker/inquiry-tracker.component';
import { SharedAttachmentSummaryRowComponent } from './components/summary/shared-attachment-summary-row/shared-attachment-summary-row.component';
import { FileUploadSWPComponent } from './components/form/file-upload-swp/file-upload-swp.component';
import { InputTimeComponent } from './components/form/input-time/input-time.component';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { SlideToggleComponent } from './components/form/slide-toggle/slide-toggle.component';
import { InputLocationComponent } from './components/form/input-location/input-location.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { InputDatetimeComponent } from './components/form/input-datetime/input-datetime.component';
import {
  NGX_MAT_DATE_FORMATS,
  NgxMatDateAdapter,
  NgxMatDatetimePickerModule,
  NgxMatNativeDateModule,
  NgxMatTimepickerModule
} from '@angular-material-components/datetime-picker';
import { StepperFullJourneyComponent } from './components/summary/stepper-full-journey/stepper-full-journey.component';
import { GoogleTranslateDirective } from './directives/google-translate.directive';
import { GoogleTranslateComponent } from './components/app/google-translate/google-translate.component';
import { NumberOnlyDirective } from './directives/numbers-only.directive';
import { CustomNgxDatetimeAdapter } from './models/customngxdatetimeadapter';
import { NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular-material-components/moment-adapter';
import { MatTooltipModule } from '@angular/material/tooltip';
import { VoiceSearchComponent } from './components/app/voice-search/voice-search.component';
import { SharedStepperRequestTimelineComponent } from './components/summary/shared-stepper-request-timeline/shared-stepper-request-timeline.component';
import { NgxExtendedPdfViewerModule } from 'ngx-extended-pdf-viewer';
import { TreeMenuComponent } from './components/app/tree-menu/tree-menu.component';
import { SelectSearchApiComponent } from './components/form/select-search-api/select-search-api.component';


// Register locale data for Arabic and English
registerLocaleData(localeAr);
registerLocaleData(localeEn);



@NgModule({
  declarations: [
    InputComponent,
    TextareaComponent,
    SelectComponent,
    SelectSearchComponent,
    SelectSearchApiComponent,
    RadioComponent,
    FileUploadSWPComponent,
    FileUploadComponent,
    SharedSummaryComponent,
    SharedSummaryRowComponent,
    NgbdSortableHeader,
    TextareaComponent,
    ApplicationsComponent,
    InputHiddenComponent,
    InputDateComponent,
    InputTimeComponent,
    HelperComponent,
    ServiceCardComponent,
    MaxLengthDirective,
    CheckboxComponent,
    InputWithActionComponent,
    ExternalLinkDirective,
    ModalComponent,
    ModalInfoComponent,
    PaymentResultComponent,
    SubmitResultComponent,
    PersonalDetailsComponent,
    DocumentCardComponent,
    InquiryTrackerComponent,
    SharedAttachmentSummaryRowComponent,
    SlideToggleComponent,
    InputLocationComponent,
    InputDatetimeComponent,
    StepperFullJourneyComponent,
    GoogleTranslateDirective,
    GoogleTranslateComponent,
    NumberOnlyDirective,
    VoiceSearchComponent,
    SharedStepperRequestTimelineComponent,
    TreeMenuComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    ToastrModule.forRoot({ preventDuplicates: true }),
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    CdkStepperModule,
    MatDatepickerModule,
    NgxMaterialTimepickerModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatMenuModule,
    MatSidenavModule,
    MatStepperModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionModule,
    MatExpansionPanelHeader,
    NgxMatSelectSearchModule,
    MatRadioModule,
    MatDividerModule,
    DecimalPipe, AsyncPipe, NgbHighlight, NgbPaginationModule,
    NgxSpinnerModule.forRoot({ type: 'ball-scale-multiple' }),
    NgxMaskDirective,
    MatProgressBarModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    GoogleMapsModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule,
    NgxMatNativeDateModule,
    NgxExtendedPdfViewerModule,
  ],
  exports: [
    CommonModule,
    ToastrModule,
    RouterModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    CdkStepperModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatMenuModule,
    MatSidenavModule,
    MatStepperModule,
    MatInputModule,
    MatAccordion,
    MatExpansionPanel,
    MatExpansionModule,
    MatExpansionPanelHeader,
    MatFormFieldModule,
    InputComponent,
    TextareaComponent,
    MatSelectModule,
    SelectComponent,
    NgxMatSelectSearchModule,
    SelectSearchComponent,
    SelectSearchApiComponent,
    MatRadioModule,
    RadioComponent,
    FileUploadSWPComponent,
    FileUploadComponent,
    SharedSummaryComponent,
    SharedSummaryRowComponent,
    SharedAttachmentSummaryRowComponent,
    DecimalPipe, AsyncPipe, NgbHighlight, NgbdSortableHeader, NgbPaginationModule,
    NgxSpinnerModule,
    ApplicationsComponent,
    InputHiddenComponent,
    MatDatepickerModule,
    InputDateComponent,
    InputTimeComponent,
    HelperComponent,
    ServiceCardComponent,
    MatDividerModule,
    MaxLengthDirective,
    MatProgressBarModule,
    MatCheckboxModule,
    CheckboxComponent,
    InputWithActionComponent,
    ExternalLinkDirective,
    ModalComponent,
    ModalInfoComponent,
    PaymentResultComponent,
    SubmitResultComponent,
    PersonalDetailsComponent,
    DocumentCardComponent,
    SlideToggleComponent,
    InputLocationComponent,
    InputDatetimeComponent,
    InquiryTrackerComponent,
    StepperFullJourneyComponent,
    GoogleTranslateDirective,
    GoogleTranslateComponent,
    MatTooltipModule,
    VoiceSearchComponent,
    NumberOnlyDirective,
    SharedStepperRequestTimelineComponent,
    NgxExtendedPdfViewerModule,
    TreeMenuComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: STEPPER_GLOBAL_OPTIONS,
      useValue: { displayDefaultIndicatorType: false }
    },
    { provide: MAT_DATE_LOCALE, useValue: 'en' },
    {
      provide: MAT_DATE_FORMATS, useValue: {
        parse: {
          dateInput: 'DD/MM/YYYY',
        },
        display: {
          dateInput: 'DD/MM/YYYY',
          monthYearLabel: 'MMMM YYYY',
          dateA11yLabel: 'LL',
          monthYearA11yLabel: 'MMMM YYYY',
        },
      }
    },
    { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS] },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    DatePipe,
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxDatetimeAdapter,
      deps: [MAT_DATE_LOCALE, NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {
      provide: NGX_MAT_DATE_FORMATS, useValue: {
        parse: {
          dateInput: 'l, LTS'
        },
        display: {
          dateInput: 'DD/MM/YYYY h:mm A',
          monthYearLabel: 'MMM YYYY',
          dateA11yLabel: 'LL',
          monthYearA11yLabel: 'MMMM YYYY',
        }
      }
    }
  ],
})
export class SharedModule { }


