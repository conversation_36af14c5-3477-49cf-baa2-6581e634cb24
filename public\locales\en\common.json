{"3stepsToSignup": "Three steps to register for the Social Welfare Program", "4stepsToSignup": "Four steps to register for the Social Welfare Program", "5to10mins": "2-5 minutes", "8to10mins": "8-10 minutes", "1to2mins": "1-2 minutes", "editReason": "Reason to Edit", "reasonToEdit": "Reason to Edit", "aboutMocd": "About MoCD", "menu": "<PERSON><PERSON>", "accessibilityStatement": "Accessibility Statement", "Additional-info": "Additional information", "additionalEligibilityCriteriaForProgram": "Additional information on eligibility criteria:", "additionalEligibilityCriteriaForProgramDesc": "Household income includes the following elements:", "additionalEligibilityCriteriaForProgramPoint1": "Employment salary or income from private work (e.g., owned businesses, self-employment, etc.).", "additionalEligibilityCriteriaForProgramPoint2": "Financial returns (e.g., rental income, income from real estate assets, interests, dividends, gains from financial assets, etc.).", "additionalEligibilityCriteriaForProgramPoint3": "Income from other social benefits (e.g., pension and local social assistance).", "afterStudying": "After reviewing your case number : ", "ageException": "Age Exemption", "ageExceptionDesc": "In special cases, exempting applicants from the age requirement could be considered. To learn more about conditions for special cases, please read the Frequently Asked Questions section, or contact our call center at 800623.", "ageOfBenefactor": "Age:", "ageOfBenefactorDesc": "21 years and above (except for some special cases).", "allStatus": "All Status", "altLanguage": "العربية", "applayForSocial": "Apply For Social Aid", "application-process": "Application Process", "application-process-desc": "Once an applicant completes their application form and submits it, the team at the Ministry of Community Empowerment will review the application and respond with a decision within 22 days.", "applicationSummary": "Application Summary", "applyForFarmer": "Apply", "applyForService": "Apply", "accreditedUniversities": "(Please refer to this <a href='https://www.caa.ae/Pages/Institutes/All.aspx' download style='text-decoration: underline;'>link</a> for the list of accredited universities)", "applying-for-farmer-service": "Applying for Farmer Welfare", "applying-for-social-aid": "Applying for Social Welfare", "applying-for-to-whom": "Issue “To Whom It May Concern” Certificate", "applying-for-to-whom-but": "Issue To Whom It May Concern", "applyingForFarmerAid": "Applying for Social Welfare", "applyingForSocialWelfare": "Applying for Social Welfare", "applyingForHousing/EducationAllownce": "Applying for Housing and Education Excellence for Higher Education Allowance", "approved": "Approved", "archive": "Archive", "assetsOwnedByFamily": "Household assets", "assetsOwnedByFamilyDesc": "Household assets include:", "assetsOwnedByFamilyPoint1": "Bank deposits and property (including cash, real estate, etc.).", "assetsOwnedByFamilyPoint2": "Bank deposits and property (including cash, real estate, etc.).", "assetsOwnedByFamilyPoint3": "Income from commercial licenses.", "AED": "AED", "back": "Back", "BacktoHome": "Back to Home", "bank-statement": "Bank Statement (for the past 6 months from the date of application)", "YesCustomText": "Yes (full ownership)", "NoCustomText": "No (partial ownership or no ownership of any residential property)", "cancel": "Cancel", "cantFindAnswer": "Can’t find the answers you are looking for ?", "careers": "Careers", "caseNumberUpdated": "Case Number: {{draftId}}", "caseNumber": "Case Number", "4stepsToSignupFarmer": "4 steps to register", "caseStatus": {"draft": "Draft", "pending": "Pending", "pendingAllowanceAmountVerification": "Pending Allowance Amount Verification", "pendingAuditReview": "Pending Audit Review", "pendingBeneficiaryInformation": "Pending Beneficiary Information", "pendingCommitteeReview": "Pending Committee Review", "pendingDisbursement": "Pending Disbursement", "pendingDocumentSubmission": "Pending Document Submission", "pendingEligibilityCheck": "Pending Eligibility Check", "pendingExceptionCheck": "Pending Exception Check", "pendingFinalReview": "Pending Final Review", "requestApproved": "Case Approved", "requestRejected": "Case Rejected", "submitted": "Submitted", "temporaryQueue": "Temporary Queue", "PendingRefund": "Pending Refund", "OverduePayment": "Overdue Payment", "PaymentCompleted": "Payment Completed", "PaymentAccumulated": "PaymentAccumulated", "PendingConfirmation": "Pending Confirmation", "PendingPayment": "Pending Payment", "Refunded": "Refunded", "Stopped": "Stopped"}, "refund": "Refund Details", "ProceedToPayment": "Proceed To Payment", "PaymentRatesLabelHeader": "Please select your preferred payment rates: ", "PaymentRatesLabel": "Installment rates", "PendingRefund": "Pending Refund", "PendingRefundApproved": "Eligible / Approved - Pending Refund", "PendingRefundNotApproved": "Ineligible / Rejected - Pending Refund", "RemainingRefundAmount": "Remaining Refund Amount", "channelsPrograms": "Channels & Programs", "children": "- Children", "clickHere": "Click Here", "commerical-licenses": "Commerical licenses where the applicant and/or their household family members are registered as owners", "communicateWith": "COMMUNICATE WITH", "communicateWithLeadership": "Communicate with leadership", "complaintReview": "Your Inquiry / Suggestion has been submitted successfully and will be processed for review ", "complaints": "Inquiries / Suggestions", "complaintSummary": "Thank you for submitting your Inquiry / Suggestion You can check your Inquiry / Suggestion on Inquiries / Suggestions page \n we will contact you If we need any additional information", "complaintSummaryHead": "{{inquiryType}} Summary", "complaintSummaryTitle": "{{inquiryType}} Summary", "conditions": "Conditions", "ConfirmSelectRefundOption": "Are you sure you want to to proceed with the selected option ", "confirm": "Confirm", "contactMocd": "CONTACT MOCD", "contactUs": "Contact Us", "CopyofPassport": "Copy of Passport (with minimum six months validity)", "CopyoftheFamilyBook": "Copy of UAE Family Book (issued in the past 3 months)", "copyrights": "Copyrights", "copyrightText": "© Copyright 2025. Ministry of Community Empowerment - United Arab Emirates", "cost": "Application Cost", "customerHappinessCenters": "Customer Happiness Centers", "customerHappinessCharter": "Customer Happiness Charter", "delete": "Delete", "description": "The MOCD is seeking to improve the social development in Emirates by achieving the objectives of national agenda for the Emirates Vision 2021AD, strengthening the society and family coherence and finding a society which participates effectively in the building and development by adopting a long term strategic plans derived from the vision of wise leadership of government aiming at bringing happiness to the society and consolidating the position of country as a place of happiness.", "descriptionHeader": "Description", "disclaimer": "Disclaimer", "documentsRequired": {"documentsRequiredPart1": "Valid Emirates ID", "documentsRequiredPart2": "Salary certificate (head of household/wife)", "documentsRequiredPart3": "Real estate documents (i.e., rental contracts)", "documentsRequiredPart4": "Proof of children's ongoing university education (if eligible)", "documentsRequiredPart5": "Commercial licenses in which the applicant and their wife are registered as owners/partners, and profits from commercial licenses.", "documentsRequiredPart6": "Other documents, according to the case."}, "Documentsspecifictoeachcategory": "Category-specific Documents", "download": "Download", "draft": "Draft", "draftUpdateSuccess": "Your application has been saved as a draft successfully. You may update details and submit at any time from the 'My Cases' screen.", "dueDate": "Due Date", "ThisFieldShouldbeNumber": "This Field Should be Number", "DescriptionLimitationMsg": "Description must be at most 700 characters", "edit": "Edit", "InvalidNumberOfChildren": "The value must be greater than zero, greater than, or equal to the number of criteria selected", "PleaseEnterNumbergraterThanZero": "Please Enter Number greater Than Zero", "PleaseEntera1or2-digit": "Please insert one or two-digit number", "eligibility-criteria": "Eligibility Criteria", "eligibility-criteria-age": "Age", "eligibility-criteria-age-desc": "In special cases, you may be exempt from the age criterion depending on your situation. These include, but are not limited to", "eligibility-criteria-age-item1": "If you are an orphan", "eligibility-criteria-age-item2": "If you are a child and you do not know who your family is", "eligibility-criteria-age-item3": "If you are child of prisoner and your other parent is not present", "eligibility-criteria-age-item4": "If you are a person of determination or a person with certain health disabilities", "eligibility-criteria-age-text": "21 years old above", "eligibility-criteria-ageExemption": "Age Exemption", "eligibility-criteria-citizenship": "Citizenship", "eligibility-criteria-citizenship-text": "UAE citizens", "eligibility-criteria-dec": "The program examines applicants' eligibility based on the program's criteria - household income, household assets, citizenship, residency, and age.", "eligibility-criteria-dec2": "For Employed Households [plug in eligibility criteria summary table from word document]", "eligibility-criteria-dec3": "For Unemployed Households", "eligibility-criteria-dec4": "[plug in eligibility criteria summary table from word document]", "eligibility-criteria-dec5": "In special cases, applicants may be exempt from certain criteria.", "eligibility-criteria-dec6": "[plug in eligibility exemption rules summary table from word document]", "eligibility-criteria-dec7": "In other special cases, some eligible applicants may be exempt from the employment requirement and still qualify for employed benefits.", "eligibility-criteria-dec8": "[plug in employment requirement exemption rules table from word document]", "eligibility-criteria-householdAssets": "Household Assets", "eligibility-criteria-householdAssets-desc": "Assets include, but are not limited to", "eligibility-criteria-householdAssets-item1": "Personal holdings (e.g., cash, non-primary home, non-primary vehicles)", "eligibility-criteria-householdAssets-item2": "Investments in real estate, businesses", "eligibility-criteria-householdAssets-item3": "Trade licenses", "eligibility-criteria-householdAssets-text": "Total household assets should be below asset threshold ", "eligibility-criteria-householdIncome": "Household Income", "eligibility-criteria-householdIncome-desc": "Income includes, but is not limited to", "eligibility-criteria-householdIncome-item1": "Income from employment or self-employment, including bonuses, commissions", "eligibility-criteria-householdIncome-item2": "Financial income (e.g., real estate income)", "eligibility-criteria-householdIncome-item3": "Social benefits (e.g., pension)", "eligibility-criteria-householdIncome-text": "Total household income should be below income threshold", "eligibility-criteria-residency": "Residency", "eligibility-criteria-residency-text": "UAE residents – household members that are not residing in the UAE will not qualify for their respective allowance component", "eligibility-criteria-residencyExemption": "Residency Exemption", "eligibility-criteria-residencyExemption-desc": "If you are undergoing medical treatment outside the UAE, additional documentation that is approved by the relevant health authorities must be provided along with the application for the residency criteria to be waived", "eligibility-criteria-start": "The criteria you need to meet to receive Social Welfare support depend on your current situation. To receive the program’s allowance(s), the following criteria* apply", "eligibilityCriteriaForProgram": "Program eligibility criteria", "eligibilityCriteriaForProgramDesc1": "All the conditions you must meet to receive social welfare allowances rely on your current situation, according to the below five criteria:", "eligibilityCriteriaForProgramDesc2": " دخل الأسرة: مجموع الدخل الصافي الذي يجنيه رب الأسرة والزوجة، بما في ذلك على سبيل المثال: الراتب الذي يتلقاه رب الأسرة، المداخيل من الإيجارات، والربح من التراخيص التجارية، يجب أن يكون أقل من الحد الأدنى للمساعدة المفترضة والتي تشمل العلاوة المطبقة على المستفيد وأفراد الأسرة.", "eligibilityCriteriaForUnder45Program": "Eligibility criteria for jobseekers (25-44 years old)", "eligPoints": {"eligibilityUnder45Point1": "Registered in the NAFIS program for no less than 3 months.", "eligibilityUnder45Point2": "Applicants' income (head of the household/wife) must not exceed the value of allowance.", "eligibilityUnder45Point3": "Head of the household/wife must not be benefitting from aid program in the UAE.", "eligibilityUnder45Point4": "Completion of or exemption from national service.", "eligibilityUnder45Point5": "Good conduct and behaviour.", "eligibilityUnder45Point6": "Must not be a student or registered in any standard educational institution."}, "email": "Email Address", "EmiratesID": "Emirates Id", "emiratesID": "Emirates ID", "employeeMail": "Employee Mail", "estimated-process-time": "Estimated Processing Time", "experience-service": "How was your experience using the service?", "familyIncome": "Family income:", "familyIncomeDesc": "Total net income earned by the head of the household and their spouse:", "familyIncomeNoColon": "Family income", "familyOwnedAssets": "Household assets:", "familyOwnedAssetsDesc": "Total value of assets and property owned by family members (wholly or partially) must be less than the specified maximum asset value to benefit from the program.", "Faq": "FAQs", "faq-pdf": "FAQ PDF", "FaqGuide": "This guide will help you leearn more about our service and how it works.", "farmerDocumentsRequired": {"1": "Valid Emirates ID", "2": "Salary certificate (head of household/wife)", "3": "Real estate documents (i.e., rental contracts)", "4": "Commercial licenses in which the applicant and their wife are registered as owners/partners, and profits from commercial licenses.", "5": "Other documents regarding Family income"}, "farmerForthStep": "4. if accepted you will get email and sms", "farmerSecondStep": "2. fill request ", "farmerServiceChannel": "Registration is opened on the website of the Ministry of Community Empowerment to apply for support for farm owners.", "farmerServiceDescription": "This service is a new subsidy for farm owners with limited income, at a value of 8,400 dirhams annually for each beneficiary, which is equivalent to 2,500 kilowatt-hours per month, or the value of consumption, whichever is less, through a direct deduction from the farm’s monthly electricity consumption bill at the Union Water and Electricity Company.", "farmerServiceEntity": "The Ministry of Community Empowerment in cooperation with the Union Water and Electricity Company.", "farmerServiceTerms": {"0": "The farm owner must be a citizen of the United Arab Emirates.", "1": "This grant applies only to owners of farms connected to the service provider 'Union Water and Electricity Company'.", "2": "The owner must belong to one of the following categories", "3": "Beneficiaries of federal or local social support.", "4": "Beneficiaries of federal or local social support.", "5": "Individuals with limited income (total income of the couple less than 25,000).", "6": "Consumption support will be applied to the electricity bill only.", "7": "The actual monthly consumption of the beneficiaries will be calculated.", "8": "Monthly support will be provided with a value of AED 700 per month (equivalent to 2,500 kilowatt-hours) or the value of monthly consumption, whichever is lower.", "9": "Support is granted on a monthly basis and cannot be carried over from one month to another, nor is it granted retroactively.", "10": "Beneficiaries can check the amount of support through the monthly consumption bill for the farm sent by the 'Union Water and Electricity Company'.", "11": "The owner will receive assistance for only one farm and one account if the farm has more than one account.", "12": "The farm must not be used for commercial purposes.", "13": "If the owner receives other electricity support from the ministry or local authorities, the higher support value will be disbursed.", "14": "Coordination will take place between the Ministry of Community Empowerment and the Union Water and Electricity Company, and the approval of the lists of beneficiaries on a monthly basis, starting from the date of 07 of each month.", "15": "The subsidy will be stopped in the event that the farm account with the Union Water and Electricity Company is closed, and the subsidy will not be activated for any new account of the same customer except through the Ministry of Community Empowerment."}, "free": "Free", "free-service": "Free of charge", "frequentlyAskedQuestions": "Frequently Asked Questions", "genericErrorDescription": "Please try again later.", "genericErrorTitle": "An error has occurred", "goHome": "Go back to home", "googleMaps": "GOOGLE MAPS", "greeting": "Hello in English!", "guidelines": "Guidelines", "HaveQuestion": "Have a question?", "helpful-text": "Did you find this content helpful?", "hours": "hours", "IncomeSourcesDocument": "Title deeds of owned real estate (land, commercial properties, and/or residential properties)", "individual": "- Individual", "inProgress": "In Progress", "instantly": "Instantly", "Inflation": "Inflation Service", "applyForHousingEducationTopup": "Applying for both Housing Allowances and Education Excellence Allowances for Higher Education", "applyForHousingAllowance": "Applying for a Housing Allowance", "applyForAcademicExcellenceAllowance": "Applying for an Education Excellence Allowance for Higher Education", "actions": "Actions", "keywords": "n", "placeholder": "", "landingApply": "Click here to apply for the service", "landingForMoreInfoInflation": "For more details about inflation subsidies click here", "landingForMoreInformation": "For more details about the program click here", "landingHeader": "UAE President's Initiatives", "landingPageDec": "A government subsidy and monthly social benefits to enhance the standard living of citizens and achieve the wellbeing of all the members of the community. It also enables beneficiaries to meet both economic and living requirements. The new social subsidy program includes: children's allowance, Education Excellence for Higher Education, housing allowance and periodic increases.", "landingPageDec2": "Inflation Subsidies: a government monthly subsidy for low-income Emirati families to raise their wellbeing and cope with the worldwide rise in fuel, food, electricity and water prices. The subsidies come in line with the latest economic and social developments in the UAE to meet the living standard needs.", "landingPageDec3": "The subsidy value provided to beneficiaries includes: fuel subsidy, food subsidy, electricity and water subsidy.The Ministry of Community Empowerment began implementing the fuel subsidy on July, followed by the food subsidy on August, electricity and water subsidy starting from the September 2022 consumption bill.", "landingPageLinkText": "All subsidies are submitted through the website of the Ministry of Community Empowerment:", "landingPageTitle": "Social Welfare", "landingPageTitle2": "Inflation Subsidies", "leadership": "LEADERSHIP", "logIn": "Log In", "logOut": "Log Out", "makeSureRequiredDocumentsReady": "Ensure that all required documents are available before starting the application process.", "mins": "mins", "missingDocuments": " , the documents for the request is not complete click on the case number to proceed", "mobileApp": "Mobile App", "mobileNumber": "Mobile Number", "mocdSiteUrl": "ministryofcommunitydevelopment.gov.ae", "monthlyIncome": "Monthly income", "monthlyIncomeDesc": "Total monthly salaries, income from commercial licenses, real estate returns, and any other revenues earned by both husband and wife.", "more-faq": "For more information please download the", "moreInfoRequired": "Extra info required", "mustBeNumber": "Entered value must be a number.", "myAllowance": "My Allowance", "myCases": "My Cases", "myProfile": "My Profile", "name": "Name", "nationaility": "Citizenship:", "nationailityDesc": "Emirati citizens residing in the UAE.", "navbar-about": "About the program", "navbar-complaints": "Inquiries / Suggestions", "navbar-faq": "FAQ", "navbar-home": "Home", "navbar-applyToReund": "Refund", "PayFullAmount": "Pay Total Amount", "navbar-howToApply": "Smart services", "navbar-myAllowance": "My Allowance", "navbar-myCases": "My Cases", "newComplaint": "New Complaint", "newInquirie": "New", "newRequest": "New Case", "newsAndUpdatesTitle": "Tutorials and Guidelines", "nextStep": "Next", "no": "No", "noChangesTitle": "No Changes Detected", "noChangesDescription": "You haven't made any changes in Housing or Education. Please make modifications before proceeding.", "noNotifications": "No notifications", "notifications": "Notifications", "or": "or", "ourNewsLetter": "OUR NEWSLETTER", "PaymentFailed": "Payment Failed", "payment-error-title": "There was an error processing your payment", "payment-error-subTitle": "You will be redirected to the home page shortly", "pendingItems": "Pending Items", "pleaseWaitDownloadingFile": "Please wait while we download your file.", "pleaseWaitLoadDraft": "Please wait while we load your application.", "PointofServiceDelivery": "Points of Program Application", "PointofServiceDeliveryBody": "- Social Welfare Program Portal", "privacyPolicy": "Privacy Policy", "problemSolved": "Resolved", "proceed": "Proceed", "proceedMocdText": "For more information about the Ministry's services", "process": "Process", "program-details": "Program Details", "reachUs": "Please reach out to our friendly team.", "readMore": "Read More", "rejected": "Rejected", "remoteAccess": "Remote Access", "request": "Case", "request-bloced-desc": "At this time, content is only accessible from with the UAE. However, we are working hard to provide you with MOCD services outside the UAE soon", "request-bloced-title": "Sorry but you dont have access to this website!", "requestReview": "Your application has been submitted successfully and will be processed for review by the relevant department.", "required-documents": "Required Documents for the Application", "required-documents-des": "Keep them handy before you start your application!", "resdiencyException": "Residency Exemption", "resdiencyExceptionDesc": "If you are undergoing medical treatment outside the UAE, additional documentation that is approved by the relevant health authorities must be provided along with the application for the residency criteria to be waived", "residency": "Residency:", "residencyDesc": "Residents in the UAE", "save": "Save", "saveAsDraft": "Save as Draft", "search": "Search", "SearchbyKeyword": "Search by Keyword...", "Sendusanemail": "800623", "service-description": "Service Description", "service-nformation": "Service Information", "serviceBeneficiaries": "The Social Welfare Program provides appropriate social support to individuals and families in the UAE, to ensure they receive a minimum monthly income that helps them secure their needs and enhance the wider community's quality of life.", "ServiceChannel": "service delivery channel", "ServiceDescription": "Program Description", "ServiceDescriptionBody": "The Social Welfare Program provides social support for Emirati individuals and families in the UAE, with the aim of ensuring better quality of life for them and their households. Every applicant that meets the eligibility criteria will benefit from the program. Following a structured formula based on the minimum income threshold that a household should receive for a decent and sustainable life, each beneficiary will receive a basic allowance that will empower them to ease their economic circumstances. In its new structure, the program provides social support through 5 allowances:", "ServiceEntity": "service provider", "serviceInformation": "Service Info", "sitemap": "Sitemap", "spouse": "- Spouse", "status": "Status", "StillHaveQuestions": "Still Have Questions?", "submit": "Submit", "submitted": "Submitted", "subscribeTo": "SUBSCRIBE TO", "SelectedOption": "Selected Option", "support": "Support", "termsAndConditions": "Terms & Conditions", "termsConditions": "Terms & Conditions", "thanksForApplaying": "Thank you for applying to the Social Welfare Program. You may check the status of your application at any time, via the 'My Cases' page. Should we require any additional information, we will reach out to you soon.", "thanksForEditingBody": "Thank you for amending the application to obtain the social assistance service. Your application for the service has been submitted and is under review. The Ministry of Community Empowerment will contact you soon.", "thanksForEditingTitle": "Your request has been successfully modified and is under review.", "the-target-audience": "The Target Audience", "the-target-audience-desc": "All citizens and residents of the UAE.", "ThisPageCouldNotBeFound": "Oops,This Page Could Not Be Found.", "ThisPageCouldNotBeFoundDescription": "The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.", "timeRequiredToComplete": "Time required to complete the service", "title": "Ministry of Community Empowerment", "tollFreeNumber": "Tollfree 800623", "uaePromiseGuidelines": "UAE Promise Guidelines", "under-maintenance-desc": "The page you are trying to access is currently under maintenance and will be available again in the next hour.", "under-maintenance-desc2": "In the meantime, feel free to go to mocd.gov.ae for updates.", "under-maintenance-title": "The site is currently down for maintenance", "unemployedJob": "- Unemployed job seeker (lost job from non-private sector)", "unemployedOver": "- Unemployed over the age of 45", "upto-days": "21 days", "userExperience": "User Experience (UX Lab)", "valid-emirates-id": "Valid Emirates ID", "emiratesIdValidationTitle": "Something went Wrong", "incorrectNationality": "The husband should not be a UAE national.", "emiratesIdValidation": "Invalid Emirates ID", "emiratesIdValidationFailedDob": "Date of Birth is not correct", "emiratesIdValidationFailedGender": "Invalid emirates ID", "video-title": "How to apply for Social Subsidy Program", "viewAll": "View All", "viewLocationIn": "VIEW LOCATION IN", "whistleblower": "Whistleblower Form (IAO)", "whom-service-desc": "A To Whom It May Concern Certificate is issued to people and agencies, to find out whether the people who are inquired about them receive social assistance from the MOCD or not. You must create a personal profile to benefit from this this service.", "yes": "Yes", "you-can-apply": "You can apply to this service quickly!", "eligibilityCriteria": "Eligibility Criteria", "familyIncomePoint1": "Monthly salary or income from private work (e.g., owned businesses, self-employment, financial income including rental contracts)", "familyIncomePoint2": "Income from other social benefits (e.g., pensions and other social benefits in the UAE)", "farmerAidBreadTitle": "Low-income UAE national farm owners", "farmerServiceTime": "Support will be implemented starting in July 2023.", "nationailityDesc2": "In the event that the applicant for the social support program is undergoing treatment outside the country or is accompanying a patient, additional documents certified and approved by the competent health authorities must be submitted.", "requestReview2": "Your request was submitted successfully.", "serviceTime": "When will the aid apply?", "signupSteps": {"step1desc": "Complete the application form and submit it via the Social Welfare Program Portal to the Ministry of Community Empowerment", "step1title": "1. Create an account/log in to the website", "step2desc": "Once the application is completed and sent, the relevant team at the Ministry of Community Empowerment will review it and respond.", "step2title": "2. Fill out the application for obtaining social welfare and attach the required documents", "step3desc": "In the event that the request is approved, the bonuses will be activated for the beneficiary of the program according to the category of the bonus due", "step3title": "3. Reviewing the application by the competent team to ensure that eligibility requirements and conditions are met", "step4title": "4. If the submitted application is approved, you will be notified via a text message / e-mail and the bonus will be activated according to the dues."}, "startsInJuly": "Service begins in July", "PaidRefundAmount": "Paid Refund Amount ", "TotalRefundAmount": "Total Refund Amount", "AccumulatedAmount": "Accumulated Amount", "thanksForApplaying2": "The request will be reviewed and addressed via the email registered with us.", "textResizer": "Text Resize", "textResizerDesc": "Use the slider below to increase or decrease font size", "ourPartners": "Our Partners", "processSteps": "Request steps", "aboutWebsite": "About Website", "siteMap": "Sitemap", "next": "Next", "previous": "Previous", "allowanceCalculator": "Allowance Calculator", "pages": "Pages", "more": "More", "Beneficiaries": "Beneficiaries", "appeal": "Appeal", "reOpen": "Reopen", "Reopened": "Reopened", "InformationProvided": "Information Provided", "appealConfirmation": "Are you sure you want to file an appeal?", "reOpenConfirmation": "Are you sure you want to Reopen?", "reOpenReson": "Please enter a reson to reopen the case ", "sentForAppeal": "<PERSON>t for appeal", "cantCreateComplaint": "The request cannot be submitted as there is currently another active inquiry/complaint with the same topic under reference number: {{caseNum}}", "guardianOtpHeader": "Guardian OTP Verification", "otpVerificationSubTitle": "An OTP has been sent to your guardian on this number {{otpNumber}}", "complaint": "<PERSON><PERSON><PERSON><PERSON>", "complaintSubText": "Submit a complaint regarding a recent service used.", "inquiry": "Inquiry", "inquirySubText": "Request information pertaining to your service of interest.", "suggestion": "Suggestion", "suggestionSubText": "Provide a suggestion on how we could improve your experience.", "thankYou": "Thank You", "thankYouSubText": "Express appreciation towards an employee or service used.", "complaintAuthText": "Are you sure you want to continue as a guest ?", "continue": "Continue", "lastUpdate": "Website last updated on:", "websiteInfo": "This site is best viewed in Screen Resolution 1920 x 1080. Support, Microsoft Edge, Firefox 10+, Google Chrome 12+, Safari 3+", "closed": "Closed", "myInquiriesSuggestions": "My Inquiries / Suggestions", "expEidDate": "You can't apply with expired Emirates ID", "notEmarati": "only Emirati citizens can apply for this request", "farmerServiceRemoveTitle": "To apply for 'Farm Owners Subsidy', please contact the service provider ‘Etihad Water and Electricity Company’ Through this", "DearCustomer": "Dear valued customer,", "linkText": "link", "thankYouTitle": "Thank you", "utlityAllowanceError": "This utility account is ineligible to receive utility allowance. Please insert another utility account number, or proceed to the next section. in case the information is not correct please contact your utility provider to update your information.", "notEligibleForInflation": "You are not eligible for inflation subsidy", "InactiveError": "This utility account is not eligible to receive inflation allowance because it is inactive. Please insert another utility account number, or proceed to the next section without applying to utility allowance. in case the information is not correct please contact your utility provider to update your information.", "Non-ResidentialError": "This utility account is not eligible to receive inflation allowance because it is non-residential. Please insert another utility account number, or proceed to the next section without applying to utility allowance. in case the information is not correct please contact your utility provider to update your information.", "Non-EmiratiError": "This utility account is not eligible to receive inflation allowance because the account holder is non-Emirati. Please insert another utility account number, or proceed to the next section without applying to utility allowance. in case the information is not correct please contact your utility provider to update your information.", "ReceivingUtilityAidError": "This utility account is not eligible to receive inflation allowance because it is receiving another utility aid. Please proceed to the next section without applying to utility allowance. in case the information is not correct please contact your utility provider to update your information.", "inflationEdit": "Please update all inflation related information on the inflation allowance case that appears under 'My Cases' section", "topups": "Education Excellence for Higher Education", "info": "Info", "RecevingUtilityInfo": "This utility account is already receiving inflation allowance", "downloadTemplate": "Download Template"}