/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { NmwpEstablishmentDetailsComponent } from './nmwp-establishment-details.component';

describe('NmwpEstablishmentDetailsComponent', () => {
  let component: NmwpEstablishmentDetailsComponent;
  let fixture: ComponentFixture<NmwpEstablishmentDetailsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ NmwpEstablishmentDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NmwpEstablishmentDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
