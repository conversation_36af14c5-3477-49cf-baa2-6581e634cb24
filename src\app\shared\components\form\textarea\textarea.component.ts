import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { ValidationService } from '../../../services/validation.service';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-textarea',
  templateUrl: './textarea.component.html',
  styleUrl: './textarea.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class TextareaComponent {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Input() rows: number = 5;
  @Input() isReadOnly: boolean = false;

  @Input() showGoogleTranslate: boolean = false;
  @Input() showGoogleTranslateToRelatedCompoent: boolean = false;
  @Input() googleTranslateTarget: string;
  @Input() googleTranslateToRelatedCompoent: FormControl | undefined | null;
  private _placeholder: string = '';

  constructor(private validationService: ValidationService, private translate:TranslateService, private cd: ChangeDetectorRef) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder='';
    });
   }

  public get Placeholder() {
    if(this._placeholder)
    {
      return this._placeholder;
    }
    this._placeholder = this.placeholder? this.placeholder: `${this.translate.instant('Please enter')}${this.translate.instant(this.label)}`;
    return this._placeholder;
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }
  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key],this.control);
        }
      }
    }
    return null;
  }
  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
