<div class="container">
  <div class="card">
    <div class="card-body">
      <div class="row center pageCss">
          @if(isSuccess){
          <div class="container">
            <img src="assets/images/success.png" alt="" style="width: 150px;">
          </div>
          <h4 class="card-title"><strong>{{'payment.Status' | translate}}: </strong> {{'payment.Success' | translate}}</h4>
          }
          @else{
          <div class="container">
            <img src="assets/images/error.png" alt="" style="width: 150px;">
          </div>
          <h4 class="card-title"><strong>{{'payment.Status' | translate}}: </strong> {{'payment.Failed' | translate}}</h4>
          }

        @if(data)
        {
          <h4 class="card-title"><strong>{{'payment.Payment Date' | translate}}: </strong> {{data.PaymentDate | date:'dd/MM/yyyy'}}</h4>
          <h4 class="card-title"><strong>{{'payment.Payment Amount' | translate}}: </strong> {{data.PaymentAmount}}</h4>
          <h4 class="card-title"><strong>{{'payment.Payment Provider Message' | translate}}: </strong> {{data.PaymentProviderMessage}}</h4>
          <h4 class="card-title"><strong>{{'payment.Payment ID' | translate}}: </strong> {{paymentId}}</h4>
        }

      </div>
    </div>
    <div class="center p-2">
      <button mat-flat-button color="primary" (click)="goToMyApps()">{{'userPages.applications.title' | translate}}</button>

    </div>
  </div>
  </div>
