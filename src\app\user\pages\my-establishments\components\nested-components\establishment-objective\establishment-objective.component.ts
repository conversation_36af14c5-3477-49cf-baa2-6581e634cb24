import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { filter, tap } from 'rxjs';
import { ActionFormType } from '../../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { LegalFormTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/legal-form-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { arabicEnglishSpaceValidator } from '../../../../../../shared/validators/arabic-english-space-validator';
import { flexibleletterPatternValidator } from '../../../../../../shared/validators/flexible-letter-pattern-validator';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-establishment-objective',
  templateUrl: './establishment-objective.component.html',
  styleUrls: ['./establishment-objective.component.scss']
})
export class EstablishmentObjectiveComponent extends MyEstablishmentComponentBase implements OnInit, OnChanges {



  internalObjectiveForm: FormGroup;
  objectivesList: any[];
  includeConditionIds: boolean = false;

  get objectives(): FormArray {
    return this.form.get('objectives') as FormArray;
  }
  get fControls(): any {
    return this.internalObjectiveForm.controls;
  }


  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() legalFormType: LegalFormTypesEnum;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();

  selectedLegalFormType: LegalFormTypesEnum;


  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.objectives.";
    this.intiForm();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }

    if(changes['legalFormType']?.currentValue && changes['legalFormType']?.currentValue !== undefined) {
      this.selectedLegalFormType = this.legalFormType;
    }
  }

  ngOnInit() {
    // this.StepperService.requestData$.subscribe(_ => {
    //   if (_ && (_.isFullDetails == true)) {
    //     this.objectives.clear();
    //     this.mapData(_?.ObjectivesForm) // todo: make sure of the form name in subject
    //     // Object.keys(this.form.controls).forEach((control) => {
    //     //   if (
    //     //     (this.form.get(control)?.value === null ||
    //     //       this.form.get(control)?.value === '' ||
    //     //       !this.form.get(control)?.value) &&
    //     //     this.isNotAllowedToEdit === false
    //     //   ) {
    //     //     this.form.get(control)?.enable();
    //     //   } else {
    //     //     this.form.get(control)?.disable();
    //     //   }
    //     // });
    //   } else if (_ && (_.isFullDetails == false) && _.ObjectivesForm) {
    //     this.objectives.clear();
    //     this.mapData(_?.ObjectivesForm);
    //   }
    // })
    this.MyEstablishmentService.lookupData$.pipe(
      filter(data => !!data), tap(data => {
        this.objectivesList = data.Objectifs;
        this.filterObjectiveBasedOnType();

        this.StepperService.requestData$.subscribe(_ => {
          if (_ && (_.isFullDetails == true)) {
            this.mapData(_?.ObjectivesForm) // todo: make sure of the form name in subject
          } else if (_ && (_.isFullDetails == false) && _.ObjectivesForm) {
            this.objectives.clear();
            this.mapData(_?.ObjectivesForm);
          }
        })
      })
    ).subscribe();
    this.pagination();
    this.objectives.valueChanges.subscribe(_ => this.pagination());
  }



  intiForm = (): void => {
    this.internalObjectiveForm = this.FormBuilder.group({
      Id: [''],
      NPOEstablishment: new FormControl(""),
      Objectives: new FormControl("", [Validators.required,arabicEnglishSpaceValidator()]),
      ObjectivesAR: new FormControl("", [Validators.required,arabicEnglishSpaceValidator()]),
      MeansOfAchievingObjective: new FormControl("", [Validators.required,flexibleletterPatternValidator()]),
      MeansOfAchievingObjectiveAR: new FormControl("", [Validators.required,flexibleletterPatternValidator()]),
      SatusReason: [],
    });
  }

  isItemDuplicated = (item: any): boolean => {
    if (!this.objectives?.controls) return false;

    return this.objectives.controls.some(control => {
      const { Objectives, ObjectivesAR, Id } = {
        Objectives: control.get('Objectives')?.value?.trim(),
        ObjectivesAR: control.get('ObjectivesAR')?.value?.trim(),
        Id: control.get('Id')?.value
      };

      return (
        (Objectives === item.Objectives?.trim() || ObjectivesAR === item.ObjectivesAR?.trim()) &&
        Id !== item.Id
      );
    });
  };

  manageObjective(item: any, type: GridActionTypesEnum) {
    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError('notify.error', 'notify.objectiveIsAlearyExist');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.objectives.push(this.FormBuilder.group({
        Id: [this.generateDistinctId()],
        Objectives: [item.Objectives,],
        ObjectivesAR: [item.ObjectivesAR,],
        MeansOfAchievingObjective: [item.MeansOfAchievingObjective,],
        MeansOfAchievingObjectiveAR: [item.MeansOfAchievingObjectiveAR,],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
      }));
    else
      this.objectives.at(this.objectivEditIndex).patchValue(
        {
          Id: item.Id,
          Objectives: item.Objectives,
          ObjectivesAR: item.ObjectivesAR,
          MeansOfAchievingObjective: item.MeansOfAchievingObjective,
          MeansOfAchievingObjectiveAR: item.MeansOfAchievingObjectiveAR,
          NPOEstablishment: item.NPOEstablishment,
          SatusReason: item.SatusReason?.trim() || 'draft',
        }
      );

    this.internalObjectiveForm.reset();
    this.modalService.dismissAll();
  }
  objectivEditIndex: number;
  handleEdit(data: any, idx: number) {
    this.objectivEditIndex = idx;
    this.internalObjectiveForm.patchValue({
      Id: data?.value?.Id,
      Objectives: data?.value?.Objectives,
      ObjectivesAR: data?.value?.ObjectivesAR,
      MeansOfAchievingObjective: data?.value?.MeansOfAchievingObjective,
      MeansOfAchievingObjectiveAR: data?.value?.MeansOfAchievingObjectiveAR,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
    this.FormService.enableFields(this.internalObjectiveForm, ['Objectives', 'ObjectivesAR', 'MeansOfAchievingObjective', 'MeansOfAchievingObjectiveAR'])
  }

  handleClone(data: any) {
    this.internalObjectiveForm.patchValue({
      Objectives: data?.value?.Objectives,
      ObjectivesAR: data?.value?.ObjectivesAR,
      MeansOfAchievingObjective: data?.value?.MeansOfAchievingObjective,
      MeansOfAchievingObjectiveAR: data?.value?.MeansOfAchievingObjectiveAR,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
    this.FormService.enableFields(this.internalObjectiveForm, ['Objectives', 'ObjectivesAR', 'MeansOfAchievingObjective', 'MeansOfAchievingObjectiveAR'])
  }

  async removeObjective(data: any, idx: number) {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.objectives?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.Id, this.OBJECTIVE_GRID_IN_CRM);

      this.objectives.removeAt(idx);
    }
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("ObjectivesForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("ObjectivesForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("ObjectivesForm", false);
    this.handleSaveRequest(submitParams);
  }

  isValidForm = (): boolean => {
    // const legalFormMapping = {
    //   [LegalFormTypesEnum.AssociationByDecree]: 'AssociationByDecree',
    //   [LegalFormTypesEnum.NationalSocietyByDecree]: 'NationalSocietyByDecree'
    // }

    // const selectedForm = legalFormMapping[this.legalFormType];

    // if (selectedForm) {
    //   return this.objectives.length >= 1;
    // }


    // return this.objectives.length >= 5;
    return true;
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      Objectif: (this.objectives.controls as Array<any>).map((control, index) => {
        let _$ = control.value;
        return {
          Id: (this.includeConditionIds && _$.Id) ? _$.Id : (_$.canEdit == false ? this.EMPTY_GUID : _$.Id ?? this.EMPTY_GUID),
          Objectives: _$.Objectives,
          ObjectivesAR: _$.ObjectivesAR,
          MeansOfAchievingObjective: _$.MeansOfAchievingObjective,
          MeansOfAchievingObjectiveAR: _$.MeansOfAchievingObjectiveAR,
          Establishment: this.EMPTY_GUID,
          Priority: index + 1,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$.SatusReason?.trim() || 'draft'
        };
      })

    }
  }

  mapData = (data: any): void => {
    if (!data) return;

    data.Objectif = data.Objectif.sort((a, b) => a.Priority - b.Priority);

    data.Objectif.forEach((item: any) => {
      this.objectives.push(this.FormBuilder.group({
        Id: [item.Id ?? ''],
        Objectives: [item.Objectives],
        ObjectivesAR: [item.ObjectivesAR],
        MeansOfAchievingObjective: [item.MeansOfAchievingObjective],
        MeansOfAchievingObjectiveAR: [item.MeansOfAchievingObjectiveAR],
        NPOEstablishment: [item.Establishment],
        // canEdit: [(this.objectivesList?.find(_ => _.Objectives == item.Objectives && _.ObjectivesAR == item.ObjectivesAR)) ? false : true],
        canEdit: true,
        SatusReason: [item.SatusReason?.trim() || 'draft']
      }));
    });

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.objectives?.value || this.objectives?.value?.length == 0)) {
      this.fillPredefinedObjectives();
    } else {
      this.includeConditionIds = true;
    }
  }


  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "objectives", this.fControls);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;


  checkObjectiveExist = (item: any): boolean => {
    if (!item?.Objectives || !item?.ObjectivesAR) return false;

    const itemObjectives = item.Objectives.trim().toLowerCase();
    const itemObjectivesAR = item.ObjectivesAR.trim().toLowerCase();

    return this.objectives.controls.some(control => {
      const objectives = control.get('Objectives')?.value?.trim().toLowerCase();
      const objectivesAR = control.get('ObjectivesAR')?.value?.trim().toLowerCase();

      return objectives === itemObjectives && objectivesAR === itemObjectivesAR;
    });
  }




  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.objectives.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
    data$.forEach(_ => this.dataTable.push(_));
  }



  filterObjectiveBasedOnType = (): void => {
    const legalFormMapping = {
      [LegalFormTypesEnum.Union]: 'Union',
      [LegalFormTypesEnum.SocialSolidarityFunds]: 'Social Solidarity Fund'
    }

    const selectedForm = legalFormMapping[this.selectedLegalFormType];

    if (selectedForm) {
      this.objectivesList = this.objectivesList.filter(objective => objective.LegalForm === selectedForm);
    } else {
      this.objectivesList = [];
    }

    this.objectivesList = this.objectivesList.sort((a, b) => a.Priority - b.Priority);
  };

  get countOfPredefindConditions(): number {
    return this.objectivesList?.length ?? -1;
  }

  fillPredefinedObjectives = (): void => {
    if (this.objectivesList?.length > 0) {
      this.objectivesList.forEach(objective => {
        if (!this.checkObjectiveExist(objective?.Id)) {
          this.objectives.push(this.FormBuilder.group({
            Id: [objective.Id],
            NPOEstablishment: [objective.NPOEstablishment],
            Objectives: [objective.Objectives],
            ObjectivesAR: [objective.ObjectivesAR],
            MeansOfAchievingObjective: [objective.MeansOfAchievingObjective],
            MeansOfAchievingObjectiveAR: [objective.MeansOfAchievingObjectiveAR],
            canEdit: [false],
            SatusReason: [objective.SatusReason?.trim() || 'draft']
          }))
        }
      })
    }
  }

}
