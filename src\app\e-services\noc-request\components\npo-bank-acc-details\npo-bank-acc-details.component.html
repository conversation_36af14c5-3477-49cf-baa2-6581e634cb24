<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="col-md-12 d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center">
        {{(messageTranslationPrefix+'title') | translate}}
      </h1>
    </div>


    <div class="row section-separator">
      <div class="col-md-6 col-sm-12">
        <app-select-search [label]="messageTranslationPrefix+'selectBank' | translate" [placeholder]="messageTranslationPrefix+'selectBank' | translate" [control]="fb?.Bank" [data]="banks" (onValueChange)="selectedBank($event)" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [label]="messageTranslationPrefix+'selectBankAccount' | translate" [placeholder]="messageTranslationPrefix+'selectBankAccount' | translate" [control]="fb?.BankAccount"  [data]="bankAccounts"/>
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'accountOwnerNameEn' | translate" [control]="fb?.AccountOwnerNameEn" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'accountOwnerNameAr' | translate" [control]="fb?.AccountOwnerNameAr" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'branchName' | translate" [control]="fb?.BranchName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'bankAddress' | translate" [control]="fb?.BankAddress" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'ibanNumber' | translate" [control]="fb?.IBANNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [label]="messageTranslationPrefix+'accountType' | translate" [control]="fb?.AccountType" [data]="accountTypes" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'accountOpenDate' | translate" [control]="fb?.AccountOpenDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select-search [label]="messageTranslationPrefix+'currencyType' | translate" [control]="fb?.CurrencyType" [data]="currencyTypes" />
      </div>

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
        (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()"> {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
