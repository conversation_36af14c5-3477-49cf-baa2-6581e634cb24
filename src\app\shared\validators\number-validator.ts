import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function numberValidator(minNumber): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (isNaN(value) || value === null || value === undefined || value === '') {
      return { required: true };
    }

    if (value <= minNumber) {
      return { invalidNumber: true };
    }
    return null;
  };
}
