export interface FoundingMemberConfirmation {
  BeneficiaryInfo: BeneficiaryInfo[];
  FounderStatus?: any;
  LegalForm?: { LegalFormname?: string, LegalFormnameAr?: string };
  ProposedNames?: ProposedNames[];
  data?: any;
  dataCount?: number;
  Status?: number;
  MessageAR?: string;
  MessageEN?: string;
}

export interface BeneficiaryInfo {
  EmirateId?: string;
  DoB?: string;
  FullName?: string;
  EmailAdresse?: string;
  MobilePhone?: string;
  Natinoality?: string;
  NatinoalityAr?: string;
  Emirate?: string;
  EmirateAr?: string;
  PassportNumber?: string;
  Residencyissuancedate?: string;
  Residencyexpirydate?: string;
}

export interface ProposedNames {
  Name?: string;
  NameAr?: string;
}

export interface PostResponseForFounderMember {
  ApprouveOrReject?: number;
  AcademicQualification?: number | string | null;
  JobTitle?: string;
  Employer?: string;
  PersonalPhoto?: PeronalAndPassportPhoto;
  PassportPhoto: PeronalAndPassportPhoto;
}

export interface PeronalAndPassportPhoto {
  Id?: string;
  DocumentType?: string;
  mimeType?: string;
  FileName?: string;
  Base64?: string;
}
