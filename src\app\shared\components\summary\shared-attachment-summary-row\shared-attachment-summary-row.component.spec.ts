import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SharedAttachmentSummaryRowComponent } from './shared-attachment-summary-row.component';

describe('SharedAttachmentSummaryRowComponent', () => {
  let component: SharedAttachmentSummaryRowComponent;
  let fixture: ComponentFixture<SharedAttachmentSummaryRowComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SharedAttachmentSummaryRowComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(SharedAttachmentSummaryRowComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
