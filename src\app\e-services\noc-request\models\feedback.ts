export interface Feedback {
  Decision?: number;
  RegardingId?: string;
  Regarding?: string;
  RegardingType?: string;
  Comments?: string;
  Section?: string;
  SectionAr?: string;
  SectionTechnicalname?: string;
  FieldorGridName?: string;
  FieldorGridNamearabic?: string;
  FieldorGridTechnicalname?: string;
  Type?: FileType;
}


export enum FileType {
  Field = 0,
  Grid = 1,
  Attachement = 2
}
