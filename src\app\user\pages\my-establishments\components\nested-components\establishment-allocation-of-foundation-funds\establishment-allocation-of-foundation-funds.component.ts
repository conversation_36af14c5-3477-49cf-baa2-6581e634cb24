import { sumBy } from 'lodash';
import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, FormControl, Validators } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { NATURE_OF_FUNDS_ALLOCATED, ALLOCATION_CYCLE } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { letterPatternValidatorGeneral } from '../../../../../../shared/validators/letter-pattern-validator-accept-general';

@Component({
  selector: 'app-establishment-allocation-of-foundation-funds',
  templateUrl: './establishment-allocation-of-foundation-funds.component.html',
  styleUrls: ['./establishment-allocation-of-foundation-funds.component.scss']
})
export class EstablishmentAllocationOfFoundationFundsComponent extends MyEstablishmentComponentBase implements OnInit, OnChanges {
  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() formStatusCode: number = 1;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();

  membersOptions: Lookup[] = [];
  natureOfFundsAllocatedList: Lookup[] = NATURE_OF_FUNDS_ALLOCATED;
  allocationCycleList: Lookup[] = ALLOCATION_CYCLE;
  fundsEditIndex: number;
  foundingMemberList: any[] = [];
  internalFundsForm: FormGroup;
  totalOfMember: number = 0;
  showFromDeleteFM: boolean = false;
  get fb(): any {
    return this.internalFundsForm?.controls;
  }
  get funds(): FormArray {
    return this.form?.get("funds") as FormArray;
  }

  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.allocationOfFoundationFunds.";
    this.intiForm();

    this.StepperService.formData$.subscribe((data: any) => {
      this.checkMember(data);
    });
  }

  checkMember = (data: any): void => {
    this.membersOptions = [];
    this.foundingMemberList = data?.FoundingMembersForm?.members;
    data?.FoundingMembersForm?.members?.forEach(member => {
      if (!this.checkMemberOptions(member?.emiratesId)) {
        this.membersOptions.push(new Lookup(member?.emiratesId, member?.emiratesId, member?.emiratesId, member?.dateOfBirth));
      }
    });

    this.totalOfMember = data?.FoundingMembersForm?.members?.length ?? -1;
    if (this.totalOfMember >= 0)
      this.removeDeletedMemberFromFormData(data?.FoundingMembersForm?.members);
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit(): void {
    this.subscribeToNpoRequestData();
    this.resetFromValidation(this.form);

    this.pagination();
    this.funds.valueChanges.subscribe(_ => this.pagination());
  }

  intiForm = (): void => {
    this.internalFundsForm = this.FormBuilder.group({
      id: [''],
      foundingMemberEID: new FormControl("", [Validators.required]),
      NatureOfFundsAllocated: new FormControl("", [Validators.required]),
      DescriptionOfInKindFundsEn: new FormControl("", [Validators.required, letterPatternValidatorGeneral('en')]),
      DescriptionOfInKindFundsAr: new FormControl("", [Validators.required, letterPatternValidatorGeneral('ar')]),
      FundsAmount: new FormControl("", [Validators.required, Validators.min(1)]),
      AllocationCycle: new FormControl("", [Validators.required]),
      contactId: new FormControl(""),
      SatusReason: [''],
      canEdit: new FormControl('')
    });

    this.internalFundsForm.get("NatureOfFundsAllocated")?.valueChanges.subscribe(_ => {
      if (_) {
        if (_.ID == 2) {
          this.manageValidationDescriptionOfInKindFunds(true);
        } else {
          this.manageValidationDescriptionOfInKindFunds(false);
        }
      }
    })

  }

  updateMembers = (data: any): void => {
    this.membersOptions = [];
    this.foundingMemberList = data?.FounderMember;

    data?.FounderMember?.forEach(member => {
      if (!this.checkMemberOptions(member?.Contact?.EmirateId)) {
        this.membersOptions.push(new Lookup(member?.Contact?.EmirateId, member?.Contact?.EmirateId, member?.Contact?.EmirateId, member?.Contact?.EmirateId));
      }
    });
    this.totalOfMember = data?.FounderMember?.length;
    if (this.totalOfMember > 0)
      this.removeDeletedMember(data?.FounderMember);
  }

  private subscribeToNpoRequestData(): void {
    this.StepperService.requestData$.subscribe(_ => {
      if (_ && (_.isFullDetails == true)) {
        this.mapData(_.AllocationFundForm);
        this.updateMembers(_.FoundingMembersForm);
        // Object.keys(this.form.controls).forEach((control) => {
        //   if (
        //     (this.form.get(control)?.value === null ||
        //     this.form.get(control)?.value === "" ||
        //     !this.form.get(control)?.value) && this.isNotAllowedToEdit === false
        //   ) {
        //     this.form.get(control)?.enable();
        //   } else {
        //     this.form.get(control)?.disable();
        //   }
        // });
      }
      else if (_ && (_.isFullDetails == false)) {
        this.funds.clear();
        this.mapData(_?.AllocationFundForm);
        this.updateMembers(_.FoundingMembersForm);
      }
    });
  }

  manageMember(item: any, type: GridActionTypesEnum) {
    if (this.totalOfMember === 1 && Number(item.FundsAmount) < 5000000) {
      this.NotifyService.showError('notify.error', 'notify.minFundIs5Million');
      return;
    }

    if (type !== GridActionTypesEnum.EDIT) {
      this.funds.push(this.FormBuilder.group({
        id: [this.generateDistinctId()],
        foundingMemberEID: [item.foundingMemberEID],
        NatureOfFundsAllocated: [item.NatureOfFundsAllocated, Validators.required],
        DescriptionOfInKindFundsEn: [item.DescriptionOfInKindFundsEn],
        DescriptionOfInKindFundsAr: [item.DescriptionOfInKindFundsAr],
        FundsAmount: [item.FundsAmount, [Validators.required, Validators.min(1)]],
        AllocationCycle: [item.AllocationCycle, Validators.required],
        contactId: [item.contactId],
        SatusReason: [item?.SatusReason?.trim() || 'draft'],
        canEdit: true,
      }));
    } else {
      this.funds.at(this.fundsEditIndex).patchValue({
        id: item.id,
        foundingMemberEID: item.foundingMemberEID,
        NatureOfFundsAllocated: item.NatureOfFundsAllocated,
        DescriptionOfInKindFundsEn: item.DescriptionOfInKindFundsEn,
        DescriptionOfInKindFundsAr: item.DescriptionOfInKindFundsAr,
        FundsAmount: item.FundsAmount,
        AllocationCycle: item.AllocationCycle,
        contactId: item.contactId,
        SatusReason: item?.SatusReason?.trim() || 'draft',
        canEdit: true,
      });
    }

    this.internalFundsForm.reset();
    this.modalService.dismissAll();
  }


  async removeMember(data: any, idx: number) {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let fund = this.funds?.value[idx];
      if (fund)
        this.removeFromCRM(fund?.id, this.ALLOCATED_FUND_GRID_IN_CRM);

      this.addEID(data);
      this.funds.removeAt(idx);
      this.saveAsDraft(true);
    }
  }

  addEID = (data: any): void => {
    const eid = data?.value?.foundingMemberEID?.NameEnglish
    this.membersOptions.push(new Lookup(eid, eid, eid, ''));
  }

  handleEdit(data: any, idx: number) {
    this.removeSelectedDataFromList();

    this.fundsEditIndex = idx;

    const ctrMP$ = this.membersOptions.find(_ => _.ID == data?.value?.foundingMemberEID.ID);

    if (!ctrMP$) {
      this.membersOptions.push(data?.value?.foundingMemberEID);
    }

    this.internalFundsForm.patchValue({
      id: data?.value?.id,
      foundingMemberEID: data?.value?.foundingMemberEID,
      NatureOfFundsAllocated: data?.value?.NatureOfFundsAllocated,
      DescriptionOfInKindFundsEn: data?.value?.DescriptionOfInKindFundsEn,
      DescriptionOfInKindFundsAr: data?.value?.DescriptionOfInKindFundsAr,
      FundsAmount: data?.value?.FundsAmount,
      AllocationCycle: data?.value?.AllocationCycle,
      contactId: data?.value?.contactId,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.internalFundsForm, ['foundingMemberEID', 'NatureOfFundsAllocated', 'DescriptionOfInKindFundsEn', 'DescriptionOfInKindFundsAr', 'FundsAmount', 'AllocationCycle']);
  }

  private removeDeletedMember(members: any[]): void {
    this.funds?.controls?.forEach((element, index) => {
      const eid = element?.get('foundingMemberEID')?.value?.ID;
      const isMemberExist = members.find(member => member?.Contact?.EmirateId === eid);
      if (!isMemberExist) {
        this.funds.removeAt(index);
        this.removeFromCRM(element?.get('id')?.value, this.ALLOCATED_FUND_GRID_IN_CRM);
      }
    })

    this.funds.updateValueAndValidity();
  }

  private removeDeletedMemberFromFormData(members: any[]): void {
    if (members?.length > 0) {
      this.funds?.controls?.forEach((element, index) => {
        const eid = element?.get('foundingMemberEID')?.value?.ID;
        const isMemberExist = members.find(member => member?.emiratesId === eid);
        if (!isMemberExist) {
          this.funds.removeAt(index);
          this.removeFromCRM(element?.get('id')?.value, this.ALLOCATED_FUND_GRID_IN_CRM);
          this.showFromDeleteFM = true;
          this.saveAsDraft(true);
        }
      })
    } else {
      if (this.funds?.controls?.length > 0) {
        this.funds?.controls?.forEach((element, index) => {
          this.funds.removeAt(index);
          this.removeFromCRM(element?.get('id')?.value, this.ALLOCATED_FUND_GRID_IN_CRM);
          this.showFromDeleteFM = true;
          this.saveAsDraft(true);
        })
      }
    }

    this.funds?.updateValueAndValidity();
    this.pagination();
  }

  // isValidForm = (): boolean => this.gettotalFundsAmount() >= 5000000 && (this.funds.controls.length == this.totalOfMember);
  isValidForm = (): boolean => true;



  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("AllocationFundForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("AllocationFundForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      AllocationFunds: (this.funds.controls as Array<any>).map((control, index) => {
        const value = control.value;

        return {
          Id: value.id ?? this.EMPTY_GUID,
          Name: "",
          DescriptionAr: value.DescriptionOfInKindFundsAr ?? '',
          Description: value.DescriptionOfInKindFundsEn ?? '',
          ValueOfTheFund: value.FundsAmount ?? 0,
          NatureOfAllocatedFunds: value.NatureOfFundsAllocated?.ID ?? null,
          AllocationCycle: value.AllocationCycle?.ID ?? null,
          EmirateId: value.foundingMemberEID?.ID ?? null,
          Priority: index + 1,
          SatusReason: value?.SatusReason?.trim() || 'draft',
          Contact: {
            Id: value.contactId ?? this.EMPTY_GUID,
            EmirateId: value.foundingMemberEID?.ID ?? null
          }
        };
      })
    };
  };

  mapData = (data: any): void => {
    if (!data) return;

    data.AllocationFunds = data.AllocationFunds.sort((a, b) => a.Priority - b.Priority);

    data.AllocationFunds.forEach((item: any) => {
      let eid = item?.Contact?.EmirateId ?? '';
      this.funds.push(this.FormBuilder.group({
        id: [item.Id],
        DescriptionOfInKindFundsAr: [item.DescriptionAr ?? ''],
        DescriptionOfInKindFundsEn: [item.Description],
        FundsAmount: [item.ValueOfTheFund],
        foundingMemberEID: [new Lookup(eid, eid, eid, eid)],
        NatureOfFundsAllocated: [this.natureOfFundsAllocatedList.find(_ => _.ID === item.NatureOfAllocatedFunds)],
        AllocationCycle: [this.allocationCycleList.find(_ => _.ID === item.AllocationCycle)],
        contactId: item?.Contact?.Id ?? '',
        SatusReason: item?.SatusReason?.trim() || 'draft',
        canEdit: true
      }));
    });
  };

  onModelOpening = (event: any): void => {
    this.removeSelectedDataFromList();
    this.removeDublication();
  }

  removeSelectedDataFromList() {
    this.funds?.value?.forEach(element => {
      if (this?.membersOptions?.find(_ => _.NameEnglish === element?.foundingMemberEID?.NameEnglish))
        this.membersOptions = this.membersOptions.filter(_ => _.NameEnglish !== element?.foundingMemberEID?.NameEnglish);
    });
  }

  removeDublication() {
    this.membersOptions = this.membersOptions.filter((value, index, self) =>
      index === self.findIndex(_ => _.NameEnglish === value.NameEnglish)
    );
  }

  gettotalFundsAmount = (): number => {
    return sumBy(this.funds.controls, (control: any) => Number(control.value.FundsAmount))
  }
  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "allocationOfFoundationFunds", this.fb);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  checkMemberOptions = (id: string): boolean => this.membersOptions.findIndex(_ => _.ID == id) > -1;

  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.funds?.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize) ?? [];
    data$.forEach(_ => this.dataTable.push(_));
  }

  manageValidationDescriptionOfInKindFunds = (enable: boolean): void => {
    if (enable) {
      this.FormService.addValidators(this.internalFundsForm, ['DescriptionOfInKindFundsEn'], []);
      this.FormService.addValidators(this.internalFundsForm, ['DescriptionOfInKindFundsAr'], []);
    } else {
      this.FormService.clearFields(this.internalFundsForm, ['DescriptionOfInKindFundsEn', 'DescriptionOfInKindFundsAr'], true);
    }
  }

  checkMemberStatus = (): boolean => {
    const excludedStatusCodes = [1, 100000000];
    if (excludedStatusCodes.includes(this.formStatusCode)) {
      return false;
    }

    return this.foundingMemberList.some(member => {
      const status = member?.SatusReason?.trim() || 'draft';
      const normalizedStatus = status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
      return normalizedStatus === "rejected" || normalizedStatus === "refused" || normalizedStatus === "draft" || normalizedStatus === "";
    });
  };
}
