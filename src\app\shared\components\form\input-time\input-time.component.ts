import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-input-time',
  templateUrl: './input-time.component.html',
  styleUrls: ['./input-time.component.scss']
})
export class InputTimeComponent{
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Output() change: EventEmitter<any> = new EventEmitter<any>();

  constructor(private translate: TranslateService, private validationService: ValidationService) { 
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder='';
    });
  }

  _placeholder: string = '';

  public get Placeholder() {
      if(this._placeholder)
      {
        return this._placeholder;
      }
      this._placeholder = this.placeholder? this.placeholder: `${this.translate.instant('Please enter')}${this.translate.instant(this.label)}`;
      return this._placeholder;
    }

  onTimeChange(data: any) {
    this.change.emit(data);
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
