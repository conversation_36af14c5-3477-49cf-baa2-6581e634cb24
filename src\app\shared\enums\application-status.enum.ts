export enum RequestStatusCode {
  Draft = 1,
  Completed = 100,
  PendingPayment = 100000005,
  OnHold = 2,
  Returned = 100000002,
  Submitted = 100000000,
  InProgress = 100000001,
  Paid = 180790001,
  AdditionalDocumentsRequested = 180790003,
  InitialVerification = 180790002,
  Approved = 100000003,
  Rejected = 100000004
}

export interface EnumWithDescriptionAndDisplay {
  value: number;
  description: string;
  display?: string;
}

export const RequestStatusCodeLabels: Record<RequestStatusCode, EnumWithDescriptionAndDisplay> = {
  [RequestStatusCode.Draft]: {
    value: 1,
    description: "مسودة"
  },
  [RequestStatusCode.Completed]: {//this return approved and rejected applications
    value: 100,
    description: "منجز",
    display: "Completed"
  },
  [RequestStatusCode.PendingPayment]: {
    value: 100000005,
    description: "بإنتظار الدفع",
    display: "Pending Payment"
  },
  [RequestStatusCode.OnHold]: {
    value: 2,
    description: "معلق"
  },
  [RequestStatusCode.Returned]: {
    value: 100000002,
    description: "تم الإرجاع"
  },
  [RequestStatusCode.Submitted]: {
    value: 100000000,
    description: "تم التقديم"
  },
  [RequestStatusCode.InProgress]: {
    value: 100000001,
    description: "قيد الإجراء",
    display: "In Progress"
  },
  [RequestStatusCode.Paid]: {
    value: 180790001,
    description: "تم الدفع"
  },
  [RequestStatusCode.AdditionalDocumentsRequested]: {
    value: 180790003,
    description: "مطلوب مستندات إضافية"
  },
  [RequestStatusCode.InitialVerification]: {
    value: 180790002,
    description: "موافقة مبدئية"
  },
  [RequestStatusCode.Approved]: {
    value: 100000003,
    description: "تمت الموافقة"
  },
  [RequestStatusCode.Rejected]: {
    value: 100000004,
    description: "تم الرفض"
  }
};


export function getValueFromName(name: string): number | undefined {
  const enumKey = RequestStatusCode[name];
  if (enumKey !== undefined) {
    return RequestStatusCodeLabels[enumKey]?.value;
  }
  return undefined;
}
