

import { inject, Injectable } from '@angular/core';
import {
  ResolveFn,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Resolve,
} from '@angular/router';
import { MyEstablishmentService } from '../services/my-establishment.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class NMWPEstablishmentResolver implements Resolve<any> {
  constructor(private myEstablishmentService: MyEstablishmentService) { }

  resolve(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<any> {
    return this.myEstablishmentService.getEstablishmentNMWPBYID(
      route.paramMap.get('establishmentId')
    );
  }
}
