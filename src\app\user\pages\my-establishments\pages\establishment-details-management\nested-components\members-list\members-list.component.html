<div class="container">
  <div class="application-overview">
    <div class="application-overview__header">
      <div>
        <h1>{{'fundraisingPermitList.title'|translate}}</h1>
      </div>
    </div>

    <div class="application-overview__content">
      <!-- Application table -->
      <div class="table-listing-full-width">
        <div>
          <table class="table">
            <thead>
              <tr>
                <th scope="col">
                  {{'fundraisingPermitList.permitNumber' | translate}}
                </th>
                <th scope=" col" class="text-center">
                  {{'fundraisingPermitList.establishmentName' | translate}}
                </th>
                <th scope=" col" class="text-center">
                  {{'fundraisingPermitList.legalForm' | translate}}
                </th>
                <th scope="col" class="text-center">
                  {{'fundraisingPermitList.location' | translate}}
                </th>
                <th scope="col" class="text-center">
                  {{'fundraisingPermitList.startDate'| translate}}
                </th>
                <th scope="col" class="text-center">
                  {{'fundraisingPermitList.endDate'| translate}}
                </th>
                <th scope="col" class="text-center">
                  {{'fundraisingPermitList.applicationStatus'| translate}}
                </th>
                <th scope="col" class="text-center">
                  {{'fundraisingPermitList.permitStatus'| translate}}
                </th>
                <th scope="col">
                  {{'fundraisingPermitList.actions' | translate}}
                </th>
              </tr>
            </thead>
            <tbody>

              @for (previousPermit of reprsentedDataTable; track previousPermit.id; let i = $index) {
              <tr>
                <td class="mobile-only">
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.permitNumber' | translate}}:
                    </div>
                    <div>
                      <span class="table-listing-full-width__inner-title">
                        {{previousPermit?.RequestNumber}}
                      </span>
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.establishmentName' | translate}}:
                    </div>
                    <div>
                      <span class="table-listing-full-width__inner-title">
                        @if(LanguageService.IsArabic){
                        {{previousPermit?.AccountNameArabic }}
                        } @else {
                        {{previousPermit?.AccountNameEnglish }}
                        }
                      </span>
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.legalForm' | translate}}:
                    </div>
                    <div>
                      <span class="table-listing-full-width__inner-title">
                        @if(LanguageService.IsArabic){
                        {{previousPermit?.LegalFormArabic }}
                        } @else {
                        {{previousPermit?.LegalFormEnglish }}
                        }
                      </span>
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.location' | translate}}:
                    </div>
                    <div>
                      @if(LanguageService.IsArabic){
                      {{previousPermit?.EmirateNameArabic }}
                      } @else {
                      {{previousPermit?.EmirateNameEnglish }}
                      }
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.startDate' | translate}}:
                    </div>
                    <div>
                      {{previousPermit?.FundRaisingStartDate | date:'dd/MM/yyyy'}}

                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.endDate' | translate}}:
                    </div>
                    <div>
                      {{previousPermit?.FundRaisingEndDate | date:'dd/MM/yyyy'}}
                    </div>
                  </div>
                  <div class="table-listing-full-width__inner-title-container">
                    <div class="fw-bold text-primary">
                      {{'fundraisingPermitList.status'| translate}}:
                    </div>
                    <div class="table-listing-full-width__status">
                      <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                        <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="previousPermit.Status=='Rejected'" />
                        <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="previousPermit.Status=='Active'||previousPermit.Status=='Approved'" />
                      </svg>
                      <span> {{getStatusByNameSTG(previousPermit?.Status)}}</span>
                    </div>
                    <div class="table-listing-full-width__status">
                      <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                        <circle cx="4.75" cy="4" r="4" fill="#eb0505"
                          *ngIf="previousPermit?.PermitStatus=='100000001'" />
                        <circle cx="4.75" cy="4" r="4" fill="#4A9D5C"
                          *ngIf="previousPermit?.PermitStatus=='100000000'" />
                      </svg>
                      <span> {{getPermitStatus(previousPermit?.PermitStatus)}}</span>
                    </div>

                  </div>
                </td>

                <td class="desktop-only ">
                  <span class="table-listing-full-width__inner-title"> {{previousPermit?.RequestNumber}}</span>
                </td>
                <td class="desktop-only text-center">
                  @if(LanguageService.IsArabic){
                  {{previousPermit?.AccountNameArabic }}
                  } @else {
                  {{previousPermit?.AccountNameEnglish }}
                  }
                </td>
                <td class="desktop-only text-center">
                  @if(LanguageService.IsArabic){
                  {{previousPermit?.LegalFormArabic }}
                  } @else {
                  {{previousPermit?.LegalFormEnglish }}
                  }
                </td>
                <td class="desktop-only text-center">
                  @if(LanguageService.IsArabic){
                  {{previousPermit?.EmirateNameArabic }}
                  } @else {
                  {{previousPermit?.EmirateNameEnglish }}
                  }
                </td>
                <td class="desktop-only text-center">
                  {{previousPermit?.FundRaisingStartDate | date:'dd/MM/yyyy'}}
                </td>
                <td class="desktop-only text-center">
                  {{previousPermit?.FundRaisingEndDate | date:'dd/MM/yyyy'}}
                </td>
                <td class="desktop-only text-center table-listing-full-width__status">
                  <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                    <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="previousPermit.Status=='Active'||previousPermit.Status=='Approved'" />
                    <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="previousPermit.Status=='Rejected'" />
                  </svg>
                  <span> {{getStatusByNameSTG(previousPermit?.Status)}} </span>
                </td>
                <td class="desktop-only text-center table-listing-full-width__status">
                  <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                    <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="previousPermit?.PermitStatus=='100000001'" />
                    <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="previousPermit?.PermitStatus=='100000000'" />
                  </svg>
                  <span> {{getPermitStatus(previousPermit?.PermitStatus)}} </span>
                </td>
                <td class="text-center">
                  <button class="action-button dropdown-toggle" type="button" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path
                        d="M10.9375 10C10.9375 10.1854 10.8825 10.3667 10.7795 10.5208C10.6765 10.675 10.5301 10.7952 10.3588 10.8661C10.1875 10.9371 9.99896 10.9557 9.8171 10.9195C9.63525 10.8833 9.4682 10.794 9.33709 10.6629C9.20598 10.5318 9.11669 10.3648 9.08051 10.1829C9.04434 10.001 9.06291 9.81254 9.13386 9.64123C9.20482 9.46993 9.32498 9.32351 9.47915 9.2205C9.63332 9.11748 9.81458 9.0625 10 9.0625C10.2486 9.0625 10.4871 9.16127 10.6629 9.33709C10.8387 9.5129 10.9375 9.75136 10.9375 10ZM10 5.625C10.1854 5.625 10.3667 5.57002 10.5208 5.467C10.675 5.36399 10.7952 5.21757 10.8661 5.04627C10.9371 4.87496 10.9557 4.68646 10.9195 4.5046C10.8833 4.32275 10.794 4.1557 10.6629 4.02459C10.5318 3.89348 10.3648 3.80419 10.1829 3.76801C10.001 3.73184 9.81254 3.75041 9.64123 3.82136C9.46993 3.89232 9.32351 4.01248 9.2205 4.16665C9.11748 4.32082 9.0625 4.50208 9.0625 4.6875C9.0625 4.93614 9.16127 5.1746 9.33709 5.35041C9.5129 5.52623 9.75136 5.625 10 5.625ZM10 14.375C9.81458 14.375 9.63332 14.43 9.47915 14.533C9.32498 14.636 9.20482 14.7824 9.13386 14.9537C9.06291 15.125 9.04434 15.3135 9.08051 15.4954C9.11669 15.6773 9.20598 15.8443 9.33709 15.9754C9.4682 16.1065 9.63525 16.1958 9.8171 16.232C9.99896 16.2682 10.1875 16.2496 10.3588 16.1786C10.5301 16.1077 10.6765 15.9875 10.7795 15.8333C10.8825 15.6792 10.9375 15.4979 10.9375 15.3125C10.9375 15.0639 10.8387 14.8254 10.6629 14.6496C10.4871 14.4738 10.2486 14.375 10 14.375Z"
                        fill="#92722A" />
                    </svg>
                  </button>
                  <div class="action-popup dropdown-menu">
                    <ul>
                      @if(previousPermit.IsEligableForExtension){
                      <li class="dropdown-item" (click)="openPermit('extend',previousPermit?.Id)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M19.3211 6.74688C19.2937 6.68516 18.632 5.21719 17.1609 3.74609C15.2008 1.78594 12.725 0.75 9.99999 0.75C7.27499 0.75 4.79921 1.78594 2.83905 3.74609C1.36796 5.21719 0.703118 6.6875 0.678899 6.74688C0.643362 6.82681 0.625 6.91331 0.625 7.00078C0.625 7.08826 0.643362 7.17476 0.678899 7.25469C0.706243 7.31641 1.36796 8.78359 2.83905 10.2547C4.79921 12.2141 7.27499 13.25 9.99999 13.25C12.725 13.25 15.2008 12.2141 17.1609 10.2547C18.632 8.78359 19.2937 7.31641 19.3211 7.25469C19.3566 7.17476 19.375 7.08826 19.375 7.00078C19.375 6.91331 19.3566 6.82681 19.3211 6.74688ZM9.99999 12C7.5953 12 5.49452 11.1258 3.75546 9.40234C3.0419 8.69273 2.43483 7.88356 1.95312 7C2.4347 6.11636 3.04179 5.30717 3.75546 4.59766C5.49452 2.87422 7.5953 2 9.99999 2C12.4047 2 14.5055 2.87422 16.2445 4.59766C16.9595 5.307 17.5679 6.11619 18.0508 7C17.4875 8.05156 15.0336 12 9.99999 12ZM9.99999 3.25C9.25831 3.25 8.53329 3.46993 7.9166 3.88199C7.29992 4.29404 6.81927 4.87971 6.53544 5.56494C6.25162 6.25016 6.17735 7.00416 6.32205 7.73159C6.46674 8.45902 6.82389 9.1272 7.34834 9.65165C7.87279 10.1761 8.54097 10.5333 9.2684 10.6779C9.99583 10.8226 10.7498 10.7484 11.4351 10.4645C12.1203 10.1807 12.7059 9.70007 13.118 9.08339C13.5301 8.4667 13.75 7.74168 13.75 7C13.749 6.00576 13.3535 5.05253 12.6505 4.34949C11.9475 3.64645 10.9942 3.25103 9.99999 3.25ZM9.99999 9.5C9.50554 9.5 9.02219 9.35338 8.61107 9.07867C8.19994 8.80397 7.87951 8.41352 7.69029 7.95671C7.50107 7.49989 7.45157 6.99723 7.54803 6.51227C7.64449 6.02732 7.88259 5.58186 8.23222 5.23223C8.58186 4.8826 9.02731 4.6445 9.51227 4.54804C9.99722 4.45157 10.4999 4.50108 10.9567 4.6903C11.4135 4.87952 11.804 5.19995 12.0787 5.61107C12.3534 6.0222 12.5 6.50555 12.5 7C12.5 7.66304 12.2366 8.29893 11.7678 8.76777C11.2989 9.23661 10.663 9.5 9.99999 9.5Z"
                            fill="#92722A" />
                        </svg>
                        <span>
                          {{'fundraisingPermitList.extendFundraisingPermit' | translate}}
                        </span>
                      </li>
                      }

                      @if(previousPermit.IsEligableForAppeal){
                      <li class="dropdown-item" (click)="openPermit('appeal',previousPermit?.Id)">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                          <path
                            d="M19.3211 6.74688C19.2937 6.68516 18.632 5.21719 17.1609 3.74609C15.2008 1.78594 12.725 0.75 9.99999 0.75C7.27499 0.75 4.79921 1.78594 2.83905 3.74609C1.36796 5.21719 0.703118 6.6875 0.678899 6.74688C0.643362 6.82681 0.625 6.91331 0.625 7.00078C0.625 7.08826 0.643362 7.17476 0.678899 7.25469C0.706243 7.31641 1.36796 8.78359 2.83905 10.2547C4.79921 12.2141 7.27499 13.25 9.99999 13.25C12.725 13.25 15.2008 12.2141 17.1609 10.2547C18.632 8.78359 19.2937 7.31641 19.3211 7.25469C19.3566 7.17476 19.375 7.08826 19.375 7.00078C19.375 6.91331 19.3566 6.82681 19.3211 6.74688ZM9.99999 12C7.5953 12 5.49452 11.1258 3.75546 9.40234C3.0419 8.69273 2.43483 7.88356 1.95312 7C2.4347 6.11636 3.04179 5.30717 3.75546 4.59766C5.49452 2.87422 7.5953 2 9.99999 2C12.4047 2 14.5055 2.87422 16.2445 4.59766C16.9595 5.307 17.5679 6.11619 18.0508 7C17.4875 8.05156 15.0336 12 9.99999 12ZM9.99999 3.25C9.25831 3.25 8.53329 3.46993 7.9166 3.88199C7.29992 4.29404 6.81927 4.87971 6.53544 5.56494C6.25162 6.25016 6.17735 7.00416 6.32205 7.73159C6.46674 8.45902 6.82389 9.1272 7.34834 9.65165C7.87279 10.1761 8.54097 10.5333 9.2684 10.6779C9.99583 10.8226 10.7498 10.7484 11.4351 10.4645C12.1203 10.1807 12.7059 9.70007 13.118 9.08339C13.5301 8.4667 13.75 7.74168 13.75 7C13.749 6.00576 13.3535 5.05253 12.6505 4.34949C11.9475 3.64645 10.9942 3.25103 9.99999 3.25ZM9.99999 9.5C9.50554 9.5 9.02219 9.35338 8.61107 9.07867C8.19994 8.80397 7.87951 8.41352 7.69029 7.95671C7.50107 7.49989 7.45157 6.99723 7.54803 6.51227C7.64449 6.02732 7.88259 5.58186 8.23222 5.23223C8.58186 4.8826 9.02731 4.6445 9.51227 4.54804C9.99722 4.45157 10.4999 4.50108 10.9567 4.6903C11.4135 4.87952 11.804 5.19995 12.0787 5.61107C12.3534 6.0222 12.5 6.50555 12.5 7C12.5 7.66304 12.2366 8.29893 11.7678 8.76777C11.2989 9.23661 10.663 9.5 9.99999 9.5Z"
                            fill="#92722A" />
                        </svg>
                        <span>
                          {{'fundraisingPermitList.appealToPermitRejection' | translate}}
                        </span>
                      </li>
                      }
                    </ul>
                  </div>
                </td>
              </tr>
              }
            </tbody>
          </table>
        </div>

        <div class="application-overview__footer">
          <div class="aegov-pagination">
            <ngb-pagination [maxSize]="10" [ellipses]="true" [(page)]="page" [pageSize]="pageSize"
              [collectionSize]="previousPermitsArray.length" (pageChange)="pagination()" aria-label="Custom pagination">
              <ng-template ngbPaginationPrevious>
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                  <path
                    d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                </svg>
                <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
              </ng-template>
              <ng-template ngbPaginationNext>
                <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                  <path
                    d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                </svg>
              </ng-template>
            </ngb-pagination>
          </div>


        </div>
      </div>
      <!-- end Application table -->
    </div>

  </div>
</div>
