import { DOCUMENT } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Directive, ElementRef, HostListener, Inject, Input, OnInit, Renderer2 } from '@angular/core';
import { NgControl } from '@angular/forms';
import { Observable } from 'rxjs';

export interface GoogleTranslateObject {
  q: string[];
  target: string;
}
export interface Solution {
  title: string;
  description: string;
  detail: string;
}

@Directive({
  selector: '[appGoogleTranslate]'
})
export class GoogleTranslateDirective  {

  @Input('appGoogleTranslate') target: string = 'en';
  constructor(
    private control: NgControl,
    private el: ElementRef,
    private renderer: Renderer2,
    private http: HttpClient,
    @Inject(DOCUMENT) private document: Document) { }

  @HostListener('input', ['$event.target.value'])
  onInput(value: string) {
    if (value.length) {
      this.translate({
        q: [value],
        target: this.target
      }).subscribe(_ => {
        const child = this.document.createElement('div');
        child.innerHTML = `${_.data.translations[0].translatedText}`;
        this.renderer.appendChild(this.el.nativeElement.parentNode.parentNode, child);
      });
    }
  }
  @HostListener('blur')
  onInputBlur(): void {
    const input = this.el.nativeElement.value;
    if (!input.trim()) {
      this.el.nativeElement.value = '';
      this.control.control?.setValue(this.el.nativeElement.value);
    }
  }

  accept = (): void => {
    //this.control.control?.setValue(value.slice(0, maxLength));
  }

  translate = (obj: GoogleTranslateObject): Observable<any> => {
    const url = 'https://translation.googleapis.com/language/translate/v2?key=AIzaSyCIr2Tc3WMfUVoslY65Y6SLq67lmkQ1xPw';
    return this.http.post(url, obj);
  }

}
