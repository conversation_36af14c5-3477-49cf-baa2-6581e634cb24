import { Attachment } from '../../../models/attachment.model';
import { TranslateService } from '@ngx-translate/core';
import { Component, EventEmitter, input, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { AlertService } from '../../../services/alert.service';
import { ValidationService } from '../../../services/validation.service';
import { NotifyService } from '../../../services/notify.service';
import { StepperService } from '../../../services/stepper.service';
import { SocialAidService } from '../../../../e-services/social-aid/services/social-aid.service';


@Component({
  selector: 'app-file-upload-swp',
  templateUrl: './file-upload-swp.component.html',
  styleUrl: './file-upload-swp.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class FileUploadSWPComponent implements OnInit {
  @Input() isAdditional: boolean;
  @Input() isSummary: boolean = false;
  @Input() description: string;
  @Input() label: string;
  @Input() docTypeId: string;
  @Input() placeholder: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() fileData: any;
  @Input() columns: number = 6;
  @Input() isPhoto: boolean = false;
  @Output() fileUploaded: EventEmitter<Attachment> = new EventEmitter<Attachment>();
  @Output() fileDeleted: EventEmitter<Attachment> = new EventEmitter<Attachment>();
  @Output() fileAttachmentDelete: EventEmitter<string> = new EventEmitter<string>();

  file: File | null = null;

  isValidFormat: boolean = false;
  isValidSize: boolean = false;

  constructor(private alert: AlertService, protected stepper: StepperService, private notify: NotifyService, private validationService: ValidationService, private socialAidService: SocialAidService) { }


  ngOnInit(): void {
    if (this.fileData) {
      this.socialAidService.getRequestDocumentById(this.fileData?.Id).subscribe(res => {
        this.control?.clearValidators();
        this.control?.updateValueAndValidity();
        this.file = this.base64ToFile(res?.Data?.AttachmentBody, res?.Data?.FileName, res?.Data?.MimeType);
      });
    }
  }

  base64ToFile(base64: string, fileName: string, contentType: string): File {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new File([new Blob([byteArray], { type: contentType })], fileName, { type: contentType });
  }

  handleFileInput(f: any) {
    var files = f.target.files as FileList;
    this.file = files.item(0);

    this.isValidSize = this.checkValidSize();
    this.isValidFormat = this.checkFileType();

    if (!this.isValidSize) {
      this.notify.showError('notify.error', 'notify.invalidFileSize');
      this.file = null;
      this.control.setValue('');
    }
    if (!this.isValidFormat) {
      this.notify.showError('notify.error', 'notify.invalidFileFormat');
      this.file = null;
      this.control.setValue('');
    }

    if (this.file && this.isValidSize && this.isValidFormat) {
      this.fileUploaded.emit(new Attachment(this.label, this.docTypeId, this.file));
    }
  }
  async deleteFile() {
    const confirmed = await this.alert.confirmSubmit('');
    if (confirmed && this.file !== null) {
      this.fileDeleted.emit(new Attachment(this.label, this.docTypeId, this.file));
      this.file = null;
      this.control.addValidators(Validators.required);
      this.control.setValue('');
    }
  }
  downloadFile() {
    this.socialAidService.getRequestDocumentById(this.fileData?.Id).subscribe(res => {
      this.downloadFileDirect(res?.Data?.AttachmentBody, res?.Data?.FileName, res?.Data?.MimeType);
    });
  }
  private downloadFileDirect(base64Data: string, fileName: string, fileType: string) {
    const blob = this.base64ToBlob(base64Data, fileType);
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }

  private base64ToBlob(base64: string, type: string): Blob {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type });
  }


  async deleteAttachmentType() {
    this.fileAttachmentDelete.emit(this.docTypeId);
  }
  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  checkValidSize() {
    const file = this.file;
    if (file && file.size <= (2 * 1024 * 1024)) {
      return true;
    }
    return false;
  }

  checkFileType() {
    const file = this.file;
    if (file) {
      const fileType = file.name.split('.')[1]?.toLowerCase();
      if (!this.isPhoto && fileType && ['pdf', 'png', 'jpg', 'jpeg'].includes(fileType)) {
        return true;
      } else if (this.isPhoto && fileType && ['png', 'jpg', 'jpeg'].includes(fileType)) {
        return true;
      }
      else {
        return false;
      }
    }
    return false;
  }


  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  isMobileScreen(): boolean {
    // Check if the viewport matches the `lg` breakpoint (Bootstrap default is ≥ 992px)
    return window.matchMedia('(max-width: 1023px)').matches;
  }

}
