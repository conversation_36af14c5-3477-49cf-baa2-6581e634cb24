
.application-overview{
    background: #fff !important;
    padding: 12px 24px !important;
    border-radius: 16px !important;
  // border-radius: 24px;
  // border: 1px $mocdyellow;
  // background: white;
  // padding: 24px 16px;
  // min-height: 600px;
  // @media only screen and (min-width: 1024px) {
  //   padding: 35px 24px;
  // }
  &__header{
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 24px;
    @media (max-width: 1024px){
      margin-bottom: 12px !important;
    }
    @media (min-width: 1024px) {
      flex-direction: row;
      justify-content: space-between;
    }
    h2{
      // font-family: Inter;
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 26px;
      margin-bottom: 8px;
    }
    p{
      font-size: 16px;
      font-weight: 400;
      line-height: 22px; 
      margin-bottom: 0px;
      color: #676767;
      @media only screen and (min-width: 1024px) {
        font-size: 18px;
        line-height: 24px;
      }
    }
  }
  &__filters{
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 24px;
    @media (max-width: 1024px){
      gap: 12px;
      margin-bottom: 12px;
    }
    @media only screen and (min-width: 1024px) {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    &-tabs{
      display: flex;
      gap: 4px;  
      @media only screen and (min-width: 1024px) {
        // gap: 12px 16px;
        gap: 16px;  
      }
      .form-check{
        padding: 0px;
        border-radius: 8px;
        @media (max-width: 1024px){
          min-width: fit-content !important;
          position: relative;
          overflow: hidden;

          input {
            margin: 0;
            position: absolute;
            opacity: 0;
            pointer-events: none; // Ensure input interaction is restricted to the label
          }
      
          label {
            display: block; // Ensure full label is clickable
            padding: 15px 16px;
            text-align: center;
            user-select: none; // Prevent accidental text selection
          }
      
          &:focus-within {
            outline: none; // Remove default focus outline
            // box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1); // Optional focus styling
          }
        }

        &:has(.form-check-input:checked){
          background-color: $aegold-100;
          label{
            color: $aegold-800;
          }
        }
        &:hover{
          background-color: $aeblack-50;
          cursor: pointer;
        }
        input{
          margin: 0;
          position: absolute;
          opacity: 0;
        }
        label{
          padding: 15px 16px;
          font-size: 14px;
          font-weight: 500;
          line-height: 20px;
          color: $aeblack-800;
          white-space: nowrap;
          @media only screen and (min-width: 1024px) {
            font-size: 16px;
            line-height: 22px;
          }
          &:hover{
            // background-color: $aeblack-50;
            cursor: pointer;
          }
        }
      }
    }

    
    .mat-mdc-form-field-type-mat-select{
      padding: 0px;
      &.mat-focused{
        .mdc-text-field--filled{
          border: 2px solid $aegold-600;
          .mat-mdc-form-field-infix{
            .mdc-floating-label{
              mat-label{
                color: $aegold-600;
                svg{
                  fill: $aegold-600;
                }
              }
            }
            .icon-toggle{
              color: $aegold-600;
              // transform: rotate(180deg);
            }
            mat-select{
              .mat-mdc-select-value-text{
                color: $aegold-600;
              }
            }
          }
        }
      }
      .mat-mdc-text-field-wrapper{
        padding: 10px 32px 10px 12px;
        height: 52px;
        [ng-reflect-dir=rtl] &, [dir=rtl] &{
          padding: 10px 12px 10px 32px;
        }
        .mat-mdc-form-field-focus-overlay {
          background-color: white;
        }
        .mdc-line-ripple{
          display: none;
        }
      }
      .mat-mdc-form-field-subscript-wrapper{
        display: none;
        margin-bottom: 24px !important;
      }
      .mdc-text-field--filled {
        border-radius: 8px;
        border: 2px solid $aegold-400;
        background: white;
        .mat-mdc-form-field-infix {
          display: flex;
          align-items: center;
          width: auto;
          padding: 0px;
          min-height: initial;
          .icon-toggle{
            position: absolute;
            right: -20px;
            color: $aegold-400;
            font-size: 10px;
            margin-top: 2px;
            [ng-reflect-dir=rtl] &, [dir=rtl] &{
              right: initial;
              left: -20px;
              font-family: "Font Awesome 6 Free" !important;
            }
          }
          .mdc-floating-label{
            position: static;
            transform: none;
            width: auto;
            text-overflow: unset;
            max-width: initial;
            &.mdc-floating-label--float-above{
              transform: none;
            }
            mat-label{
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              color: $aegold-400; 
              @media only screen and (min-width: 1024px) {
                font-size: 14px;
                line-height: 20px;
              }
              svg{
                width: 20px;
                height: 20px;
                fill: $aegold-400;
                margin-right: 8px;
                [ng-reflect-dir=rtl] &, [dir=rtl] &{
                  margin-right: 0px;
                  margin-left: 8px;
                }
              }
            }
          }
          mat-select{
            width: auto;
            &[aria-expanded="true"]{
              +.icon-toggle{
                color: $aegold-600;
                transform: rotate(180deg);
              }
            }
            .mat-mdc-select-value{
              width: unset !important;
            }
            .mat-mdc-select-value-text{
              font-size: 12px;
              font-weight: 400;
              line-height: 16px;
              color: $aegold-400;

              max-width: 95px;
              text-overflow: ellipsis;
              overflow: hidden;
              /* width: 80px; */
              display: block;

              @media only screen and (min-width: 1024px) {
                font-size: 14px;
                line-height: 20px;
              }
            }
            .mat-mdc-select-arrow-wrapper{
              display: none;
            }
          }
        }

      }

    }
 

    .aegov-form-control {
      .form-control-input{
        box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.10);
        transition: all 0.3s;
        border: 2px solid $aegold-400;
        &:has(input:focus) {
          box-shadow: 0px 0px 0px 1px $aegold-500 inset;
        }
        
        span{
          a , button{
            border: none;
            background: none;
            padding-left: 16px;
            [ng-reflect-dir=rtl] &, [dir=rtl] &{
              padding-left: 0px;
              padding-right: 12px;
            }

            &:hover{
              svg{
                fill: $aegold-500;
              }
            }
            svg{
              fill: $aegold-600;
              width: 28px;
              height: 28px;
            }
          }
        }
  
        input{
          padding: 4px 16px 4px 12px;
          height: 48px;
          box-shadow: none;
          border: none;
          [ng-reflect-dir=rtl] &, [dir=rtl] &{
            padding: 4px 12px 4px 16px;
          }
          &:focus , &:focus-visible{
            border: none;
            box-shadow: none;
            
          }
        }

      }
    }

    
  }
  &__content{
    min-height: 368px;
  }
  &__footer{
    position: relative;
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin-top: 16px;
    gap: 16px;
    @media only screen and (min-width: 1024px) {
      align-items: center;
      margin-top: 45px;
    }
    &-total-count{
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      color: $aeblack-400;
      @media only screen and (min-width: 768px) {
        position: absolute;
        left: 0;
        // top: 50%;
        // transform: translateY(-50%);
        [ng-reflect-dir=rtl] &, [dir=rtl] &{
          left: initial;
          right: 0;
        }
      }

      @media (max-width: 1024px){
        row-gap: 10px !important;
      }
      
    }
    .aegov-pagination{
      display: flex;
      justify-content: center;
    }
    .mat-mdc-form-field-type-mat-select{  
      padding: 0px;
      .mat-mdc-text-field-wrapper{
        padding: 10px 32px 10px 12px;
        @media (max-width: 1024px){
          padding: 5px 24px 5px 24px !important;
        }
        [ng-reflect-dir=rtl] &, [dir=rtl] &{
          padding: 10px 12px 10px 32px;
        }
        .mat-mdc-form-field-focus-overlay {
          background-color: white;
        }
        .mdc-line-ripple{
          display: none;
        }
      }
      .mat-mdc-form-field-subscript-wrapper{
        display: none;
        margin-bottom: 24px !important;
      }
      .mdc-text-field--filled {
        border-radius: 8px;
        border: 1px solid $aegold-600;
        background: white;
        .mat-mdc-form-field-infix {
          display: flex;
          align-items: center;
          width: fit-content;
          padding: 0px !important;
          min-height: initial;
          .icon-toggle{
            position: absolute;
            right: -20px;
            color: $aegold-400;
            font-size: 10px;
            margin-top: 2px;
            [ng-reflect-dir=rtl] &, [dir=rtl] &{
              right: initial;
              left: -20px;
              font-family: "Font Awesome 6 Free" !important;
            }
          }
          .mdc-floating-label{
            position: static;
            transform: none;
            width: auto;
            text-overflow: unset;
            max-width: initial;
            &.mdc-floating-label--float-above{
              transform: none;
            }
          }
          mat-select{
            .mat-mdc-select-value-text{
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              color: $aegold-600;
            }
            .mat-mdc-select-placeholder{
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              color: $aegold-600;
            }
          }
        }
      }
    }
    .pagination{
      margin: 0;
    }
    .mobile-only{
      display: none !important;
      @media (max-width: 767px) {
        display: flex !important;
      }
    }
    mat-form-field{
      width: max-content;
    }
  }
  &__empty-state{
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 8px;
    min-height: 368px;
    h4{
      // font-family: Inter;
      font-size: 20px;
      font-style: normal;
      font-weight: 700;
      line-height: 26px;
      color: $aeblack-900;
      margin-bottom: 0px;
      @media only screen and (min-width: 1024px) {
        font-size: 26px;
        line-height: 32px;
      }
    }
    p{
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      color: #676767;
      margin-bottom: 8px;
      @media only screen and (min-width: 1024px) {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 16px;
      }
    }
  }
}
.mat-mdc-select-panel{
  min-width: fit-content;
}

.w-sm-100 {
  @media (max-width:1024px){
    width: 100% !important;
  }
}

.justify-content-sm-between {
  @media (max-width:1024px){
    justify-content: space-between !important;
  }
}