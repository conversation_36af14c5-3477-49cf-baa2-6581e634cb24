import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { InputType } from '../../../enums/input-type.enum';
import { DataService } from '../../../services/data.service';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-input-with-action',
  templateUrl: './input-with-action.component.html',
  styleUrl: './input-with-action.component.scss',
  host: {
    '[class]': "'col-md-' + columns",
  },
})
export class InputWithActionComponent {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() hint: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() type: InputType = InputType.Default;
  @Input() columns: number = 6;
  @Input() action: boolean = false;
  @Output() actionCompleted: EventEmitter<any> = new EventEmitter();

  constructor(private data: DataService, private validationService: ValidationService) {}

  public get InputType() {
    return InputType;
  }

  performAction() {
    if (this.control.value) {
      if(this.type == InputType.EmiratesId  && this.control.value.length == 15)
        {
          this.data.getIcpData(this.control.value).subscribe((data) => {
            this.actionCompleted.emit(data);
          });
        }
      // else if(this.type == InputType.PodEmiratesId  && this.control.value.length == 15)
      //   {
      //     this.data.getPodCard(this.control.value, '').subscribe((data) => {
      //       this.actionCompleted.emit(data);
      //     });
      //   }
      else if(this.type == InputType.PodCardNo  && this.control.value.length == 12)
        {
          const PodCardNo=this.control.value.substring(0, 4) + '-' + this.control.value.substring(4);
          this.data.getPodCard('', PodCardNo).subscribe((data) => {
            this.actionCompleted.emit(data);
          });
        }
    }

  }


  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
