.col-wrap {
    word-wrap: break-word;
    // max-width: 160px;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    padding: 10px;
    // white-space: nowrap;
}

/* Works on Firefox */
* {
    scrollbar-width: none;
    // scrollbar-width: thin;
    // scrollbar-color: #f2eccf #ffffff;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: #ffffff;
}

*::-webkit-scrollbar-thumb {
    background-color: #f2eccf;
    border-radius: 20px;
    border: 3px solid #ffffff;
}


.application-overview {
    background: #fff !important;
    padding: 12px 24px !important;
    border-radius: 16px !important;
}

.total-count {
    @media (max-width:1024px) {
        display: none !important;
    }
}

.maxWidth {
    max-width: 300px;

    @media (max-width: 1024px) {
        max-width: unset !important;
    }
}

.application-overview__content .action-popup {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1215686275);
    border-radius: 8px;
    border: none !important;
    padding: 0 !important;
    width: auto !important;
    max-width: 90%;
    font-size: 14px;
    text-align: start !important;
}

// Edit Reasons Modal Styles - Following established design patterns
.edit-reasons-modal {
    .modal-dialog {
        display: flex;
        align-items: center;
        min-height: 90%;
        margin-top: 0;
        max-width: 600px;

        @media (max-width: 1024px) {
            max-width: 100% !important;
        }

        @media (max-width: 576px) {
            margin: 0.5rem;
        }
    }

    .modal-content {
        border: none;
        border-radius: 12px;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
        padding: 24px;

        @media (min-width: 768px) {
            padding: 32px 24px;
        }

        @media (min-width: 1024px) {
            padding: 40px;
        }

        .modal-header,
        .modal-body,
        .modal-footer {
            padding: 0;
            border: none;
        }
    }

    .modal-header {
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;

        @media (max-width: 1024px) {
            margin-bottom: 16px;
        }

        .modal-title {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            line-height: 32px;
            color: #212529;
            letter-spacing: normal;

            @media (max-width: 1024px) {
                font-size: 20px;
                line-height: 26px;
            }
        }

        .btn-close {
            margin: 0;
            margin-left: auto;

            [ng-reflect-dir="rtl"] &,
            [dir="rtl"] & {
                margin-right: auto;
                margin-left: 0;
            }
        }
    }

    .modal-body {
        margin-bottom: 24px;

        .row {
            row-gap: 10px;
        }

        .edit-reasons-form {
            // Use existing project form field styles - no custom overrides needed
        }
    }

    .modal-footer {
        display: flex;
        flex-direction: row-reverse;
        gap: 10px;
        margin-top: 10px;

        @media (max-width: 1023px) {
            justify-content: space-between;
        }

        @media (max-width: 576px) {
            flex-direction: column-reverse;
            gap: 8px;
        }

        button {
            margin-left: 0;
            margin-right: 0;

            @media (max-width: 576px) {
                width: 100%;
            }
        }
    }
}