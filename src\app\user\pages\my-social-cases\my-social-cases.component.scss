.col-wrap {
    word-wrap: break-word;
    // max-width: 160px;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    padding: 10px;
    // white-space: nowrap;
}

/* Works on Firefox */
* {
    scrollbar-width: none;
    // scrollbar-width: thin;
    // scrollbar-color: #f2eccf #ffffff;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: #ffffff;
}

*::-webkit-scrollbar-thumb {
    background-color: #f2eccf;
    border-radius: 20px;
    border: 3px solid #ffffff;
}


.application-overview {
    background: #fff !important;
    padding: 12px 24px !important;
    border-radius: 16px !important;
}

.total-count {
    @media (max-width:1024px) {
        display: none !important;
    }
}

.maxWidth {
    max-width: 300px;

    @media (max-width: 1024px) {
        max-width: unset !important;
    }
}

.application-overview__content .action-popup {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.1215686275);
    border-radius: 8px;
    border: none !important;
    padding: 0 !important;
    width: auto !important;
    max-width: 90%;
    font-size: 14px;
    text-align: start !important;
}

// Edit Reasons Modal Styles
.edit-reasons-modal {
    .modal-dialog {
        max-width: 500px;
        margin: 1rem auto;

        @media (max-width: 576px) {
            max-width: 95%;
            margin: 0.5rem auto;
        }
    }

    .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid #e9ecef;
        border-radius: 12px 12px 0 0;

        .modal-title {
            font-weight: 600;
            font-size: 18px;
            color: #212529;
        }

        .btn-close {
            margin: 0;
        }
    }

    .modal-body {
        padding: 24px;

        p {
            font-size: 16px;
            color: #6c757d;
            margin-bottom: 16px;
        }

        .edit-reasons-form {
            // Use existing project form field styles - no custom overrides needed
        }
    }

    .modal-footer {
        padding: 20px 24px;
        border-top: 1px solid #e9ecef;
        border-radius: 0 0 12px 12px;
        gap: 12px;

        // Use existing project button styles - replicated to avoid @extend issues
        .btn {
            &.btn-secondary {
                border: 2px solid #92722A !important;
                border-radius: 8px !important;
                color: #92722A !important;
                padding: 12px 16px !important;
                height: 46px !important;
                background: transparent !important;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    background-color: #F2ECCF !important;
                    border: 5px solid #92722A !important;
                    padding: 7.5px 13px !important;
                }
            }

            &.btn-primary {
                border: 2px solid #92722A !important;
                border-radius: 8px !important;
                color: white !important;
                background-color: #92722A !important;
                padding: 12px 16px !important;
                height: 46px !important;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover:not(.disabled) {
                    border-color: #7A5F22 !important;
                    background-color: #7A5F22 !important;
                    color: #F2ECCF !important;
                    box-shadow: 0px 0px 0px 6px #F2ECCF;
                }

                &:disabled {
                    background-color: #F2ECCF !important;
                    border-color: #F2ECCF !important;
                    color: #C4A876 !important;
                }
            }
        }

        @media (max-width: 576px) {
            flex-direction: column-reverse;
            gap: 8px;

            .btn {
                width: 100%;
                min-width: unset;
            }
        }
    }
}