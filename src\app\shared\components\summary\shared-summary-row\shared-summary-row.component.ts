import { Attachment } from './../../../models/attachment.model';
import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Lookup } from '../../../models/lookup.model';
import { LanguageService } from '../../../services/language.service';
import { distinctUntilChanged } from 'rxjs';
import { StepperService } from '../../../services/stepper.service';


@Component({
  selector: 'div[summary-row]',
  templateUrl: './shared-summary-row.component.html',
  styleUrl: './shared-summary-row.component.scss',

})
export class SharedSummaryRowComponent implements OnInit {
  @Input() label: string;
  @Input() value: any;
  @Input() isPlain: boolean;
  @Input() isLookup: boolean;
  @Input() isFile: boolean;
  @Input() isDate: boolean;
  @Input() isDateTime: boolean;

  // valueLookup: string;
  file: File;
  fileUrl: string;
  fileName: string;
  lookup: Lookup;

  constructor(
    protected translate: TranslateService,
    protected lang: LanguageService,
    private stepper: StepperService
  ) {

  }
  ngOnInit(): void {
    this.stepper.formData$
      .pipe(
        distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b))
      )
      .subscribe((data: any) => {
        if (data) {
          if (this.isLookup) {
            this.lookup = this.value as Lookup;
          }
          else if (this.isFile) {
            if (data?.attachments && data.attachments.length > 0) {
              this.file = data.attachments.filter(x => x.name == this.label)[0].file;
              this.fileName = `${this.file.name}`
              this.createDownloadLink(this.file)
            }
          }
          else if (this.isDate) {
            var value = this.value;
            if (value)
              this.value = value;
          }
        }
      });
  }

  createDownloadLink(file: File) {
    const blob = new Blob([file], { type: file.type });
    this.fileUrl = URL.createObjectURL(blob);
  }

}
