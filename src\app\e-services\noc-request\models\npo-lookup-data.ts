import { Lookup } from '../../../shared/models/lookup.model';
import { ConditionLookup } from './condition-lookup';

export const FREQUENCY_OF_BOARD_MEETINGS_LIST: Lookup[] = [
  new Lookup(1, 'Once a month', 'مرة كل شهر'),
  new Lookup(2, 'Once every two months', 'مرة كل شهرين'),
  new Lookup(3, 'Once every three months', 'مرة كل ثلاثة أشهر'),
];

export const BOARD_ELECTION_CYCLE_LIST: Lookup[] = [
  new Lookup(1, 'One Year', 'سنة'),
  new Lookup(2, 'Two Years', 'سنتين'),
  new Lookup(3, 'Three Years', 'ثلاث سنوات'),
  new Lookup(4, 'Four Years', 'أربع سنوات'),
];

export const RENOMINATION_LIST: Lookup[] = [
  new Lookup(1, 'Allowed', 'يجوز'),
  new Lookup(0, 'Not Allowed', 'لا يجوز'),
];

export const NUMBER_OF_PERMISSIBLE_MEMBERS_LIST: Lookup[] = [
  new Lookup(1, 'One Term', 'فترة'),
  new Lookup(2, 'Two Terms', 'فترتين'),
  new Lookup(3, 'Three Terms', 'ثلاث فترات'),
  new Lookup(4, 'Unlimited', 'غير محدود'),
];

export const ELECTION_METHOD_LIST: Lookup[] = [
  {
    ID: 1,
    NameArabic: 'الانتخابات الفردية',
    NameEnglish: 'Individual Election',
    ParentID: '',
  },
  {
    ID: 2,
    NameArabic: 'انتخابات القائمة',
    NameEnglish: 'Slate Election',
    ParentID: '',
  },
  {
    ID: 3,
    NameArabic: 'الانتخابات الخاصة',
    NameEnglish: 'Special Election',
    ParentID: '',
  },
  {
    ID: 4,
    NameArabic: 'انتخاب على مرحلتين (رئيس مجلس الإدارة وعضو مجلس الإدارة)',
    NameEnglish: 'Two-Stage Election (Board Chairman and Board Member)',
    ParentID: '',
  },
  {
    ID: 5,
    NameArabic: 'انتخابات مع حصة نسائية',
    NameEnglish: 'Elections with Female Quota',
    ParentID: '',
  },
];

export const NATIONALITY_TYPES = [
  new Lookup(1, 'UAE Local', 'مواطن إماراتي'),
  new Lookup(2, 'Non-Local', 'غير مواطن'),
  new Lookup(3, 'Both', 'كلاهما'),
];

export const AcademicQualification: Lookup[] = [
  {
    ID: 1,
    NameArabic: 'مدرسة ابتدائية',
    NameEnglish: 'Elementary School',
    ParentID: '',
  },
  {
    ID: 2,
    NameArabic: 'المدرسة المتوسطة/الإعدادية',
    NameEnglish: 'Middle School/Junior High',
    ParentID: '',
  },
  {
    ID: 3,
    NameArabic: 'شهادة الثانوية العامة',
    NameEnglish: 'High School Diploma',
    ParentID: '',
  },
  {
    ID: 4,
    NameArabic: 'الدبلومات والشهادات',
    NameEnglish: 'Diplomas and Certificates',
    ParentID: '',
  },
  {
    ID: 5,
    NameArabic: 'درجة الزمالة',
    NameEnglish: 'Associate Degree',
    ParentID: '',
  },
  {
    ID: 6,
    NameArabic: 'درجة البكالوريوس',
    NameEnglish: 'Bachelor’s Degree',
    ParentID: '',
  },
  {
    ID: 7,
    NameArabic: 'درجة الماجستير',
    NameEnglish: 'Master’s Degree',
    ParentID: '',
  },
  { ID: 8, NameArabic: 'دكتوراه', NameEnglish: 'Doctorate', ParentID: '' },
  {
    ID: 9,
    NameArabic: 'الدرجات المهنية',
    NameEnglish: 'Professional Degrees',
    ParentID: '',
  },
];

export const ASSOCIATION_CLASSIFICATION_TYPES = [
  new Lookup(1, 'Charitable', 'خيري'),
  new Lookup(2, 'Social', 'اجتماعي'),
  new Lookup(3, 'Cultural', 'ثقافي'),
  new Lookup(4, 'Scientific', 'علمي'),
  new Lookup(5, 'Educational', 'تعليمي'),
  new Lookup(6, 'Professional', 'مهني'),
  new Lookup(7, 'Creative', 'إبداعي'),
  new Lookup(8, 'Artistic', 'فني'),
  new Lookup(9, 'Recreational', 'ترفيهي'),
  new Lookup(10, 'Environmental', 'بيئي'),
  new Lookup(11, 'Humanitarian services', 'خدمات إنسانية'),
];

export const DOCUMENT_TYPES = [
  {
    AllowedExtensions: 'png.jpg',
    Requirement: '',
    AllowedSize: 0,
    ID: '4a67f28e-5fe7-ee11-b10e-005056010908',
    NameEnglish: 'NPO Logo',
    NameArabic: 'صورة شعار المنظمة',
    descriptionEnglish:
      'This is the official logo of the non-profit organization, representing our mission and values in the community.',
    descriptionArabic:
      'هذا هو الشعار الرسمي للمنظمة غير الربحية، والذي يمثل رسالتنا وقيمنا في المجتمع.',
  },
];

export const JOURNEY_STEPS = [
    {
        "ID": "27f09d56-4e62-f011-b10f-005056010b9f",
        "nameEn": "RD-Application Filling",
        "nameAr": "تقديم الطلب",
        "IsCompleted": "No"
    },
    {
        "ID": "28f09d56-4e62-f011-b10f-005056010b9f",
        "nameEn": "RD-Ministry First Review",
        "nameAr": "مراجعة الوزارة",
        "IsCompleted": "No"
    },
    {
        "ID": "29f09d56-4e62-f011-b10f-005056010b9f",
        "nameEn": "RD-Ministry Approval",
        "nameAr": "موافقة الوزارة",
        "IsCompleted": "No"
    },
    {
        "ID": "2df09d56-4e62-f011-b10f-005056010b9f",
        "nameEn": "RD-Application Completed",
        "nameAr": "اكتمال الطلب",
        "IsCompleted": "No"
    }
];

export const FREQUENCY_OF_MEETING: Lookup[] = [
  {
    ID: 1,
    NameArabic: 'مرة في الشهر',
    NameEnglish: 'Once a month	',
    ParentID: '',
  },
  {
    ID: 2,
    NameArabic: 'مرة كل شهرين',
    NameEnglish: 'Once every 2 months',
    ParentID: '',
  },
  {
    ID: 3,
    NameArabic: 'مرة كل 3 أشهر',
    NameEnglish: 'Once every 3 months',
    ParentID: '',
  },
];

export const FREQUENCY_OF_MEETING_APPOINTMENT: Lookup[] = [
  { ID: 1, NameArabic: 'سنوي', NameEnglish: 'Every Year', ParentID: '' },
  {
    ID: 2,
    NameArabic: 'كل سنتين',
    NameEnglish: 'Every Two Years',
    ParentID: '',
  },
  {
    ID: 3,
    NameArabic: 'كل ثلاث سنوات',
    NameEnglish: 'Every Three Years',
    ParentID: '',
  },
  {
    ID: 4,
    NameArabic: 'كل أربع سنوات',
    NameEnglish: 'Every Four Years',
    ParentID: '',
  },
];

export const STATUS_LIST = [
  { Status: 'In Progress', Code: 100000001, StatusAR: 'قيد الإجراء' },
  { Status: 'Returned', Code: 100000002, StatusAR: 'تم الإرجاع' },
  { Status: 'Draft', Code: 1, StatusAR: 'مسودة' },
  { Status: 'Submitted', Code: 100000000, StatusAR: 'مقدم' },
  { Status: 'Approved', Code: 100000003, StatusAR: 'موافقة' },
  { Status: 'Rejected', Code: 100000004, StatusAR: 'مرفوض' },
];

export const SERVICES_PROVID_FUND: any[] = [
  {
    ID: 1,
    NameEnglish:
      'The Fund provides cash treatment assistance to the member or to one of his family members as decided by the Board of Directors and not to exceed an amount of (...) dirhams as a maximum per year and it is granted in the event of an approved medical report.',
    NameArabic:
      'يمنح الصندوق مساعدة علاج نقدية للعضو أو لأحد أفراد أسرته حسب ما يقرره مجلس الإدارة وبما لا يزيد عن مبلغ (...) درهم كحد أقصى سنوياً وتمنح في حالة وجود تقرير طبي معتمد.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 2,
    NameEnglish:
      'The Fund provides financial assistance with a maximum amount of (...) dirhams in the event of the member’s total disability based on an approved medical report stating his inability to continue working.',
    NameArabic:
      'يمنح الصندوق مساعدة مالية بحد أقصى مبلغ وقدره (...) درهم وذلك في حالة العجز الكلي للعضو بناء على تقرير طبي معتمد يوضح فيه عدم استطاعته الاستمرارية في العمل.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 3,
    NameEnglish:
      'The Fund provides financial assistance with a maximum amount of (...) dirhams in the event of partial disability of the member and based on an approved medical report.',
    NameArabic:
      'يمنح الصندوق مساعدة مالية بحد أقصى مبلغ وقدره (...) درهم وذلك في حالة العجز الجزئي للعضو وبناء على تقرير طبي معتمد.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 4,
    NameEnglish:
      'The Fund provides financial assistance with a maximum amount of (...) dirhams in the event of the death of the member to be paid to those the member was supporting at the time of his death.',
    NameArabic:
      'يقدم الصندوق إعانة مالية بحد أقصى مبلغ وقدره (...) درهم وذلك في حالة وفاة العضو على أن تصرف إلى من كان يعولهم العضو عند وفاته.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 5,
    NameEnglish:
      'The Fund shall grant the member upon withdrawal from the Fund’s membership or the termination of his practical service with the entity to which the Fund is affiliated an amount of (...) dirhams as a maximum for each year he spent in the Fund’s membership from the date of membership and up to a maximum of ten years provided that one year has passed since his membership taking into account the settlement of any obligations on the member in favour of the Fund.',
    NameArabic:
      'يمنح الصندوق للعضو عند الانسحاب من عضوية الصندوق أو انتهاء خدمته العملية لدى الجهة التابع لها الصندوق مبلغ (...) درهم كحد أقصى عن كل سنة قضاها في عضوية الصندوق اعتباراً من تاريخ العضوية وبحد أقصى عشر سنوات شريطة مرور سنة واحدة على عضويته مع مراعاة تسوية أية التزامات على العضو لصالح الصندوق.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 6,
    NameEnglish:
      'The Fund grants a good loan to its members with a maximum amount of (...) dirhams once every two years and repayment shall be in monthly installments not exceeding (12) installments.',
    NameArabic:
      'يمنح الصندوق قرضاً حسناً لأعضائه بحد أقصى مبلغ وقدره (...) درهم مرة واحدة كل سنتين ويكون السداد بأقساط شهرية لا تتجاوز (12) قسط.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 7,
    NameEnglish:
      'The Fund grants a cash wedding gift to the member once during his membership period with a maximum amount of (...) dirhams provided that the date of the first marriage contract is later than the date of his joining the Fund.',
    NameArabic:
      'يمنح الصندوق هدية زواج نقدية للعضو مرة واحدة خلال مدة عضويته بحد أقصى مبلغ وقدره (...) درهم بشرط أن يكون تاريخ عقد الزواج الأول لاحقاً لتاريخ انضمامه للصندوق.',
    ShowAmount: 'TRUE',
  },
  {
    ID: 8,
    NameEnglish:
      'The Fund provides assistance to members to meet financial circumstances according to what the Board of Directors decides and after studying each case independently.',
    NameArabic:
      'يمنح الصندوق مساعدات للأعضاء لمواجهة الظروف المادية وذلك حسب ما يقرره مجلس الإدارة وبعد دراسة كل حالة بصورة مستقلة.',
    ShowAmount: 'FALSE',
  },
];

export const NATURE_OF_FUNDS_ALLOCATED = [
  new Lookup(1, 'Cash', 'نقدية'),
  new Lookup(2, 'In-kind', 'عينية'),
];

export const ALLOCATION_CYCLE = [
  new Lookup(1, 'One time', 'مرة واحدة'),
  new Lookup(2, 'Annually', 'بشكل سنوي'),
];

export const ENTITY_SECTOR_TYPES = [
  new Lookup(1, 'Government Sector/Federal', 'القطاع الحكومي/اتحادي '),
  new Lookup(2, 'Government Sector/Local', ' القطاع الحكومي/محلي'),
  new Lookup(3, 'Private Sector', 'القطاع الخاص '),
  new Lookup(4, 'NPO Sector', ' قطاع النفع العام '),
];

export const PASSPORT_TYPES = [
  new Lookup(1,'Diplomatic','دبلوماسي'),
  new Lookup(2,'Regular','عادي'),
  new Lookup(3,'Temporary','مؤقت'),
  new Lookup(4,'Other','أخرى'),
]

export const DONOR_TYPES = [
  new Lookup(1,'Individual','فرد'),
  new Lookup(2,'Government Sector','قطاع حكومي'),
  new Lookup(3,'Private Sector','قطاع خاص'),
  new Lookup(4,'NPO Sector','قطاع مؤسسات النفع العام'),
]

export const ENTITY_TYPES = [
  new Lookup(1,'Local','محلية'),
  new Lookup(2,'Federal','اتحادية'),
]

export const DONATION_TYPES = [
  new Lookup(1,'Cah Donations','تبرعات نقدية'),
  new Lookup(2,'In-Kind Donations','تبرعات عينية'),
]