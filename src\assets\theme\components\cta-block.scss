
.cta-block{
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
  background: $aegold-50;
  border-radius: 8px;
  @media only screen and (min-width: 1024px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 32px;
  }
  &__title{
    font-size: 20px !important;
    font-weight: 700 !important;
    line-height: 26px !important;
    margin-top: 0px !important;
    color: $aeblack-800;
    @media only screen and (min-width: 1024px) {
      font-size: 24px !important;
      line-height: 30px !important;
    }
  }
  &__desc{
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    margin-top: 8px !important;
    @media only screen and (min-width: 1024px) {
      font-size: 16px;
      line-height: 24px;
    }
  }
  &__action{
    font-size: 16px !important;
    font-weight: 500;
    line-height: 24px !important;
    margin: 0px;
    @media only screen and (min-width: 1024px) {
      font-size: 18px !important;
      line-height: 24px !important;
    }
  }
}