<mat-horizontal-stepper [linear]="true" #journeyStepper>
  @for (step of steps; track $index) {
  <mat-step #stepRef
            [label]="lang.IsArabic?step.nameAr:step.nameEn"
            [completed]="step.status==2"
            [state]="step.status==2?'done':'number'">
  </mat-step>
  }

  <ng-template matStepperIcon="edit" let-index="index" let-active="active">
    <mat-icon *ngIf="index === (stepper?.steps?.length??0) - 1; else default">done</mat-icon>
    <ng-template #default>{{index + 1}}</ng-template>
  </ng-template>
</mat-horizontal-stepper>
