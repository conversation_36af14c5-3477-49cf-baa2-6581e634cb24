<div class="container py-5">
    <div class="summary_review overflow-hidden row">
        <div class="col-lg-4 col-xl-3 col-sm-12">
            <app-tree-menu [treeData]="treeMenu" [iconTemplates]="{
                establishmentDetails: establishmentDetailsIcon,
                manageEmployees: manageEmployeesIcon,
                npoAmendment: npoAmendmentIcon,
                licenseManagement: licenseManagementIcon,
                generalAssemblyMeeting: generalAssemblyMeetingIcon,
                fundraising: fundraisingIcon,
                events: eventsIcon,
                investment: investmentIcon,
                generalRequests: generalRequestsIcon,
                voluntaryDissolutionAndLiquidation: voluntaryDissolutionAndLiquidationIcon,
                decisionsAndRegulations: decisionsAndRegulationsIcon,
                joiningAndAffiliatingAssociations: joiningAndAffiliatingAssociationsIcon,
                organizingEventsAndActivities: organizingEventsAndActivitiesIcon,
                participateInActivitiesAndEventsInsideAndOutsideUAE: participateInActivitiesAndEventsInsideAndOutsideUAEIcon,
                manageBranches: manageBranchesIcon,
                nocRequest: nocRequestIcon,
                banks: banksIcon,
                membersList: membersListIcon,
              }"></app-tree-menu>
        </div>
        <div class="col-lg-8 col-xl-9 col-sm-12">
            @if(viewMode === 'Details'){
            <mat-accordion>
                <mat-expansion-panel>
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'BasicInformation') |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>
                    @if(formData?.BasicInformationForm?.npoform
                    ==LEGAL_FORM_TYPES.AssociationByDecree
                    ||formData?.BasicInformationForm?.npoform
                    ==LEGAL_FORM_TYPES.NationalSocietyByDecree)
                    {
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'npoLegalForm') |
                            translate}}</h4>
                    </div>
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'localDecreeLawNumber'" [isPlain]="true"
                            [value]="formData?.BasicInformationForm?.LocalDecreeLawNumber"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'issuanceDate'"
                            [isDate]="true" [value]="formData?.BasicInformationForm?.IssuanceDate"></div>

                    </div>
                    }
                    <div class="not_card">
                        <h4 class="title_wrapper">
                            {{(messageTranslationPrefix+'npoName') | translate}}</h4>
                    </div>

                    <div class="figma-review-card-container">
                        <div *ngIf="requestNumber" lang="en" summary-row class="figma-review-card"
                            [label]="'services.npoLicenseDeclaration.appReferenceNumber'" [isPlain]="true"
                            [value]="requestNumber"></div>
                        <div lang="en" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NameEn'" [isPlain]="true" [value]="formData?.BasicInformationForm.approvedNameEn??formData?.BasicInformationForm.name">
                        </div>
                        <div lang="ar" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NameAr'" [isPlain]="true" [value]="formData?.BasicInformationForm.approvedNameAr??formData?.BasicInformationForm.namear">
                        </div>
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'npoContactDetails')
                            | translate}}</h4>
                    </div>
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Emirate'"
                            [isPlain]="true" [value]="getEmirate(formData?.BasicInformationForm?.emirate)"></div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'GeographicLocation'" [isPlain]="true"
                            [value]="formData?.BasicInformationForm?.geographiclocation"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Landline'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.landlinenumber"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'POBox'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.pobox"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Email'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.email"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Website'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.website"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Address'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.address"></div>
                    </div>

                    @if(formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.SocialSolidarityFunds){

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'fundbelongsDetails')| translate}}
                        </h4>
                    </div>
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityNameEn'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.EntityName"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityNameAr'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.EntityNameAr"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityType'"
                            [isPlain]="true"
                            [value]="getEntitySectorType(formData?.BasicInformationForm?.Entitysectortype)">
                        </div>
                        <div *ngIf="formData?.BasicInformationForm?.Entitysectortype==3" summary-row
                            class="figma-review-card" [label]="messageTranslationPrefix+'NumberOfEmployees'"
                            [isPlain]="true" [value]="formData?.BasicInformationForm?.Numberofemployees"></div>
                    </div>
                    }

                    @if(formData?.BasicInformationForm?.npoform ==
                    LEGAL_FORM_TYPES.Association ||
                    formData?.BasicInformationForm?.npoform ==
                    LEGAL_FORM_TYPES.NationalSociety){
                    <div class="not_card">
                        @if(formData?.BasicInformationForm?.npoform ==
                        LEGAL_FORM_TYPES.Association){
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'targetGroupsAssociation')
                            | translate}}</h4>
                        }

                        @if(formData?.BasicInformationForm?.npoform ==
                        LEGAL_FORM_TYPES.NationalSociety){
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'targetGroupsNationalSociety')
                            | translate}}</h4>
                        }
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"targetGroupNameEn" |
                                            translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"targetGroupNameAr" |
                                            translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of paginateDataTargetGroup; let i = index">
                                        <td class="align-middle text-center">{{ (pageTargetGroup - 1) *
                                            pageSizeTargetGroup + i + 1 }}</td>
                                        <td class="align-middle text-center " lang="en">{{ item?.Name
                                            }}</td>
                                        <td class="align-middle text-center " lang="ar">{{ item?.NameAr
                                            }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(item?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2 d-l">{{'Total count' | translate}}:
                                {{filteredCountTargetGroup}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageTargetGroup"
                                [pageSize]="pageSizeTargetGroup" [collectionSize]="filteredCountTargetGroup"
                                (pageChange)="getPremiumDataTargetGroup()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeTargetGroup" (change)="getPremiumDataTargetGroup()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (targetGroup of paginateDataTargetGroup; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'targetGroupNameEn' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ targetGroup?.Name }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'targetGroupNameAr' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{ targetGroup?.NameAr
                                        }}</div>
                                </div>
                            </div>
                        </div>
                        }
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">
                          {{(messageTranslationPrefix+'activitiesAndPrograms') | translate}}
                        </h4>
                      </div>
                
                      <div class="col-12 table-container not_card">
                        <div class="table-responsive d-block_mobile">
                          <table class="my-table">
                            <thead>
                              <tr>
                                <th class="align-middle text-center" scope="col">#</th>
                                <th class="align-middle text-center text-wrap" scope="col">
                                  {{messageTranslationPrefix+"exampleOfActivitiesEn" | translate}}
                                </th>
                                <th class="align-middle text-center text-wrap" scope="col">
                                  {{messageTranslationPrefix+"exampleOfActivitiesAr" | translate}}
                                </th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let exampleOfActivity of paginateDataExamplesOfActivities; let i = index">
                                <td class="align-middle text-center">
                                  {{ (pageExamplesOfActivities - 1) * pageSizeExamplesOfActivities + i + 1 }}
                                </td>
                                <td class="align-middle text-center " lang="en">
                                  {{ exampleOfActivity?.Name }}
                                </td>
                                <td class="align-middle text-center " lang="ar">
                                  {{ exampleOfActivity?.NameAr }}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                
                        <div class="figma-card-container">
                          @for (exampleOfActivity of paginateDataExamplesOfActivities; track $index){
                          <div class="figma-card">
                            <div class="figma-card-content">
                              <div class="figma-card-field">
                                <span class="static-value">#</span>
                                <span class="dynamic-value">{{ $index + 1 }}</span>
                              </div>
                              <div class="figma-card-field">
                                <div class="static-value">
                                  {{ messageTranslationPrefix + 'exampleOfActivitiesEn' | translate }}:
                                </div>
                                <div class="dynamic-value" lang="en">
                                  {{ exampleOfActivity?.exampleOfActivitiesEn }}
                                </div>
                              </div>
                              <div class="figma-card-field">
                                <div class="static-value">
                                  {{ messageTranslationPrefix + 'exampleOfActivitiesAr' | translate }}:
                                </div>
                                <div class="dynamic-value" lang="ar">
                                  {{ exampleOfActivity?.exampleOfActivitiesAr }}
                                </div>
                              </div>
                            </div>
                          </div>
                          }
                        </div>
                      </div>
                
                      <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                        <span class="col-2 d-l">{{'Total count' | translate}}:
                          {{filteredCountExamplesOfActivities}}</span>
                        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageExamplesOfActivities"
                          [pageSize]="pageSizeExamplesOfActivities" [collectionSize]="filteredCountExamplesOfActivities"
                          (pageChange)="getPremiumDataExamplesOfActivities()">
                          <ng-template ngbPaginationPrevious>
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                              <path
                                d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                            </svg>
                            <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                          </ng-template>
                          <ng-template ngbPaginationNext>
                            <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                              <path
                                d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                            </svg>
                          </ng-template>
                        </ngb-pagination>
                        <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeExamplesOfActivities"
                          (change)="getPremiumDataExamplesOfActivities()">
                          <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                          <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                          <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                        </select>
                      </div>
                    }
                </mat-expansion-panel>

                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            @if(formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.SocialSolidarityFunds){
                            {{ messageTranslationPrefix+'fundServiceObjectives' | translate }}
                            } @else {
                            {{ messageTranslationPrefix+'Objectives' | translate }}
                            }
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        @if(formData?.BasicInformationForm?.npoform ===
                                        LEGAL_FORM_TYPES.SocialSolidarityFunds){
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{ messageTranslationPrefix+"FundServiceObjectiveEn" | translate }}
                                        </th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{ messageTranslationPrefix+"FundServiceObjectiveAr" | translate }}
                                        </th>
                                        } @else {
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{ messageTranslationPrefix+"ObjectiveEn" | translate }}
                                        </th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{ messageTranslationPrefix+"ObjectiveAr" | translate }}
                                        </th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{ messageTranslationPrefix+"MeansOfAchievingObjectiveEn" | translate }}
                                        </th>
                                        <th class="align-middle text-center text-wrap text-center" scope="col">
                                            {{ messageTranslationPrefix+"MeansOfAchievingObjectiveAr" | translate }}
                                        </th>
                                        }
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let objective of paginateDataObjectives; let i = index">
                                        <td class="align-middle text-center">{{ (pageObjectives - 1) *
                                            pageSizeObjectives + i + 1 }}</td>
                                        <td class="align-middle text-center text-truncate" lang="en"
                                            [matTooltip]="objective?.Objectives" style="max-width:150px;">{{
                                            objective?.Objectives }}</td>
                                        <td class="align-middle text-center text-truncate" lang="ar"
                                            [matTooltip]="objective?.ObjectivesAR" style="max-width:150px;">{{
                                            objective?.ObjectivesAR }}</td>
                                        @if(formData?.BasicInformationForm?.npoform !==
                                        LEGAL_FORM_TYPES.SocialSolidarityFunds){
                                        <td class="align-middle text-center text-truncate" lang="en"
                                            [matTooltip]="objective?.MeansOfAchievingObjective"
                                            style="max-width:150px;">{{
                                            objective?.MeansOfAchievingObjective }}</td>
                                        <td class="align-middle text-center text-truncate" lang="ar"
                                            [matTooltip]="objective?.MeansOfAchievingObjectiveAR"
                                            style="max-width:150px;">
                                            {{
                                            objective?.MeansOfAchievingObjectiveAR }}</td>
                                        }
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2 d-l">{{'Total count' | translate}}:
                                {{filteredCountObjectives}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageObjectives"
                                [pageSize]="pageSizeObjectives" [collectionSize]="filteredCountObjectives"
                                (pageChange)="getPremiumDataObjectives()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeObjectives" (change)="getPremiumDataObjectives()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (objective of paginateDataObjectives; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                @if(formData?.BasicInformationForm?.npoform ===
                                LEGAL_FORM_TYPES.SocialSolidarityFunds){
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'ObjectiveEn' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="en">
                                        {{ objective?.Objectives }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'ObjectiveAr' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="ar">
                                        {{ objective?.ObjectivesAR }}
                                    </div>
                                </div>
                                } @else {
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'ObjectiveEn' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="en">
                                        {{ objective?.Objectives }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'ObjectiveAr' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="ar">
                                        {{ objective?.ObjectivesAR }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'MeansOfAchievingObjectiveEn' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="en">
                                        {{ objective?.MeansOfAchievingObjective }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">
                                        {{ messageTranslationPrefix + 'MeansOfAchievingObjectiveAr' | translate }}:
                                    </div>
                                    <div class="dynamic-value" lang="ar">
                                        {{ objective?.MeansOfAchievingObjectiveAR }}
                                    </div>
                                </div>
                                }
                            </div>
                        </div>
                        }
                    </div>
                </mat-expansion-panel>

                @if(formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.SocialSolidarityFunds){
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'FundServices') |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>
                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"ServiceTitle" |
                                            translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"ServiceTitleAR" |
                                            translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let fundService of paginateDataFundServices; let i = index">
                                        <td class="align-middle text-center">{{ (pageFundServices - 1) *
                                            pageSizeFundServices + i + 1 }}</td>
                                        <td class="align-middle text-center text-truncate" lang="en"
                                            [matTooltip]="fundService?.ServiceTitle" style="max-width:150px;">{{
                                            fundService?.Description }}</td>
                                        <td class="align-middle text-center text-truncate" lang="ar"
                                            [matTooltip]="fundService?.ServiceTitleAR" style="max-width:150px;">{{
                                            fundService?.DescriptionAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(fundService?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2 d-l">{{'Total count' | translate}}:
                                {{filteredCountFundServices}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageFundServices"
                                [pageSize]="pageSizeFundServices" [collectionSize]="filteredCountFundServices"
                                (pageChange)="getPremiumDataFundServices()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeFundServices" (change)="getPremiumDataFundServices()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (objective of paginateDataFundServices; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'ServiceTitle' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ objective?.ServiceTitle }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'ServiceTitleAR' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{ objective?.ServiceTitleAR
                                        }}
                                    </div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(objective?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                </mat-expansion-panel>
                }

                @if(formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.AssociationByDecree ||
                formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.NationalSocietyByDecree ||
                formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Association ||
                formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.SocialSolidarityFunds ||
                formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union ||
                formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.NationalSociety){
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'foundingMembers') |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>
                    @if(formData?.BasicInformationForm?.npoform ===
                    LEGAL_FORM_TYPES.Association || formData?.BasicInformationForm?.npoform
                    === LEGAL_FORM_TYPES.NationalSociety ||
                    formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.Union){
                    @if(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ==
                    true || formData?.FoundingMembersForm?.nbrfmlessthan7code == true){
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'exceptionCases')
                            | translate}}</h4>
                    </div>
                    }
                    @if(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ==
                    true){

                    <div class="row not_card">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'IsFoundingMembersHoldingTheNationalityIsLessThan70'"
                            [isPlain]="true"
                            [value]="(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
                        </div>
                    </div>
                    <div class="figma-review-card-container">
                        <div lang="en" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70En'" [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.ExceptionsRequestNationalityless70?.Description">
                        </div>
                        <div lang="ar" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'" [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.ExceptionsRequestNationalityless70?.DescriptionAr">
                        </div>
                    </div>
                    }
                    @if(formData?.FoundingMembersForm?.nbrfmlessthan7code == true){

                    <div class="row not_card">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'IsNumberOfFoundingMembersIsLessThan7Members'"
                            [isPlain]="true"
                            [value]="(formData?.FoundingMembersForm?.nbrfmlessthan7code ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
                        </div>
                    </div>
                    <div class="figma-review-card-container">
                        <div lang="en" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70En'" [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.ExceptionsRequestNumberless7?.Description">
                        </div>
                        <div lang="ar" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'" [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.ExceptionsRequestNumberless7?.DescriptionAr">
                        </div>
                    </div>
                    }
                    }

                    @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union){

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'foundingMembers')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderEmiratesID" |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"dateOfBirth" |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+(LanguageService.IsArabic ?'founderNameArabic':'founderNameEnglish') |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderNationality" |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"responseDate" |translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |translate}}
                          </th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of paginateDataFoundingMember; let i = index">
                                        <td class="align-middle text-center">{{ (pageFoundingMember - 1)*
                                            pageSizeFoundingMember
                                            + i + 1 }}</td>
                                        <td class="align-middle text-center">{{ item?.Contact?.EmirateId}}</td>
                                        <td class="align-middle text-center">{{ item?.Contact?.Dob |date:
                                            'dd/MM/yyyy'
                                            }}
                                        </td>
                                        <td class="align-middle text-center" lang="en">
                                            {{(LanguageService.IsArabic ? item?.Contact?.FullnameAr :item?.Contact?.FullName)??''}}
                                        </td>
                                        <td class="align-middle text-center" lang="en">
                                            {{(LanguageService.IsArabic ?item?.NationalityAr:item?.Nationality) ??''}}
                                        </td>
                                        <td class="align-middle text-center">
                                            {{item?.Responsedate| date: 'dd/MM/yyyy' }}</td>
                                        <!-- <td class="align-middle text-center status-container">
                            <div style="padding: 8px;" [style.background-color]="getStatusBgColor(item?.SatusReason)"
                              [style.color]="getStatusColor(item?.SatusReason)">
                              {{ getStatusByName(item?.SatusReason) }}
                            </div>
                          </td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountFoundingMember}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageFoundingMember"
                                [pageSize]="pageSizeFoundingMember" [collectionSize]="filteredCountFoundingMember"
                                (pageChange)="getPremiumDataFoundingMember()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeFoundingMember" (change)="getPremiumDataFoundingMember()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (item of paginateDataFoundingMember; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderEmiratesID' | translate }}:</div>
                                    <div class="dynamic-value">{{ item?.Contact?.EmirateId }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'dateOfBirth' | translate }}:</div>
                                    <div class="dynamic-value">{{ item?.Contact?.Dob | date:
                                        'dd/MM/yyyy' }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +(LanguageService.IsArabic ?'founderNameArabic':'founderNameEnglish') | translate }}:</div>
                                    <div class="dynamic-value" lang="en"> {{(LanguageService.IsArabic ? item?.Contact?.FullnameAr :item?.Contact?.FullName)??''}}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderNationality' | translate }}:</div>
                                    <div class="dynamic-value" lang="en"> {{(LanguageService.IsArabic ?item?.NationalityAr:item?.Nationality) ??''}}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'responseDate' | translate }}:</div>
                                    <div class="dynamic-value">{{ item?.ResponseDate }}
                                    </div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |
                          translate }}:</div>
                        <div style="padding: 8px;" class="dynamic-value" [style.color]="getStatusColor(item?.SatusReason)"
                          [style.background-color]="getStatusBgColor(item?.SatusReason)">{{
                          item?.SatusReason }}</div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                    }

                    @if(formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union)
                    {
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'associationsNationalSocieties')
                            | translate}}</h4>
                    </div>
                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"npoEstablishmentNameEN"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"npoEstablishmentNameAr"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"npoEstablishmentDate"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"npoEstablishmentLegalFormEN"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"npoEstablishmentLegalFormAr"
                                            | translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of paginateDataNpoNumber; let i = index">
                                        <td class="align-middle text-center">{{ (pageNpoNumber - 1) *
                                            pageSizeNpoNumber + i + 1 }}</td>
                                        <td class="align-middle text-center" lang="en">{{
                                            item?.npoEstablishmentNameEN }}</td>
                                        <td class="align-middle text-center" lang="ar">{{
                                            item?.npoEstablishmentNameAr }}</td>
                                        <td class="align-middle text-center">{{
                                            item?.npoEstablishmentDate }}</td>
                                        <td class="align-middle text-center" lang="en">{{
                                            item?.npoEstablishmentLegalFormEN }}</td>
                                        <td class="align-middle text-center" lang="ar">{{
                                            item?.npoEstablishmentLegalFormAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(item?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountNpoNumber}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageNpoNumber"
                                [pageSize]="pageSizeNpoNumber" [collectionSize]="filteredCountNpoNumber"
                                (pageChange)="getPremiumDataNpoNumber()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeNpoNumber" (change)="getPremiumDataNpoNumber()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (item of paginateDataNpoNumber; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>

                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentNameEN'
                                        |
                                        translate
                                        }}:</div>
                                    <div class="dynamic-value" lang="en">{{item?.npoEstablishmentNameEN }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentNameAr'
                                        |
                                        translate
                                        }}:</div>
                                    <div class="dynamic-value" lang="ar">{{item?.npoEstablishmentNameAr }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentDate' |
                                        translate
                                        }}:</div>
                                    <div class="dynamic-value">{{ item?.npoEstablishmentDate }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'npoEstablishmentLegalFormEN' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{
                                        item?.npoEstablishmentLegalFormEN }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'npoEstablishmentLegalFormAr' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{
                                        item?.npoEstablishmentLegalFormAr }}
                                    </div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(item?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                    }

                    <!-- <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'foundersMeeting')
                            | translate}}</h4>
                    </div>

                    @if(canSeeAgendaSection()){
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MeetingPlace'"
                            [isPlain]="true" [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.MeetingPlace">
                        </div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Emirate'"
                            [isPlain]="true"
                            [value]="getEmirate(formData?.FoundingMembersForm?.Npofoundersmeeting?.Emirate)"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Date'"
                            [isDateTime]="true"
                            [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.Meetingdate">
                        </div>
                    </div>

                    <div class="d-flex align-items-center not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'agendaItems') | translate}}</h4>
                    </div>

                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'DiscussingTheEstablishmentOfPublicBenefitInstitution'"
                            [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.Discussingtheestablishment">
                        </div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'PreparingTheDraftStatute'" [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.Preparingthedraftstatute">
                        </div>
                        <div summary-row class="figma-review-card"
                            *ngIf="this.formData?.BasicInformationForm?.npoform !== this.LEGAL_FORM_TYPES.NationalSociety"
                            [label]="messageTranslationPrefix+'ElectionOfMembersOfTheTemporaryCommittee'"
                            [isPlain]="true"
                            [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.Electionofmembersofthetemporarycommittee">
                        </div>
                    </div>
                    } @else{
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'founderDecisionDate'" [isDateTime]="true"
                            [value]="formData?.FoundingMembersForm?.Npofoundersmeeting?.Meetingdate">
                        </div>
                    </div>
                    } -->
                </mat-expansion-panel>
                }

                @if(formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.AssociationByDecree ||
                formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Association ||
                formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.SocialSolidarityFunds ||
                formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union)
                {
                @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union &&
                formData?.BasicInformationForm?.npoform !=
                LEGAL_FORM_TYPES.AssociationByDecree){
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'interimCommittee')
                            |translate}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'interimCommitteeMembers')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderEmiratesID" |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"temporaryCommitteeMemberName"| translate}}
                                        </th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"memberPosition"
                                            |translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status"
                            |translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(member of paginateDataInterimMembers; track member; let i =
                                    $index){
                                    <tr>
                                        <td class="align-middle text-center">{{ (pageInterimMembers - 1)*
                                            pageSizeInterimMembers
                                            + i + 1 }}</td>
                                        <td class="align-middle text-center">{{member?.Contact?.EmirateId }}</td>
                                        <td class="align-middle text-center">{{
                                            this.canSeeFoundingMember(member?.SatusReason) ?
                                            (LanguageService.IsArabic ? member?.Contact?.FullnameAr :
                                            member?.Contact?.FullName)
                                            : '' }}</td>
                                        <td [lang]="LanguageService.IsArabic ? 'ar':'en'"
                                            class="align-middle text-center">
                                            {{getPosition(member.PositionId) }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(member?.SatusReason) }}</td> -->
                                    </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountInterimMembers}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageInterimMembers"
                                [pageSize]="pageSizeInterimMembers" [collectionSize]="filteredCountInterimMembers"
                                (pageChange)="getPremiumDataInterimCommittee()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeInterimMembers" (change)="getPremiumDataInterimCommittee()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (member of paginateDataInterimMembers; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderEmiratesID' | translate }}:</div>
                                    <div class="dynamic-value">{{ member?.Contact?.EmirateId }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'temporaryCommitteeMemberName' | translate }}:</div>
                                    <div class="dynamic-value">{{this.canSeeFoundingMember(member?.SatusReason) ?
                                        (member?.Name
                                        ??'') : '' }}
                                    </div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'memberPosition' | translate }}:</div>
                                    <div [lang]="LanguageService.IsArabic ? 'ar':'en'" class="dynamic-value">{{
                                        getPosition(member.PositionId)
                                        }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(member?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>

                    <!-- <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'interimCommitteeInformation')
                            | translate}}</h4>
                    </div>

                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MeetingPlace'"
                            [isPlain]="true"
                            [value]="formData?.InterimCommitteeForm?.Npointerimcommitteeandfoundersmeeting?.MeetingPlace">
                        </div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Emirate'"
                            [isPlain]="true"
                            [value]="getEmirate(formData?.InterimCommitteeForm?.Npointerimcommitteeandfoundersmeeting?.Emirate)">
                        </div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Date'"
                            [isDateTime]="true"
                            [value]="formData?.InterimCommitteeForm?.Npointerimcommitteeandfoundersmeeting?.Meetingdate">
                        </div>

                    </div>

                    <div class="d-flex align-items-center not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'agendaItems') | translate}}</h4>
                    </div>

                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'Definetheadministrativepositions'" [isPlain]="true"
                            [value]="formData?.InterimCommitteeForm?.Npointerimcommitteeandfoundersmeeting?.Definetheadministrativepositions">
                        </div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'Appointthecommissioner'" [isPlain]="true"
                            [value]="formData?.InterimCommitteeForm?.Npointerimcommitteeandfoundersmeeting?.Appointthecommissioner">
                        </div>
                    </div> -->

                </mat-expansion-panel>
                }

                @if(formData?.BasicInformationForm?.npoform !=
                LEGAL_FORM_TYPES.AssociationByDecree){
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'membership' |
                            translate)}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    <!-- @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union) -->
                    <!-- { -->
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'membershipInformation')
                            | translate}}</h4>
                    </div>
                    <div class="figma-review-card-container">
                        @if(formData?.BasicInformationForm?.npoform ==LEGAL_FORM_TYPES.Association){
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MembershipFees'" [isPlain]="true"
                          [value]="formData?.MembershipForm?.Membershipfees|currency:'AED'"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EnrollmentFees'" [isPlain]="true"
                          [value]="formData?.MembershipForm?.Enrollmentfees|currency:'AED'"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'beneficiaryMembershipFees'"
                          [isPlain]="true" [value]="formData?.MembershipForm?.BeneficiaryOrMembershipFees|currency:'AED'"></div>
                        }@else {
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'normalMembershipFees'"
                          [isPlain]="true" [value]="formData?.MembershipForm?.Membershipfees|currency:'AED'"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EnrollmentFees'" [isPlain]="true"
                          [value]="formData?.MembershipForm?.Enrollmentfees|currency:'AED'"></div>
                        }
                    </div>
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'membershipConditions')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"membershipConditionEn"| translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"membershipConditionAr"| translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    @for(memberShip of paginateDataMemberShip; track memberShip; let i= $index){
                                    <tr>
                                        <td class="align-middle text-center">{{ (pageMemberShip - 1) *
                                            pageSizeMemberShip +
                                            i +
                                            1 }}</td>
                                        <td class="align-middle text-center" lang="en">{{memberShip?.Name }}</td>
                                        <td class="align-middle text-center" lang="ar">{{memberShip?.NameAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(memberShip?.SatusReason) }}</td> -->
                                    </tr>
                                    }

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountMemberShip}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageMemberShip"
                                [pageSize]="pageSizeMemberShip" [collectionSize]="filteredCountMemberShip"
                                (pageChange)="getPremiumDataMemberShip()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeMemberShip" (change)="getPremiumDataMemberShip()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (memberShip of paginateDataMemberShip; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'membershipConditionEn' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ memberShip?.Name }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'membershipConditionAr' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{ memberShip?.NameAr
                                        }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(memberShip?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                </mat-expansion-panel>
                }
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'boardOfDirectors') |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    @if(formData?.BoardOfDirectorsForm?.nbrboardmemberexceed11code === 1){
                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'exceptionRequests')
                            | translate}}</h4>
                    </div>

                    <div class="roe not_card">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'MemberIsExceeds11'" [isPlain]="true"
                            [value]="(formData?.BoardOfDirectorsForm?.nbrboardmemberexceed11code ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
                        </div>
                    </div>
                    <div class="figma-review-card-container">
                        <div lang="en" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70En'" [isPlain]="true"
                            [value]="formData?.BoardOfDirectorsForm?.ExceptionsRequest?.Description"></div>
                        <div lang="ar" summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'" [isPlain]="true"
                            [value]="formData?.BoardOfDirectorsForm?.ExceptionsRequest?.DescriptionAr"></div>
                    </div>
                    }

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsInformation')
                            | translate}}</h4>
                    </div>

                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'numberOfBoardMembers'" [isPlain]="true"
                            [value]="formData?.BoardOfDirectorsForm?.nbrofboardmembers"></div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'FrequencyOfMonthlyBoardMeetings'" [isPlain]="true"
                            [value]="getFrequencyOfBoardMeetings(formData?.BoardOfDirectorsForm?.Frequencyofmonthlyboardmeetings)">
                        </div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'localBoardMembersPercentage'" [isPlain]="true"
                            [value]="formData?.BoardOfDirectorsForm?.percentageofuaelocalboardmembers"></div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ElectionMethod'"
                            [isPlain]="true"
                            [value]="getElectionMethod(formData?.BoardOfDirectorsForm?.Electionmethod)">
                        </div>
                        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'CanBeRenominated'"
                            [isPlain]="true"
                            [value]="getRenominationStatus(formData?.BoardOfDirectorsForm?.canboardmemberberenomatedforotherterm)">
                        </div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'NumberOfPermissibleTerms'" [isPlain]="true"
                            *ngIf="getRenominationStatus(formData?.BoardOfDirectorsForm?.canboardmemberberenomatedforotherterm) != ''"
                            [value]="getPermissibleTerms(formData?.BoardOfDirectorsForm?.NbrofpermissibleTerms)">
                        </div>
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'BoardElectionCycle'" [isPlain]="true"
                            [value]="getBoardElectionCycle(formData?.BoardOfDirectorsForm?.Boardelectioncycle)">
                        </div>
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsConditions')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"nominationConditionEnglish"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"nominationConditionArabic"
                                            | translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let membershipCondition of paginateDataBoardOfDirectorsMembershipConditions; let i = index">
                                        <td class="align-middle text-center">{{
                                            (pageBoardOfDirectorsMembershipConditions - 1) *
                                            pageSizeBoardOfDirectorsMembershipConditions + i + 1 }}</td>
                                        <td class="align-middle text-center" lang="en">{{
                                            membershipCondition?.Name }}</td>
                                        <td class="align-middle text-center" lang="ar">{{
                                            membershipCondition?.NameAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(membershipCondition?.SatusReason) }}</td> -->
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountBoardOfDirectorsMembershipConditions}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true"
                                [(page)]="pageBoardOfDirectorsMembershipConditions"
                                [pageSize]="pageSizeBoardOfDirectorsMembershipConditions"
                                [collectionSize]="filteredCountBoardOfDirectorsMembershipConditions"
                                (pageChange)="getPremiumDataBoardOfDirectorsMembershipConditions()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeBoardOfDirectorsMembershipConditions"
                                (change)="getPremiumDataBoardOfDirectorsMembershipConditions()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (membershipCondition of
                        paginateDataBoardOfDirectorsMembershipConditions; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'nominationConditionEnglish' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                                        }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'nominationConditionArabic' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{
                                        membershipCondition?.NameAr }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(membershipCondition?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsPositions')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"adminPositionTitleEnglish"
                                            | translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"adminPositionTitleArabic"
                                            | translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let bOfDPosition of paginateDataBoardOfDirectorsPositions; let i = index">
                                        <td class="align-middle text-center">{{
                                            (pageBoardOfDirectorsPositions - 1) *
                                            pageSizeBoardOfDirectorsPositions + i + 1 }}</td>
                                        <td class="align-middle text-center" lang="en">{{
                                            bOfDPosition?.Name }}</td>
                                        <td class="align-middle text-center" lang="ar">{{
                                            bOfDPosition?.NameAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(bOfDPosition?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountBoardOfDirectorsPositions}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfDirectorsPositions"
                                [pageSize]="pageSizeBoardOfDirectorsPositions"
                                [collectionSize]="filteredCountBoardOfDirectorsPositions"
                                (pageChange)="getPremiumDataBoardOfDirectorsPositions()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeBoardOfDirectorsPositions"
                                (change)="getPremiumDataBoardOfDirectorsPositions()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (bOfDPosition of paginateDataBoardOfDirectorsPositions; track
                        $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'adminPositionTitleEnglish' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ bOfDPosition?.Name
                                        }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'adminPositionTitleArabic' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{ bOfDPosition?.NameAr
                                        }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(bOfDPosition?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                </mat-expansion-panel>
                }
                @if(formData?.BasicInformationForm?.npoform ==
                LEGAL_FORM_TYPES.NationalSocietyByDecree ||
                formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.NationalSociety)
                {
                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{messageTranslationPrefix+'allocationOfFoundationFunds'
                            | translate}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'allocationOfFoundationFunds')
                            | translate}}</h4>
                    </div>

                    <p> {{ messageTranslationPrefix+"totalFundsAmountIs" | translate }} {{
                        gettotalFundsAmount() | number: '1.0-2' }} {{ 'AED' | translate}}</p>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"foundingMemberEID" | translate
                                            }}</th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"NatureOfFundsAllocated" |
                                            translate }}</th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"DescriptionOfInKindFundsEn" |
                                            translate }}</th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"DescriptionOfInKindFundsAr" |
                                            translate }}</th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"FundsAmount" | translate }}
                                        </th>
                                        <th class="align-middle text-center" scope="col">{{
                                            messageTranslationPrefix+"AllocationCycle" | translate
                                            }}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let fund of paginateDataAllocationOfFoundationFunds; let i = index">
                                        <td class="align-middle">{{ (pageAllocationOfFoundationFunds -
                                            1) * pageSizeAllocationOfFoundationFunds + i
                                            + 1 }}</td>
                                        <td class="align-middle">{{ fund?.Contact?.EmirateId }}</td>
                                        <td class="align-middle">{{
                                            getNatureOfAllocatedFunds(fund?.NatureOfAllocatedFunds)
                                            }}</td>
                                        <td class="align-middle" lang="en">{{ fund?.Description }}</td>
                                        <td class="align-middle" lang="ar">{{ fund?.DescriptionAr}}</td>
                                        <td class="align-middle">
                                            {{ fund?.ValueOfTheFund | number: '1.0-2' }} {{ 'AED' | translate}}
                                        </td>
                                        <td class="align-middle">{{
                                            getAllocationCycle(fund?.AllocationCycle) }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(fund?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountAllocationOfFoundationFunds}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageAllocationOfFoundationFunds"
                                [pageSize]="pageSizeAllocationOfFoundationFunds"
                                [collectionSize]="filteredCountAllocationOfFoundationFunds"
                                (pageChange)="getPremiumDataAllocationOfFoundationFunds()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeAllocationOfFoundationFunds"
                                (change)="getPremiumDataAllocationOfFoundationFunds()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (fund of paginateDataAllocationOfFoundationFunds; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'foundingMemberEID' | translate }}:</div>
                                    <div class="dynamic-value">{{ fund?.Contact?.EmirateId }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'NatureOfFundsAllocated' | translate }}:</div>
                                    <div class="dynamic-value">{{
                                        getNatureOfAllocatedFunds(fund?.NatureOfAllocatedFunds) }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'DescriptionOfInKindFundsEn' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ fund?.Description }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'DescriptionOfInKindFundsAr' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{ fund?.DescriptionAr
                                        }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'FundsAmount' | translate }}:</div>
                                    <div class="dynamic-value">{{ fund?.ValueOfTheFund |
                                        currency:'AED' }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'AllocationCycle' | translate }}:</div>
                                    <div class="dynamic-value">{{
                                        getAllocationCycle(fund?.AllocationCycle) }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(fund?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>
                </mat-expansion-panel>

                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{messageTranslationPrefix+'boardOfTrustees' |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesInformation')
                            | translate}}</h4>
                    </div>
                    <div class="figma-review-card-container">
                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'frequencyOfMeetings'" [isPlain]="true"
                            [value]="getFrequencyOfMeetings(formData?.BoardOfTrusteesForm?.frequencyOfMeetings)">
                        </div>

                        <div summary-row class="figma-review-card"
                            [label]="messageTranslationPrefix+'frequencyOfAppointments'" [isPlain]="true"
                            [value]="getFrequencyOfAppointments(formData?.BoardOfTrusteesForm?.frequencyOfAppointments)">
                        </div>
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesConditions')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle" scope="col">{{messageTranslationPrefix+"Id" |
                                            translate}}
                                        </th>
                                        <th class="align-middle" scope="col">
                                            {{messageTranslationPrefix+"conditionForNominationEn"
                                            | translate}}
                                        </th>
                                        <th class="align-middle" scope="col">
                                            {{messageTranslationPrefix+"conditionForNominationAr"
                                            | translate}}
                                        </th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let membershipCondition of paginateDataBoardOfTrusteeMembershipConditions; let i = index">
                                        <td class="align-middle">{{
                                            (pageBoardOfTrusteeMembershipConditions - 1) *
                                            pageSizeBoardOfTrusteeMembershipConditions + i + 1 }}</td>
                                        <td class="align-middle" lang="en">{{ membershipCondition?.Name
                                            }}</td>
                                        <td class="align-middle" lang="ar">{{
                                            membershipCondition?.NameAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(membershipCondition?.SatusReason) }}</td> -->

                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountBoardOfTrusteeMembershipConditions}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true"
                                [(page)]="pageBoardOfTrusteeMembershipConditions"
                                [pageSize]="pageSizeBoardOfTrusteeMembershipConditions"
                                [collectionSize]="filteredCountBoardOfTrusteeMembershipConditions"
                                (pageChange)="getPremiumDataBoardOfTrusteeMembershipConditions()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeBoardOfTrusteeMembershipConditions"
                                (change)="getPremiumDataBoardOfTrusteeMembershipConditions()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (membershipCondition of
                        paginateDataBoardOfTrusteeMembershipConditions; track $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'conditionForNominationEn' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                                        }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'conditionForNominationAr' | translate }}:</div>
                                    <div class="dynamic-value" lang="ar">{{
                                        membershipCondition?.NameAr }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(membershipCondition?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesPositions')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle" scope="col">{{messageTranslationPrefix+"Id" |
                                            translate}}
                                        </th>
                                        <th class="align-middle" scope="col">
                                            {{messageTranslationPrefix+"administrativePositionTitleEn"
                                            |
                                            translate}}</th>
                                        <th class="align-middle" scope="col">
                                            {{messageTranslationPrefix+"administrativePositionTitleAr"
                                            |
                                            translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}</th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr
                                        *ngFor="let membershipCondition of paginateDataBoardOfTrusteePositions; let i = index">
                                        <td class="align-middle">{{ (pageBoardOfTrusteePositions - 1) *
                                            pageSizeBoardOfTrusteePositions + i + 1 }}
                                        </td>
                                        <td class="align-middle" lang="en">{{ membershipCondition?.Name
                                            }}</td>
                                        <td class="align-middle" lang="ar">{{
                                            membershipCondition?.NameAr }}</td>
                                        <!-- <td class="align-middle text-center">{{getStatusByName(membershipCondition?.SatusReason) }}</td> -->
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountBoardOfTrusteePositions}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfTrusteePositions"
                                [pageSize]="pageSizeBoardOfTrusteePositions"
                                [collectionSize]="filteredCountBoardOfTrusteePositions"
                                (pageChange)="getPremiumDataBoardOfTrusteePositions()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeBoardOfTrusteePositions"
                                (change)="getPremiumDataBoardOfTrusteePositions()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (membershipCondition of paginateDataBoardOfTrusteePositions; track
                        $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'administrativePositionTitleEn' | translate }}</div>
                                    <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                                        }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'administrativePositionTitleAr' | translate }}</div>
                                    <div class="dynamic-value" lang="ar">{{
                                        membershipCondition?.NameAr }}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |translate }}:</div>
                        <div class="dynamic-value">{{ getStatusByName(membershipCondition?.SatusReason)}}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>

                    <div class="not_card">
                        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesMembers')
                            | translate}}</h4>
                    </div>

                    <div class="col-12 table-container not_card d-block_mobile">
                        <div class="table-responsive">
                            <table class="my-table">
                                <thead>
                                    <tr>
                                        <th class="align-middle text-center" scope="col">#</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderEmiratesID"
                                            |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"dateOfBirth"
                                            |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderNameEnglish"
                                            |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"founderNationality"
                                            |translate}}</th>
                                        <th class="align-middle text-center text-wrap" scope="col">
                                            {{messageTranslationPrefix+"Position"
                                            |translate}}</th>
                                        <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" |
                            translate}}
                          </th> -->
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let item of paginateDataBoardOfTrusteeMembershipMembers; let i = index">
                                        <td class="align-middle text-center">{{
                                            (pageBoardOfTrusteeMembershipMembers - 1) *
                                            pageSizeBoardOfTrusteeMembershipMembers + i + 1 }}</td>
                                        <td class="align-middle text-center">{{ item?.Contact?.EmirateId
                                            }}</td>
                                        <td class="align-middle text-center">{{ item?.Contact?.Dob |date:
                                            'dd/MM/yyyy'
                                            }}
                                        </td>
                                        <td class="align-middle text-center" lang="en">
                                            {{this.canSeeFoundingMember(item?.SatusReason) ? item?.Name:''}}</td>
                                        <td class="align-middle text-center" lang="en">
                                            {{this.canSeeFoundingMember(item?.SatusReason) ? item?.Nationality:'' }}
                                        </td>
                                        <td class="align-middle text-center">{{ LanguageService.IsArabic?
                                            item?.Position?.NameAr
                                            : item?.Position?.Name}}</td>
                                        <!-- <td class="align-middle text-center status-container">
                            <div style="padding: 8px;" [style.background-color]="getStatusBgColor(item?.SatusReason)"
                              [style.color]="getStatusColor(item?.SatusReason)">
                              {{getStatusByName(item?.SatusReason) }}
                            </div>
                          </td> -->
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
                            <span class="col-2">{{'Total count' | translate}}:
                                {{filteredCountBoardOfTrusteeMembershipMembers}}</span>
                            <ngb-pagination [maxSize]="5" [ellipses]="true"
                                [(page)]="pageBoardOfTrusteeMembershipMembers"
                                [pageSize]="pageSizeBoardOfTrusteeMembershipMembers"
                                [collectionSize]="filteredCountBoardOfTrusteeMembershipMembers"
                                (pageChange)="getPremiumDataBoardOfTrusteeMembershipMembers()">
                                <ng-template ngbPaginationPrevious>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                                    </svg>
                                    <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                                </ng-template>
                                <ng-template ngbPaginationNext>
                                    <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                                        <path
                                            d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                                    </svg>
                                </ng-template>
                            </ngb-pagination>
                            <select class="form-select " style="width: auto" name="pageSize"
                                [(ngModel)]="pageSizeBoardOfTrusteeMembershipMembers"
                                (change)="getPremiumDataBoardOfTrusteeMembershipMembers()">
                                <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
                                <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="figma-card-container">
                        @for (item of paginateDataBoardOfTrusteeMembershipMembers; track
                        $index){
                        <div class="figma-card">
                            <div class="figma-card-content">
                                <div class="figma-card-field">
                                    <span class="static-value">#</span>
                                    <span class="dynamic-value">{{ $index + 1 }}</span>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderEmiratesID' | translate }}:</div>
                                    <div class="dynamic-value">{{ item?.Contact?.EmirateId }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'dateOfBirth' | translate }}:</div>
                                    <div class="dynamic-value">{{ item?.Contact?.Dob | date:
                                        'dd/MM/yyyy' }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderNameEnglish' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ item?.Name }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix +
                                        'founderNationality' | translate }}:</div>
                                    <div class="dynamic-value" lang="en">{{ item?.Nationality }}</div>
                                </div>
                                <div class="figma-card-field">
                                    <div class="static-value">{{ messageTranslationPrefix + 'Position'
                                        | translate }}:</div>
                                    <div class="dynamic-value">{{ getPosition(item.PositionId)}}</div>
                                </div>
                                <!-- <div class="figma-card-field">
                        <div class="static-value">{{ messageTranslationPrefix + 'status' |
                          translate }}:</div>
                        <div style="padding: 8px;" class="dynamic-value" [style.color]="getStatusColor(item?.SatusReason)"
                          [style.background-color]="getStatusBgColor(item?.SatusReason)"> {{getStatusByName(item?.SatusReason) }}
                        </div>
                      </div> -->
                            </div>
                        </div>
                        }
                    </div>

                </mat-expansion-panel>
                }

                <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
                    <mat-expansion-panel-header>
                        <mat-panel-title>{{(messageTranslationPrefix+'uploadDocuments') |
                            translate}}</mat-panel-title>
                    </mat-expansion-panel-header>

                    @for (file of formData?.UploadDocumentForm?.Document; track $index) {
                    <div class="figma-review-card-container">
                        <span class="figma-review-card">
                            <h4>
                                <strong
                                    [lang]="LanguageService.IsArabic ? 'ar':'en'">{{getDocTypeName(file?.DocumentType)}}
                                    :
                                </strong>
                            </h4>
                            <a [href]="generateDownloadLink(file)" [download]="file?.FileName">{{file?.FileName}}</a>
                        </span>
                    </div>
                    }
                </mat-expansion-panel>
            </mat-accordion>
            <div class="custom-download-buttons d-flex align-items-center justify-content-center download-buttons-container">
                <ng-container *ngFor="let button of downloadButtons">
                    <button type="button" (click)="download(button)" class="btn btn-lg download-button">
                        {{LanguageService.IsArabic?button?.ButtonLabelAr:
                        button?.ButtonLabelEn}}
                        <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                                fill="#92722A" />
                        </svg>
                    </button>
                </ng-container>
            </div>
            } @else if(viewMode === 'PermitList'){
            <app-fundraising-permit-list />
            }
        </div>
    </div>
</div>



<!-- <div class="button_gp">
    <ng-container *ngIf="!isNotAllowedToEdit">
        <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
            (click)="previous.emit()">
            {{'Previous' | translate}}
        </button>
        <button type="button" class="btn basic-button" (click)="submit(true)">
            {{ messageTranslationPrefix+"saveAsDraft" | translate }}
        </button>
    </ng-container>

    <button *ngIf="requestId" type="button" class="btn btn-primary basic-filled-button" (click)="submit(false)">
        {{"submitRequest" |translate}}</button> -->


<!-- @if(this.formData?.Id){ -->
<!-- <ng-container *ngFor="let button of downloadButtons">
      <button type="button" class="btn download-button" (click)="download(button)">
        <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
            fill="#92722A" />
        </svg>
        {{LanguageService.IsArabic?button?.ButtonLabelAr: button?.ButtonLabelEn}}
      </button>
    </ng-container> -->
<!-- } -->
<!-- </div> -->
<ng-template #establishmentDetailsIcon>
    <svg width="19" height="16" viewBox="0 0 19 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M18.375 7.5C18.3757 6.78224 18.1997 6.07535 17.8627 5.44164C17.5257 4.80793 17.0379 4.26688 16.4424 3.86618C15.8469 3.46548 15.162 3.21745 14.448 3.14395C13.734 3.07045 13.0129 3.17375 12.3483 3.44472C11.6836 3.7157 11.0959 4.14604 10.6368 4.6978C10.1778 5.24956 9.8615 5.90579 9.71593 6.60862C9.57036 7.31146 9.59994 8.03932 9.80207 8.72803C10.0042 9.41673 10.3727 10.0451 10.875 10.5578V15C10.8749 15.1066 10.9021 15.2114 10.954 15.3046C11.0058 15.3977 11.0807 15.476 11.1713 15.532C11.262 15.5881 11.3655 15.62 11.4719 15.6248C11.5784 15.6296 11.6844 15.6071 11.7797 15.5594L14 14.4484L16.2203 15.5594C16.3156 15.6071 16.4216 15.6296 16.5281 15.6248C16.6345 15.62 16.738 15.5881 16.8287 15.532C16.9193 15.476 16.9942 15.3977 17.046 15.3046C17.0979 15.2114 17.1251 15.1066 17.125 15V10.5578C17.9266 9.74195 18.3755 8.64377 18.375 7.5ZM14 4.375C14.6181 4.375 15.2223 4.55828 15.7362 4.90166C16.2501 5.24504 16.6506 5.7331 16.8871 6.30411C17.1236 6.87513 17.1855 7.50347 17.065 8.10966C16.9444 8.71585 16.6467 9.27267 16.2097 9.70971C15.7727 10.1467 15.2158 10.4444 14.6097 10.565C14.0035 10.6855 13.3751 10.6236 12.8041 10.3871C12.2331 10.1506 11.745 9.75006 11.4017 9.23616C11.0583 8.72225 10.875 8.11807 10.875 7.5C10.875 6.6712 11.2042 5.87634 11.7903 5.29029C12.3763 4.70424 13.1712 4.375 14 4.375ZM14.2797 13.1906C14.1929 13.1472 14.0971 13.1246 14 13.1246C13.9029 13.1246 13.8071 13.1472 13.7203 13.1906L12.125 13.9891V11.4523C12.7109 11.7306 13.3514 11.875 14 11.875C14.6486 11.875 15.2891 11.7306 15.875 11.4523V13.9891L14.2797 13.1906ZM9.625 12.5C9.625 12.6658 9.55915 12.8247 9.44194 12.9419C9.32473 13.0592 9.16576 13.125 9 13.125H2.125C1.79348 13.125 1.47554 12.9933 1.24112 12.7589C1.0067 12.5245 0.875 12.2065 0.875 11.875V1.875C0.875 1.54348 1.0067 1.22554 1.24112 0.991116C1.47554 0.756696 1.79348 0.625 2.125 0.625H15.875C16.2065 0.625 16.5245 0.756696 16.7589 0.991116C16.9933 1.22554 17.125 1.54348 17.125 1.875C17.125 2.04076 17.0592 2.19973 16.9419 2.31694C16.8247 2.43415 16.6658 2.5 16.5 2.5C16.3342 2.5 16.1753 2.43415 16.0581 2.31694C15.9408 2.19973 15.875 2.04076 15.875 1.875H2.125V11.875H9C9.16576 11.875 9.32473 11.9408 9.44194 12.0581C9.55915 12.1753 9.625 12.3342 9.625 12.5ZM8.375 8.125C8.375 8.29076 8.30915 8.44973 8.19194 8.56694C8.07473 8.68415 7.91576 8.75 7.75 8.75H4.625C4.45924 8.75 4.30027 8.68415 4.18306 8.56694C4.06585 8.44973 4 8.29076 4 8.125C4 7.95924 4.06585 7.80027 4.18306 7.68306C4.30027 7.56585 4.45924 7.5 4.625 7.5H7.75C7.91576 7.5 8.07473 7.56585 8.19194 7.68306C8.30915 7.80027 8.375 7.95924 8.375 8.125ZM8.375 5.625C8.375 5.79076 8.30915 5.94973 8.19194 6.06694C8.07473 6.18415 7.91576 6.25 7.75 6.25H4.625C4.45924 6.25 4.30027 6.18415 4.18306 6.06694C4.06585 5.94973 4 5.79076 4 5.625C4 5.45924 4.06585 5.30027 4.18306 5.18306C4.30027 5.06585 4.45924 5 4.625 5H7.75C7.91576 5 8.07473 5.06585 8.19194 5.18306C8.30915 5.30027 8.375 5.45924 8.375 5.625Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #manageEmployeesIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M17.0407 15.0625C15.8508 13.0055 14.0172 11.5305 11.8774 10.8312C12.9358 10.2011 13.7582 9.24099 14.2182 8.09827C14.6781 6.95554 14.7503 5.69343 14.4235 4.50574C14.0968 3.31805 13.3892 2.27045 12.4094 1.52384C11.4296 0.77722 10.2318 0.372864 9.00003 0.372864C7.76821 0.372864 6.57044 0.77722 5.59067 1.52384C4.6109 2.27045 3.90331 3.31805 3.57654 4.50574C3.24978 5.69343 3.32193 6.95554 3.78189 8.09827C4.24186 9.24099 5.06422 10.2011 6.12268 10.8312C3.98284 11.5297 2.14925 13.0047 0.959403 15.0625C0.91577 15.1336 0.886828 15.2128 0.874285 15.2953C0.861743 15.3778 0.865854 15.462 0.886376 15.5429C0.906897 15.6238 0.943414 15.6998 0.993772 15.7663C1.04413 15.8329 1.10731 15.8887 1.17958 15.9304C1.25185 15.9722 1.33175 15.999 1.41457 16.0094C1.49738 16.0198 1.58143 16.0134 1.66176 15.9908C1.74209 15.9682 1.81708 15.9296 1.88228 15.8776C1.94749 15.8255 2.00161 15.7608 2.04143 15.6875C3.51331 13.1437 6.11487 11.625 9.00003 11.625C11.8852 11.625 14.4867 13.1437 15.9586 15.6875C15.9985 15.7608 16.0526 15.8255 16.1178 15.8776C16.183 15.9296 16.258 15.9682 16.3383 15.9908C16.4186 16.0134 16.5027 16.0198 16.5855 16.0094C16.6683 15.999 16.7482 15.9722 16.8205 15.9304C16.8927 15.8887 16.9559 15.8329 17.0063 15.7663C17.0566 15.6998 17.0932 15.6238 17.1137 15.5429C17.1342 15.462 17.1383 15.3778 17.1258 15.2953C17.1132 15.2128 17.0843 15.1336 17.0407 15.0625ZM4.62503 5.99999C4.62503 5.1347 4.88162 4.28883 5.36235 3.56937C5.84308 2.8499 6.52636 2.28915 7.32579 1.95802C8.12522 1.62688 9.00488 1.54024 9.85355 1.70905C10.7022 1.87786 11.4818 2.29454 12.0936 2.9064C12.7055 3.51825 13.1222 4.2978 13.291 5.14647C13.4598 5.99514 13.3731 6.8748 13.042 7.67423C12.7109 8.47366 12.1501 9.15694 11.4306 9.63767C10.7112 10.1184 9.86532 10.375 9.00003 10.375C7.84009 10.3737 6.72801 9.91241 5.90781 9.09221C5.0876 8.27201 4.62627 7.15993 4.62503 5.99999Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #npoAmendmentIcon>
    <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M14.625 6.25C14.625 6.41576 14.5592 6.57473 14.4419 6.69194C14.3247 6.80915 14.1658 6.875 14 6.875H10.875C10.7092 6.875 10.5503 6.80915 10.4331 6.69194C10.3158 6.57473 10.25 6.41576 10.25 6.25C10.25 6.08424 10.3158 5.92527 10.4331 5.80806C10.5503 5.69085 10.7092 5.625 10.875 5.625H14C14.1658 5.625 14.3247 5.69085 14.4419 5.80806C14.5592 5.92527 14.625 6.08424 14.625 6.25ZM14 8.125H10.875C10.7092 8.125 10.5503 8.19085 10.4331 8.30806C10.3158 8.42527 10.25 8.58424 10.25 8.75C10.25 8.91576 10.3158 9.07473 10.4331 9.19194C10.5503 9.30915 10.7092 9.375 10.875 9.375H14C14.1658 9.375 14.3247 9.30915 14.4419 9.19194C14.5592 9.07473 14.625 8.91576 14.625 8.75C14.625 8.58424 14.5592 8.42527 14.4419 8.30806C14.3247 8.19085 14.1658 8.125 14 8.125ZM17.125 1.875V13.125C17.125 13.4565 16.9933 13.7745 16.7589 14.0089C16.5245 14.2433 16.2065 14.375 15.875 14.375H2.125C1.79348 14.375 1.47554 14.2433 1.24112 14.0089C1.0067 13.7745 0.875 13.4565 0.875 13.125V1.875C0.875 1.54348 1.0067 1.22554 1.24112 0.991116C1.47554 0.756696 1.79348 0.625 2.125 0.625H15.875C16.2065 0.625 16.5245 0.756696 16.7589 0.991116C16.9933 1.22554 17.125 1.54348 17.125 1.875ZM15.875 13.125V1.875H2.125V13.125H15.875ZM9.60469 10.4688C9.64613 10.6293 9.62208 10.7998 9.53784 10.9426C9.45359 11.0855 9.31605 11.189 9.15547 11.2305C8.99489 11.2719 8.82442 11.2479 8.68157 11.1636C8.53872 11.0794 8.43519 10.9418 8.39375 10.7812C8.18828 9.97969 7.37344 9.375 6.49922 9.375C5.625 9.375 4.81094 9.97969 4.60469 10.7812C4.56325 10.9418 4.45971 11.0794 4.31686 11.1636C4.17401 11.2479 4.00355 11.2719 3.84297 11.2305C3.68239 11.189 3.54485 11.0855 3.4606 10.9426C3.37636 10.7998 3.35231 10.6293 3.39375 10.4688C3.59656 9.71387 4.07598 9.06293 4.73672 8.64531C4.3856 8.29628 4.14602 7.85085 4.04835 7.36549C3.95068 6.88013 3.99932 6.3767 4.1881 5.91902C4.37688 5.46134 4.6973 5.07001 5.10875 4.79465C5.52019 4.51929 6.00413 4.37229 6.49922 4.37229C6.99431 4.37229 7.47825 4.51929 7.88969 4.79465C8.30114 5.07001 8.62156 5.46134 8.81034 5.91902C8.99912 6.3767 9.04776 6.88013 8.95009 7.36549C8.85242 7.85085 8.61284 8.29628 8.26172 8.64531C8.92318 9.06233 9.40303 9.71347 9.60547 10.4688H9.60469ZM6.5 8.125C6.74723 8.125 6.9889 8.05169 7.19446 7.91434C7.40002 7.77699 7.56024 7.58176 7.65485 7.35335C7.74946 7.12495 7.77421 6.87361 7.72598 6.63114C7.67775 6.38866 7.5587 6.16593 7.38388 5.99112C7.20907 5.8163 6.98634 5.69725 6.74386 5.64902C6.50139 5.60079 6.25005 5.62554 6.02165 5.72015C5.79324 5.81476 5.59801 5.97498 5.46066 6.18054C5.32331 6.3861 5.25 6.62777 5.25 6.875C5.25 7.20652 5.3817 7.52446 5.61612 7.75888C5.85054 7.9933 6.16848 8.125 6.5 8.125Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #licenseManagementIcon>
    <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M14.625 6.25C14.625 6.41576 14.5592 6.57473 14.4419 6.69194C14.3247 6.80915 14.1658 6.875 14 6.875H10.875C10.7092 6.875 10.5503 6.80915 10.4331 6.69194C10.3158 6.57473 10.25 6.41576 10.25 6.25C10.25 6.08424 10.3158 5.92527 10.4331 5.80806C10.5503 5.69085 10.7092 5.625 10.875 5.625H14C14.1658 5.625 14.3247 5.69085 14.4419 5.80806C14.5592 5.92527 14.625 6.08424 14.625 6.25ZM14 8.125H10.875C10.7092 8.125 10.5503 8.19085 10.4331 8.30806C10.3158 8.42527 10.25 8.58424 10.25 8.75C10.25 8.91576 10.3158 9.07473 10.4331 9.19194C10.5503 9.30915 10.7092 9.375 10.875 9.375H14C14.1658 9.375 14.3247 9.30915 14.4419 9.19194C14.5592 9.07473 14.625 8.91576 14.625 8.75C14.625 8.58424 14.5592 8.42527 14.4419 8.30806C14.3247 8.19085 14.1658 8.125 14 8.125ZM17.125 1.875V13.125C17.125 13.4565 16.9933 13.7745 16.7589 14.0089C16.5245 14.2433 16.2065 14.375 15.875 14.375H2.125C1.79348 14.375 1.47554 14.2433 1.24112 14.0089C1.0067 13.7745 0.875 13.4565 0.875 13.125V1.875C0.875 1.54348 1.0067 1.22554 1.24112 0.991116C1.47554 0.756696 1.79348 0.625 2.125 0.625H15.875C16.2065 0.625 16.5245 0.756696 16.7589 0.991116C16.9933 1.22554 17.125 1.54348 17.125 1.875ZM15.875 13.125V1.875H2.125V13.125H15.875ZM9.60469 10.4688C9.64613 10.6293 9.62208 10.7998 9.53784 10.9426C9.45359 11.0855 9.31605 11.189 9.15547 11.2305C8.99489 11.2719 8.82442 11.2479 8.68157 11.1636C8.53872 11.0794 8.43519 10.9418 8.39375 10.7812C8.18828 9.97969 7.37344 9.375 6.49922 9.375C5.625 9.375 4.81094 9.97969 4.60469 10.7812C4.56325 10.9418 4.45971 11.0794 4.31686 11.1636C4.17401 11.2479 4.00355 11.2719 3.84297 11.2305C3.68239 11.189 3.54485 11.0855 3.4606 10.9426C3.37636 10.7998 3.35231 10.6293 3.39375 10.4688C3.59656 9.71387 4.07598 9.06293 4.73672 8.64531C4.3856 8.29628 4.14602 7.85085 4.04835 7.36549C3.95068 6.88013 3.99932 6.3767 4.1881 5.91902C4.37688 5.46134 4.6973 5.07001 5.10875 4.79465C5.52019 4.51929 6.00413 4.37229 6.49922 4.37229C6.99431 4.37229 7.47825 4.51929 7.88969 4.79465C8.30114 5.07001 8.62156 5.46134 8.81034 5.91902C8.99912 6.3767 9.04776 6.88013 8.95009 7.36549C8.85242 7.85085 8.61284 8.29628 8.26172 8.64531C8.92318 9.06233 9.40303 9.71347 9.60547 10.4688H9.60469ZM6.5 8.125C6.74723 8.125 6.9889 8.05169 7.19446 7.91434C7.40002 7.77699 7.56024 7.58176 7.65485 7.35335C7.74946 7.12495 7.77421 6.87361 7.72598 6.63114C7.67775 6.38866 7.5587 6.16593 7.38388 5.99112C7.20907 5.8163 6.98634 5.69725 6.74386 5.64902C6.50139 5.60079 6.25005 5.62554 6.02165 5.72015C5.79324 5.81476 5.59801 5.97498 5.46066 6.18054C5.32331 6.3861 5.25 6.62777 5.25 6.875C5.25 7.20652 5.3817 7.52446 5.61612 7.75888C5.85054 7.9933 6.16848 8.125 6.5 8.125Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #generalAssemblyMeetingIcon>
    <svg width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M14.625 6.25C14.625 6.41576 14.5592 6.57473 14.4419 6.69194C14.3247 6.80915 14.1658 6.875 14 6.875H10.875C10.7092 6.875 10.5503 6.80915 10.4331 6.69194C10.3158 6.57473 10.25 6.41576 10.25 6.25C10.25 6.08424 10.3158 5.92527 10.4331 5.80806C10.5503 5.69085 10.7092 5.625 10.875 5.625H14C14.1658 5.625 14.3247 5.69085 14.4419 5.80806C14.5592 5.92527 14.625 6.08424 14.625 6.25ZM14 8.125H10.875C10.7092 8.125 10.5503 8.19085 10.4331 8.30806C10.3158 8.42527 10.25 8.58424 10.25 8.75C10.25 8.91576 10.3158 9.07473 10.4331 9.19194C10.5503 9.30915 10.7092 9.375 10.875 9.375H14C14.1658 9.375 14.3247 9.30915 14.4419 9.19194C14.5592 9.07473 14.625 8.91576 14.625 8.75C14.625 8.58424 14.5592 8.42527 14.4419 8.30806C14.3247 8.19085 14.1658 8.125 14 8.125ZM17.125 1.875V13.125C17.125 13.4565 16.9933 13.7745 16.7589 14.0089C16.5245 14.2433 16.2065 14.375 15.875 14.375H2.125C1.79348 14.375 1.47554 14.2433 1.24112 14.0089C1.0067 13.7745 0.875 13.4565 0.875 13.125V1.875C0.875 1.54348 1.0067 1.22554 1.24112 0.991116C1.47554 0.756696 1.79348 0.625 2.125 0.625H15.875C16.2065 0.625 16.5245 0.756696 16.7589 0.991116C16.9933 1.22554 17.125 1.54348 17.125 1.875ZM15.875 13.125V1.875H2.125V13.125H15.875ZM9.60469 10.4688C9.64613 10.6293 9.62208 10.7998 9.53784 10.9426C9.45359 11.0855 9.31605 11.189 9.15547 11.2305C8.99489 11.2719 8.82442 11.2479 8.68157 11.1636C8.53872 11.0794 8.43519 10.9418 8.39375 10.7812C8.18828 9.97969 7.37344 9.375 6.49922 9.375C5.625 9.375 4.81094 9.97969 4.60469 10.7812C4.56325 10.9418 4.45971 11.0794 4.31686 11.1636C4.17401 11.2479 4.00355 11.2719 3.84297 11.2305C3.68239 11.189 3.54485 11.0855 3.4606 10.9426C3.37636 10.7998 3.35231 10.6293 3.39375 10.4688C3.59656 9.71387 4.07598 9.06293 4.73672 8.64531C4.3856 8.29628 4.14602 7.85085 4.04835 7.36549C3.95068 6.88013 3.99932 6.3767 4.1881 5.91902C4.37688 5.46134 4.6973 5.07001 5.10875 4.79465C5.52019 4.51929 6.00413 4.37229 6.49922 4.37229C6.99431 4.37229 7.47825 4.51929 7.88969 4.79465C8.30114 5.07001 8.62156 5.46134 8.81034 5.91902C8.99912 6.3767 9.04776 6.88013 8.95009 7.36549C8.85242 7.85085 8.61284 8.29628 8.26172 8.64531C8.92318 9.06233 9.40303 9.71347 9.60547 10.4688H9.60469ZM6.5 8.125C6.74723 8.125 6.9889 8.05169 7.19446 7.91434C7.40002 7.77699 7.56024 7.58176 7.65485 7.35335C7.74946 7.12495 7.77421 6.87361 7.72598 6.63114C7.67775 6.38866 7.5587 6.16593 7.38388 5.99112C7.20907 5.8163 6.98634 5.69725 6.74386 5.64902C6.50139 5.60079 6.25005 5.62554 6.02165 5.72015C5.79324 5.81476 5.59801 5.97498 5.46066 6.18054C5.32331 6.3861 5.25 6.62777 5.25 6.875C5.25 7.20652 5.3817 7.52446 5.61612 7.75888C5.85054 7.9933 6.16848 8.125 6.5 8.125Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #fundraisingIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M15.875 2.625H9.625V1.375C9.625 1.20924 9.55915 1.05027 9.44194 0.933058C9.32473 0.815848 9.16576 0.75 9 0.75C8.83424 0.75 8.67527 0.815848 8.55806 0.933058C8.44085 1.05027 8.375 1.20924 8.375 1.375V2.625H2.125C1.79348 2.625 1.47554 2.7567 1.24112 2.99112C1.0067 3.22554 0.875 3.54348 0.875 3.875V13.25C0.875 13.5815 1.0067 13.8995 1.24112 14.1339C1.47554 14.3683 1.79348 14.5 2.125 14.5H5.2L3.51172 16.6094C3.40812 16.7389 3.36021 16.9042 3.37852 17.0691C3.39683 17.2339 3.47987 17.3847 3.60938 17.4883C3.73888 17.5919 3.90423 17.6398 4.06905 17.6215C4.23388 17.6032 4.38468 17.5201 4.48828 17.3906L6.8 14.5H11.2L13.5117 17.3906C13.563 17.4547 13.6264 17.5081 13.6984 17.5477C13.7703 17.5874 13.8493 17.6124 13.9309 17.6215C14.0126 17.6305 14.0952 17.6235 14.174 17.6006C14.2529 17.5777 14.3265 17.5396 14.3906 17.4883C14.4547 17.437 14.5081 17.3736 14.5477 17.3016C14.5874 17.2297 14.6124 17.1507 14.6215 17.0691C14.6305 16.9874 14.6235 16.9048 14.6006 16.826C14.5777 16.7471 14.5396 16.6735 14.4883 16.6094L12.8 14.5H15.875C16.2065 14.5 16.5245 14.3683 16.7589 14.1339C16.9933 13.8995 17.125 13.5815 17.125 13.25V3.875C17.125 3.54348 16.9933 3.22554 16.7589 2.99112C16.5245 2.7567 16.2065 2.625 15.875 2.625ZM15.875 13.25H2.125V3.875H15.875V13.25Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #eventsIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M17.9945 9.02032C17.7902 8.86281 17.5559 8.74855 17.3059 8.68448C17.0559 8.62042 16.7956 8.60789 16.5406 8.64767C18.0078 7.16642 18.75 5.69376 18.75 4.25001C18.75 2.18204 17.0867 0.500012 15.0422 0.500012C14.4997 0.496605 13.963 0.611985 13.4699 0.83805C12.9767 1.06411 12.539 1.39539 12.1875 1.80861C11.836 1.39539 11.3983 1.06411 10.9051 0.83805C10.412 0.611985 9.87531 0.496605 9.33281 0.500012C7.28828 0.500012 5.625 2.18204 5.625 4.25001C5.625 5.10939 5.87812 5.94454 6.41094 6.82814C5.97456 6.93869 5.57632 7.16549 5.25859 7.48439L3.49141 9.25001H1.25C0.918479 9.25001 0.600537 9.38171 0.366117 9.61613C0.131696 9.85055 0 10.1685 0 10.5L0 13.625C0 13.9565 0.131696 14.2745 0.366117 14.5089C0.600537 14.7433 0.918479 14.875 1.25 14.875H9.375C9.4261 14.875 9.47701 14.8687 9.52656 14.8563L14.5266 13.6063C14.5584 13.5987 14.5896 13.5882 14.6195 13.575L17.6562 12.2828L17.6906 12.2672C17.9825 12.1214 18.2324 11.9036 18.4168 11.6345C18.6013 11.3654 18.7142 11.0537 18.7449 10.7289C18.7756 10.4041 18.7231 10.0768 18.5924 9.77792C18.4617 9.479 18.257 9.21828 17.9977 9.02032H17.9945ZM9.33281 1.75001C9.81688 1.74293 10.2921 1.88038 10.6976 2.1448C11.1031 2.40922 11.4206 2.78859 11.6094 3.23439C11.6565 3.34902 11.7366 3.44706 11.8395 3.51607C11.9424 3.58507 12.0636 3.62191 12.1875 3.62191C12.3114 3.62191 12.4326 3.58507 12.5355 3.51607C12.6384 3.44706 12.7185 3.34902 12.7656 3.23439C12.9544 2.78859 13.2719 2.40922 13.6774 2.1448C14.0829 1.88038 14.5581 1.74293 15.0422 1.75001C16.3742 1.75001 17.5 2.89454 17.5 4.25001C17.5 5.77423 16.2664 7.49845 13.9328 9.2422L13.0664 9.44142C13.1425 9.12016 13.1448 8.78586 13.0733 8.46357C13.0017 8.14127 12.8582 7.83935 12.6534 7.58043C12.4486 7.3215 12.1878 7.11229 11.8907 6.96848C11.5935 6.82467 11.2676 6.74998 10.9375 6.75001H7.86562C7.19141 5.84064 6.875 5.04064 6.875 4.25001C6.875 2.89454 8.00078 1.75001 9.33281 1.75001ZM1.25 10.5H3.125V13.625H1.25V10.5ZM17.143 11.1414L14.1742 12.4055L9.29688 13.625H4.375V10.1336L6.14297 8.36642C6.25862 8.24984 6.39629 8.15741 6.54798 8.09451C6.69967 8.03161 6.86235 7.99949 7.02656 8.00001H10.9375C11.1861 8.00001 11.4246 8.09878 11.6004 8.2746C11.7762 8.45042 11.875 8.68887 11.875 8.93751C11.875 9.18615 11.7762 9.42461 11.6004 9.60042C11.4246 9.77624 11.1861 9.87501 10.9375 9.87501H8.75C8.58424 9.87501 8.42527 9.94086 8.30806 10.0581C8.19085 10.1753 8.125 10.3343 8.125 10.5C8.125 10.6658 8.19085 10.8247 8.30806 10.942C8.42527 11.0592 8.58424 11.125 8.75 11.125H11.25C11.297 11.1249 11.3439 11.1196 11.3898 11.1094L16.6242 9.90548L16.6484 9.89923C16.8082 9.85487 16.9788 9.87117 17.1273 9.945C17.2758 10.0188 17.3917 10.145 17.4528 10.2991C17.5139 10.4533 17.5158 10.6246 17.4582 10.7801C17.4005 10.9356 17.2874 11.0643 17.1406 11.1414H17.143Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #investmentIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M9 0.875C7.39303 0.875 5.82214 1.35152 4.486 2.24431C3.14985 3.1371 2.10844 4.40605 1.49348 5.8907C0.87852 7.37535 0.717618 9.00901 1.03112 10.5851C1.34463 12.1612 2.11846 13.6089 3.25476 14.7452C4.39106 15.8815 5.8388 16.6554 7.4149 16.9689C8.99099 17.2824 10.6247 17.1215 12.1093 16.5065C13.594 15.8916 14.8629 14.8502 15.7557 13.514C16.6485 12.1779 17.125 10.607 17.125 9C17.1227 6.84581 16.266 4.78051 14.7427 3.25727C13.2195 1.73403 11.1542 0.877275 9 0.875ZM9 15.875C7.64026 15.875 6.31105 15.4718 5.18046 14.7164C4.04987 13.9609 3.16868 12.8872 2.64833 11.6309C2.12798 10.3747 1.99183 8.99237 2.2571 7.65875C2.52238 6.32513 3.17716 5.10013 4.13864 4.13864C5.10013 3.17716 6.32514 2.52237 7.65876 2.2571C8.99238 1.99183 10.3747 2.12798 11.631 2.64833C12.8872 3.16868 13.9609 4.04987 14.7164 5.18045C15.4718 6.31104 15.875 7.64025 15.875 9C15.8729 10.8227 15.1479 12.5702 13.8591 13.8591C12.5702 15.1479 10.8227 15.8729 9 15.875ZM12.125 10.5625C12.125 11.1427 11.8945 11.6991 11.4843 12.1093C11.0741 12.5195 10.5177 12.75 9.9375 12.75H9.625V13.375C9.625 13.5408 9.55916 13.6997 9.44195 13.8169C9.32474 13.9342 9.16576 14 9 14C8.83424 14 8.67527 13.9342 8.55806 13.8169C8.44085 13.6997 8.375 13.5408 8.375 13.375V12.75H7.125C6.95924 12.75 6.80027 12.6842 6.68306 12.5669C6.56585 12.4497 6.5 12.2908 6.5 12.125C6.5 11.9592 6.56585 11.8003 6.68306 11.6831C6.80027 11.5658 6.95924 11.5 7.125 11.5H9.9375C10.1861 11.5 10.4246 11.4012 10.6004 11.2254C10.7762 11.0496 10.875 10.8111 10.875 10.5625C10.875 10.3139 10.7762 10.0754 10.6004 9.89959C10.4246 9.72377 10.1861 9.625 9.9375 9.625H8.0625C7.48234 9.625 6.92594 9.39453 6.51571 8.9843C6.10547 8.57406 5.875 8.01766 5.875 7.4375C5.875 6.85734 6.10547 6.30094 6.51571 5.8907C6.92594 5.48047 7.48234 5.25 8.0625 5.25H8.375V4.625C8.375 4.45924 8.44085 4.30027 8.55806 4.18306C8.67527 4.06585 8.83424 4 9 4C9.16576 4 9.32474 4.06585 9.44195 4.18306C9.55916 4.30027 9.625 4.45924 9.625 4.625V5.25H10.875C11.0408 5.25 11.1997 5.31585 11.3169 5.43306C11.4342 5.55027 11.5 5.70924 11.5 5.875C11.5 6.04076 11.4342 6.19973 11.3169 6.31694C11.1997 6.43415 11.0408 6.5 10.875 6.5H8.0625C7.81386 6.5 7.57541 6.59877 7.39959 6.77459C7.22378 6.9504 7.125 7.18886 7.125 7.4375C7.125 7.68614 7.22378 7.9246 7.39959 8.10041C7.57541 8.27623 7.81386 8.375 8.0625 8.375H9.9375C10.5177 8.375 11.0741 8.60547 11.4843 9.0157C11.8945 9.42594 12.125 9.98234 12.125 10.5625Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #generalRequestsIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M14.25 1.5H12.375V0.875C12.375 0.70924 12.3092 0.550268 12.1919 0.433058C12.0747 0.315848 11.9158 0.25 11.75 0.25C11.5842 0.25 11.4253 0.315848 11.3081 0.433058C11.1908 0.550268 11.125 0.70924 11.125 0.875V1.5H4.875V0.875C4.875 0.70924 4.80915 0.550268 4.69194 0.433058C4.57473 0.315848 4.41576 0.25 4.25 0.25C4.08424 0.25 3.92527 0.315848 3.80806 0.433058C3.69085 0.550268 3.625 0.70924 3.625 0.875V1.5H1.75C1.41848 1.5 1.10054 1.6317 0.866116 1.86612C0.631696 2.10054 0.5 2.41848 0.5 2.75V15.25C0.5 15.5815 0.631696 15.8995 0.866116 16.1339C1.10054 16.3683 1.41848 16.5 1.75 16.5H14.25C14.5815 16.5 14.8995 16.3683 15.1339 16.1339C15.3683 15.8995 15.5 15.5815 15.5 15.25V2.75C15.5 2.41848 15.3683 2.10054 15.1339 1.86612C14.8995 1.6317 14.5815 1.5 14.25 1.5ZM3.625 2.75V3.375C3.625 3.54076 3.69085 3.69973 3.80806 3.81694C3.92527 3.93415 4.08424 4 4.25 4C4.41576 4 4.57473 3.93415 4.69194 3.81694C4.80915 3.69973 4.875 3.54076 4.875 3.375V2.75H11.125V3.375C11.125 3.54076 11.1908 3.69973 11.3081 3.81694C11.4253 3.93415 11.5842 4 11.75 4C11.9158 4 12.0747 3.93415 12.1919 3.81694C12.3092 3.69973 12.375 3.54076 12.375 3.375V2.75H14.25V5.25H1.75V2.75H3.625ZM14.25 15.25H1.75V6.5H14.25V15.25Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #voluntaryDissolutionAndLiquidationIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M10 2.875C9.38193 2.875 8.77775 3.05828 8.26384 3.40166C7.74994 3.74504 7.3494 4.2331 7.11288 4.80411C6.87635 5.37513 6.81447 6.00347 6.93505 6.60966C7.05562 7.21585 7.35325 7.77267 7.79029 8.20971C8.22733 8.64675 8.78415 8.94438 9.39034 9.06495C9.99653 9.18553 10.6249 9.12365 11.1959 8.88712C11.7669 8.6506 12.255 8.25006 12.5983 7.73616C12.9417 7.22225 13.125 6.61807 13.125 6C13.125 5.1712 12.7958 4.37634 12.2097 3.79029C11.6237 3.20424 10.8288 2.875 10 2.875ZM10 7.875C9.62916 7.875 9.26665 7.76503 8.95831 7.55901C8.64996 7.35298 8.40964 7.06014 8.26773 6.71753C8.12581 6.37492 8.08868 5.99792 8.16103 5.63421C8.23337 5.27049 8.41195 4.9364 8.67417 4.67417C8.9364 4.41195 9.27049 4.23337 9.63421 4.16103C9.99792 4.08868 10.3749 4.12581 10.7175 4.26773C11.0601 4.40964 11.353 4.64996 11.559 4.95831C11.765 5.26665 11.875 5.62916 11.875 6C11.875 6.49728 11.6775 6.97419 11.3258 7.32583C10.9742 7.67746 10.4973 7.875 10 7.875ZM18.75 0.375H1.25C1.08424 0.375 0.925268 0.440848 0.808058 0.558058C0.690848 0.675269 0.625 0.83424 0.625 1V11C0.625 11.1658 0.690848 11.3247 0.808058 11.4419C0.925268 11.5592 1.08424 11.625 1.25 11.625H18.75C18.9158 11.625 19.0747 11.5592 19.1919 11.4419C19.3092 11.3247 19.375 11.1658 19.375 11V1C19.375 0.83424 19.3092 0.675269 19.1919 0.558058C19.0747 0.440848 18.9158 0.375 18.75 0.375ZM15.1289 10.375H4.87109C4.66125 9.66531 4.27719 9.01941 3.75389 8.49611C3.23059 7.97281 2.58468 7.58875 1.875 7.37891V4.62109C2.58468 4.41125 3.23059 4.02719 3.75389 3.50389C4.27719 2.98059 4.66125 2.33468 4.87109 1.625H15.1289C15.3387 2.33468 15.7228 2.98059 16.2461 3.50389C16.7694 4.02719 17.4153 4.41125 18.125 4.62109V7.37891C17.4153 7.58875 16.7694 7.97281 16.2461 8.49611C15.7228 9.01941 15.3387 9.66531 15.1289 10.375ZM18.125 3.29453C17.3753 2.97218 16.7778 2.37466 16.4555 1.625H18.125V3.29453ZM3.54453 1.625C3.22218 2.37466 2.62466 2.97218 1.875 3.29453V1.625H3.54453ZM1.875 8.70547C2.62466 9.02782 3.22218 9.62534 3.54453 10.375H1.875V8.70547ZM16.4555 10.375C16.7778 9.62534 17.3753 9.02782 18.125 8.70547V10.375H16.4555Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #decisionsAndRegulationsIcon>
    <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M15.7594 4.73203L12.268 1.2414C12.1519 1.1253 12.0141 1.0332 11.8624 0.970361C11.7107 0.907525 11.5482 0.875183 11.384 0.875183C11.2198 0.875183 11.0572 0.907525 10.9056 0.970361C10.7539 1.0332 10.6161 1.1253 10.5 1.2414L0.866412 10.875C0.749834 10.9906 0.657407 11.1283 0.594506 11.28C0.531604 11.4317 0.499482 11.5944 0.500006 11.7586V15.25C0.500006 15.5815 0.631702 15.8995 0.866123 16.1339C1.10054 16.3683 1.41849 16.5 1.75001 16.5H5.24141C5.40562 16.5005 5.5683 16.4684 5.71999 16.4055C5.87168 16.3426 6.00935 16.2502 6.12501 16.1336L12.6617 9.59765L12.9336 10.6836L10.0586 13.5578C9.94132 13.675 9.8754 13.8339 9.87533 13.9997C9.87525 14.1655 9.94104 14.3245 10.0582 14.4418C10.1754 14.5591 10.3343 14.625 10.5001 14.6251C10.6659 14.6251 10.8249 14.5594 10.9422 14.4422L14.0672 11.3172C14.1436 11.2409 14.199 11.1461 14.2279 11.042C14.2568 10.938 14.2582 10.8282 14.232 10.7234L13.693 8.5664L15.7594 6.49999C15.8755 6.38392 15.9676 6.24611 16.0304 6.09443C16.0933 5.94275 16.1256 5.78019 16.1256 5.61601C16.1256 5.45183 16.0933 5.28927 16.0304 5.13759C15.9676 4.98591 15.8755 4.8481 15.7594 4.73203ZM1.75001 13.0086L3.99141 15.25H1.75001V13.0086ZM5.50001 14.9914L2.0086 11.5L8.62501 4.88359L12.1164 8.37499L5.50001 14.9914ZM13 7.4914L9.50938 3.99999L11.3844 2.12499L14.875 5.6164L13 7.4914Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #joiningAndAffiliatingAssociationsIcon>
    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_10438_14383)">
            <path
                d="M5.89783 2.86984C7.12204 2.08524 8.53333 1.67188 10 1.67188C11.4667 1.67188 12.878 2.08524 14.1022 2.86984C14.1164 4.15021 15.1617 5.1875 16.4453 5.1875C17.7377 5.1875 18.7891 4.13617 18.7891 2.84375C18.7891 1.55133 17.7377 0.5 16.4453 0.5C15.5743 0.5 14.8135 0.977905 14.4095 1.68469C13.0739 0.907867 11.5642 0.5 10 0.5C8.43582 0.5 6.92612 0.907867 5.59052 1.68469C5.18646 0.977905 4.42566 0.5 3.55469 0.5C2.26227 0.5 1.21094 1.55133 1.21094 2.84375C1.21094 4.13617 2.26227 5.1875 3.55469 5.1875C4.83826 5.1875 5.88364 4.15021 5.89783 2.86984ZM16.4453 1.67188C17.0915 1.67188 17.6172 2.19754 17.6172 2.84375C17.6172 3.48996 17.0915 4.01562 16.4453 4.01562C15.7991 4.01562 15.2734 3.48996 15.2734 2.84375C15.2734 2.19754 15.7991 1.67188 16.4453 1.67188ZM3.55469 4.01562C2.90848 4.01562 2.38281 3.48996 2.38281 2.84375C2.38281 2.19754 2.90848 1.67188 3.55469 1.67188C4.2009 1.67188 4.72656 2.19754 4.72656 2.84375C4.72656 3.48996 4.2009 4.01562 3.55469 4.01562Z"
                fill="currentColor" />
            <path
                d="M12.3438 12.2578C12.3438 10.9654 11.2924 9.91406 10 9.91406C8.70758 9.91406 7.65625 10.9654 7.65625 12.2578C7.65625 13.5502 8.70758 14.6016 10 14.6016C11.2924 14.6016 12.3438 13.5502 12.3438 12.2578ZM10 13.4297C9.35379 13.4297 8.82812 12.904 8.82812 12.2578C8.82812 11.6116 9.35379 11.0859 10 11.0859C10.6462 11.0859 11.1719 11.6116 11.1719 12.2578C11.1719 12.904 10.6462 13.4297 10 13.4297Z"
                fill="currentColor" />
            <path
                d="M16.4453 5.1875C14.5068 5.1875 12.9297 6.76465 12.9297 8.70312V10.5C12.9297 10.8236 13.192 11.0782 13.5156 11.0782H17.4138C16.8636 13.4207 15.242 15.3663 13.022 16.3228C12.4083 15.2929 11.2834 14.6016 10 14.6016C8.71658 14.6016 7.59171 15.2929 6.978 16.3228C4.758 15.3663 3.13644 13.4285 2.58621 11.0859H6.48438C6.80801 11.0859 7.07031 10.8236 7.07031 10.5V8.70312C7.07031 6.76465 5.49316 5.1875 3.55469 5.1875C1.61621 5.1875 0.0390625 6.76465 0.0390625 8.70312V10.5C0.0390625 10.8236 0.301361 11.0859 0.625 11.0859H1.38733C1.67328 12.4926 2.30209 13.811 3.22311 14.925C4.12277 16.0132 5.26642 16.8664 6.55472 17.4159C6.50864 17.6425 6.48438 17.877 6.48438 18.1172V19.9141C6.48438 20.2377 6.74667 20.5 7.07031 20.5H12.9297C13.2533 20.5 13.5156 20.2377 13.5156 19.9141V18.1172C13.5156 17.877 13.4914 17.6425 13.4453 17.4157C14.7336 16.8664 15.8774 16.0132 16.7769 14.925C17.6979 13.811 18.3267 12.4925 18.6127 11.0859H19.375C19.6986 11.0859 19.9609 10.8235 19.9609 10.5V8.70312C19.9609 6.76465 18.3838 5.1875 16.4453 5.1875ZM1.21094 8.70312C1.21094 7.41071 2.26227 6.35938 3.55469 6.35938C4.84711 6.35938 5.89844 7.41071 5.89844 8.70312V9.91406H1.21094V8.70312ZM12.3438 19.3281H7.65625V18.1172C7.65625 16.8248 8.70758 15.7734 10 15.7734C11.2924 15.7734 12.3438 16.8248 12.3438 18.1172V19.3281ZM18.7891 9.91406H14.1016V8.70312C14.1016 7.41071 15.1529 6.35938 16.4453 6.35938C17.7377 6.35938 18.7891 7.41071 18.7891 8.70312V9.91406Z"
                fill="currentColor" />
        </g>
        <defs>
            <clipPath id="clip0_10438_14383">
                <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
            </clipPath>
        </defs>
    </svg>

</ng-template>
<ng-template #organizingEventsAndActivitiesIcon>
    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_10438_14389)">
            <path
                d="M9.1273 14.4651C9.23808 14.5758 9.38822 14.638 9.54493 14.638C9.70148 14.638 9.85163 14.5759 9.96241 14.4651L12.0707 12.3568C12.3014 12.1261 12.3014 11.7523 12.0707 11.5216C11.8402 11.291 11.4662 11.291 11.2356 11.5216L9.54493 13.2122L8.77406 12.4414C8.54334 12.2108 8.1695 12.2108 7.93879 12.4414C7.70823 12.6719 7.70823 13.0459 7.93879 13.2766L9.1273 14.4651Z"
                fill="currentColor" />
            <path
                d="M10.0048 17.6814C12.5898 17.6814 14.6927 15.5782 14.6927 12.9933C14.6927 10.4083 12.5898 8.3053 10.0048 8.3053C7.41962 8.3053 5.31665 10.4083 5.31665 12.9933C5.31665 15.5782 7.41977 17.6814 10.0048 17.6814ZM10.0048 9.48633C11.9385 9.48633 13.5117 11.0595 13.5117 12.9933C13.5117 14.927 11.9385 16.5003 10.0048 16.5003C8.07101 16.5003 6.49768 14.927 6.49768 12.9933C6.49768 11.0595 8.07101 9.48633 10.0048 9.48633Z"
                fill="currentColor" />
            <path
                d="M17.865 1.75748H16.7879V1.09052C16.7879 0.764435 16.5234 0.5 16.1974 0.5C15.8713 0.5 15.6068 0.764435 15.6068 1.09052V1.75748H14.4464V1.09052C14.4464 0.764435 14.182 0.5 13.8557 0.5C13.5297 0.5 13.2652 0.764435 13.2652 1.09052V1.75748H6.74423V1.09052C6.74423 0.764435 6.4798 0.5 6.15356 0.5C5.82748 0.5 5.56305 0.764435 5.56305 1.09052V1.75748H4.40262V1.09052C4.40262 0.764435 4.13818 0.5 3.81195 0.5C3.48587 0.5 3.22144 0.764435 3.22144 1.09052V1.75748H2.14417C1.09116 1.75748 0.234375 2.61426 0.234375 3.66742V18.5902C0.234375 19.6432 1.09116 20.5 2.14417 20.5H17.865C18.9182 20.5 19.7748 19.6432 19.7748 18.5902V3.66742C19.7748 2.61426 18.918 1.75748 17.865 1.75748ZM18.5936 18.5902C18.5936 18.992 18.2668 19.319 17.865 19.319H2.14417C1.7424 19.319 1.41541 18.992 1.41541 18.5902V6.66791H18.5936V18.5902ZM1.41541 3.66742C1.41541 3.2655 1.7424 2.93866 2.14417 2.93866H3.22144V3.60562C3.22144 3.9317 3.48587 4.19614 3.8121 4.19614C4.13818 4.19614 4.40262 3.9317 4.40262 3.60562V2.93866H5.56305V3.60562C5.56305 3.9317 5.82748 4.19614 6.15372 4.19614C6.4798 4.19614 6.74423 3.9317 6.74423 3.60562V2.93866H13.2652V3.60562C13.2652 3.9317 13.5297 4.19614 13.8559 4.19614C14.182 4.19614 14.4464 3.9317 14.4464 3.60562V2.93866H15.6068V3.60562C15.6068 3.9317 15.8713 4.19614 16.1974 4.19614C16.5236 4.19614 16.788 3.9317 16.788 3.60562V2.93866H17.865C18.2668 2.93866 18.5938 3.2655 18.5938 3.66742V5.48672H1.41541V3.66742Z"
                fill="currentColor" />
        </g>
        <defs>
            <clipPath id="clip0_10438_14389">
                <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
            </clipPath>
        </defs>
    </svg>

</ng-template>
<ng-template #participateInActivitiesAndEventsInsideAndOutsideUAEIcon>
    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clip-path="url(#clip0_10438_14389)">
            <path
                d="M9.1273 14.4651C9.23808 14.5758 9.38822 14.638 9.54493 14.638C9.70148 14.638 9.85163 14.5759 9.96241 14.4651L12.0707 12.3568C12.3014 12.1261 12.3014 11.7523 12.0707 11.5216C11.8402 11.291 11.4662 11.291 11.2356 11.5216L9.54493 13.2122L8.77406 12.4414C8.54334 12.2108 8.1695 12.2108 7.93879 12.4414C7.70823 12.6719 7.70823 13.0459 7.93879 13.2766L9.1273 14.4651Z"
                fill="currentColor" />
            <path
                d="M10.0048 17.6814C12.5898 17.6814 14.6927 15.5782 14.6927 12.9933C14.6927 10.4083 12.5898 8.3053 10.0048 8.3053C7.41962 8.3053 5.31665 10.4083 5.31665 12.9933C5.31665 15.5782 7.41977 17.6814 10.0048 17.6814ZM10.0048 9.48633C11.9385 9.48633 13.5117 11.0595 13.5117 12.9933C13.5117 14.927 11.9385 16.5003 10.0048 16.5003C8.07101 16.5003 6.49768 14.927 6.49768 12.9933C6.49768 11.0595 8.07101 9.48633 10.0048 9.48633Z"
                fill="currentColor" />
            <path
                d="M17.865 1.75748H16.7879V1.09052C16.7879 0.764435 16.5234 0.5 16.1974 0.5C15.8713 0.5 15.6068 0.764435 15.6068 1.09052V1.75748H14.4464V1.09052C14.4464 0.764435 14.182 0.5 13.8557 0.5C13.5297 0.5 13.2652 0.764435 13.2652 1.09052V1.75748H6.74423V1.09052C6.74423 0.764435 6.4798 0.5 6.15356 0.5C5.82748 0.5 5.56305 0.764435 5.56305 1.09052V1.75748H4.40262V1.09052C4.40262 0.764435 4.13818 0.5 3.81195 0.5C3.48587 0.5 3.22144 0.764435 3.22144 1.09052V1.75748H2.14417C1.09116 1.75748 0.234375 2.61426 0.234375 3.66742V18.5902C0.234375 19.6432 1.09116 20.5 2.14417 20.5H17.865C18.9182 20.5 19.7748 19.6432 19.7748 18.5902V3.66742C19.7748 2.61426 18.918 1.75748 17.865 1.75748ZM18.5936 18.5902C18.5936 18.992 18.2668 19.319 17.865 19.319H2.14417C1.7424 19.319 1.41541 18.992 1.41541 18.5902V6.66791H18.5936V18.5902ZM1.41541 3.66742C1.41541 3.2655 1.7424 2.93866 2.14417 2.93866H3.22144V3.60562C3.22144 3.9317 3.48587 4.19614 3.8121 4.19614C4.13818 4.19614 4.40262 3.9317 4.40262 3.60562V2.93866H5.56305V3.60562C5.56305 3.9317 5.82748 4.19614 6.15372 4.19614C6.4798 4.19614 6.74423 3.9317 6.74423 3.60562V2.93866H13.2652V3.60562C13.2652 3.9317 13.5297 4.19614 13.8559 4.19614C14.182 4.19614 14.4464 3.9317 14.4464 3.60562V2.93866H15.6068V3.60562C15.6068 3.9317 15.8713 4.19614 16.1974 4.19614C16.5236 4.19614 16.788 3.9317 16.788 3.60562V2.93866H17.865C18.2668 2.93866 18.5938 3.2655 18.5938 3.66742V5.48672H1.41541V3.66742Z"
                fill="currentColor" />
        </g>
        <defs>
            <clipPath id="clip0_10438_14389">
                <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
            </clipPath>
        </defs>
    </svg>

</ng-template>

<ng-template #manageBranchesIcon>
    <svg width="14" height="19" viewBox="0 0 14 19" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M12.625 17H8.76094C9.41004 16.4204 10.0222 15.8007 10.5938 15.1445C12.7383 12.6781 13.875 10.0781 13.875 7.625C13.875 5.80164 13.1507 4.05295 11.8614 2.76364C10.572 1.47433 8.82336 0.75 7 0.75C5.17664 0.75 3.42795 1.47433 2.13864 2.76364C0.849328 4.05295 0.125 5.80164 0.125 7.625C0.125 10.0781 1.25859 12.6781 3.40625 15.1445C3.97782 15.8007 4.58996 16.4204 5.23906 17H1.375C1.20924 17 1.05027 17.0658 0.933058 17.1831C0.815848 17.3003 0.75 17.4592 0.75 17.625C0.75 17.7908 0.815848 17.9497 0.933058 18.0669C1.05027 18.1842 1.20924 18.25 1.375 18.25H12.625C12.7908 18.25 12.9497 18.1842 13.0669 18.0669C13.1842 17.9497 13.25 17.7908 13.25 17.625C13.25 17.4592 13.1842 17.3003 13.0669 17.1831C12.9497 17.0658 12.7908 17 12.625 17ZM1.375 7.625C1.375 6.13316 1.96763 4.70242 3.02252 3.64752C4.07742 2.59263 5.50816 2 7 2C8.49184 2 9.92258 2.59263 10.9775 3.64752C12.0324 4.70242 12.625 6.13316 12.625 7.625C12.625 12.0961 8.29141 15.8281 7 16.8438C5.70859 15.8281 1.375 12.0961 1.375 7.625ZM10.125 7.625C10.125 7.00693 9.94172 6.40275 9.59834 5.88884C9.25496 5.37494 8.7669 4.9744 8.19589 4.73788C7.62487 4.50135 6.99653 4.43947 6.39034 4.56005C5.78415 4.68062 5.22733 4.97825 4.79029 5.41529C4.35325 5.85233 4.05562 6.40915 3.93505 7.01534C3.81447 7.62153 3.87635 8.24987 4.11288 8.82089C4.3494 9.3919 4.74994 9.87996 5.26384 10.2233C5.77775 10.5667 6.38193 10.75 7 10.75C7.8288 10.75 8.62366 10.4208 9.20971 9.83471C9.79576 9.24866 10.125 8.4538 10.125 7.625ZM5.125 7.625C5.125 7.25416 5.23497 6.89165 5.44099 6.58331C5.64702 6.27496 5.93986 6.03464 6.28247 5.89273C6.62508 5.75081 7.00208 5.71368 7.3658 5.78603C7.72951 5.85837 8.0636 6.03695 8.32583 6.29917C8.58805 6.5614 8.76663 6.89549 8.83897 7.25921C8.91132 7.62292 8.87419 7.99992 8.73227 8.34253C8.59036 8.68514 8.35004 8.97798 8.04169 9.18401C7.73335 9.39003 7.37084 9.5 7 9.5C6.50272 9.5 6.02581 9.30246 5.67417 8.95083C5.32254 8.59919 5.125 8.12228 5.125 7.625Z"
            fill="currentColor" />
    </svg>
</ng-template>

<ng-template #nocRequestIcon>
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M6.875 7.5C6.875 7.33424 6.94085 7.17527 7.05806 7.05806C7.17527 6.94085 7.33424 6.875 7.5 6.875H12.5C12.6658 6.875 12.8247 6.94085 12.9419 7.05806C13.0592 7.17527 13.125 7.33424 13.125 7.5C13.125 7.66576 13.0592 7.82473 12.9419 7.94194C12.8247 8.05915 12.6658 8.125 12.5 8.125H7.5C7.33424 8.125 7.17527 8.05915 7.05806 7.94194C6.94085 7.82473 6.875 7.66576 6.875 7.5ZM7.5 10.625H12.5C12.6658 10.625 12.8247 10.5592 12.9419 10.4419C13.0592 10.3247 13.125 10.1658 13.125 10C13.125 9.83424 13.0592 9.67527 12.9419 9.55806C12.8247 9.44085 12.6658 9.375 12.5 9.375H7.5C7.33424 9.375 7.17527 9.44085 7.05806 9.55806C6.94085 9.67527 6.875 9.83424 6.875 10C6.875 10.1658 6.94085 10.3247 7.05806 10.4419C7.17527 10.5592 7.33424 10.625 7.5 10.625ZM10 11.875H7.5C7.33424 11.875 7.17527 11.9408 7.05806 12.0581C6.94085 12.1753 6.875 12.3342 6.875 12.5C6.875 12.6658 6.94085 12.8247 7.05806 12.9419C7.17527 13.0592 7.33424 13.125 7.5 13.125H10C10.1658 13.125 10.3247 13.0592 10.4419 12.9419C10.5592 12.8247 10.625 12.6658 10.625 12.5C10.625 12.3342 10.5592 12.1753 10.4419 12.0581C10.3247 11.9408 10.1658 11.875 10 11.875ZM17.5 3.75V12.2414C17.5005 12.4056 17.4684 12.5683 17.4055 12.72C17.3426 12.8717 17.2502 13.0093 17.1336 13.125L13.125 17.1336C13.0093 17.2502 12.8717 17.3426 12.72 17.4055C12.5683 17.4684 12.4056 17.5005 12.2414 17.5H3.75C3.41848 17.5 3.10054 17.3683 2.86612 17.1339C2.6317 16.8995 2.5 16.5815 2.5 16.25V3.75C2.5 3.41848 2.6317 3.10054 2.86612 2.86612C3.10054 2.6317 3.41848 2.5 3.75 2.5H16.25C16.5815 2.5 16.8995 2.6317 17.1339 2.86612C17.3683 3.10054 17.5 3.41848 17.5 3.75ZM3.75 16.25H11.875V12.5C11.875 12.3342 11.9408 12.1753 12.0581 12.0581C12.1753 11.9408 12.3342 11.875 12.5 11.875H16.25V3.75H3.75V16.25ZM13.125 13.125V15.3672L15.3664 13.125H13.125Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #banksIcon>
    <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
            d="M1.875 6.62498H3.75V11.625H2.5C2.33424 11.625 2.17527 11.6908 2.05806 11.808C1.94085 11.9252 1.875 12.0842 1.875 12.25C1.875 12.4157 1.94085 12.5747 2.05806 12.6919C2.17527 12.8091 2.33424 12.875 2.5 12.875H17.5C17.6658 12.875 17.8247 12.8091 17.9419 12.6919C18.0592 12.5747 18.125 12.4157 18.125 12.25C18.125 12.0842 18.0592 11.9252 17.9419 11.808C17.8247 11.6908 17.6658 11.625 17.5 11.625H16.25V6.62498H18.125C18.261 6.62484 18.3932 6.58035 18.5016 6.49827C18.6101 6.41618 18.6887 6.30097 18.7258 6.17012C18.7628 6.03927 18.7561 5.89992 18.7068 5.7732C18.6574 5.64648 18.5681 5.53931 18.4523 5.46795L10.3273 0.467946C10.2289 0.407413 10.1156 0.375366 10 0.375366C9.88442 0.375366 9.77111 0.407413 9.67266 0.467946L1.54766 5.46795C1.4319 5.53931 1.34257 5.64648 1.29323 5.7732C1.24388 5.89992 1.23722 6.03927 1.27424 6.17012C1.31126 6.30097 1.38994 6.41618 1.49836 6.49827C1.60678 6.58035 1.73901 6.62484 1.875 6.62498ZM5 6.62498H7.5V11.625H5V6.62498ZM11.25 6.62498V11.625H8.75V6.62498H11.25ZM15 11.625H12.5V6.62498H15V11.625ZM10 1.73357L15.9172 5.37498H4.08281L10 1.73357ZM19.375 14.75C19.375 14.9157 19.3092 15.0747 19.1919 15.1919C19.0747 15.3091 18.9158 15.375 18.75 15.375H1.25C1.08424 15.375 0.925268 15.3091 0.808058 15.1919C0.690848 15.0747 0.625 14.9157 0.625 14.75C0.625 14.5842 0.690848 14.4252 0.808058 14.308C0.925268 14.1908 1.08424 14.125 1.25 14.125H18.75C18.9158 14.125 19.0747 14.1908 19.1919 14.308C19.3092 14.4252 19.375 14.5842 19.375 14.75Z"
            fill="currentColor" />
    </svg>
</ng-template>
<ng-template #membersListIcon>
    
</ng-template>