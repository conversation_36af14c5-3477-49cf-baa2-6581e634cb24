import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Feedback, FileType } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Attachment } from '../../../../../../shared/models/attachment.model';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { Lookup } from '../../../../../../shared/models/lookup.model';

@Component({
  selector: 'app-establishment-upload-documents',
  templateUrl: './establishment-upload-documents.component.html',
  styleUrls: ['./establishment-upload-documents.component.scss']
})
export class EstablishmentUploadDocumentsComponent extends MyEstablishmentComponentBase
  implements OnInit, OnChanges {


  get f(): any { return this.form.controls; }
  attachments: Attachment[] = [];
  attachmentsData: any[] = [];
  documentList: any[] = [];
  requestId: any;
  establishmentId: any;
  serviceCatalogues: any[] = [];
  NPO_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO DeclarationServiceCatalogue';
  NPO_DECREE_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO Declaration By DecreeServiceCatalogue';
  documents: any[] = [];
  NPO_LINCEING_DELEACRTION: string = "NPO License Declaration";
  NPO_LINCEING: string = "NPO Declaration";
  @Input() selectedLegalFormType: any;
  @Input() form: FormGroup;
  @Input() legalFormType: any;
  @Input() legalFromTypes: Lookup[] = [];
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;

  @Input() npoRequestId: string | undefined;
  @Input() establishmentRequestId: string | undefined;
  @Input() esablishmentStatusCode: number | undefined;
  @Input() migrationID: string | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.uploadDocuments.';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.form = this.FormBuilder.group({});
    this.MyEstablishmentService.lookupData$.subscribe(data => {
      if (data) {
        this.documentList = data.DocumentType;
        this.reportParameter = data.ReportParametre;
        this.serviceCatalogues = data.CrmConfiguration;

        if (this.legalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree || this.legalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree) {
          const service = this.serviceCatalogues.find(_ => _.Name.toLocaleLowerCase().trim() == this.NPO_DECREE_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim());
          if (service) {
            this.documentList = this.documentList.filter(_ => _.ServiceCatalogue == service.Value);
          }
        } else {
          const service = this.serviceCatalogues.find(_ => _.Name.toLocaleLowerCase().trim() == this.NPO_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim());
          if (service) {
            this.documentList = this.documentList.filter(_ => _.ServiceCatalogue == service.Value);
          }
        }

        this.setupFormControls();
        this.StepperService.requestData$.subscribe(_ => {
          this.requestId = this.StepperService.requestId;
          if (_ && (_.isFullDetails == true)) {
            this.mapData(_?.UploadDocumentForm);
            this.checkAttachmentRequest();
            this.getDownloadButton(_.StatusCode);
          }
          else if (_ && (_.isFullDetails == false) && _?.MembershipForm) {
            this.documents = _?.UploadDocumentForm?.Document;
            this.attachments = [];
            this.attachmentsData = [];
            this.mapData(_?.UploadDocumentForm);
            this.checkAttachmentRequest();
            this.getDownloadButton(_.StatusCode);

          }


        });
      }
    });

    setInterval(() => {
      this.checkAllowUpload();
      this.checkEditableFildes();
    }, 2000);

  }


  isValidForm = (): boolean => {
    if (this.documentList.length === 0) return false;
    const requiredDocs = this.documentList.filter(doc => doc.Requirement === 'Required');
    if (requiredDocs.length === 0) return this.attachments.length > 0;
    const hasInvalidRequiredDocs = requiredDocs.some(doc => this.form.get(doc.ID)?.invalid);
    return !hasInvalidRequiredDocs;
  };

  getFormControl(id: string): FormControl {
    return this.form.get(id) as FormControl;
  }

  checkAllowUpload = (): void => {
    this.documentList?.forEach(doc => {
      if (this.isNotAllowedToEdit) {
        this.form.get(doc.ID)?.disable();
      } else {
        this.form.get(doc.ID)?.enable();
      }
    });
  }

  setupFormControls() {
    this.documentList?.forEach(doc => {
      const isRequired = doc.Requirement === 'Required';
      this.form.addControl(doc.ID, this.FormBuilder.control('', isRequired ? Validators.required : null));
    });
  }

  onFileUploaded(attachment: Attachment) {
    this.uploadFile(attachment);
  }

  onFileDeleted(attachment: Attachment) {
    const index = this.attachments.findIndex(item => item.docTypeId === attachment.docTypeId);
    if (index !== -1) {
      this.MyEstablishmentService.deleteAttachment(this.attachments[index].id).subscribe((res) => {
        this.NotifyService.showSuccess('notify.success', 'notify.fileDeletedSuccess');
        const id = this.attachments[index].id;
        this.attachments = this.attachments.filter(_ => _.id !== id);
        this.attachmentsData = this.attachmentsData.filter(_ => _.id !== id);
        this.removeFromCRM(id, this.DOCUMENTS_GRID_IN_CRM);
        this.saveAsDraft(true);
      });
    }
  }

  uploadFile(attachment: Attachment) {
    const reader = new FileReader();
    reader.readAsDataURL(attachment.file);
    reader.onload = () => {
      let base64 = reader.result?.toString();
      if (base64) {
        base64 = base64.split(",")[1];
      }
      var userInfo = this.AuthService.getUserInfo();
      this.requestId = this.StepperService.requestId;

      if (this.requestId && !attachment.isEditMode) {
        const request = {
          Applicant: userInfo.crmUserId,
          DocumentType: attachment.docTypeId,
          EstablishmentId: this.establishmentId,
          NPORequest: this.requestId,
          mimeType: attachment.file.type,
          FileName: attachment.file.name,
          Base64: base64,
        };
        this.MyEstablishmentService.saveAttachment(request).subscribe((res) => {
          attachment.id = res.Id;
          this.attachments.push(attachment);
          this.attachmentsData.push({
            id: res.Id,
            ...request
          });
        });
      } else if (this.requestId && attachment.isEditMode) {
        this.attachments.push(attachment);
      } else {
        this.attachments.push(attachment);
        this.attachmentsData.push({
          id: this.EMPTY_GUID,
          Applicant: userInfo.crmUserId,
          DocumentType: attachment.docTypeId,
          EstablishmentId: this.establishmentId,
          mimeType: attachment.file.type,
          FileName: attachment.file.name,
          Base64: base64,
        });
      }
    };
  }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("UploadDocumentForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams("UploadDocumentForm", false);
      this.handleSaveRequest(submitParams);
    }
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      Document: this.attachmentsData.map(_ => {
        return {
          Id: _.id,
          Establishment: _.EstablishmentId,
          Applicant: this.currentApplicantId,
          DocumentType: _.DocumentType,
          mimeType: _.mimeType,
          FileName: _.FileName,
          Base64: _.Base64
        }
      })
    }
  }

  mapData = (data: any): void => {
    if (!data || !data.Document) return;

    data.Document.forEach((item: any) => {
      const attachment: any = {
        id: item.Id,
        docTypeId: item.DocumentType,
        file: null,
        mimeType: item.mimeType ?? item.MimeType,
        FileName: item.FileName,
        isEditMode: true,
        Base64: item.Base64
      };

      this.attachments.push(attachment);
      this.attachmentsData.push({
        id: item.Id,
        EstablishmentId: item.Establishment,
        currentApplicantId: item.Applicant,
        DocumentType: item.DocumentType,
        mimeType: item.mimeType ?? item.MimeType,
        FileName: item.FileName,
        Base64: item.Base64
      });


    });
    this.documents = data.Document;
  };

  checkEditableFildes = (): void => {
    if (this.isReturnForUpdate) {
      // const allowedToEdit = this.feedbackList?.filter(_ => _.Type == FileType.Attachement);
      // if (allowedToEdit && allowedToEdit?.length > 0) {
      //   this.documentList?.forEach(doc => {
      //     this.form.get(doc.ID)?.enable();
      //   });
      // } else {
      //   this.documentList?.forEach(doc => {
      //     this.form.get(doc.ID)?.disable();
      //   });
      // }

      this.documentList?.forEach(doc => {
        this.form.get(doc.ID)?.enable();
      });
    }
  }

  checkAttachmentRequest = (): void => {
    if (this.requestId) {
      this.MyEstablishmentService.fetchAttachments(this.requestId)
        .subscribe(res => {
          var data = res?.data;
          this.mapData({ Document: data });
        });
    }
  }

  getexternalInjectFile = (docTypeId: string): any => this.attachments.find(_ => _.docTypeId == docTypeId);



  getDocTypeName = (id: string): string => {
    let doctype = this.documentList.find(_ => _.ID == id);
    return (this.LanguageService.IsArabic ? doctype?.NameArabic : doctype?.NameEnglish) ?? ''
  }
  generateDownloadLink = (file: any): string => `data:${file?.mimeType};base64,${file?.Base64}`;

  isMobileScreen(): boolean {
    // Check if the viewport matches the `lg` breakpoint (Bootstrap default is ≥ 992px)
    return window.matchMedia('(max-width: 1023px)').matches;
  }



  downloadButtons: any[] = [];
  reportParameter: any[] = [];
  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes.find(_ => _.ID == this.selectedLegalFormType);
    if (type) {
      return type.NameEnglish;
    }
    return "";
  }
  getDownloadButton = (statusCode: number): void => {
    if (!this.legalFormTypeTitle) {
      return;
    }
    const legalFormType = this.legalFormTypeTitle.toLocaleLowerCase().trim();
    let collection: any;
    if (this.migrationID) {
      collection = this.reportParameter.filter(param => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType && param.ServiceCatlogue?.toLocaleLowerCase().trim() == this.NPO_LINCEING_DELEACRTION.toLocaleLowerCase().trim());
    } else {
      collection = this.reportParameter.filter(param => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType && param.ServiceCatlogue?.toLocaleLowerCase().trim() == this.NPO_LINCEING.toLocaleLowerCase().trim());
    }
    if (statusCode == 1) {
      this.downloadButtons = collection;
    }
  };
  download = (file: any): void => {
    this.downloadNpoFiles('application/pdf', file.ReportName, this.npoRequestId ?? '', file.LogicalName, this.establishmentRequestId ?? '');
  }




}
