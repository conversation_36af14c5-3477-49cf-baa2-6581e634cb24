import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, Validators } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { DONOR_TYPES, PASSPORT_TYPES } from '../../models/npo-lookup-data';
import { LegalFormTypesEnum } from '../../enums/legal-form-types-enum';
import { customEmailValidator } from '../../../../shared/validators/custom.email.validator';
import { customMobileValidator } from '../../../../shared/validators/custom.mobile.validator';
import { internationalPhoneValidator } from '../../../../shared/validators/international.phone.validator';
import { Donor_Types_Enum } from '../../enums/donor-types.enum';
import { emirateLandLineValidator } from '../../../../shared/validators/emirate.landLine.validator';
import { letterPatternValidatorGeneralNotNumberOnly } from '../../../../shared/validators/letter-pattern-validator-accept-general-not-num-only';

@Component({
  selector: 'app-donor-details',
  templateUrl: './donor-details.component.html',
  styleUrls: ['./donor-details.component.scss'],
})
export class DonorDetailsComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  countries: Lookup[] = [];
  emirates: Lookup[] = [];
  nationalities: Lookup[] = [];
  minDate: Date;
  donorTypes: Lookup[] = [];
  passportTypes: Lookup[] = [];
  entityLegalForms: Lookup[] = [];
  licensingAuthorities: Lookup[] = [];
  entityTypes: Lookup[] = [];
  legalForms: Lookup[] = [];
  categories: Lookup[] = [];
  RequestType = LegalFormTypesEnum;
  donorTypesEnum = Donor_Types_Enum;
  individualDonorDetails: any | undefined = undefined;

  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() selectedLegalFormType: LegalFormTypesEnum | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() npoEstablishmentDonor: EventEmitter<any> = new EventEmitter<any>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.donorDetails.';

    const today = new Date();
    today.setDate(today.getDate());
    this.minDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.countries = data?.Countries ?? [];
        this.nationalities = data?.Nationalities ?? [];
        this.emirates = data?.Emirates ?? [];
        this.donorTypes = data?.DonorType ?? [];
        this.entityLegalForms = data?.EntityLegalForm ?? [];
        this.licensingAuthorities = data?.LicensingAuthority ?? [];
        this.passportTypes = data?.PassportType ?? [];
        this.entityTypes = data?.EntityType ?? [];
        this.legalForms = data?.LegalForms ?? [];
        this.categories = data?.Categories ?? [];

        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.DonorDetails);
          } else if (_ && _.isFullDetails == false) {
            this.mapData(_?.DonorDetails);
          }
        });
      }
    });

    
    this.form.get('Emirate')?.valueChanges.subscribe(value => {
      if(value && value!== '' && value!== null && value !== undefined && value!== this.EMPTY_GUID && this.selectedLegalFormType===this.LEGAL_FORM_TYPES.InsideUae){
        if(value?.NameEnglish?.toUpperCase() === 'UMM AL QUWAIN' || value?.NameEnglish?.toUpperCase() === 'AJMAN'){
          this.FormService.addValidators(this.form,['PhoneNumber'],[emirateLandLineValidator(true,false)])
        }
        else if(value?.NameEnglish?.toUpperCase() === 'FUJAIRAH'){
           this.FormService.addValidators(this.form,['PhoneNumber'],[emirateLandLineValidator(false,true)])
        }
        else{
            this.FormService.addValidators(this.form,['PhoneNumber'],[emirateLandLineValidator()])
        }
      }
    })
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );
    return result;
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'DonorDetails',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'DonorDetails',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    if (this.fb?.EntityName?.value === '' || this.fb?.EntityName?.value === null) {
      this.fb?.EntityName?.setValue(undefined);
    }
    if (this.fb?.CompanyName?.value === '' || this.fb?.CompanyName?.value === null) {
      this.fb?.CompanyName?.setValue(undefined);
    }
    return {
      DonorType: this.fb?.DonorType?.value?.ID ?? '',

      individualDonor: this.fb?.individualDonor?.value ?? '',

      EstablishmentDonor: this.fb?.EstablishmentDonor?.value ?? '',
      EstablishmentDonorDetails: this.fb?.EstablishmentDonorDetails?.value ?? '',

      Emirate: this.fb?.Emirate?.value?.ID ?? this.fb?.HeadquartersEmirate?.value?.ID ?? '',
      LicensingAuthority: this.fb?.LicensingAuthority?.value?.ID ?? '',
      OutsideLicensingAuthority: this.fb?.OutsideLicensingAuthority?.value ?? '',
      Nationality: this.fb?.Nationality?.value?.ID ?? '',
      PlaceofBirth: this.fb?.PlaceOfBirth?.value?.ID ?? '',
      CurrentResidence: this.fb?.CurrentResidence?.value?.ID ?? '',
      Country: this.fb?.Country?.value?.ID ?? '',
      EntityType: this.fb?.EntityType?.value?.ID ?? '',
      PassportType: this.fb?.PassportType?.value?.ID ?? '',


      CompanyOrEntityName: this.fb?.EntityName?.value ?? this.fb?.CompanyName?.value ?? this.fb?.NpoNameEn?.value ?? '',
      CommercialLicenseNumber: this.fb?.CommercialLicenseNumber?.value ?? '',
      LicenseIssuanceDate: this.fb?.LicenseIssuanceDate?.value ?? '',
      LicenseExpiryDate: this.fb?.LicenseExpiryDate?.value ?? '',
      PhoneNumber: (this.fb?.PhoneNumber?.value != null && this.fb?.PhoneNumber?.value != "" && this.fb?.PhoneNumber?.value != undefined) ? this.fb?.PhoneNumber?.value :
        ((this.fb?.Mobile?.value != null && this.fb?.Mobile?.value != "" && this.fb?.Mobile?.value != undefined) ? this.fb?.Mobile?.value : ""),
      Email: this.fb?.Email?.value ?? '',
      FullName: this.fb?.FullName?.value ?? '',
      PassportNumber: this.fb?.PassportNumber?.value ?? '',
      PassportExpiryDate: this.fb?.PassportExpiryDate?.value ?? '',
      JobTitle: this.fb?.JobTitle?.value ?? '',
      Employer: this.fb?.Employer?.value ?? '',
      City: this.fb?.City?.value ?? '',
      LicenseNumber: this.fb?.LicenseNumber?.value ?? '',
      BusinessDomain: this.fb?.BusinessDomain?.value ?? '',
      Category: this.fb?.Category?.value?.ID ?? ''
    };
  };

  mapData = (data: any): void => {
    if (!data) return;

    this.fb?.DonorType?.setValue(this.donorTypes?.find((type) => type.ID == data?.DonorType));

    this.selectedDonorType({
      value: { ID: data?.DonorType }
    });

    this.fb?.individualDonor?.setValue(data?.individualDonor);
    this.fb?.EstablishmentDonor?.setValue(data?.EstablishmentDonor);


    this.fb?.EntityName?.setValue(data?.CompanyOrEntityName);
    this.fb?.CompanyName?.setValue(data?.CompanyOrEntityName);
    this.fb?.CommercialLicenseNumber?.setValue(data?.CommercialLicenseNumber);
    this.fb?.LicenseIssuanceDate?.setValue(data?.LicenseIssuanceDate);
    this.fb?.LicenseExpiryDate?.setValue(data?.LicenseExpiryDate);
    this.fb?.PhoneNumber?.setValue(data?.PhoneNumber);
    this.fb?.Mobile?.setValue(data?.PhoneNumber);
    this.fb?.Email?.setValue(data?.Email);
    this.fb?.FullName?.setValue(data?.FullName);
    this.fb?.PassportNumber?.setValue(data?.PassportNumber);
    this.fb?.PassportExpiryDate?.setValue(data?.PassportExpiryDate);
    this.fb?.JobTitle?.setValue(data?.JobTitle);
    this.fb?.Employer?.setValue(data?.Employer);
    this.fb?.City?.setValue(data?.City);
    this.fb?.LicenseNumber?.setValue(data?.LicenseNumber);
    this.fb?.BusinessDomain?.setValue(data?.BusinessDomain);
    this.fb?.Category?.setValue(data?.Category);


    this.fb?.PlaceOfBirth?.setValue(this.countries?.find((type) => type.ID == data?.PlaceofBirth));
    this.fb?.CurrentResidence?.setValue(this.countries?.find((type) => type.ID == data?.CurrentResidence));
    this.fb?.Country?.setValue(this.countries?.find((type) => type.ID == data?.Country));
    this.fb?.EntityType?.setValue(this.entityTypes?.find((type) => type.ID == data?.EntityType));
    this.fb?.PassportType?.setValue(this.passportTypes?.find((type) => type.ID == data?.PassportType));

    this.fb?.Nationality?.setValue(this.nationalities?.find((type) => type.ID == data?.Nationality));
    this.fb?.LicensingAuthority?.setValue(this.licensingAuthorities?.find((type) => type.ID == data?.LicensingAuthority));
    this.fb?.OutsideLicensingAuthority?.setValue(data?.OutsideLicensingAuthority);
    this.fb?.Emirate?.setValue(this.emirates?.find((type) => type.ID == data?.Emirate));
    this.fb?.HeadquartersEmirate?.setValue(this.emirates?.find((type) => type.ID == data?.Emirate));

    if (data?.EstablishmentDonor && data?.EstablishmentDonor !== '' && data?.EstablishmentDonor !== undefined && data?.EstablishmentDonor !== null && data?.EstablishmentDonor !== this.EMPTY_GUID) {
      this.fillEstablishmentDonorDetails(data?.estalishmentDonorDetails);
      this.npoEstablishmentDonor.emit(data?.estalishmentDonorDetails);
    }
  };


  getClearedFields = (valuesToRemove: string[]): string[] => {
    const originalArray = ['individualDonor', 'FullName', 'Nationality', 'PlaceOfBirth', 'CurrentResidence', 'Mobile',
      'Email', 'PassportNumber', 'PassportType', 'OtherPassportType', 'PassportExpiryDate', 'JobTitle',
      'Employer', 'CompanyName', 'Country', 'City', 'LicensingAuthority', 'CommercialLicenseNumber',
      'LicenseNumber', 'Category', 'LicenseIssuanceDate', 'LicenseExpiryDate', 'EntityName', 'BusinessDomain',
      'PhoneNumber', 'EmiratesId', 'DateOfBirth', 'NameAr', 'NameEn', 'UnifiedNumber', 'Emirate', 'EntityType',
      'NpoNameAr', 'NpoNameEn', 'NpoLegalForm', 'LicensingEntity', 'DeclarationDecisionLink', 'HeadquartersEmirate', 'EstablishmentDonor'];

    const filteredArray = originalArray.filter(item => !valuesToRemove.includes(item));
    return filteredArray;
  }

  clearedAllFields = (): void => {
    const originalArray = ['individualDonor', 'FullName', 'Nationality', 'PlaceOfBirth', 'CurrentResidence', 'Mobile',
      'Email', 'PassportNumber', 'PassportType', 'OtherPassportType', 'PassportExpiryDate', 'JobTitle',
      'Employer', 'CompanyName', 'Country', 'City', 'LicensingAuthority', 'CommercialLicenseNumber',
      'LicenseNumber', 'Category', 'LicenseIssuanceDate', 'LicenseExpiryDate', 'EntityName', 'BusinessDomain',
      'PhoneNumber', 'EmiratesId', 'DateOfBirth', 'NameAr', 'NameEn', 'UnifiedNumber', 'Emirate', 'EntityType',
      'NpoNameAr', 'NpoNameEn', 'NpoLegalForm', 'LicensingEntity', 'DeclarationDecisionLink', 'HeadquartersEmirate', 'EstablishmentDonor'];


    this.FormService.clearFields(this.form, originalArray, true);
  }


  selectedDonorType(event: any) {
    this.clearedAllFields();
    /**
     * Individual InsideUae */
    if (event.value.ID == 1 && this.selectedLegalFormType == this.RequestType.InsideUae) {
      const requiredFields = ['individualDonor', 'JobTitle', 'Employer'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
    }
    /**
     * Individual OutsideUae */
    else if (event.value.ID == 1 && this.selectedLegalFormType == this.RequestType.OutsideUae) {
      const requiredFields = ['FullName', 'Nationality', 'PlaceOfBirth', 'CurrentResidence', 'PassportNumber', 'PassportType', 'PassportExpiryDate'];
      const requiredEmail = ['Email'];
      const requiredMobile = ['Mobile'];
      const requiredJobDetails = ['JobTitle', 'Employer'];

      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);
      this.FormService.addValidators(this.form, requiredMobile, [Validators.required, internationalPhoneValidator()]);
      this.FormService.addValidators(this.form, requiredJobDetails, [Validators.required, letterPatternValidatorGeneralNotNumberOnly(true)]);


      this.form.get('PassportType')?.valueChanges.subscribe((value) => {
        if (value && value !== null && value !== undefined && value !== '' && value.ID == 4) {
          this.FormService.addValidators(this.form, ['OtherPassportType'], [Validators.required]);
        } else {
          this.FormService.clearFields(this.form, ['OtherPassportType'], true);
        }
      });
    }
    /**
     * Public Sector InsideUae */
    else if (event.value.ID == 2 && this.selectedLegalFormType == this.RequestType.InsideUae) {
      const requiredFields = ['EntityName', 'EntityType', 'Emirate', 'PhoneNumber', 'Email'];
      const requiredEmail = ['Email'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);
    }
    /**
    * Public Sector OutsideUae */
    else if (event.value.ID == 2 && this.selectedLegalFormType == this.RequestType.OutsideUae) {
      const requiredFields = ['EntityName', 'Country', 'City', 'BusinessDomain', 'Email'];
      const requiredPhoneNumber = ['PhoneNumber'];
      const requiredEmail = ['Email'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]); 
      this.FormService.addValidators(this.form, requiredPhoneNumber, [Validators.required, internationalPhoneValidator()]);
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);
    }
    /**
     * Private Sector InsideUae */
    else if (event.value.ID == 3 && this.selectedLegalFormType == this.RequestType.InsideUae) {
      const requiredFields = ['CompanyName', 'Emirate', 'LicensingAuthority', 'CommercialLicenseNumber', 'LicenseIssuanceDate', 'LicenseExpiryDate','PhoneNumber'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
      const requiredEmail = ['Email'];
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);

      this.FormService.enableFields(this.form, ['LicenseIssuanceDate', 'LicenseExpiryDate']);
      this.form.get('LicenseIssuanceDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('LicenseIssuanceDate')!, this.form.get('LicenseExpiryDate')!);
        if (this.form.get('LicenseIssuanceDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('LicenseIssuanceDate')?.setErrors(null);
        }
      });
      this.form.get('LicenseExpiryDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('LicenseIssuanceDate')!, this.form.get('LicenseExpiryDate')!);
        if (this.form.get('LicenseExpiryDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('LicenseExpiryDate')?.setErrors(null);
        }
      });

    }
    /**
   * Private Sector OutsideUae */
    else if (event.value.ID == 3 && this.selectedLegalFormType == this.RequestType.OutsideUae) {
      const requiredFields = ['CompanyName', 'Country', 'City', 'OutsideLicensingAuthority', 'CommercialLicenseNumber', 'LicenseIssuanceDate', 'LicenseExpiryDate'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
      const requiredPhoneNumber = ['PhoneNumber'];
      const requiredEmail = ['Email'];
      this.FormService.addValidators(this.form, requiredPhoneNumber, [Validators.required, internationalPhoneValidator()]);
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);

      this.FormService.enableFields(this.form, ['LicenseIssuanceDate', 'LicenseExpiryDate']);
      this.form.get('LicenseIssuanceDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('LicenseIssuanceDate')!, this.form.get('LicenseExpiryDate')!);
        if (this.form.get('LicenseIssuanceDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('LicenseIssuanceDate')?.setErrors(null);
        }
      });
      this.form.get('LicenseExpiryDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('LicenseIssuanceDate')!, this.form.get('LicenseExpiryDate')!);
        if (this.form.get('LicenseExpiryDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('LicenseExpiryDate')?.setErrors(null);
        }
      });
    }
    /**
     * NPO InsideUae */
    else if (event.value.ID == 4 && this.selectedLegalFormType == this.RequestType.InsideUae) {
      const requiredFields = ['EstablishmentDonor'];
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
      this.FormService.disableFields(this.form, ['NpoNameEn', 'NpoNameAr', 'NpoLegalForm', 'HeadquartersEmirate',
      'LicensingEntity', 'LicenseNumber', 'Category', 'LicenseIssuanceDate', 'LicenseExpiryDate', 'DeclarationDecisionLink'
    ]);
    }
    /**
     * NPO OutsideUae */
    else if (event.value.ID == 4 && this.selectedLegalFormType == this.RequestType.OutsideUae) {
      const requiredFields = ['EntityName', 'Country', 'City', 'OutsideLicensingAuthority', 'LicenseNumber', 'LicenseIssuanceDate', 'LicenseExpiryDate', 'Category'];
      const requiredPhoneNumber = ['PhoneNumber'];
      const requiredEmail = ['Email'];
      this.FormService.addValidators(this.form, requiredPhoneNumber, [Validators.required, internationalPhoneValidator()]);
      this.FormService.addValidators(this.form, requiredEmail, [Validators.required, customEmailValidator()]);
      this.FormService.addValidators(this.form, requiredFields, [Validators.required]);
    }
  }

  deleteIndividualDonor() {
    this.individualDonorDetails = undefined;
    this.FormService.clearFields(this.form, ['individualDonor', 'JobTitle', 'Employer', 'EmiratesId', 'DateOfBirth']);
  }

  getIndividualInfo = (): void => {
    const emiratesId = this.fb?.EmiratesId?.value ?? '';
    const dateOfBirth = this.fb?.DateOfBirth?.value ?? '';

    const formattedDate = this.FormService.formatDate(dateOfBirth, 'yyyy-MM-dd');
    if (!formattedDate) {
      this.NotifyService.showError('notify.error', 'Invalid date format.');
      return;
    }
    this.NocRequestService.checkIndividualDonorValidation(emiratesId, formattedDate).subscribe(
      (response) => {
        if (response && response?.individualDonorValidation?.Valid) {
          this.fb?.individualDonor?.setValue(response?.individualDonorValidation?.contactId);
          this.individualDonorDetails = {
            emiratesId: emiratesId,
            dateOfBirth: dateOfBirth,
            fullArabicName: response?.individualDonorValidation?.fullArabicName,
            fullEnglishName: response?.individualDonorValidation?.fullEnglishName,
          };
        } else {
          this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'لم يتم العثور على البيانات.' : 'Data not found.');
        }
      },
      (error) => {
        this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'خطأ في جلب البيانات.' : 'Error fetching data.');
      }
    );
  };

  getEstablishmentDonorDetails = (): void => {
    if (this.selectedLegalFormType == this.RequestType.InsideUae && this.fb?.DonorType?.value?.ID == 4) {
      const name = this.fb?.UnifiedNumber?.value ?? '';
      if (name) {
        this.NocRequestService.getEstablishmentDonorDetails(name).subscribe(
          (response) => {
            if (response && response?.estalishmentDonorDetails) {
              this.npoEstablishmentDonor.emit(response?.estalishmentDonorDetails);

              this.fillEstablishmentDonorDetails(response?.estalishmentDonorDetails);


            } else {
              this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'لم يتم العثور على البيانات.' : 'Data not found.');
            }
          },
          (error) => {
            this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'خطأ في جلب البيانات.' : 'Error fetching data.');
          }
        );
      }
    }
  }

  fillEstablishmentDonorDetails = (estalishmentDonorDetails): void => {
    this.FormService.disableFields(this.form, ['NpoNameEn', 'NpoNameAr', 'NpoLegalForm', 'HeadquartersEmirate',
      'LicensingEntity', 'LicenseNumber', 'Category', 'LicenseIssuanceDate', 'LicenseExpiryDate', 'DeclarationDecisionLink'
    ]);

    this.fb?.EstablishmentDonor?.setValue(estalishmentDonorDetails?.establishmentId);
    this.fb?.NpoNameEn?.setValue(estalishmentDonorDetails?.NPONameEnglish);
    this.fb?.NpoNameAr?.setValue(estalishmentDonorDetails?.NPONameArabic);

    this.fb?.NpoLegalForm?.setValue(this.legalForms?.find((type) => type.ID == estalishmentDonorDetails?.NPOLegalForm));
    this.fb?.HeadquartersEmirate?.setValue(this.emirates?.find((type) => type.ID == estalishmentDonorDetails?.HeadquarterEmirate));
    this.fb?.LicensingEntity?.setValue(this.licensingAuthorities?.find((type) => type.ID == estalishmentDonorDetails?.LicensingEntity));
    this.fb?.LicenseNumber?.setValue(estalishmentDonorDetails?.LicenseNumber);

    this.fb?.Category?.setValue(this.categories?.find((type) => type.ID == estalishmentDonorDetails?.MainCategory));

    this.fb?.LicenseIssuanceDate?.setValue(estalishmentDonorDetails?.LicenseIssuanceDate);
    this.fb?.LicenseExpiryDate?.setValue(estalishmentDonorDetails?.LicenseExpiryDate);
  }
  
  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "donorDetails", this.fb);
  }
}
