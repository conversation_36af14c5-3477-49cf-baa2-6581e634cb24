<!-- <div class="container" *ngIf="auth.isAuthenticated()">
  <div class="card m-2">
    <div class="card-body text-center table-container">
      <div class="container">
        <p></p>
        <div class="mb-3 row d-flex justify-content-between p-2">
          <div class="col-xs-3 col-sm-auto">
            <form class="form">
              <table>
                <td>
                  <input id="table-complete-search" type="text" class="form-control" name="searchTerm" [placeholder]="'Search' | translate" [(ngModel)]="searchTerm" />
                </td>
                <td><span><button mat-button color="primary" (click)="search()"><i class="fa fa-magnifying-glass"></i></button></span></td>
              </table>
            </form>
          </div>
          <div class="col-xs-3 col-sm-auto">
            <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="statusId"
              [disabled]="disabled" (change)="onStatusChange()">
              <option value="-1">{{ "status.all" | translate }}</option>
              <option value="5">{{ "Completed" | translate }}</option>
              <option value="1">{{ "inProgress" | translate }}</option>
              <option value="662410002">{{ "Reopened" | translate }}</option>
              <option value="1000">{{ "InformationProvided" | translate }}</option>
            </select>
          </div>

        </div>
      </div>

      <table class="table table-striped table-bordered">
        <thead>
          <tr>
            <th scope="col" class="text-center" sortable="TicketNumber" (sort)="onSort($event)">
              {{'compliants.complaintNumber' | translate}}</th>

            <th scope="col" class="text-center" sortable="'CaseType'" (sort)="onSort($event)">
              {{'compliants.complaintRequestType' | translate}}</th>

            <th scope="col" class="text-center" sortable="'CreatedOn'" (sort)="onSort($event)">
              {{'compliants.raisedAt' | translate}}</th>

            <th scope="col" class="text-center" sortable="'Status'" (sort)="onSort($event)">
              {{'compliants.status' | translate}}</th>
            <th scope="col">{{'applications.Actions' | translate}}</th>
          </tr>
        </thead>
        <tbody>
          @for (app of filteredData; track app.name; let i = $index) {
          <tr>
            <td class="align-middle">{{ app.TicketNumber }}</td>
            <td class="align-middle">{{ app.CaseType }}</td>
            <td class="align-middle">{{ app.CreatedOn | date:'dd/MM/yyyy' }}</td>
            <td class="align-middle col-wrap">
              {{ getStatusLabel(app.Status) | translate }}
            </td>
            <td class="align-middle">
              <div *ngIf="(app.CaseType === 'Inquiry' ||  app.CaseType === 'Complaint') && app.Status === 5 && app.IsReopen == 0 && !daysPassed(app.CreatedOn)">
                <button mat-button color="primary" [matMenuTriggerFor]="menu"><mat-icon>more_vert</mat-icon></button>
                <mat-menu #menu="matMenu">
                  <app-modal [operation]="'edit'" [form]="modalRequest" [title]="'compliants.reopenRequest'" [size]="'lg'"
                    buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
                    buttonLabel="{{'compliants.reopenRequest'|translate}}" (submitAction)="editMember($event, i)"
                    (opened)="handleMemberEdit(app)">
                    <div summary-row [label]="'compliants.ticketNumber'" [isPlain]="true" [value]="app.TicketNumber">
                    </div>
                    <div summary-row [label]="'compliants.complaintRequestType'" [isPlain]="true" [value]="app.CaseType">
                    </div>
                    <div summary-row [label]="'compliants.title'" [isPlain]="true" [value]="app.Title"></div>
                    <div class="row">
                      <app-textarea [columns]="12" [label]="'compliants.reopenReason' | translate"
                        [control]="d?.Reason"></app-textarea>
                    </div>
                  </app-modal>
                </mat-menu>
              </div>

            </td>
          </tr>
          }
        </tbody>
      </table>



      <div class="d-flex justify-content-between p-2">
        <span class="col-2">{{'Total count' | translate}}: {{filteredCount}}</span>
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize"
          [collectionSize]="filteredCount" (pageChange)="onPageChange()"></ngb-pagination>




        <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSize"
          (change)="onSelectChange()">
          <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
        </select>
      </div>

    </div>
  </div>
</div> -->



<div class="container mt-5" *ngIf="auth.isAuthenticated()">
  <!-- new application.component -->
  <div class="application-overview">
    <div class="application-overview__header">
      <div>
        <h2>{{'userPages.cases.title' | translate}}</h2>
        <p>{{'Keep track of your applications and tasks'|translate}}</p>
      </div>
      <div class="application-overview__header-action">
        <a href="#" class="aegov-link " title="View All ">
          {{'View All' | translate}}
           <svg class="" width="24" height="24" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
          </svg>
          <span class="sr-only"> {{'View All' | translate}}</span>
        </a>
      </div>
    </div>
    <div class="application-overview__filters">
      <div class="d-flex gap-3 order-lg-1">
        <div>
          <mat-form-field>
            <mat-label>
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" >
                <path d="M3.33334 15C3.09723 15 2.89945 14.92 2.74 14.76C2.58056 14.6 2.50056 14.4022 2.5 14.1667C2.49945 13.9311 2.57945 13.7333 2.74 13.5733C2.90056 13.4133 3.09834 13.3333 3.33334 13.3333H6.66667C6.90278 13.3333 7.10084 13.4133 7.26084 13.5733C7.42084 13.7333 7.50056 13.9311 7.5 14.1667C7.49945 14.4022 7.41945 14.6003 7.26 14.7608C7.10056 14.9214 6.90278 15.0011 6.66667 15H3.33334ZM3.33334 10.8333C3.09723 10.8333 2.89945 10.7533 2.74 10.5933C2.58056 10.4333 2.50056 10.2356 2.5 10C2.49945 9.76444 2.57945 9.56667 2.74 9.40667C2.90056 9.24667 3.09834 9.16667 3.33334 9.16667H11.6667C11.9028 9.16667 12.1008 9.24667 12.2608 9.40667C12.4208 9.56667 12.5006 9.76444 12.5 10C12.4994 10.2356 12.4194 10.4336 12.26 10.5942C12.1006 10.7547 11.9028 10.8344 11.6667 10.8333H3.33334ZM3.33334 6.66667C3.09723 6.66667 2.89945 6.58667 2.74 6.42667C2.58056 6.26667 2.50056 6.06889 2.5 5.83333C2.49945 5.59778 2.57945 5.4 2.74 5.24C2.90056 5.08 3.09834 5 3.33334 5H16.6667C16.9028 5 17.1008 5.08 17.2608 5.24C17.4208 5.4 17.5006 5.59778 17.5 5.83333C17.4994 6.06889 17.4194 6.26694 17.26 6.4275C17.1006 6.58806 16.9028 6.66778 16.6667 6.66667H3.33334Z" />
              </svg>
              {{'sort.sortBy'|translate}}:
            </mat-label>
            <mat-select [(ngModel)]="sortOption" (selectionChange)="onSortOptionChangeCases()">
                <mat-option value="relevant">{{ 'sort.mostRelevant' | translate }}</mat-option>
                <mat-option value="oldest">{{ 'sort.oldestFirst' | translate }}</mat-option>
                <mat-option value="newest">{{ 'sort.newestFirst' | translate }}</mat-option>
            </mat-select>
            <i class="icon-toggle fa-solid fa-chevron-down"></i>
          </mat-form-field>
        </div>
        <div>
          <form>
            <div class="aegov-form-control">
              <div class="form-control-input">
                <span class="control-suffix">
                  <button class="" (click)="searchCases()" type="submit">
                    <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" >
                      <path d="M25.1191 23.8809L19.6427 18.4056C21.2299 16.5 22.0214 14.0558 21.8525 11.5814C21.6836 9.10709 20.5672 6.79313 18.7357 5.12091C16.9041 3.4487 14.4984 2.54697 12.0189 2.60332C9.53944 2.65967 7.17715 3.66976 5.42345 5.42345C3.66976 7.17715 2.65967 9.53944 2.60332 12.0189C2.54697 14.4984 3.4487 16.9041 5.12091 18.7357C6.79313 20.5672 9.10709 21.6836 11.5814 21.8525C14.0558 22.0214 16.5 21.2299 18.4056 19.6427L23.8809 25.1191C23.9622 25.2004 24.0588 25.2649 24.165 25.3088C24.2712 25.3528 24.385 25.3755 24.5 25.3755C24.615 25.3755 24.7288 25.3528 24.835 25.3088C24.9413 25.2649 25.0378 25.2004 25.1191 25.1191C25.2004 25.0378 25.2649 24.9413 25.3088 24.835C25.3528 24.7288 25.3755 24.615 25.3755 24.5C25.3755 24.385 25.3528 24.2712 25.3088 24.165C25.2649 24.0588 25.2004 23.9622 25.1191 23.8809ZM4.37501 12.25C4.37501 10.6925 4.83687 9.16993 5.70218 7.87489C6.5675 6.57985 7.79741 5.57049 9.23637 4.97445C10.6753 4.37841 12.2587 4.22246 13.7863 4.52632C15.3139 4.83018 16.7171 5.5802 17.8185 6.68154C18.9198 7.78288 19.6698 9.18607 19.9737 10.7137C20.2775 12.2413 20.1216 13.8247 19.5256 15.2636C18.9295 16.7026 17.9202 17.9325 16.6251 18.7978C15.3301 19.6631 13.8075 20.125 12.25 20.125C10.1621 20.1227 8.16044 19.2923 6.6841 17.8159C5.20775 16.3396 4.37732 14.3379 4.37501 12.25Z" />
                    </svg>
                  </button>
                </span>
                <input type="text" id="table-complete-search" name="searchTerm" [placeholder]="'Search' | translate" [(ngModel)]="searchTerm">

              </div>
            </div>
          </form>
        </div>
      </div>
      <div class="overflow-x-auto application-overview__filters-tabs-parent">
        <!-- radio tabs status filter -->
        <form class="overflow-x-auto application-overview__filters-tabs">
          <div class="form-check" (click)="statusId='-1';onStatusChangeCases()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1Cases" [checked]="statusId=='-1'">
            <label class="form-check-label" for="flexRadioDefault1Cases1">
              {{ "status.all" | translate }}
            </label>
          </div>
          <div class="form-check" (click)="statusId='5';onStatusChangeCases()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2Cases" [checked]="statusId=='5'">
            <label class="form-check-label" for="flexRadioDefault2Cases1">
              {{ "Completed" | translate }}
            </label>
          </div>
          <div class="form-check" (click)="statusId='1';onStatusChangeCases()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault3Cases" [checked]="statusId=='1'">
            <label class="form-check-label" for="flexRadioDefault3Cases1">
              {{ "inProgress" | translate }}
            </label>
          </div>
          <div class="form-check" (click)="statusId='662410002';onStatusChangeCases()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault4Cases" [checked]="statusId=='662410002'">
            <label class="form-check-label" for="flexRadioDefault4Cases1">
              {{ "Reopened" | translate }}
            </label>
          </div>
          <div class="form-check" (click)="statusId='1000';onStatusChangeCases()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault5Cases" [checked]="statusId=='1000'">
            <label class="form-check-label" for="flexRadioDefault5Cases1">
              {{ "InformationProvided" | translate }}
            </label>
          </div>
        </form>

      </div>
    </div>
    <div class="application-overview__content">
      <!-- empty state need to remove d-none class to be shown while need to hide or remove table and footer actions"paggination , total count" -->
      <div class="application-overview__empty-state text-center d-none">
        <h4>You have no applications</h4>
        <p>Once you complete an application it will available here.</p>
        <button class="aegov-btn btn-outline">
          All Services
          <span class="sr-only">All Services</span>
        </button>
      </div>
      <!--/end empty state -->

      <!-- Application table -->
      <div class="table-listing-full-width">
        <div>
          <table class="my-table">
            <thead>
              <tr>
                <th scope="col" class="text-center" sortable="TicketNumber" (sort)="onSort($event)">{{'compliants.complaintNumber' | translate}}</th>
                <th scope="col" class="text-center" sortable="'CaseType'" (sort)="onSort($event)">{{'compliants.complaintRequestType' | translate}}</th>
                <th scope="col" class="text-center" sortable="'CreatedOn'" (sort)="onSort($event)">{{'compliants.raisedAt' | translate}}</th>
                <th scope="col" class="text-center" sortable="'Status'" (sort)="onSort($event)">{{'compliants.status' | translate}}</th>
                <th scope="col">{{'applications.Actions' | translate}}</th>
              </tr>
            </thead>
            <tbody>
              @for (app of filteredData; track app.Id; let i = $index) {
              <tr>
                <td class="mobile-only">
                    <div class="table-listing-full-width__inner-title-container">
                      <span class="table-listing-full-width__inner-title">{{ app.TicketNumber }}</span>
                      <!-- <div class="table-listing-full-width__new"><label>New</label></div> -->
                    </div>
                      <p>{{ app.CaseType }}</p>
                      <p>{{ app.CreatedOn | date:'dd/MM/yyyy' }}</p>
                      <div class="table-listing-full-width__status">
                        <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                          <circle cx="4.75" cy="4" r="4" fill="#F8C027" *ngIf="app.Status==5"/>
                          <circle cx="4.75" cy="4" r="4" fill="#C3C6CB" *ngIf="app.Status==1"/>
                          <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.Status==662410002"/>
                          <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="app.Status==1000"/>
                          <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.Status==662410004"/>
                        </svg>
                        <span>{{ getStatusLabel(app.Status) | translate}}</span>
                      </div>
                </td>

                <td class="desktop-only">
                  <span class="table-listing-full-width__inner-title">{{ app.TicketNumber }}</span>
                  <!-- <div class="table-listing-full-width__new"><label>New</label></div> -->
                </td>
                <td class="desktop-only">{{ app.CaseType }}</td>
                <td class="desktop-only">{{ app.CreatedOn | date:'dd/MM/yyyy' }}</td>
                <td class="desktop-only table-listing-full-width__status">
                  <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                    <circle cx="4.75" cy="4" r="4" fill="#F8C027" *ngIf="app.Status==5"/>
                    <circle cx="4.75" cy="4" r="4" fill="#C3C6CB" *ngIf="app.Status==1"/>
                    <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.Status==662410002"/>
                    <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="app.Status==1000"/>
                    <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.Status==662410004"/>
                  </svg>
                  <span> {{ getStatusLabel(app.Status) | translate}} </span>
                </td>
                <td>
                  @if((app.CaseType === 'Inquiry' || app.CaseType === 'Complaint') && app.Status === 5 && app.IsReopen == 0 &&
                  !daysPassed(app.CreatedOn)){

                  <button class="action-button dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path
                        d="M10.9375 10C10.9375 10.1854 10.8825 10.3667 10.7795 10.5208C10.6765 10.675 10.5301 10.7952 10.3588 10.8661C10.1875 10.9371 9.99896 10.9557 9.8171 10.9195C9.63525 10.8833 9.4682 10.794 9.33709 10.6629C9.20598 10.5318 9.11669 10.3648 9.08051 10.1829C9.04434 10.001 9.06291 9.81254 9.13386 9.64123C9.20482 9.46993 9.32498 9.32351 9.47915 9.2205C9.63332 9.11748 9.81458 9.0625 10 9.0625C10.2486 9.0625 10.4871 9.16127 10.6629 9.33709C10.8387 9.5129 10.9375 9.75136 10.9375 10ZM10 5.625C10.1854 5.625 10.3667 5.57002 10.5208 5.467C10.675 5.36399 10.7952 5.21757 10.8661 5.04627C10.9371 4.87496 10.9557 4.68646 10.9195 4.5046C10.8833 4.32275 10.794 4.1557 10.6629 4.02459C10.5318 3.89348 10.3648 3.80419 10.1829 3.76801C10.001 3.73184 9.81254 3.75041 9.64123 3.82136C9.46993 3.89232 9.32351 4.01248 9.2205 4.16665C9.11748 4.32082 9.0625 4.50208 9.0625 4.6875C9.0625 4.93614 9.16127 5.1746 9.33709 5.35041C9.5129 5.52623 9.75136 5.625 10 5.625ZM10 14.375C9.81458 14.375 9.63332 14.43 9.47915 14.533C9.32498 14.636 9.20482 14.7824 9.13386 14.9537C9.06291 15.125 9.04434 15.3135 9.08051 15.4954C9.11669 15.6773 9.20598 15.8443 9.33709 15.9754C9.4682 16.1065 9.63525 16.1958 9.8171 16.232C9.99896 16.2682 10.1875 16.2496 10.3588 16.1786C10.5301 16.1077 10.6765 15.9875 10.7795 15.8333C10.8825 15.6792 10.9375 15.4979 10.9375 15.3125C10.9375 15.0639 10.8387 14.8254 10.6629 14.6496C10.4871 14.4738 10.2486 14.375 10 14.375Z"
                        fill="#92722A" />
                    </svg>
                  </button>
                  <div class="action-popup dropdown-menu">
                    <ul>
                      <li class="dropdown-item" (click)="handleMemberEdit(app)">
                        <app-modal [operation]="'edit'" [form]="modalRequest" [title]="'compliants.reopenRequest'" [size]="'lg'"
                          buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
                          buttonLabel="{{'compliants.reopenRequest'|translate}}" (submitAction)="editMember($event, i)"
                          (opened)="handleMemberEdit(app)">
                          <div summary-row [label]="'compliants.ticketNumber'" [isPlain]="true" [value]="app.TicketNumber">
                          </div>
                          <div summary-row [label]="'compliants.complaintRequestType'" [isPlain]="true" [value]="app.CaseType">
                          </div>
                          <div summary-row [label]="'compliants.title'" [isPlain]="true" [value]="app.Title"></div>
                          <div class="row">
                            <app-textarea [columns]="12" [label]="'compliants.reopenReason' | translate"
                              [control]="d?.Reason"></app-textarea>
                          </div>
                        </app-modal>
                      </li>
                    </ul>
                  </div>
                  }
                </td>
              </tr>
            }
            </tbody>
          </table>
        </div>
      </div>
      <!-- end Application table -->



    </div>
    <div class="application-overview__footer">
      <div class="application-overview__footer-total-count d-flex justify-content-between align-items-center">
        <span>{{'Total count' | translate}}: {{filteredCount}}</span>
        <div class="mobile-only d-flex gap-2 align-items-center">
          <span>Show per page:</span>
          <mat-form-field>
            <mat-select placeholder="5" [(ngModel)]="pageSize" (change)="onSelectChange()">
                <mat-option [value]="5">5 {{'itemsPerPage' | translate}}</mat-option>
                <mat-option [value]="10">10 {{'itemsPerPage' | translate}}</mat-option>
                <mat-option [value]="50">50 {{'itemsPerPage' | translate}}</mat-option>
            </mat-select>
            <i class="icon-toggle fa-solid fa-chevron-down"></i>
          </mat-form-field>

        </div>
      </div>
      <div class="aegov-pagination">
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize" [collectionSize]="filteredCount" (pageChange)="onPageChange()" aria-label="Custom pagination">
          <ng-template ngbPaginationPrevious>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" >
              <path d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
            </svg>
            <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
          </ng-template>
          <ng-template ngbPaginationNext>
            <span class="d-none d-lg-block">{{'Next' | translate}}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" >
              <path d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z"/>
            </svg>
          </ng-template>
          <ng-template ngbPaginationNumber let-p>{{ getPageSymbol(p) }}</ng-template>
        </ngb-pagination>
        <!-- <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize" [collectionSize]="filteredCount" (pageChange)="onPageChange()"></ngb-pagination>  -->
      </div>


    </div>
  </div>
  <!--/end new application.component -->



</div>

