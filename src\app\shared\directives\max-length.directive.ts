import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
  selector: '[appMaxLength]'
})
export class MaxLengthDirective {
  @Input('appMaxLength') isTextArea: boolean=false;

  constructor(private control: NgControl, private el: ElementRef) {}

  @HostListener('input', ['$event.target.value'])
  onInput(value: string) {
    var maxLength = 200;
    if(this.isTextArea)
    {
      maxLength = 500;
    }
    if (value.length > maxLength) {
      this.control.control?.setValue(value.slice(0, maxLength));
    }
  }
  @HostListener('blur')
  onInputBlur(): void {
    const input = this.el.nativeElement.value;
    if (!input.trim()) {
      this.el.nativeElement.value = ''; 
      this.control.control?.setValue(this.el.nativeElement.value);
    }
  }
}
