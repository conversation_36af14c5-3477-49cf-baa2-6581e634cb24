<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="col-md-12 d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center">
        {{(messageTranslationPrefix+'title') | translate}}
      </h1>
    </div>


    <div class="row section-separator">
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoNameAr' | translate" [control]="fb?.NpoNameAr" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoNameEn' | translate" [control]="fb?.NpoNameEn" />
      </div>

      <div class="col-md-6 col-sm-12">
        <app-select [label]="messageTranslationPrefix+'NpoLegalForm' | translate" class="select-with-caret"
          [control]="fb?.NpoLegalForm" [data]="legalFormTypes"></app-select>
      </div>
      <div class="col-md-6 col-sm-12">
        <app-select [label]="messageTranslationPrefix+'HeadquarterEmirate' | translate" class="select-with-caret"
          [control]="fb?.HeadquarterEmirate" [data]="emirates"></app-select>
      </div>

      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'NpoDeclarationDecision' | translate"
          [control]="fb?.NpoDeclarationDecisionLink" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'MainCategory' | translate" [control]="fb?.MainCategory" />
      </div>

      <div class="col-md-6 col-sm-12">
        <app-select [label]="messageTranslationPrefix+'LicensingEntity' | translate" class="select-with-caret"
          [control]="fb?.LicensingEntity" [data]="filteredLicensingAuthorities" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'LicenseNumber' | translate" [control]="fb?.LicenseNumber" />
      </div>

      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'LicenseIssuanceDate' | translate"
          [control]="fb?.LicenseIssuanceDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'LicenseExpiryDate' | translate"
          [control]="fb?.LicenseExpiryDate" />
      </div>

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="button" class="btn basic-filled-button" (click)="next.emit()">
        {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
