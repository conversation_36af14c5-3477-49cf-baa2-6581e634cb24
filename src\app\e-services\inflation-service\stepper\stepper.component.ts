import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { TranslateService } from '@ngx-translate/core';
import { StepperService } from '../../../shared/services/stepper.service';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from '../../../shared/services/language.service';
import { customEmailValidator } from '../../../shared/validators/custom.email.validator';
import { customMobileValidator } from '../../../shared/validators/custom.mobile.validator';
import { HttpClient } from '@angular/common/http';
import { AuthService } from '../../../shared/services/auth.service';
import { InflationService } from '../services/inflation.service';
import { ServiceSteps } from '../enums/steps.enum';
import { SummaryStepsFullJourny } from '../../../shared/models/summry-steps-full-journey';
import { SummaryComponent } from '../components/summary/summary.component';
import { AttachmentsComponent } from '../components/attachments/attachments.component';
import { InflationInformationComponent } from '../components/request-form/inflation-information/inflation-information.component';
import { FamilyBookInformationComponent } from '../components/request-form/family-book-information/family-book-information.component';
import { PersonalInformationComponent } from '../components/request-form/personal-information/personal-information.component';
import { CaseInformationComponent } from '../components/request-form/case-information/case-information.component';
@Component({
  selector: 'app-stepper',
  templateUrl: './stepper.component.html',
  styleUrl: './stepper.component.scss'
})
export class StepperComponent {
  @ViewChild('stepper') stepper: MatStepper;

  @ViewChild('ApplicantInfoComponentTemplate') RequestFormComponentTemplate: CaseInformationComponent;
  @ViewChild('PersonalInfoComponentTemplate') PersonalInfoComponentTemplate: PersonalInformationComponent;
  @ViewChild('FamilyMembersComponentTemplate') FamilyMembersComponentTemplate: FamilyBookInformationComponent;
  @ViewChild('InflactionInfoComponentTemplate') InflactionInfoComponentTemplate: InflationInformationComponent;
  @ViewChild('AttachmentsComponentTemplate') AttachmentsComponentTemplate: AttachmentsComponent;
  @ViewChild('SummaryComponentTemplate') SummaryComponentTemplate: SummaryComponent;

  stepperForm: FormGroup;
  messageTranslationPrefix: string = 'services.swp.';
  journeySteps: SummaryStepsFullJourny[];
  journeyIndexActiveStep: number;
  lastselectedTabIndex: unknown;
  isReturnForUpdate: boolean = false;

  ContactId: string | null;
  isUaeNational: boolean = true;
  constructor(
    private formBuilder: FormBuilder,
    protected translate: TranslateService,
    protected stepperService: StepperService,
    private toastr: ToastrService,
    protected lang: LanguageService,
    private http: HttpClient,
    private inflationService: InflationService,
    private authService: AuthService,
  ) {
    this.stepperForm = this.formBuilder.group({
      caseInfo: this.formBuilder.group({
        reasonForApply: new FormControl('', [Validators.required])
      }),
      personalInfo: this.formBuilder.group({
        EmiratesID: [{ value: this.authService.getUserInfo()?.userInfo?.idn, disabled: true }, Validators.required],
        PassportNumber: [{ value: '', disabled: true }],
        IDNBackNumber: [{ value: '', disabled: true }],
        caseID: [{ value: '', disabled: true }],
        MaritalStatus: [{ value: '', disabled: true }, Validators.required],
        PreferredPhoneNumber: [{ value: '' }, [Validators.required, customMobileValidator(), Validators.minLength(12)]],
        alternativeNumber: [{ value: '', disabled: true }, Validators.required],
        PreferredEmail: ['', [Validators.required, customEmailValidator()]],
        AlternativeEmail: [{ value: '', disabled: true }, Validators.required],
        Occupations: ['', Validators.required],
        jobTitle: [''],
        Emirates: ['', Validators.required],
        Area: ['', Validators.required],
        Center: ['', Validators.required],
      }),
      familybookInfo: this.formBuilder.group({
        members: this.formBuilder.array([]),
      }),
      inflationInfo: this.formBuilder.group({
        ApplyInflationAllowance: ['', Validators.required],
        ApplyUtilityAllowance: [''],
        UtilityProvider: [''],
        UtilityAccountNumber: [''],
      }),
      attachments: this.formBuilder.group({
      }),
      summary: this.formBuilder.group({
        termsAndConditions: ['', Validators.requiredTrue],
        consent: ['', [Validators.required, Validators.requiredTrue]],
      }),
    });
  }

  ngOnInit(): void {
    this.inflationService.lookupData$.subscribe((data) => {
    });

    const userInfo = this.authService.getUserInfo();
    if (userInfo?.userInfo?.nationalityEN?.toUpperCase() != "UAE") {
      this.isUaeNational = false;
    }
    if (!this.ContactId) {
      this.inflationService.getSWPContactId(userInfo?.userInfo?.idn).subscribe((res) => {
        if (res.Status === 2000) {
          this.inflationService.setContactId(res.data);
        }
      });
    }

    this.inflationService.beneficiaryDetails$.subscribe(details => {
      // this.beneficiaryDetails = details;
    });

    this.inflationService.getBeneficiaryDetails(userInfo?.userInfo?.idn);

  }
  get caseInfo() {
    return this.stepperForm.get('caseInfo') as FormGroup;
  }
  get personalInfo() {
    return this.stepperForm.get('personalInfo') as FormGroup;
  }
  get familybookInfo() {
    return this.stepperForm.get('familybookInfo') as FormGroup;
  }
  get inflationInfo() {
    return this.stepperForm.get('inflationInfo') as FormGroup;
  }
  get attachments() {
    return this.stepperForm.get('attachments') as FormGroup;
  }
  get summary() {
    return this.stepperForm.get('summary') as FormGroup;
  }

  updateCurrentPortalStep = (index: number): void => {
    window.scrollTo(0, 0);
  }

  get progress() {
    var progressToal = 0;
    if (this.caseInfo.valid) {
      progressToal += 20;
    }
    if (this.personalInfo.valid) {
      progressToal += 20;
    }
    if (this.familybookInfo.valid) {
      progressToal += 10;
    }
    if (this.inflationInfo.valid) {
      progressToal += 10;
    }
    if (this.attachments.valid) {
      progressToal += 30;
    }
    if (this.summary.valid) {
      progressToal += 10;
    }
    return progressToal;
  }


  updateFormData() {
    this.stepperService.updateFormData(this.caseInfo.getRawValue());
    this.stepperService.updateFormData(this.personalInfo.getRawValue());
    this.stepperService.updateFormData(this.familybookInfo.getRawValue());
    this.stepperService.updateFormData(this.inflationInfo.getRawValue());
    this.stepperService.updateFormData(this.attachments.getRawValue());
  }

  goNext() {
    this.updateFormData();
    this.stepper.next();
  }
  goPrevious() {
    this.stepper.previous();
  }

  selectionChanged(event: any) {
    const currentStep = event.previouslySelectedIndex;
    if (currentStep === 0) {
      if (this.caseInfo.invalid) {
        this.caseInfo.markAllAsTouched();
        return;
      }
      if (this.personalInfo.invalid) {
        this.personalInfo.markAllAsTouched();
        return;
      }
      if (this.familybookInfo.invalid) {
        this.familybookInfo.markAllAsTouched();
        return;
      }
      if (this.inflationInfo.invalid) {
        this.inflationInfo.markAllAsTouched();
        return;
      }
    }
    else if (currentStep === 1) {
      if (this.attachments.invalid) {
        this.attachments.markAllAsTouched();
        return;
      }
    } else if (currentStep === 2) {
      if (this.summary.invalid) {
        this.summary.markAllAsTouched();
        return;
      }
    }
  }

  get ServiceSteps() {
    return ServiceSteps;
  }

  goBack() {
    this.stepper.previous();
  }

  goForward() {
    this.stepper.next();
  }

}
