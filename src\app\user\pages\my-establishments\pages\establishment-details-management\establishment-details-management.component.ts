import {
  Component,
  EventEmitter,
  Injector,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { MyEstablishmentComponentBase } from '../../models/base/my-establishment-component-base';
import { Lookup } from '../../../../../shared/models/lookup.model';
import {
  ALLOCATION_CYCLE,
  BOARD_ELECTION_CYCLE_LIST,
  ELECTION_METHOD_LIST,
  FREQUENCY_OF_BOARD_MEETINGS_LIST,
  FREQUENCY_OF_MEETING,
  FREQUENCY_OF_MEETING_APPOINTMENT,
  NATIONALITY_TYPES,
  NATURE_OF_FUNDS_ALLOCATED,
  NUMBER_OF_PERMISSIBLE_MEMBERS_LIST,
  RENOMINATION_LIST,
} from '../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { sumBy } from 'lodash';
import { FormGroup } from '@angular/forms';
import { Feedback } from '../../../../../e-services/npo-license-declaration/models/feedback';
import { SubmitionType } from '../../../../../e-services/npo-license-declaration/enums/submition-type.enum';
import { TreeNode } from '../../models/tree-menu';

@Component({
  selector: 'app-establishment-details-management',
  templateUrl: './establishment-details-management.component.html',
  styleUrl: './establishment-details-management.component.scss',
})
export class EstablishmentDetailsManagementComponent
  extends MyEstablishmentComponentBase
  implements OnInit {
  NPO_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO DeclarationServiceCatalogue';
  NPO_DECREE_SERVICES_CATALOGUE_NAME: string =
    'NPO-NPO Declaration By DecreeServiceCatalogue';
  NPO_LINCEING_DELEACRTION: string = 'NPO License Declaration';
  NPO_LINCEING: string = 'NPO Declaration';

  downloadButtons: any[] = [];
  formData: any;
  emirates: Lookup[] = [];
  positions: Lookup[] = [];
  EntitySectorTypes: Lookup[];
  panelOpenState: boolean = false;
  reportParameter: any[] = [];

  expandedNodes = new Set<string>();
  treeMenu: TreeNode[] = [
    {
      key: 'establishmentDetails',
      allowed: true,
      nameEn: 'My Establishment Details',
      nameAr: 'تفاصيل مؤسستي',
      icon: 'establishmentDetails',
      disabled: false,
      isOrdered: false
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Manage Employees',
      nameAr: 'إدارة الموظفين',
      children: [],
      icon: 'manageEmployees',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'NPO Amendment',
      nameAr: 'تعديل مؤسسة النفع العام',
      children: [],
      icon: 'npoAmendment',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'License Management',
      nameAr: 'إدارة التراخيص',
      children: [],
      icon: 'licenseManagement',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'General Assembly Meetings',
      nameAr: 'اجتماعات الجمعية العامة',
      children: [],
      icon: 'generalAssemblyMeeting',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'fundraising',
      allowed: false,
      nameEn: 'Fundraising',
      nameAr: 'جمع التبرعات',
      children: [
        {
          icon: '',
          key: 'permit',
          allowed: true,
          nameEn: 'Request to Issue Fundraising Permit',
          nameAr: 'طلب تصريح جمع التبرعات',
          route: `fund-raising-service/type-npo/${this.ActivatedRoute.snapshot.paramMap.get('establishmentId')}`,
          disabled: false,
          isOrdered: true
        },
        // {
        //   icon: '',
        //   key: 'extend',
        //   allowed: true,
        //   nameEn: 'Extend Fundraising Permit',
        //   nameAr: 'تمديد تصريح جمع التبرعات',
        //   route: '',
        //   disabled: true,
        // },
        // {
        //   icon: '',
        //   key: 'establishmentDetails',
        //   allowed: true,
        //   nameEn: 'Appeal to Fundraising Permit Rejection',
        //   nameAr: 'الاستئناف على رفض تصريح جمع التبرعات',
        //   route: '',
        //   disabled: true,
        // },
        {
          icon: '',
          key: 'establishmentDetails',
          allowed: true,
          nameEn: 'Fundraising Permits List',
          nameAr: 'قائمة تصاريح جمع التبرعات',
          click: (): any => (this.viewMode = 'PermitList'),
          disabled: false,
          isOrdered: true
        },
      ],
      icon: 'fundraising',
      disabled: false,
      isOrdered: true
    },
    {
      key: 'events',
      allowed: true,
      nameEn: 'Events / Activities',
      nameAr: 'الفعاليات / الانشطة',
      children: [
        {
          key: 'organizingEventsAndActivities',
          allowed: false,
          nameEn: 'Organize Activities / Events (Inside UAE)',
          nameAr:
            'تنظيم أنشطة/فعاليات (داخل دولة الإمارات العربية المتحدة)',
          icon: '',
          // we need to add the indication of NMWP or NPO comping from backend
          route: `organizing-events-and-activities/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          disabled: false,
          isOrdered: true
        },
        {
          key: 'participateInActivitiesAndEventsInsideAndOutsideUAE',
          allowed: true,
          nameEn:
            'Participate in Activities and Events Request (Inside/Outside UAE)',
          nameAr:
            'مشاركة في الأنشطة والفعاليات (داخل/خارج الإمارات العربية المتحدة)',
          icon: '',
          route: `activities-and-events-participation/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          disabled: false,
          isOrdered: true
        },
      ],
      icon: 'events',
      disabled: false,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Investment',
      nameAr: 'استثمار',
      children: [],
      icon: 'investment',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'General Requests',
      nameAr: 'الطلبات العامة',
      children: [],
      icon: 'generalRequests',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Voluntary Dissolution & Liquidation',
      nameAr: 'الحل والتصفية الطوعية',
      children: [],
      icon: 'voluntaryDissolutionAndLiquidation',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Decisions and Regulations',
      nameAr: 'القرارات واللوائح',
      children: [],
      icon: 'decisionsAndRegulations',
      disabled: true,
      isOrdered: true
    },
    {
      key: 'joiningAndAffiliatingAssociations',
      allowed: false,
      nameEn: 'Affiliate, Subscribe, or Join Associations or Regional and International Entities',
      nameAr: 'طلب تصريح الانتساب أو الاشتراك أو الانضمام إلى الجمعيات والهيئات الإقليمية أو الدولية',
      icon: 'joiningAndAffiliatingAssociations',
      route: `joining-and-affiliating-associations/${this.ActivatedRoute.snapshot.paramMap.get(
        'establishmentId'
      )}`,
      disabled: false,
      isOrdered: true
    },
    {
      key: 'branches',
      allowed: false,
      nameEn: 'Manage Branches',
      nameAr: 'إدارة الفروع',
      icon: 'manageBranches',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'openingBranchesRequest',
          allowed: true,
          nameEn: 'Request for Approval of Opening NPO Branch ',
          nameAr: ' طلب الموافقة على إنشاء فرع مؤسسة نفع عام ',
          route: `request-for-approval-of-opening-npo-branch/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          icon: '',
          disabled: false,
          isOrdered: true
        },
      ],
    },
    {
      key: 'nocRequest',
      allowed: false,
      nameEn: 'Request for Receive Donations NOC',
      nameAr: 'طلب الحصول على شهادة عدم ممانعة لتلقي التبرعات',
      icon: 'nocRequest',
      disabled: false,
      isOrdered: true,
      route: `noc-request/${this.ActivatedRoute.snapshot.paramMap.get('establishmentId')}`
    },
    {
      key: 'banks',
      allowed: false,
      nameEn: 'Request to issue a certificate for banks and official bodies',
      nameAr: 'طلب اصدار شهادة للبنوك والجهات الرسمية',
      icon: 'banks',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'newBankAccountCertificate',
          allowed: true,
          nameEn: 'Request to open a new bank account',
          nameAr: 'طلب فتح حساب بنكي جديد',
          icon: '',
          disabled: false,
          isOrdered: true,
          route: `request-to-open-new-bank-account-certificate/${this.ActivatedRoute.snapshot.paramMap.get('establishmentId')}`
        },
        {
          key: 'npoBankAccountsList',
          allowed: true,
          nameEn: 'NPO Bank Accounts List',
          nameAr: 'قائمة الحسابات البنكية للمؤسسة',
          icon: '',
          disabled: true,
          isOrdered: true,
          route: ``
        },
      ]
    },
    {
      key: 'membersList',
      allowed: false,
      nameEn: 'Members List',
      nameAr: 'قائمة الاعضاء',
      icon: 'membersList',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'pendingMembershipRequests',
          allowed: true,
          nameEn: 'Pending Membership Requests',
          nameAr: 'طلبات العضوية المعلقة',
          icon: '',
          disabled: true,
          isOrdered: true,
          click: (): any => (this.viewMode = 'PendingMemrshipRequests'),
        },
        {
          key: 'registerNewMember',
          allowed: true,
          nameEn: 'Register New Member',
          nameAr: 'تسجيل عضو جديد',
          icon: '',
          disabled: false,
          isOrdered: true,
          route: `request-to-join-npo/${this.ActivatedRoute.snapshot.paramMap.get('establishmentId')}`
        },
      ]
    },
  ];

  viewMode: string = 'Details';

  @Input() proposedNameEn: string | undefined;
  @Input() proposedNameAr: string | undefined;
  @Input() requestNumber: string | undefined;
  @Input() selectedLegalFormType: any;
  @Input() legalFromTypes: Lookup[] = [];
  @Input() migrationID: string | undefined;
  @Input() form: FormGroup;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() showAction: boolean = false;
  @Input() showGetFoundingMemberButton: boolean = false;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() feedbackList: Feedback[] | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();

  private showMessage: boolean = false;
  private submitationType: SubmitionType = SubmitionType.SaveAsDraft;
  private referenceNumber: string = '';
  public docTypes: any[];
  public get ShowSuccessMessage(): boolean {
    return this.showMessage;
  }
  public get SubmitationType(): number {
    return this.submitationType;
  }
  public get ReferenceNumber(): string {
    return this.referenceNumber;
  }

  constructor(injector: Injector) {
    super(injector);

    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.reviewAndSubmit.';
    this.getLegalFormTypes();
  }

  orderMenuItems = (tree: TreeNode[], key: string = "nameEn"): TreeNode[] => {
    return tree
      .map(node => {
        const children = node.children?.length && node.isOrdered
          ? this.orderMenuItems(node.children, key)
          : node.children;

        return {
          ...node,
          children: children
        };
      })
      .sort((a, b) => {
        if (a.isOrdered && b.isOrdered) {
          return a[key].localeCompare(b.nameEn);
        }
        return 0;
      });
  }


  getLegalFormTypes = (): void => {
    this.MyEstablishmentService.getLegalFormTypes().subscribe(_ => {
      this.legalFromTypes = _;
    });
  }

  ngOnInit(): void {
    this.treeMenu = this.orderMenuItems(this.treeMenu, this.LanguageService.IsArabic ? 'nameAr' : 'nameEn');
    this.MyEstablishmentService.getLookupData();
    this.MyEstablishmentService.lookupData$.subscribe({
      next: (data) => {
        if (data) {
          this.emirates = data.Emirates;
          this.positions = data.InteriimCommiteePosition;
          this.reportParameter = data.ReportParametre;

          this.ActivatedRoute.data.subscribe(({ establishmentData }) => {
            this.formData = establishmentData?.data[0];

            if (
              this.formData?.BasicInformationForm?.IsEligableForFundRaising
            ) {
              this.changeTreeMenuVisibility('fundraising', true);
            }

            if (
              this.formData?.BasicInformationForm?.IsEligableForAffiliating
            ) {
              this.changeTreeMenuVisibility('joiningAndAffiliatingAssociations', true);
            }

            if (
              this.formData?.BasicInformationForm?.IsEligableForOrganizeActivitiesEvents
            ) {
              this.changeTreeMenuVisibilityByParent('events', 'organizingEventsAndActivities', true);
            }


            if (this.formData?.BasicInformationForm?.IsEligibleForRequestOpeningNPOBranch) {
              this.changeTreeMenuVisibility('branches', true);
            }


            if (this.formData?.BasicInformationForm?.IsEligibleForRequestOpeningBankAccount) {
              this.changeTreeMenuVisibility('banks', true);
            }

            if (this.formData?.BasicInformationForm?.IsEligableForRecieveDonation) {
              this.changeTreeMenuVisibility('nocRequest', true);
            }



            this.collectionSizeProposedName =
              this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
            this.filteredCountProposedName =
              this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
            this.getPremiumDataProposedName();

            this.collectionSizeNpoNumber =
              this.formData?.BasicInformationForm?.npoNumbers?.length || 0;
            this.filteredCountNpoNumber =
              this.formData?.BasicInformationForm?.npoNumbers?.length || 0;
            this.getPremiumDataNpoNumber();

            this.collectionSizeFoundingMember =
              this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
            this.filteredCountFoundingMember =
              this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
            this.getPremiumDataFoundingMember();

            this.collectionSizeObjectives =
              this.formData?.ObjectivesForm?.Objectif?.length || 0;
            this.filteredCountObjectives =
              this.formData?.ObjectivesForm?.Objectif?.length || 0;
            this.getPremiumDataObjectives();

            this.collectionSizeInterimMembers =
              this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
            this.filteredCountInterimMembers =
              this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
            this.getPremiumDataInterimCommittee();

            this.collectionSizeMemberShip =
              this.formData?.MembershipForm?.MembershipConditions?.length || 0;
            this.filteredCountMemberShip =
              this.formData?.MembershipForm?.MembershipConditions?.length || 0;
            this.getPremiumDataMemberShip();

            this.collectionSizeBoardOfDirectorsMembershipConditions =
              this.formData?.BoardOfDirectorsForm?.MembershipConditions
                ?.length || 0;
            this.filteredCountBoardOfDirectorsMembershipConditions =
              this.formData?.BoardOfDirectorsForm?.MembershipConditions
                ?.length || 0;
            this.getPremiumDataBoardOfDirectorsMembershipConditions();

            this.collectionSizeBoardOfDirectorsPositions =
              this.formData?.BoardOfDirectorsForm?.Positions?.length || 0;
            this.filteredCountBoardOfDirectorsPositions =
              this.formData?.BoardOfDirectorsForm?.Positions?.length || 0;
            this.getPremiumDataBoardOfDirectorsPositions();

            this.collectionSizeBoardOfTrusteePositions =
              this.formData?.BoardOfTrusteesForm?.Positions?.length || 0;
            this.filteredCountBoardOfTrusteePositions =
              this.formData?.BoardOfTrusteesForm?.Positions?.length || 0;
            this.getPremiumDataBoardOfTrusteePositions();

            this.collectionSizeBoardOfTrusteeMembershipConditions =
              this.formData?.BoardOfTrusteesForm?.Conditions?.length || 0;
            this.filteredCountBoardOfTrusteeMembershipConditions =
              this.formData?.BoardOfTrusteesForm?.Conditions?.length || 0;
            this.getPremiumDataBoardOfTrusteeMembershipConditions();

            this.collectionSizeBoardOfTrusteeMembershipMembers =
              this.formData?.BoardOfTrusteesForm?.Memberlist?.length || 0;
            this.filteredCountBoardOfTrusteeMembershipMembers =
              this.formData?.BoardOfTrusteesForm?.Memberlist?.length || 0;
            this.getPremiumDataBoardOfTrusteeMembershipMembers();

            this.collectionSizeTargetGroup =
              this.formData?.BasicInformationForm?.NPOTargetgroup?.length || 0;
            this.filteredCountTargetGroup =
              this.formData?.BasicInformationForm?.NPOTargetgroup?.length || 0;
            this.getPremiumDataTargetGroup();

            this.collectionSizeFundServices =
              this.formData?.FundServicesForm?.FundServices?.length || 0;
            this.filteredCountFundServices =
              this.formData?.FundServiceForm?.FundService?.length || 0;
            this.getPremiumDataFundServices();

            this.collectionSizeAllocationOfFoundationFunds =
              this.formData?.AllocationFundForm?.AllocationFunds?.length || 0;
            this.filteredCountAllocationOfFoundationFunds =
              this.formData?.AllocationFundForm?.AllocationFunds?.length || 0;
            this.getPremiumDataAllocationOfFoundationFunds();

            this.collectionSizeExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.length || 0;
            this.filteredCountExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.length || 0;
            this.getPremiumDataExamplesOfActivities();

            this.docTypes = data?.DocumentType;
            const serviceCatalogues = data.CrmConfiguration;
            const legalFormType = this.formData?.BasicInformationForm?.npoform;

            if (
              legalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree ||
              legalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree
            ) {
              const service = serviceCatalogues.find(
                (_) =>
                  _.Name.toLocaleLowerCase().trim() ==
                  this.NPO_DECREE_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim()
              );
              if (service) {
                this.docTypes = this.docTypes?.filter(
                  (_) => _.ServiceCatalogue === service.Value
                );
              }
            } else {
              const service = serviceCatalogues.find(
                (_) =>
                  _.Name.toLocaleLowerCase().trim() ==
                  this.NPO_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim()
              );
              if (service) {
                this.docTypes = this.docTypes?.filter(
                  (_) => _.ServiceCatalogue === service.Value
                );
              }
            }

            this.getDownloadButton();
            // this.checkTempleteReplace();
          });
        }
      },
    });

    this.Translation.onLangChange.subscribe(event => {
      this.treeMenu = this.orderMenuItems(this.treeMenu, this.LanguageService.IsArabic ? 'nameAr' : 'nameEn');
    });
  }

  changeTreeMenuVisibility(key: string, allowed: boolean): void {
    let menu = this.treeMenu.find((tree) => tree.key === key);
    if (menu) menu.allowed = allowed;
  }

  changeTreeMenuVisibilityByParent(parentKey: string, key: string, allowed: boolean): void {
    let menu = this.treeMenu.find((tree) => tree.key === parentKey)?.children?.find(_ => _.key === key);
    if (menu) menu.allowed = allowed;
  }

  pageProposedName = 1;
  pageSizeProposedName = 10;
  collectionSizeProposedName = 0;
  filteredCountProposedName = 0;
  paginateDataProposedName: any[] = [];
  getPremiumDataProposedName(): void {
    const start = (this.pageProposedName - 1) * this.pageSizeProposedName;
    const end = start + this.pageSizeProposedName;
    this.paginateDataProposedName =
      this.formData?.BasicInformationForm?.ProposedNames?.slice(start, end) ||
      [];
  }

  pageNpoNumber = 1;
  pageSizeNpoNumber = 10;
  collectionSizeNpoNumber = 0;
  filteredCountNpoNumber = 0;
  paginateDataNpoNumber: any[] = [];
  getPremiumDataNpoNumber(): void {
    const start = (this.pageNpoNumber - 1) * this.pageSizeNpoNumber;
    const end = start + this.pageSizeNpoNumber;
    this.paginateDataNpoNumber =
      this.formData?.FoundingMembersForm?.npoNumbers?.slice(start, end) || [];
  }

  pageFoundingMember = 1;
  pageSizeFoundingMember = 10;
  collectionSizeFoundingMember = 0;
  filteredCountFoundingMember = 0;
  paginateDataFoundingMember: any[] = [];
  getPremiumDataFoundingMember(): void {
    const start = (this.pageFoundingMember - 1) * this.pageSizeFoundingMember;
    const end = start + this.pageSizeFoundingMember;
    this.paginateDataFoundingMember =
      this.formData?.FoundingMembersForm?.FounderMember?.slice(start, end) ||
      [];
  }

  pageObjectives = 1;
  pageSizeObjectives = 10;
  collectionSizeObjectives = 0;
  filteredCountObjectives = 0;
  paginateDataObjectives: any[] = [];

  getPremiumDataObjectives(): void {
    const start = (this.pageObjectives - 1) * this.pageSizeObjectives;
    const end = start + this.pageSizeObjectives;
    this.paginateDataObjectives =
      this.formData?.ObjectivesForm?.Objectif?.slice(start, end) || [];
  }

  pageTargetGroup = 1;
  pageSizeTargetGroup = 10;
  collectionSizeTargetGroup = 0;
  filteredCountTargetGroup = 0;
  paginateDataTargetGroup: any[] = [];
  getPremiumDataTargetGroup(): void {
    const start = (this.pageTargetGroup - 1) * this.pageSizeTargetGroup;
    const end = start + this.pageSizeTargetGroup;
    this.paginateDataTargetGroup =
      this.formData?.BasicInformationForm?.NPOTargetgroup?.slice(start, end) ||
      [];
  }

  pageInterimMembers = 1;
  pageSizeInterimMembers = 10;
  collectionSizeInterimMembers = 0;
  filteredCountInterimMembers = 0;
  paginateDataInterimMembers: any[] = [];

  getPremiumDataInterimCommittee(): void {
    const start = (this.pageInterimMembers - 1) * this.pageSizeInterimMembers;
    const end = start + this.pageSizeInterimMembers;
    this.paginateDataInterimMembers =
      this.formData?.InterimCommitteeForm?.InterimCommitee?.slice(start, end) ||
      [];
  }

  pageMemberShip = 1;
  pageSizeMemberShip = 10;
  collectionSizeMemberShip = 0;
  filteredCountMemberShip = 0;
  paginateDataMemberShip: any[] = [];

  getPremiumDataMemberShip(): void {
    const start = (this.pageMemberShip - 1) * this.pageSizeMemberShip;
    const end = start + this.pageSizeMemberShip;
    this.paginateDataMemberShip =
      this.formData?.MembershipForm?.MembershipConditions?.slice(start, end) ||
      [];
  }

  pageBoardOfDirectorsMembershipConditions = 1;
  pageSizeBoardOfDirectorsMembershipConditions = 10;
  collectionSizeBoardOfDirectorsMembershipConditions = 0;
  filteredCountBoardOfDirectorsMembershipConditions = 0;
  paginateDataBoardOfDirectorsMembershipConditions: any[] = [];
  getPremiumDataBoardOfDirectorsMembershipConditions(): void {
    const start =
      (this.pageBoardOfDirectorsMembershipConditions - 1) *
      this.pageSizeBoardOfDirectorsMembershipConditions;
    const end = start + this.pageSizeBoardOfDirectorsMembershipConditions;
    this.paginateDataBoardOfDirectorsMembershipConditions =
      this.formData?.BoardOfDirectorsForm?.MembershipConditions?.slice(
        start,
        end
      ) || [];
  }

  pageBoardOfDirectorsPositions = 1;
  pageSizeBoardOfDirectorsPositions = 10;
  collectionSizeBoardOfDirectorsPositions = 0;
  filteredCountBoardOfDirectorsPositions = 0;
  paginateDataBoardOfDirectorsPositions: any[] = [];
  getPremiumDataBoardOfDirectorsPositions(): void {
    const start =
      (this.pageBoardOfDirectorsPositions - 1) *
      this.pageSizeBoardOfDirectorsPositions;
    const end = start + this.pageSizeBoardOfDirectorsPositions;
    this.paginateDataBoardOfDirectorsPositions =
      this.formData?.BoardOfDirectorsForm?.Positions?.slice(start, end) || [];
  }

  pageBoardOfTrusteeMembershipConditions = 1;
  pageSizeBoardOfTrusteeMembershipConditions = 10;
  collectionSizeBoardOfTrusteeMembershipConditions = 0;
  filteredCountBoardOfTrusteeMembershipConditions = 0;
  paginateDataBoardOfTrusteeMembershipConditions: any[] = [];
  getPremiumDataBoardOfTrusteeMembershipConditions(): void {
    const start =
      (this.pageBoardOfTrusteeMembershipConditions - 1) *
      this.pageSizeBoardOfTrusteeMembershipConditions;
    const end = start + this.pageSizeBoardOfTrusteeMembershipConditions;
    this.paginateDataBoardOfTrusteeMembershipConditions =
      this.formData?.BoardOfTrusteesForm?.Conditions?.slice(start, end) || [];
  }

  pageBoardOfTrusteeMembershipMembers = 1;
  pageSizeBoardOfTrusteeMembershipMembers = 10;
  collectionSizeBoardOfTrusteeMembershipMembers = 0;
  filteredCountBoardOfTrusteeMembershipMembers = 0;
  paginateDataBoardOfTrusteeMembershipMembers: any[] = [];
  getPremiumDataBoardOfTrusteeMembershipMembers(): void {
    const start =
      (this.pageBoardOfTrusteeMembershipMembers - 1) *
      this.pageSizeBoardOfTrusteeMembershipMembers;
    const end = start + this.pageSizeBoardOfTrusteeMembershipMembers;
    this.paginateDataBoardOfTrusteeMembershipMembers =
      this.formData?.BoardOfTrusteesForm?.Memberlist?.slice(start, end) || [];
  }

  pageBoardOfTrusteePositions = 1;
  pageSizeBoardOfTrusteePositions = 10;
  collectionSizeBoardOfTrusteePositions = 0;
  filteredCountBoardOfTrusteePositions = 0;
  paginateDataBoardOfTrusteePositions: any[] = [];
  getPremiumDataBoardOfTrusteePositions(): void {
    const start =
      (this.pageBoardOfTrusteePositions - 1) *
      this.pageSizeBoardOfTrusteePositions;
    const end = start + this.pageSizeBoardOfTrusteePositions;
    this.paginateDataBoardOfTrusteePositions =
      this.formData?.BoardOfTrusteesForm?.Positions?.slice(start, end) || [];
  }

  pageFundServices = 1;
  pageSizeFundServices = 10;
  collectionSizeFundServices = 0;
  filteredCountFundServices = 0;
  paginateDataFundServices: any[] = [];
  getPremiumDataFundServices(): void {
    const start = (this.pageFundServices - 1) * this.pageSizeFundServices;
    const end = start + this.pageSizeFundServices;
    this.paginateDataFundServices =
      this.formData?.FundServiceForm?.FundService?.slice(start, end) || [];
  }

  pageAllocationOfFoundationFunds = 1;
  pageSizeAllocationOfFoundationFunds = 10;
  collectionSizeAllocationOfFoundationFunds = 0;
  filteredCountAllocationOfFoundationFunds = 0;
  paginateDataAllocationOfFoundationFunds: any[] = [];
  getPremiumDataAllocationOfFoundationFunds(): void {
    const start =
      (this.pageAllocationOfFoundationFunds - 1) *
      this.pageSizeAllocationOfFoundationFunds;
    const end = start + this.pageSizeAllocationOfFoundationFunds;
    this.paginateDataAllocationOfFoundationFunds =
      this.formData?.AllocationFundForm?.AllocationFunds?.slice(start, end) ||
      [];
  }

  pageExamplesOfActivities = 1;
  pageSizeExamplesOfActivities = 10;
  collectionSizeExamplesOfActivities = 0;
  filteredCountExamplesOfActivities = 0;
  paginateDataExamplesOfActivities: any[] = [];
  getPremiumDataExamplesOfActivities(): void {
    const start = (this.pageExamplesOfActivities - 1) * this.pageSizeExamplesOfActivities;
    const end = start + this.pageSizeExamplesOfActivities;
    this.paginateDataExamplesOfActivities = this.formData?.BasicInformationForm?.NpoActivityPrograms?.slice(start, end) || [];
  }

  getEmirate(emirateId: any): string {
    const emirate = this.emirates?.find((e) => e.ID == emirateId);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getEntitySectorType = (id): string => {
    const type = this.EntitySectorTypes?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? type?.NameArabic ?? ''
      : type?.NameEnglish ?? '';
  };

  getPosition(positionId: any): string {
    const postion = this.positions?.find((_) => _.ID === positionId);
    return this.LanguageService.IsArabic
      ? postion?.NameArabic ?? ''
      : postion?.NameEnglish ?? '';
  }

  getFrequencyOfBoardMeetings(id: any): string {
    const frequency = FREQUENCY_OF_BOARD_MEETINGS_LIST?.find(
      (freq) => freq.ID == id
    );
    return this.LanguageService.IsArabic
      ? frequency?.NameArabic ?? ''
      : frequency?.NameEnglish ?? '';
  }

  getElectionMethod(id: any): string {
    const method = ELECTION_METHOD_LIST?.find((m) => m.ID == id);
    return this.LanguageService.IsArabic
      ? method?.NameArabic ?? ''
      : method?.NameEnglish ?? '';
  }

  getRenominationStatus(id: any): string {
    const renomination = RENOMINATION_LIST?.find((r) => r.ID == id);
    return this.LanguageService.IsArabic
      ? renomination?.NameArabic ?? ''
      : renomination?.NameEnglish ?? '';
  }

  getPermissibleTerms(id: any): string {
    const terms = NUMBER_OF_PERMISSIBLE_MEMBERS_LIST?.find((t) => t.ID == id);
    return this.LanguageService.IsArabic
      ? terms?.NameArabic ?? ''
      : terms?.NameEnglish ?? '';
  }

  getBoardElectionCycle(id: any): string {
    const cycle = BOARD_ELECTION_CYCLE_LIST?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? cycle?.NameArabic ?? ''
      : cycle?.NameEnglish ?? '';
  }

  gettotalFundsAmount = (): number => {
    return sumBy(
      this.formData?.AllocationFundForm?.AllocationFunds,
      (control: any) => Number(control.ValueOfTheFund)
    );
  };

  getNatureOfAllocatedFunds(id: any): string {
    const obj = NATURE_OF_FUNDS_ALLOCATED?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? obj?.NameArabic ?? ''
      : obj?.NameEnglish ?? '';
  }

  getAllocationCycle(id: any): string {
    const obj = ALLOCATION_CYCLE?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? obj?.NameArabic ?? ''
      : obj?.NameEnglish ?? '';
  }

  getAnnualMembershipDueDateValue() {
    return this.LanguageService.IsArabic
      ? 'مع بداية السنة المالية (جزء من قيمة الاشتراك)'
      : 'Beginning of the fiscal year(Partial subscription Amount)';
  }

  getFrequencyOfMeetings(id: any): string {
    const frequencyOfMeetings = FREQUENCY_OF_MEETING?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? frequencyOfMeetings?.NameArabic ?? ''
      : frequencyOfMeetings?.NameEnglish ?? '';
  }

  getFrequencyOfAppointments(id: any): string {
    const frequencyOfMeetings = FREQUENCY_OF_MEETING_APPOINTMENT?.find(
      (c) => c.ID == id
    );
    return this.LanguageService.IsArabic
      ? frequencyOfMeetings?.NameArabic ?? ''
      : frequencyOfMeetings?.NameEnglish ?? '';
  }

  canSeeAgendaSection = (): boolean => {
    if (
      (this.collectionSizeFoundingMember > 1 &&
        this.formData?.BasicInformationForm?.npoform ===
        this.LEGAL_FORM_TYPES.NationalSociety) ||
      this.formData?.BasicInformationForm?.npoform !==
      this.LEGAL_FORM_TYPES.NationalSociety
    )
      return true;

    return false;
  };

  isUnionLegalForm = (): boolean =>
    this.formData?.BasicInformationForm?.npoform == this.LEGAL_FORM_TYPES.Union;

  getNationalType = (isLocalCondition: number): string => {
    let type = NATIONALITY_TYPES.find((type) => type.ID == isLocalCondition);
    if (type) {
      return this.LanguageService.IsArabic ? type.NameArabic : type.NameEnglish;
    }
    return '';
  };

  getDocTypeName = (id: string): string => {
    let doctype = this.docTypes?.find((_) => _.ID == id);
    return (
      (this.LanguageService.IsArabic
        ? doctype?.NameArabic
        : doctype?.NameEnglish) ?? ''
    );
  };

  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes.find(
      (_) => _.ID == this.formData?.BasicInformationForm?.npoform
    );
    if (type) {
      return type.NameEnglish;
    }
    return '';
  }


  getDownloadButton = (): void => {
    if (!this.legalFormTypeTitle) {
      return;
    }
    const legalFormType = this.legalFormTypeTitle.toLocaleLowerCase().trim();
    const collection = this.reportParameter.filter(param => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType && param.ServiceCatlogue?.toLocaleLowerCase().trim() == this.NPO_LINCEING.toLocaleLowerCase().trim());
    this.downloadButtons = collection.filter(_ => _.RequestStatus == "100000003" || _.RequestStatus == null);
  };

  generateDownloadLink = (file: any): string =>
    `data:${file?.mimeType};base64,${file?.Base64}`;

  get requestId(): string {
    return this.ActivatedRoute.snapshot.paramMap.get('id') ?? '';
  }

  submit = async (saveDraft: boolean = true): Promise<void> => {
    const translationTitle = this.Translation.instant(
      saveDraft
        ? 'alert.saveTheRequestAsDraftTitle'
        : 'alert.saveTheRequestAsSubmittTitle'
    );
    const translationMessage = this.Translation.instant(
      saveDraft
        ? 'notify.saveTheRequestAsDraftMessage'
        : 'notify.saveTheRequestAsSubmitMessage'
    );
    const translations = this.Translation.instant('alert');
    let param = {
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#92722A',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmButtonText,
      cancelButtonText: translations.cancelButtonText,
    };
    const confirmed = await this.AlertService.showAlert(param);
    if (confirmed) {
      let requestCode = this.StepperService.requestCode;
      let request = this.getRequestMappingObject(
        undefined,
        undefined,
        saveDraft === true ? requestCode : 100000000
      );
      this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe(
        (res) => {
          if (res.Status == 2000) {
            this.NotifyService.showSuccess(
              'notify.success',
              saveDraft ? 'notify.draftSaved' : 'notify.submitted'
            );

            this.StepperService.requestId = res?.Id;
            this.StepperService.establishmentId = res?.EstablishmentId;
            this.referenceNumber = res?.RequestNo;
            this.submitationType = SubmitionType.SaveAsDraft;
            if (!saveDraft) {
              this.submitationType = SubmitionType.Submit;
              this.showMessage = true;
            } else {
              this.showMessage = false;
            }

            if (!this.StepperService.requestId) {
              this.StepperService.setAutoStep(true);
            } else {
              this.getRouterMode();
            }
          } else {
            let errorMessage = this.LanguageService.IsArabic
              ? res.MessageAR
              : res.MessageEN;
            this.NotifyService.showError('notify.error', errorMessage.trim());
          }
        },
        (error) => {
          this.NotifyService.showError('notify.error', error);
        }
      );
    }
  };

  childrenAccessor = (menu: TreeNode) => menu.children ?? [];

  hasChild = (_: number, menu: TreeNode) =>
    !!menu.children && menu.children.length > 0;


  download = (file: any): void => this.downloadNpoFiles('application/pdf', file.ReportName, this.formData?.NpoRequest, file.LogicalName, this.formData?.Id);
}
