.aegov-alert{
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  @media only screen and (min-width: 1024px) {
    flex-direction: row;
    align-items: start;
  }
  .alert-content{
    display: flex;
    flex-direction: column;
    gap: 8px;
    @media only screen and (min-width: 1024px) {
      flex-direction: row;
      align-items: start;
    }
    a{
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      transition: color 0.3s ease-in-out;
    }
    p{
      margin-bottom: 0px;
      font-size: 16px;
      font-weight: 400;
      line-height: 22px; 
    }
  }
  &.alert-warning{
    &-alt{
      border-color: $camelyellow-600;
      color: $camelyellow-600;
      background: none;
      svg{
        fill: $camelyellow-600;
      }
      a{
        color: $camelyellow-700;
        &:hover{
          color: $camelyellow-600;
        }
      }
      p{
        color: $camelyellow-600;
      }
    }
  }
  &.alert-info{
    border-color: $techblue-50;
    color: $camelyellow-600;
    background: $techblue-50;
    svg{
      fill: $techblue-900;
    }
    a{
      color: $techblue-900;
      &:hover{
        color: $techblue-600;
      }
    }
    p{
      color: $techblue-900;
    }
  }

}