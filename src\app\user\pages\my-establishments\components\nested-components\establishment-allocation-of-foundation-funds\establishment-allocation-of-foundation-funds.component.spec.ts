/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EstablishmentAllocationOfFoundationFundsComponent } from './establishment-allocation-of-foundation-funds.component';

describe('EstablishmentAllocationOfFoundationFundsComponent', () => {
  let component: EstablishmentAllocationOfFoundationFundsComponent;
  let fixture: ComponentFixture<EstablishmentAllocationOfFoundationFundsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EstablishmentAllocationOfFoundationFundsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EstablishmentAllocationOfFoundationFundsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
