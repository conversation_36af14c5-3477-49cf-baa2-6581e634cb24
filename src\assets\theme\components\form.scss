// -- inputs
app-input,
app-input-datetime,
app-input-location,
app-select,
app-select-search,
app-select-search-api,
app-input-date,
app-textarea,
app-input-with-action,
mat-form-field,
input {
  .mat-mdc-input-element {
    &::placeholder {
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      color: $aeblack-200;
    }
  }

  &:has(:focus-visible),
  &:has([aria-owns]) {
    .mdc-text-field {
      border: 1px solid $aegold-600 !important;
    }

    i {
      color: $aegold-600;
    }

    svg {
      fill: $aegold-600 !important;
    }
  }

  .form-label {
    margin-bottom: 4px;
    font-weight: $font-medium;
    // padding-top: 20px;
    // padding-top: 12px;
    font-size: 14px;
    line-height: 22px;

    &.requird::after {
      content: " *" !important;
      color: $aered-500;
      font-weight: $font-bold;
    }
  }

  .mat-mdc-form-field {
    padding-top: 0;
    // margin-bottom: 24px;

    .mdc-text-field {
      border: 1px solid $aegold-400;
      border-radius: 6px;

      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        // border: none;
        display: none;
      }

      label {
        display: none;
      }
    }

    mat-icon-button {
      svg {
        fill: $aegold-400;
      }
    }

    .mat-mdc-icon-button svg {
      fill: $aegold-400;
    }

    .mat-mdc-form-field-subscript-wrapper {
      @media(min-width:1024px) {
        margin-bottom: 18px !important;
      }

      @media(max-width:1023px) {
        margin-bottom: 10px !important;
      }
    }

    .mat-mdc-form-field-hint-wrapper,
    .mat-mdc-form-field-error-wrapper {
      padding: 0;
      margin-top: 4px;

      .mat-mdc-form-field-error {
        text-wrap: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
      }
    }
  }

  &.disabled {

    .mat-mdc-form-field-icon-suffix,
    mat-datepicker-toggle {
      pointer-events: none;
    }

    pointer-events: none;
    color: $aeblack-200;

    .mdc-text-field {
      border-color: $aegold-200;

      svg {
        fill: $aegold-300;
      }

      .icon-toggle {
        color: $aegold-300;
      }
    }

    .mat-mdc-form-field-subscript-wrapper {
      visibility: hidden;
    }

    &::placeholder,
    input,
    .mat-mdc-select-placeholder {
      color: $aeblack-100 !important;
    }
  }
}

.form-control-input:has(.disabled) {
  border-color: $aegold-200 !important;
  pointer-events: none;

  svg {
    fill: $aegold-300 !important;
  }

  .icon-toggle {
    color: $aegold-300 !important;
  }

  input::placeholder {
    color: $aeblack-100 !important;
  }
}

// .mdc-text-field--disabled,
// app-input .mat-mdc-form-field .mdc-text-field.mdc-text-field--disabled {
//   border-color: #d7d8db;
//   background: #fcfcfc;
//   box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
// }

app-select,
app-select-search,
app-select-search-api {

  &:has(:focus-visible),
  &:has(:focus),
  &:has([aria-expanded="true"]) {
    .mdc-text-field {
      border: 1px solid $aegold-600 !important;
    }

    i {
      color: $aegold-600;
    }

    svg {
      fill: $aegold-600 !important;
    }
  }
}

app-input,
app-input-datetime,
app-input-location,
app-select,
app-select-search,
app-select-search-api,
app-input-date,
app-input-with-action {
  .mat-mdc-text-field-wrapper:not(.mat-mdc-form-field-bottom-align) {
      // height: 50px !important;
      height: 52px !important;

    @media (max-width: 1024px){
      height: 40px !important;
    }
  }
}


app-input:has(.ng-invalid):has(.ng-touched),
app-input-location:has(.ng-invalid):has(.ng-touched),
app-textarea:has(.ng-invalid):has(.ng-touched),
app-select:has(.ng-invalid):has(.ng-touched),
app-input-date:has(.ng-invalid):has(.ng-touched),
app-input-datetime:has(.ng-invalid):has(.ng-touched) {
  .mdc-text-field {
    border: 1px solid $aered-400 !important;
    background-color: $aered-50;
  }

  .form-label {
    color: $aered-600;
  }

  .mat-mdc-input-element::placeholder {
    color: $aered-600 !important;
  }

  mat-select {
    +.icon-toggle {
      color: $aered-400;
    }

    &[aria-expanded="true"] {
      +.icon-toggle {
        color: $aered-400;
      }
    }
  }
}

// .mat-mdc-form-field .mdc-text-field--outlined {
//   height: 52px;
// }
.mdc-text-field--outlined .mat-mdc-form-field-infix,
.mdc-text-field--no-label .mat-mdc-form-field-infix {
  min-height: 0;
  // padding-top: 10px !important;
  // padding-bottom: 10px !important;
  padding: 0.8rem 0 !important;

  @media (max-width:1024px){
    padding: 0.5rem 0 !important;
  }
}

app-slide-toggle {

  @media (max-width: 1024px){
    .mat-mdc-form-field-subscript-wrapper {
      // margin-bottom: 24px !important;
      display: none !important;
    }

    .mdc-switch {
      padding: 0.1rem 0 !important;
      margin: 0 0 0.5rem !important;
    }

    .mat-internal-form-field {
      align-items: start !important;
      flex-direction: column !important;
    }
  }

  .mat-text-field,
  .mdc-text-field--outlined,
  .mdc-text-field--no-label,
  .mdc-text-field--outlined .mat-mdc-form-field-infix,
  .mdc-text-field--no-label .mat-mdc-form-field-infix,
  .mat-mdc-form-field-hint-wrapper {
    padding: 0 !important;
  }

  .mat-mdc-form-field .mdc-text-field.mdc-text-field--disabled {
    border-color: unset !important;
    background: unset !important;
    box-shadow: unset !important;
  }

  // .mat-mdc-notch-piece {
  //   border: 0 !important;
  //   border-radius: unset !important;
  //   border-color: unset !important;
  // }
}

app-forms-index {
  h3 {
    font-weight: $font-bold !important;
    font-size: 24px !important;
    line-height: 32px !important;
    color: $aeblack-800;
  }
}

// -- File Upload
app-file-upload-swp,
app-file-upload {
  mat-form-field {
    .mdc-text-field {
      border-radius: 8px;
      background-color: $aegold-50;
      padding: 24px;

      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        // border: none;
        display: none
      }

      label {
        display: none;
      }

      @media only screen and (max-width: 991px) {
        .upload-button {
          margin-top: 16px !important;
        }
      }

      .upload-button {
        margin-top: -30px;
      }

      .file-upload-label {
        font-size: 20px;
        font-weight: $font-bold;
        line-height: 28px;
        color: $aeblack-900;
        margin-bottom: 4px;
        margin-top: 0 !important;
      }

      .file-upload-desc {
        color: $aeblack-900;
        margin: 0;
      }

      .mat-mdc-form-field-infix {
        padding: 0 !important;
      }

      .file-upload-thumbnail {
        margin-right: 12px;
        background-color: transparent !important;
        width: 60px;
        height: 60px;

        @media (max-width: 767px) {
          width: 40px;
          height: 40px;
        }

        [ng-reflect-dir="rtl"] &,
        [dir="rtl"] & {
          margin-right: 0;
          margin-left: 12px;
        }
      }

      .file-name {
        font-weight: $font-medium;
        color: $aeblack-900;
        text-align: left;
        line-height: 22px;

        [ng-reflect-dir="rtl"] &,
        [dir="rtl"] & {
          text-align: right;
        }
      }

      .file-size {
        background-color: transparent !important;
        color: $aeblack-500;
        text-align: right;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
      }

      .mobile-only {
        display: none;

        @media (max-width: 767px) {
          display: table-cell;
        }
      }

      .desktop-only {
        @media (max-width: 767px) {
          display: none;
        }
      }

      .file-option {
        text-align: right !important;

        [ng-reflect-dir="rtl"] &,
        [dir="rtl"] & {
          text-align: left !important;
        }

        button {
          margin: 4px 0 4px 8px;

          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin: 4px 8px 4px 0;
          }
        }
      }
    }

    .upload-table {
      margin: 16px 0 0 0;

      td {
        padding: 0;
        width: auto !important;
      }

      div {
        background-color: transparent !important;
      }

      p {
        margin: 0;
      }

      img {
        padding: 0;
      }
    }
  }

  .uploading-state {
    .spinner-border {
      margin-right: 10px;
      margin-top: 17px;
      border-width: 2px;
      color: $aegold-600;
      height: 22px;
      width: 22px;

      [ng-reflect-dir="rtl"] &,
      [dir="rtl"] & {
        margin-right: 0;
        margin-left: 10px;
      }
    }
  }

  .upload-error {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 16px;

    span {
      color: $aered-600;
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
    }
  }
}

.form_actions {
  display: flex;
  flex-direction: column;
  gap: 16px;

  @media only screen and (min-width: 1024px) {
    flex-direction: row;
    gap: 24px;
  }

  button {
    margin: 0px;
  }
}

// -- check box
app-checkbox,
mat-checkbox {

  .mdc-notched-outline__leading,
  .mdc-notched-outline__notch,
  .mdc-notched-outline__trailing {
    // border: none;
        display: none
  }

  .mdc-text-field--outlined .mat-mdc-form-field-infix,
  .mdc-text-field--no-label .mat-mdc-form-field-infix {
    padding: 0 !important;
  }

  .mdc-checkbox {
    height: 24px;
    width: 24px;
    flex: 0 0 24px;

    .mdc-checkbox__background {
      height: 24px;
      width: 24px;
      border-radius: 4px;
      border: 1px solid $aegold-400 !important;

      svg {
        padding: 6px;
      }
    }

    .mdc-checkbox__ripple {
      display: none !important;
    }

    .mdc-checkbox__native-control:hover~.mdc-checkbox__background {
      background-color: $aegold-200;
    }

    .mdc-checkbox__native-control:active~.mdc-checkbox__background {
      outline: 1px solid $aegold-200;
    }

    .mdc-checkbox__native-control:checked~.mdc-checkbox__background {
      background-color: $aegold-400 !important;
    }
  }

  .mat-mdc-form-field-hint-wrapper,
  .mat-mdc-form-field-error-wrapper {
    padding-left: 30px;
    margin-top: -15px;
  }

  label {
    font-size: 18px;
    font-weight: 400;
    line-height: 24px;

    a {
      color: $aegold-600 !important;
    }
  }
}

//-- select options
.mat-mdc-option {
  &.mat-mdc-option-active {
    background: none !important;

    .mdc-list-item__primary-text {
      color: $aegold-600 !important;
    }

    mat-pseudo-checkbox:after {
      color: $aegold-600 !important;
    }
  }

  .mdc-list-item__primary-text {
    font-size: 12px !important;
    font-weight: 400 !important;
    line-height: 16px !important;

    // padding-left: 28px;
    // color: $aegold-600;
    // [ng-reflect-dir=rtl] &, [dir=rtl] &{
    //   padding-left: 0px;
    //   padding-right: 28px;
    // }
    @media only screen and (min-width: 1024px) {
      font-size: 14px !important;
      line-height: 20px !important;
    }

    // &:before{
    //   content: '';
    //   position: absolute;
    //   left: 30px;
    //   top: 50%;
    //   transform: translate(-10px, -50%);
    //   width: 18px;
    //   height: 18px;
    //   background-image: radial-gradient(circle, $aeblack-600 1px, transparent 1px);
    //   background-size: 92% 33.33%;
    //   [ng-reflect-dir=rtl] &, [dir=rtl] &{
    //     left: initial;
    //     right: 6px;
    //   }
    // }
  }
}

// =============================================
// =============  aegov controlls ==============
// =============================================

.aegov-form-control {
  .form-control-input {
    position: relative;
    display: flex;
    border-radius: 0.375rem;
    background-color: white;
    box-shadow: rgb(255, 255, 255) 0px 0px 0px 0px inset;

    input[type="text"],
    input[type="email"],
    input[type="url"],
    input[type="password"],
    input[type="number"],
    input[type="date"],
    input[type="datetime-local"],
    input[type="month"],
    input[type="search"],
    input[type="tel"],
    input[type="time"],
    input[type="week"],
    [multiple],
    textarea,
    select {
      display: block;
      width: 100%;
      flex: 1 1 0%;
      background-color: transparent;
      padding: 0.75rem 1rem;
      font-size: 1rem;
      line-height: 1.5rem;
      border-radius: 4px;
      border: 1px solid $aegold-400;
      // box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.10);

      --tw-text-opacity: 1;
      color: rgb(93 59 38 / var(--tw-text-opacity));

      &:focus-visible {
        outline: none;
        box-shadow: 0px 0px 0px 1px $aegold-500 inset;
      }
    }

    [type="search"]::-webkit-search-cancel-button {
      -webkit-appearance: none;
      appearance: none;
      height: 20px;
      width: 20px;
      background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28" fill="none"><path d="M22.4946 21.2559C22.5758 21.3372 22.6403 21.4337 22.6843 21.54C22.7283 21.6462 22.751 21.76 22.751 21.875C22.751 21.99 22.7283 22.1038 22.6843 22.21C22.6403 22.3163 22.5758 22.4128 22.4946 22.4941C22.4133 22.5754 22.3167 22.6398 22.2105 22.6838C22.1043 22.7278 21.9905 22.7505 21.8755 22.7505C21.7605 22.7505 21.6467 22.7278 21.5405 22.6838C21.4342 22.6398 21.3377 22.5754 21.2564 22.4941L14.0005 15.237L6.74455 22.4941C6.58036 22.6582 6.35768 22.7505 6.12549 22.7505C5.89329 22.7505 5.67061 22.6582 5.50642 22.4941C5.34224 22.3299 5.25 22.1072 5.25 21.875C5.25 21.6428 5.34224 21.4201 5.50642 21.2559L12.7635 14L5.50642 6.74406C5.34224 6.57988 5.25 6.35719 5.25 6.125C5.25 5.8928 5.34224 5.67012 5.50642 5.50594C5.67061 5.34175 5.89329 5.24951 6.12549 5.24951C6.35768 5.24951 6.58036 5.34175 6.74455 5.50594L14.0005 12.763L21.2564 5.50594C21.4206 5.34175 21.6433 5.24951 21.8755 5.24951C22.1077 5.24951 22.3304 5.34175 22.4946 5.50594C22.6587 5.67012 22.751 5.8928 22.751 6.125C22.751 6.35719 22.6587 6.57988 22.4946 6.74406L15.2375 14L22.4946 21.2559Z" fill="%2392722A"/></svg>');
      background-size: 20px 20px;
    }

    .control-prefix,
    .control-suffix {
      display: flex;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      align-items: center;
      --tw-text-opacity: 1;
      color: rgb(158 162 169 / var(--tw-text-opacity));
    }
  }
}

input:focus::placeholder {
  color: transparent;
}

mat-select {
  +.icon-toggle {
    position: absolute;
    right: 0;
    color: $aegold-400;
    margin-top: 4px;

    [ng-reflect-dir="rtl"] &,
    [dir="rtl"] & {
      right: initial;
      left: 0;
      font-family: "Font Awesome 6 Free" !important;
    }
  }

  &[aria-expanded="true"] {
    +.icon-toggle {
      transform: rotate(180deg);
      color: $aegold-600;
    }
  }

  .mat-mdc-select-arrow-wrapper {
    display: none;
  }
}

// download modal
#modal-download {
  max-width: 100%;
  z-index: 1100 !important;

  img {
    width: 100%;
  }

  .modal-dialog {
    width: 600px !important;
    max-width: 100%;

    .modal-content {
      padding: 24px !important;

      @media only screen and (min-width: 1024px) {
        padding: 32px !important;
      }

      .modal-header,
      .modal-footer {
        margin: 0;

        h2 {
          margin: 0;
          // font-family: "Inter";
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;

          @media only screen and (min-width: 1024px) {
            font-size: 32px;
            line-height: 38px;
          }
        }

        .btn-close {
          margin-left: auto !important;

          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin-right: auto !important;
            margin-left: 0 !important;
          }
        }

        .download-btn {
          margin: 0 16px 0 0 !important;

          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin: 0 0 0 16px !important;
          }
        }

        .close-btn {
          margin: 0 !important;
          height: 48px !important;
        }
      }
    }
  }
}

// delete modal
#modal-delete {
  max-width: 100%;
  z-index: 1100 !important;

  img {
    width: 100%;
  }

  .modal-dialog {
    width: 600px !important;
    max-width: 100%;

    .modal-content {
      padding: 24px !important;
      border: none;

      @media only screen and (min-width: 1024px) {
        padding: 32px !important;
      }

      .modal-header,
      .modal-footer {
        margin: 0;

        h2 {
          margin: 0;
          // font-family: "Inter";
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;

          @media only screen and (min-width: 1024px) {
            font-size: 26px;
            line-height: 32px;
          }
        }

        .btn-close {
          margin-left: auto !important;

          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin-right: auto !important;
            margin-left: 0 !important;
          }
        }

        .cancel-btn {
          margin: 0 16px 0 0 !important;

          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin: 0 0 0 16px !important;
          }
        }

        .delete-btn {
          margin: 0 !important;
          height: 44px !important;
        }
      }

      .modal-body {
        p {
          color: $aeblack-600;
          font-size: 20px;
          font-weight: 400;
          line-height: 26px;
        }
      }
    }
  }
}

app-radio {
  .mdc-radio__background {
    width: 25px !important;
    height: 25px !important;
  }

  .form-label {
    padding-top: 0 !important;
  }

  .mdc-radio {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 25px !important;
    height: 25px !important;
  }
  
  .mdc-radio__inner-circle {
    // display: none;
    background-color: $aegold-500 !important;
    border: 1px solid $aegold-500 !important;
  }

  .mdc-radio__outer-circle {
    border: 1px solid $aegold-500 !important;
  }

  &.disabled {
    pointer-events: none;

    .mdc-radio__outer-circle {
      border: 1px solid $aeblack-400 !important;
    }

    .mdc-radio__inner-circle {
      border: 1px solid $aeblack-400 !important;
    }
  }

  .mat-mdc-radio-checked {
    .mdc-radio__outer-circle {
      border: 4px solid $aegold-500 !important;
    }
    
    .mdc-radio__inner-circle {
      border: 4px solid $aegold-500 !important;
    }    
  }

  &:has(:focus-visible),
  &:has([aria-owns]) {
    .mdc-text-field {
      border: 1px solid $aegold-600 !important;
    }

    i {
      color: $aegold-600;
    }

    svg {
      fill: $aegold-600 !important;
    }
  }

  .form-label {
    margin-bottom: 4px;
    font-weight: $font-medium;
    padding-top: 24px;
    font-size: 16px;
  }

  .mat-mdc-form-field {
    padding-top: 0;

    .mdc-text-field {
      border-radius: 4px;

      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        // border: none;
        display: none
      }
    }

    mat-icon-button {
      svg {
        fill: $aegold-400;
      }
    }

    .mat-mdc-icon-button svg {
      fill: $aegold-400;
    }

    .mat-mdc-form-field-hint-wrapper,
    .mat-mdc-form-field-error-wrapper {
      padding: 0;
      margin-top: 4px;
    }

    .mat-mdc-form-field-hint-wrapper,
    .mat-mdc-form-field-bottom-align {

      // margin-bottom: 24px !important;
      @media(max-width:1024) {
        margin-bottom: 20px !important;
      }
    }

    .mat-mdc-form-field-subscript-wrapper.mat-mdc-form-field-bottom-align {
      // Initially display the div normally
      display: block;

      // Hide the div if it has no content
      &:empty {
        display: none;
      }

      // Alternatively, hide the div if it contains only whitespace or becomes empty
      &:not(:has(*)) {
        display: none;
      }
    }

    .mdc-form-field {
      gap: 12px !important;

      .mdc-radio {
        padding: 0 !important;
      }
    }

    .mat-mdc-text-field-wrapper {
      padding: 0 !important;
    }
  }

  mat-radio-group {
    display: flex;
  }
}

mat-radio-group {
  display: block;
  gap: 16px;

  .radio-main-label {
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
    padding: 16px 0;

    @media only screen and (min-width: 1024px) {
      font-size: 20px;
      line-height: 26px;
    }
  }

  .radio-buttons-warpper {
    padding: 16px 0 40px 0;
    gap: 16px;
    display: flex;
    flex-direction: column;

    @media only screen and (min-width: 1024px) {
      gap: 24px;
    }

    .radio-box {
      cursor: pointer;
      border-radius: 16px;
      border: 1px solid $aegold-600;
      background: $white;
      padding: 16px;

      @media only screen and (min-width: 1024px) {
        padding: 24px;
      }

      .radio-box__header {
        display: flex;
        gap: 16px;
        flex-direction: column;
        align-items: start;
        padding: 0 !important;

        @media only screen and (min-width: 1024px) {
          flex-direction: row;
          align-items: center;
        }
      }

      p {
        margin: 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }

      h3 {
        margin: 0 0 4px 0 !important;
        font-size: 20px;
        font-weight: 700;
        line-height: 28px;
        color: $aeblack-900;
      }

      .mdc-radio {
        display: none;
      }

      .mdc-label,
      .mdc-form-field {
        width: 100%;
        cursor: pointer;
      }

      &.mat-mdc-radio-checked {
        border: 1px solid $aegold-50 !important;
        background-color: $aegold-50 !important;

        .appform {
          display: block !important;
        }
      }

      app-input-date,
      app-input {
        padding: 0 !important;
        max-width: 100% !important;
      }

      .mdc-radio__native-control {
        display: none !important;
      }
    }
  }
}

.service_catalog {
  margin-bottom: 48px;
}

textarea {
  border: 1px solid $aegold-400 !important;
}

.fullHeight {
  @media (min-width: 992px) {
    height: 93%;
    display: block;

    mat-form-field.mat-mdc-form-field.appFullWidth {
      height: 100%;

      .mat-mdc-form-field-flex {
        height: 100%;

        .mat-mdc-form-field-input-control,
        .mat-mdc-form-field-infix {
          height: 100% !important;
          resize: none;
        }
      }
    }
  }
}

.mat-calendar-body-selected {
  background-color: $aegold-400 !important;
}

.upload_wrapper {
  @media(max-width:1023px) {
    padding: 12px !important;
  }

  padding: 24px !important;
  border-radius: 8px;
  border: 1px solid $aegold-400;
  background: #fff;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  min-height: 128px;

  .mdc-text-field {
    border-radius: 0;
    background-color: transparent;
    padding: 0;
    box-shadow: none;

    h3 {
      font-size: 16px;
      font-weight: $font-bold;
      margin-bottom: 4px;
    }
  }

  .mt-4.ng-star-inserted {
    margin-top: 0 !important;
  }
}

// @-moz-document url-prefix() {
//   .mat-mdc-text-field-wrapper.mdc-text-field.ng-tns-c508571215-12.mdc-text-field--outlined {
//     height: 56px !important;
//     flex: initial !important;
//     will-change: unset !important;
//   }
// }