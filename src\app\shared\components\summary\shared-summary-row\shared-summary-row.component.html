<!-- <td class="labelSummary"><strong>{{ label | translate}}</strong> : </td>
  <td *ngIf="isPlain">{{ value }}</td>
  <td *ngIf="isDate">{{ value | date:'dd/MM/yyyy' }}</td>
  <td *ngIf="isLookup">{{ valueLookup }}</td>
  <td *ngIf="isFile && file"><a [href]="fileUrl" [download]="fileName">{{ fileName }}</a></td> -->

<h5 class="card-title"><strong>{{label | translate}}: </strong></h5>
<p *ngIf="isPlain">{{value}}</p>
<p *ngIf="isDate">{{value | date:'dd/MM/yyyy'}}</p>
<p *ngIf="isDateTime">{{ value | date:'dd/MM/yyyy hh:mm a' }}</p>
<p *ngIf="isLookup && lookup">{{ lang.IsArabic? lookup.NameArabic: lookup.NameEnglish }}</p>
<p *ngIf="isFile && file"><a [href]="fileUrl" [download]="fileName">{{ fileName }}</a></p>