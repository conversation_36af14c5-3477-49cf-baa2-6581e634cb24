import { AfterViewInit, Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LangChangeEvent } from '@ngx-translate/core';
import { GridActionTypesEnum } from '../../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { NationalTypesEnum } from '../../../../../../../e-services/npo-license-declaration/enums/NationalTypesEnum';
import { Feedback } from '../../../../../../../e-services/npo-license-declaration/models/feedback';
import { SubmitType } from '../../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../../shared/models/lookup.model';
import { uaeEmiratesIdValidator } from '../../../../../../../shared/validators/uae.emiratesId.validator';
import { MyEstablishmentComponentBase } from '../../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-establishment-founding-members-by-decree',
  templateUrl: './establishment-founding-members-by-decree.component.html',
  styleUrls: ['./establishment-founding-members-by-decree.component.scss']
})
export class EstablishmentFoundingMembersByDecreeComponent extends MyEstablishmentComponentBase
  implements OnInit, AfterViewInit, OnChanges {


  membersForm: FormGroup;
  emirates: Lookup[];
  nationalityTypes: Lookup[];
  memberEditIndex: number;
  maxDate: Date;
  NationalTypesEnum = NationalTypesEnum;
  personalInfo: any;
  tmpDelete: boolean = false;
  NPOForm: FormGroup;
  npoNumberEditIndex: number;

  get fb(): any {
    return this.form.controls;
  }
  get members(): FormArray {
    return this.form.get('members') as FormArray;
  }
  get mControls(): any {
    return this.membersForm.controls;
  }

  get NPONumbers(): FormArray {
    return this.form.get('NPOUnifiedNumbersList') as FormArray;
  }
  get npoControls(): any {
    return this.NPOForm.controls;
  }

  @Input() form: FormGroup;
  @Input() formStatusCode: number = 1;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.foundingMembers.';
    this.initForm();

    this.nationalityTypes = [
      new Lookup('1', 'UAE Local', 'مواطن إماراتي'),
      new Lookup('2', 'Resident', 'مقيم'),
    ];

    const today = new Date();
    today.setHours(23, 59, 59, 999);
    this.maxDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.MyEstablishmentService.lookupData$.subscribe((data) => {
      if (data) {
        this.emirates = data.Emirates;
        // this.nationalities = data.Countries;
        this.StepperService.requestData$.subscribe(_ => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.FoundingMembersForm);
            this.checkEditableFildes();
            // Object.keys(this.form.controls).forEach((control) => {
            //   if (
            //     (this.form.get(control)?.value === null ||
            //       this.form.get(control)?.value === '' ||
            //       !this.form.get(control)?.value) &&
            //     this.isNotAllowedToEdit === false
            //   ) {
            //     this.form.get(control)?.enable();
            //   } else {
            //     this.form.get(control)?.disable();
            //   }
            // });
          } else if (_ && _.isFullDetails == false) {
            this.members.clear();
            this.NPONumbers.clear();
            this.mapData(_?.FoundingMembersForm);
          }
          // this.changeValidations(this.form, this.fb.IsFoundingMembersHoldingTheNationalityIsLessThan70?.value, 'ExceptionReasonFor70Ar', 'ExceptionReasonFor70En');
          // this.changeValidations(this.form, this.fb.IsNumberOfFoundingMembersIsLessThan7Members?.value, 'ExceptionReasonFor7Ar', 'ExceptionReasonFor7En');
        });
      }

      this.addValidationOnMeetingSection();
    });
    this.resetFromValidation(this.form);


    this.Translation.onLangChange.subscribe((event: LangChangeEvent) => {
      // this.changeValidations(this.form, this.fb.IsFoundingMembersHoldingTheNationalityIsLessThan70?.value, 'ExceptionReasonFor70Ar', 'ExceptionReasonFor70En');
      // this.changeValidations(this.form, this.fb.IsNumberOfFoundingMembersIsLessThan7Members?.value, 'ExceptionReasonFor7Ar', 'ExceptionReasonFor7En');
    });

    this.pagination();
    this.members.valueChanges.subscribe(_ => this.pagination());
    this.setUpValueSubscribe();
  }

  setUpValueSubscribe = (): void => {
    this.form.get("IsFoundingMembersHoldingTheNationalityIsLessThan70")?.valueChanges.subscribe(isLessThan70 => {
      const ageIsLessThan70IdValue = this.fb?.ageIsLessThan70Id?.value;
      if (!ageIsLessThan70IdValue || isLessThan70) return;

      this.removeFromCRM(ageIsLessThan70IdValue, this.NPO_EXCEPTION_GRID_IN_CRM);
    });

    this.form.get("IsNumberOfFoundingMembersIsLessThan7Members")?.valueChanges.subscribe(isLessThan7 => {
      const ageIsLessThan7IdValue = this.fb?.ageIsLessThan7Id?.value;
      if (!ageIsLessThan7IdValue || isLessThan7) return;

      this.removeFromCRM(ageIsLessThan7IdValue, this.NPO_EXCEPTION_GRID_IN_CRM);
    });
  }

  ngAfterViewInit(): void { }

  initForm = (): void => {
    this.NPOForm = this.FormBuilder.group({
      id: [''],
      npoUnifiedNumber: ['', [Validators.required]],
      AccountId: [''],
      npoEstablishmentNameEN: [''],
      npoEstablishmentNameAr: [''],
      npoEstablishmentDate: [''],
      npoEstablishmentLegalFormEN: [''],
      npoEstablishmentLegalFormAr: ['']
    });

    this.membersForm = this.FormBuilder.group({
      id: [''],
      nationalityType: new FormControl('', Validators.required),
      foundingMemberAgeIsLessThan21YearsOldLocal: new FormControl(''),
      foundingMemberAgeIsLessThan21YearsOldNonLocal: new FormControl(''),
      foundingMemberHasDiplomaticStatus: new FormControl(''),
      foundingMemberResidencyIsLessThan3Years: new FormControl(''),

      exceptionReasonEnForAgeIsLessThan21Local: new FormControl(""),
      exceptionReasonArForAgeIsLessThan21Local: new FormControl(""),

      exceptionReasonEnForAgeIsLessThan21NonLocal: new FormControl(""),
      exceptionReasonArForAgeIsLessThan21NonLocal: new FormControl(""),

      exceptionReasonEnForHasDiplomaticStatus: new FormControl(""),
      exceptionReasonArForHasDiplomaticStatus: new FormControl(""),

      exceptionReasonEnForResidencyIsLessThan3Years: new FormControl(""),
      exceptionReasonArForResidencyIsLessThan3Years: new FormControl(""),

      emiratesId: new FormControl('', [Validators.required, uaeEmiratesIdValidator()]),
      dateOfBirth: new FormControl('', Validators.required),
      contactId: new FormControl(''),

      NameEn: new FormControl(''),
      NameAr: new FormControl(''),
      NationalityEn: new FormControl(''),
      NationalityAr: new FormControl(''),
      ResponseDate: new FormControl(''),
      Status: new FormControl(''),

    });
  }

  clearValidationsAndValues = (): void => {
    this.FormService.clearFields(this.membersForm, [
      'exceptionReasonArForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21NonLocal',
      'exceptionReasonArForAgeIsLessThan21NonLocal',
      'exceptionReasonEnForHasDiplomaticStatus',
      'exceptionReasonArForHasDiplomaticStatus',
      'exceptionReasonEnForResidencyIsLessThan3Years',
      'exceptionReasonArForResidencyIsLessThan3Years',
      'foundingMemberAgeIsLessThan21YearsOldLocal',
      'foundingMemberAgeIsLessThan21YearsOldNonLocal',
      'foundingMemberHasDiplomaticStatus',
      'foundingMemberResidencyIsLessThan3Years'
    ], true);

    this.membersForm.updateValueAndValidity();
  };

  clearValidations = (): void => {
    this.FormService.clearValidators(this.membersForm, [
      'exceptionReasonArForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21NonLocal',
      'exceptionReasonArForAgeIsLessThan21NonLocal',
      'exceptionReasonEnForHasDiplomaticStatus',
      'exceptionReasonArForHasDiplomaticStatus',
      'exceptionReasonEnForResidencyIsLessThan3Years',
      'exceptionReasonArForResidencyIsLessThan3Years',
      'foundingMemberAgeIsLessThan21YearsOldLocal',
      'foundingMemberAgeIsLessThan21YearsOldNonLocal',
      'foundingMemberHasDiplomaticStatus',
      'foundingMemberResidencyIsLessThan3Years'
    ]);

    this.membersForm.updateValueAndValidity();
  };

  onModelOpening = (event: any): void => {
    this.clearValidationsAndValues();
  }

  formatDate = (date: string | Date): string => {
    const d = new Date(date);
    return d.getFullYear() + '-' + (d.getMonth() + 1).toString().padStart(2, '0') + '-' + d.getDate().toString().padStart(2, '0');
  }

  validate = (item: any): boolean => {
    if (!this.isDataNotChanged(item)) {
      this.NotifyService.showError('notify.error', 'notify.DataNotCorrect');
      return false;
    }

    if (!this.isEidNotRepeated(item)) {
      this.NotifyService.showError('notify.error', 'notify.ThisMemberIsAlreadyAdded');
      return false;
    }

    return true;
  };

  isDataNotChanged = (item: any): boolean => {
    return (
      item?.emiratesId === this.personalInfo?.identityCardnumber &&
      this.formatDate(item?.dateOfBirth) === this.formatDate(this.personalInfo?.dateOfBirth)
    );
  };

  isEidNotRepeated = (item: any): boolean => {
    if (!this.members?.controls) return true;

    const matchingMember = this.members.controls.find(control => {
      const emiratesId = control.get('emiratesId')?.value;
      const id = control.get('id')?.value;
      return emiratesId === item.emiratesId && id !== item.id;
    });

    return !matchingMember;
  };

  manageMember = (item: any, type: GridActionTypesEnum): void => {
    const formValueWithDisabled = this.membersForm.getRawValue();

    if (this.validate(formValueWithDisabled)) {
      const memberFormGroup = this.FormBuilder.group({
        id: [formValueWithDisabled.id ?? this.generateDistinctId()],
        emiratesId: formValueWithDisabled?.emiratesId,
        dateOfBirth: formValueWithDisabled?.dateOfBirth,
        nationalityType: formValueWithDisabled?.nationalityType,
        foundingMemberAgeIsLessThan21YearsOldLocal: formValueWithDisabled?.foundingMemberAgeIsLessThan21YearsOldLocal,
        foundingMemberAgeIsLessThan21YearsOldNonLocal: formValueWithDisabled?.foundingMemberAgeIsLessThan21YearsOldNonLocal,
        foundingMemberResidencyIsLessThan3Years: formValueWithDisabled?.foundingMemberResidencyIsLessThan3Years,
        foundingMemberHasDiplomaticStatus: formValueWithDisabled?.foundingMemberHasDiplomaticStatus,
        exceptionReasonEnForAgeIsLessThan21Local: formValueWithDisabled?.exceptionReasonEnForAgeIsLessThan21Local,
        exceptionReasonArForAgeIsLessThan21Local: formValueWithDisabled?.exceptionReasonArForAgeIsLessThan21Local,
        exceptionReasonEnForResidencyIsLessThan3Years: formValueWithDisabled?.exceptionReasonEnForResidencyIsLessThan3Years,
        exceptionReasonArForResidencyIsLessThan3Years: formValueWithDisabled?.exceptionReasonArForResidencyIsLessThan3Years,
        exceptionReasonEnForHasDiplomaticStatus: formValueWithDisabled?.exceptionReasonEnForHasDiplomaticStatus,
        exceptionReasonArForHasDiplomaticStatus: formValueWithDisabled?.exceptionReasonArForHasDiplomaticStatus,
        exceptionReasonEnForAgeIsLessThan21NonLocal: formValueWithDisabled?.exceptionReasonEnForAgeIsLessThan21NonLocal,
        exceptionReasonArForAgeIsLessThan21NonLocal: formValueWithDisabled?.exceptionReasonArForAgeIsLessThan21NonLocal,
        contactId: formValueWithDisabled?.contactId,
        Status: formValueWithDisabled?.Status?.trim() || 'draft',
      });

      if (type === GridActionTypesEnum.EDIT) {
        this.members.at(this.memberEditIndex)?.patchValue(memberFormGroup.value);
      } else {
        this.members.push(memberFormGroup);
      }

      this.addValidationOnMeetingSection();
      this.membersForm.reset();
      this.modalService.dismissAll();
      // this.showAddFoundBtn = false;
    }
  };

  handleEditMember = (data: any, idx: number): void => {
    this.memberEditIndex = idx;
    this.membersForm.patchValue({
      id: data?.value?.id,
      emiratesId: data?.value?.emiratesId,
      dateOfBirth: data?.value?.dateOfBirth,
      nationalityType: data?.value?.nationalityType,
      foundingMemberAgeIsLessThan21YearsOldLocal: data?.value?.foundingMemberAgeIsLessThan21YearsOldLocal,
      foundingMemberAgeIsLessThan21YearsOldNonLocal: data?.value?.foundingMemberAgeIsLessThan21YearsOldNonLocal,
      foundingMemberHasDiplomaticStatus: data?.value?.foundingMemberHasDiplomaticStatus,
      foundingMemberResidencyIsLessThan3Years: data?.value?.foundingMemberResidencyIsLessThan3Years,
      exceptionReasonEnForAgeIsLessThan21Local: data?.value?.exceptionReasonEnForAgeIsLessThan21Local,
      exceptionReasonArForAgeIsLessThan21Local: data?.value?.exceptionReasonArForAgeIsLessThan21Local,
      exceptionReasonEnForResidencyIsLessThan3Years: data?.value?.exceptionReasonEnForResidencyIsLessThan3Years,
      exceptionReasonArForResidencyIsLessThan3Years: data?.value?.exceptionReasonArForResidencyIsLessThan3Years,
      exceptionReasonEnForHasDiplomaticStatus: data?.value?.exceptionReasonEnForHasDiplomaticStatus,
      exceptionReasonArForHasDiplomaticStatus: data?.value?.exceptionReasonArForHasDiplomaticStatus,
      exceptionReasonEnForAgeIsLessThan21NonLocal: data?.value?.exceptionReasonEnForAgeIsLessThan21NonLocal,
      exceptionReasonArForAgeIsLessThan21NonLocal: data?.value?.exceptionReasonArForAgeIsLessThan21NonLocal,
      contactId: data?.value?.contactId,
      Status: data?.value?.Status?.trim() || 'draft',
    });

    this.clearValidations();
    // this.updateMemberValidations(data?.value?.nationalityType?.ID);

    this.personalInfo = {
      identityCardnumber: data?.value?.emiratesId,
      dateOfBirth: this.formatDate(data?.value?.dateOfBirth)
    }
  }

  removeMember = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.members?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.RELATIONSHIP_GRID_IN_CRM);

      this.members.removeAt(idx);
      this.removeValidationOnMeetingSection();

      const normalizedStatus = obj?.Status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
      this.tmpDelete = (normalizedStatus === "rejected" || normalizedStatus === "refused");
    }
  }

  addValidationOnMeetingSection = (): void => {
    if (this.canSeeAgendaSection()) {
      const requiredFields = ['MeetingPlace', 'Emirate'];
      const fieldsWithMaxLength = ['Discussingtheestablishment', 'Preparingthedraftstatute'];

      this.FormService.addValidators(this.form, requiredFields, []);
      this.FormService.addValidators(this.form, fieldsWithMaxLength, []);

      if (this.fb.LegalFormId?.value !== this.LEGAL_FORM_TYPES.NationalSociety)
        this.FormService.addValidators(this.form, ['Electionofmembersofthetemporarycommittee'], []);
    }
  };

  removeValidationOnMeetingSection = (): void => {
    if (this.canSeeAgendaSection() === false) {
      const allFields = [
        'MeetingPlace', 'Emirate',
        'Discussingtheestablishment', 'Preparingthedraftstatute', 'Electionofmembersofthetemporarycommittee'
      ];

      this.FormService.clearFields(this.form, allFields, true);
    }
  };


  isValidForm = (): boolean => {
    // let result: boolean = Object.keys(this.form.controls).every(controlName => {
    //   const control = this.form.get(controlName);
    //   return control?.disabled || control?.valid;
    // });
    
    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.valid;
    });

    // let isMemberRejectedExist = this.members.controls.some(member => {
    //   const status = member.get("Status")?.value;
    //   const normalizedStatus = status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
    //   return normalizedStatus === "rejected" || normalizedStatus === "refused";
    // });

    // const memberLength = this.members.length;

    // if (isMemberRejectedExist || !result || memberLength < 1) return false;

    return result;
    // return true;
  };


  isAnyMemberStillDraft = (): boolean => {
    let result = this.members.controls.some((control: any) => control.value.Status === 'Draft' || control.value.Status === '');
    return result;
  };

  percentageOfLocalMembers = (): number => {
    const totalNumber = this.members?.controls?.length || 0;

    if (totalNumber === 0) return 0;

    const localMembersCount = this.members?.controls?.filter(member => member?.value?.nationalityType?.ID === NationalTypesEnum.Local).length || 0;

    const localPercentage = (localMembersCount / totalNumber) * 100;

    return Math.floor(localPercentage);
  };


  checkPercentageOfLocalMembers = (): boolean => {
    return this.percentageOfLocalMembers() >= 70;
  };

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("FoundingMembersForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("FoundingMembersForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("FoundingMembersForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      uaenationalitylessthan70percentcode: this.updateBoolean(this.fb?.IsFoundingMembersHoldingTheNationalityIsLessThan70?.value),
      nbrfmlessthan7code: this.updateBoolean(this.fb?.IsNumberOfFoundingMembersIsLessThan7Members?.value),

      ExceptionsRequestNationalityless70: {
        Id: this.fb?.ageIsLessThan70Id?.value ?? this.EMPTY_GUID,
        Name: "",
        Description: this.fb?.ExceptionReasonFor70En?.value ?? "",
        DescriptionAr: this.fb?.ExceptionReasonFor70Ar?.value ?? ""
      },

      ExceptionsRequestNumberless7: {
        Id: this.fb?.ageIsLessThan7Id?.value ?? this.EMPTY_GUID,
        Name: "",
        Description: this.fb?.ExceptionReasonFor7En?.value ?? "",
        DescriptionAr: this.fb?.ExceptionReasonFor7Ar?.value ?? ""
      },

      npoNumbers: (this.NPONumbers && this.NPONumbers.value ? this.NPONumbers.value : []).map((_, index) => ({
        Id: _.id ?? this.EMPTY_GUID,
        npoUnifiedNumber: _.AccountId,
        AccountId: _.AccountId,
        npoEstablishmentNameEN: _.npoEstablishmentNameEN,
        npoEstablishmentNameAr: _.npoEstablishmentNameAr,
        npoEstablishmentDate: _.npoEstablishmentDate,
        npoEstablishmentLegalFormEN: _.npoEstablishmentLegalFormEN,
        npoEstablishmentLegalFormAr: _.npoEstablishmentLegalFormAr,
        Priority: index + 1,
      })),

      Npofoundersmeeting: {
        Id: this.fb?.MeetingId?.value ?? this.EMPTY_GUID,
        MeetingPlace: this.fb?.MeetingPlace?.value ?? '',
        Emirate: this.fb?.Emirate?.value?.ID ?? '',
        Discussingtheestablishment: this.fb?.Discussingtheestablishment?.value ?? '',
        Preparingthedraftstatute: this.fb?.Preparingthedraftstatute?.value ?? '',
        Electionofmembersofthetemporarycommittee: this.fb?.Electionofmembersofthetemporarycommittee?.value ?? '',
        Meetingdate: this.fb?.Date?.value ?? ''
      },

      FounderMember: (this.members.controls as Array<any>).map((control, index) => {
        const value = control?.value;

        return {
          Id: value?.id ?? this.EMPTY_GUID,
          localmember: value?.nationalityType?.ID === NationalTypesEnum.Local ? 1 : 0,
          Contact: {
            Id: value?.contactId ?? this.EMPTY_GUID,
            EmirateId: value?.emiratesId ?? '',
            Dob: value?.dateOfBirth ?? ''
          },
          Priority: index + 1,
          SatusReason: value?.Status?.trim() || 'draft',
          Name: value?.NameEn ?? '',
          Nationality: value?.NationalityEn ?? '',
          Description: "",
          Establishment: this.EMPTY_GUID,
          EstablishmentId: this.EMPTY_GUID,
          Email: "",
          ageislessthan21: this.updateBoolean(this.getAge(value)),
          residencyislessthan3years: this.updateBoolean(value?.foundingMemberResidencyIsLessThan3Years),
          hasadisplomaticstatus: this.updateBoolean(value?.foundingMemberHasDiplomaticStatus),
          Exceptionageislessthan21: {
            Id: value?.Exceptionageislessthan21?.Id ?? this.EMPTY_GUID,
            Name: "",
            Description: this.getExceptionAgeEn(value),
            DescriptionAr: this.getExceptionAgeAr(value),
          },
          Exceptionsresidencyislessthan3years: {
            Id: value?.Exceptionsresidencyislessthan3years?.Id ?? this.EMPTY_GUID,
            Name: "",
            Description: value?.exceptionReasonEnForResidencyIsLessThan3Years ?? '',
            DescriptionAr: value?.exceptionReasonArForResidencyIsLessThan3Years ?? ''
          },
          Exceptionshasadisplomaticstatus: {
            Id: value?.Exceptionshasadisplomaticstatus?.Id ?? this.EMPTY_GUID,
            Name: "",
            Description: value?.exceptionReasonEnForHasDiplomaticStatus ?? '',
            DescriptionAr: value?.exceptionReasonArForHasDiplomaticStatus ?? '',
          }
        };
      })
    };
  };

  getAge = (member: any): boolean => {
    return (member?.nationalityType?.ID === NationalTypesEnum.Local)
      ? member?.foundingMemberAgeIsLessThan21YearsOldLocal
      : member?.foundingMemberAgeIsLessThan21YearsOldNonLocal;
  }

  getExceptionAgeAr = (member: any): boolean => {
    return (member?.nationalityType?.ID === NationalTypesEnum.Local)
      ? member?.exceptionReasonArForAgeIsLessThan21Local
      : member?.exceptionReasonArForAgeIsLessThan21NonLocal;
  }

  getExceptionAgeEn = (member: any): boolean => {
    return (member?.nationalityType?.ID === NationalTypesEnum.Local)
      ? member?.exceptionReasonEnForAgeIsLessThan21Local
      : member?.exceptionReasonEnForAgeIsLessThan21NonLocal;
  }

  updateBoolean = (value: any): number => {
    return (value == true) ? 1 : 0;
  }

  mapData = (data: any): void => {
    if (!data) return;

    this.fb.IsFoundingMembersHoldingTheNationalityIsLessThan70.setValue((data?.uaenationalitylessthan70percentcode === 1) ? true : false);
    this.fb.IsNumberOfFoundingMembersIsLessThan7Members.setValue((data?.nbrfmlessthan7code === 1) ? true : false);
    this.fb.ExceptionReasonFor70En.setValue(data?.ExceptionsRequestNationalityless70?.Description);
    this.fb.ExceptionReasonFor70Ar.setValue(data?.ExceptionsRequestNationalityless70?.DescriptionAr);
    this.fb.ExceptionReasonFor7En.setValue(data?.ExceptionsRequestNumberless7?.Description);
    this.fb.ExceptionReasonFor7Ar.setValue(data?.ExceptionsRequestNumberless7?.DescriptionAr);
    this.fb.ageIsLessThan7Id.setValue(data?.ExceptionsRequestNumberless7?.Id);
    this.fb.ageIsLessThan70Id.setValue(data?.ExceptionsRequestNationalityless70?.Id);

    this.fb.MeetingId.setValue(data?.Npofoundersmeeting?.Id);
    this.fb.MeetingPlace.setValue(data?.Npofoundersmeeting?.MeetingPlace);
    this.fb.Emirate.setValue(this.emirates?.find(_ => _.ID === data?.Npofoundersmeeting?.Emirate) ?? '');

    this.fb.Discussingtheestablishment.setValue(data?.Npofoundersmeeting?.Discussingtheestablishment);
    this.fb.Preparingthedraftstatute.setValue(data?.Npofoundersmeeting?.Preparingthedraftstatute);
    this.fb.Electionofmembersofthetemporarycommittee.setValue(data?.Npofoundersmeeting?.Electionofmembersofthetemporarycommittee);


    this.fb.Date.setValue(data?.Npofoundersmeeting?.Meetingdate);

    data.FounderMember = data.FounderMember.sort((a, b) => a.Priority - b.Priority);

    data.npoNumbers?.forEach((_: any) => {
      if (!this.isNpoNumberExist(_)) {
        this.NPONumbers.push(this.FormBuilder.group({
          id: _.Id ?? this.EMPTY_GUID,
          npoUnifiedNumber: _.AccountId,
          AccountId: _.AccountId,
          npoEstablishmentNameEN: _.npoEstablishmentNameEN,
          npoEstablishmentNameAr: _.npoEstablishmentNameAr,
          npoEstablishmentDate: _.npoEstablishmentDate,
          npoEstablishmentLegalFormEN: _.npoEstablishmentLegalFormEN,
          npoEstablishmentLegalFormAr: _.npoEstablishmentLegalFormAr
        }));
      }
    });

    data.FounderMember.forEach((item: any) => {
      this.members.push(this.FormBuilder.group({
        id: [item.Id],
        emiratesId: item?.Contact?.EmirateId,
        dateOfBirth: item?.Contact?.Dob,
        contactId: item?.Contact?.Id,
        nationalityType: item?.localmember === 1 ? this.nationalityTypes?.at(0) : this.nationalityTypes?.at(1),
        exceptionReasonEnForAgeIsLessThan21Local: item?.Exceptionageislessthan21?.Description,
        exceptionReasonArForAgeIsLessThan21Local: item?.Exceptionageislessthan21?.DescriptionAr,
        exceptionReasonEnForHasDiplomaticStatus: item?.Exceptionshasadisplomaticstatus?.Description,
        exceptionReasonArForHasDiplomaticStatus: item?.Exceptionshasadisplomaticstatus?.DescriptionAr,
        exceptionReasonEnForResidencyIsLessThan3Years: item?.Exceptionsresidencyislessthan3years?.Description,
        exceptionReasonArForResidencyIsLessThan3Years: item?.Exceptionsresidencyislessthan3years?.DescriptionAr,
        exceptionReasonEnForAgeIsLessThan21NonLocal: item?.Exceptionageislessthan21?.Description,
        exceptionReasonArForAgeIsLessThan21NonLocal: item?.Exceptionageislessthan21?.DescriptionAr,
        foundingMemberAgeIsLessThan21YearsOldNonLocal: (item?.ageislessthan21 === 1) ? true : false,
        foundingMemberAgeIsLessThan21YearsOldLocal: (item?.ageislessthan21 === 1) ? true : false,
        foundingMemberHasDiplomaticStatus: (item?.hasadisplomaticstatus === 1) ? true : false,
        foundingMemberResidencyIsLessThan3Years: (item?.residencyislessthan3years === 1) ? true : false,

        Status: item?.SatusReason?.trim() || 'draft',
        NationalityEn: this.canSeeFoundingMember(item?.SatusReason) ? (item?.Nationality ?? '') : '',
        NameEn: this.canSeeFoundingMember(item?.SatusReason) ? (item?.Name ?? '') : '',
        ResponseDate: this.canSeeFoundingMember(item?.SatusReason) ? item?.Responsedate : '',
      }));
    });
  }

  canSeeAgendaSection = (): boolean => {
    if ((this.members?.length > 1 && this.fb.LegalFormId?.value === this.LEGAL_FORM_TYPES.NationalSociety)
      || this.fb?.LegalFormId?.value !== this.LEGAL_FORM_TYPES.NationalSociety)
      return true;

    return false;
  };

  get canSeeElectionOfMembersOfTheTemporaryCommittee(): boolean {
    if (this.fb.LegalFormId?.value !== this.LEGAL_FORM_TYPES.NationalSociety)
      return true;

    return false;
  };

  getInfo = (emiratesId: string, dateOfBirth: string): void => {
    const formattedDate = this.FormService.formatDate(dateOfBirth, 'yyyy-MM-dd');
    if (!formattedDate) {
      this.NotifyService.showError('notify.error', 'Invalid date format.');
      return;
    }
    this.MyEstablishmentService.VerifyFounderMember(emiratesId, formattedDate).subscribe(
      (response) => {
        if (response && response.data) {
          this.setData(response.data);
          // this.showAddFoundBtn = true;
        } else {
          this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'لم يتم العثور على البيانات.' : 'Data not found.');
        }
      },
      (error) => {
        this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'خطأ في جلب البيانات.' : 'Error fetching data.');
      }
    );
  };

  setData = (data: any): void => {
    const isLocal = data?.IsUAENational === 1;
    const isNonLocal = data?.IsUAENational === 0;

    if (!isLocal && !isNonLocal) {
      this.NotifyService.showError('notify.error', 'Invalid nationality status.');
    }

    const nationalType = isLocal ? NationalTypesEnum.Local : NationalTypesEnum.NonLocal;
    this.updatePersonalInfo(data, nationalType);
  };

  updatePersonalInfo = (data: any, nationalType: string): void => {
    this.setValues(nationalType, data?.dateOfBirth);
    //this.updateMemberValidations(nationalType);
    //this.disableFields(nationalType);
    this.personalInfo = data;
    this.membersForm.updateValueAndValidity();
  };


  private getNationalityType = (typeId: string) => {
    return this.nationalityTypes.find((type) => type.ID === typeId) || null;
  };

  calculateAge(dateOfBirth: string): number {
    if (!dateOfBirth) return 0;

    const birthDate = new Date(dateOfBirth);
    if (isNaN(birthDate.getTime())) {
      this.NotifyService.showError('notify.error', 'Invalid date of birth.');
      return 0;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }

  disableFields = (localTypeId: string): void => {
    if (localTypeId === NationalTypesEnum.Local) {
      this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.disable();
    } else {
      this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.disable();
      this.mControls.foundingMemberResidencyIsLessThan3Years.disable();
    }
  }

  setValues = (localTypeId: string, dateOfBirth: string): void => {
    const age = this.calculateAge(dateOfBirth);

    if (localTypeId === NationalTypesEnum.Local) {
      this.mControls.nationalityType.setValue(this.getNationalityType(NationalTypesEnum.Local));
      this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.setValue(age < 21);
    } else {
      this.mControls.nationalityType.setValue(this.getNationalityType(NationalTypesEnum.NonLocal));
      this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.setValue(age < 21);
      // this.mControls.foundingMemberResidencyIsLessThan3Years.setValue(age < 3);
    }
  }

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "foundingmembers", this.fb);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  checkMemberExist = (id: string): boolean => this.members.controls.findIndex(_ => _.get('id')?.value == id) > -1;



  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.members.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
    data$.forEach(_ => this.dataTable.push(_));
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
      case 'approved':
        return 'green';
      case 'pending confirmation':
        return 'orange';
      case 'rejected':
      case 'refused':
        return 'red';
      default:
        return '';
    }
  }

  getStatusBgColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
        return '#E7F5FF'
      case 'approved':
        return '#F3FAF4';
      case 'pending confirmation':
        return '#FFFBEB';
      case 'rejected':
      case 'refused':
        return '#FEF2F2';
      default:
        return '';
    }
  }
  updateMemberValidations = (localId: string): void => {
    if (localId === NationalTypesEnum.Local)
      this.changeValidations(this.membersForm, this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.value, 'exceptionReasonArForAgeIsLessThan21Local', 'exceptionReasonEnForAgeIsLessThan21Local');
    else {
      this.changeValidations(this.membersForm, this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.value, 'exceptionReasonArForAgeIsLessThan21NonLocal', 'exceptionReasonEnForAgeIsLessThan21NonLocal');
      this.changeValidations(this.membersForm, this.mControls.foundingMemberResidencyIsLessThan3Years.value, 'exceptionReasonArForResidencyIsLessThan3Years', 'exceptionReasonEnForResidencyIsLessThan3Years');
      this.changeValidations(this.membersForm, this.mControls.foundingMemberHasDiplomaticStatus.value, 'exceptionReasonArForHasDiplomaticStatus', 'exceptionReasonEnForHasDiplomaticStatus');
    }
  }

  getMeetingDate = (): Date => {
    return this.fb?.Date?.value;
  }


  checkMemberStatus = (): boolean => {
    const excludedStatusCodes = [1, 100000000];
    if (excludedStatusCodes.includes(this.formStatusCode)) {
      return false;
    }

    const result = this.members.controls.some(member => {
      const status = member.get("Status")?.value;
      const normalizedStatus = status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
      return normalizedStatus === "rejected" || normalizedStatus === "refused";
    });

    if (result === true && this.fb?.IsFoundingMembersHoldingTheNationalityIsLessThan70?.disabled) {
      const {
        IsFoundingMembersHoldingTheNationalityIsLessThan70,
        ExceptionReasonFor70Ar,
        ExceptionReasonFor70En,
        IsNumberOfFoundingMembersIsLessThan7Members,
        ExceptionReasonFor7Ar,
        ExceptionReasonFor7En
      } = this.fb;

      IsFoundingMembersHoldingTheNationalityIsLessThan70?.enable();
      ExceptionReasonFor70Ar?.enable();
      ExceptionReasonFor70En?.enable();
      IsNumberOfFoundingMembersIsLessThan7Members?.enable();
      ExceptionReasonFor7Ar?.enable();
      ExceptionReasonFor7En?.enable();
    }

    return result;
  };


  isStatusRejected = (status: string): boolean => {
    const excludedStatusCodes = [1, 100000000];
    if (excludedStatusCodes.includes(this.formStatusCode)) {
      return false;
    }

    const normalizedStatus = status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
    return normalizedStatus === "rejected" || normalizedStatus === "refused";
  };

  manageNPONumber = (item: any, type: GridActionTypesEnum): void => {
    const isDuplicate = this.NPONumbers?.value?.some(
      npo => npo.npoUnifiedNumber === item.npoUnifiedNumber && npo.id !== item.id
    );
    if (isDuplicate) {
      this.NotifyService.showError('notify.error', 'notify.NPONumberIsAlreadyExists');
      return;
    }

    this.MyEstablishmentService.GetNPoEstbalishmentbyUnifiedNumber(item.npoUnifiedNumber).subscribe((data) => {
      if (data) {
        const existingLegalForm = this.NPONumbers?.value[0]?.npoEstablishmentLegalFormEN;
        const fetchedLegalForm = data?.data[0]?.npoEstablishmentLegalFormEN;

        if (this.NPONumbers.length > 0 && existingLegalForm !== fetchedLegalForm) {
          this.NotifyService.showError('notify.error', 'notify.npoListWillBeAssociationsOrNationalSocieties');
          return;
        }

        if (data.data.length === 0) {
          this.NotifyService.showError('notify.error', 'notify.invalidNPONumber');
          return;
        }

        const npoData = data.data[0];
        const npoFormGroup = this.FormBuilder.group({
          id: [this.generateDistinctId()],
          AccountId: [npoData?.AccountId],
          npoUnifiedNumber: [item.npoUnifiedNumber, [Validators.required]],
          npoEstablishmentNameEN: [npoData?.npoEstablishmentNameEN],
          npoEstablishmentNameAr: [npoData?.npoEstablishmentNameAr],
          npoEstablishmentDate: [npoData?.npoEstablishmentDate],
          npoEstablishmentLegalFormEN: [npoData?.npoEstablishmentLegalFormEN || ''],
          npoEstablishmentLegalFormAr: [npoData?.npoEstablishmentLegalFormAr || ''],
        });

        if (type !== GridActionTypesEnum.EDIT) {
          this.NPONumbers.push(npoFormGroup);
        } else {
          this.NPONumbers.at(this.npoNumberEditIndex).setValue({
            id: item.id,
            npoUnifiedNumber: item.npoUnifiedNumber,
            AccountId: npoData?.AccountId,
            npoEstablishmentNameEN: npoData?.npoEstablishmentNameEN,
            npoEstablishmentNameAr: npoData?.npoEstablishmentNameAr,
            npoEstablishmentDate: npoData?.npoEstablishmentDate,
            npoEstablishmentLegalFormEN: npoData?.npoEstablishmentLegalFormEN,
            npoEstablishmentLegalFormAr: npoData?.npoEstablishmentLegalFormAr,
          });
        }

        this.NPOForm.reset();
        this.modalService.dismissAll();
      }
    });
  }

  handleEditNPONumber = (data: any, idx: number): void => {
    this.npoNumberEditIndex = idx;
    this.NPOForm.patchValue({
      id: data?.value?.id,
      npoUnifiedNumber: data?.value?.npoUnifiedNumber,
      AccountId: data?.value?.AccountId,
      npoEstablishmentNameEN: data?.value?.npoEstablishmentNameEN,
      npoEstablishmentNameAr: data?.value?.npoEstablishmentNameAr,
      npoEstablishmentDate: data?.value?.npoEstablishmentDate,
      npoEstablishmentLegalFormEN: data?.value?.npoEstablishmentLegalFormEN,
      npoEstablishmentLegalFormAr: data?.value?.npoEstablishmentLegalFormAr
    });
  }

  removeNPONumber = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let nPONumber = this.NPONumbers?.value[idx];
      if (nPONumber)
        this.removeFromCRM(nPONumber?.id, this.NPO_NUMBER_GRID_IN_CRM);

      this.NPONumbers.removeAt(idx);
    }
  }

  isNpoNumberExist = (item: any): boolean => {
    return this.NPONumbers.value.find(_ => _.AccountId == item?.AccountId);
  }
}
