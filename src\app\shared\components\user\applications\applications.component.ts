import { Component, Input, QueryList, signal, ViewChildren } from '@angular/core';

import { FormBuilder, FormGroup } from '@angular/forms';
import { NgbdSortableHeader, SortEvent } from '../../../directives/sortable.diractive';
import { AuthService } from '../../../services/auth.service';
import { DataService } from '../../../services/data.service';
import { AlertService } from '../../../services/alert.service';
import { Router } from '@angular/router';
import { getValueFromName } from '../../../enums/application-status.enum';
import { LanguageService } from '../../../services/language.service';
import { NpoLicenseDeclarationService } from '../../../../e-services/npo-license-declaration/services/npo-license-declaration.service';
import { ServiceCatalogue } from '../../../enums/service-catalogue.enum';

@Component({
  selector: 'app-applications',
  templateUrl: './applications.component.html',
  styleUrl: './applications.component.scss'
})
export class ApplicationsComponent {
  @Input() status: string = '-1';
  @Input() disabled: boolean = true;


  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  submitForm: FormGroup;
  apps: any;
  userInfo: any;
  statusId = "-1";
  page = 1;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 100];
  filteredData: any = [];
  filteredCount: number;
  sortBy = '';
  sortOrder = 'desc';
  searchTerm = '';

  isComponentRouteActive = signal(false, { equal: () => false })

  constructor(
    private dataService: DataService,
    public fb: FormBuilder,
    private alert: AlertService,
    private router: Router,
    protected lang: LanguageService,
    protected auth: AuthService,
    private npoLicenseDeclarationService: NpoLicenseDeclarationService
  ) {
    this.isComponentRouteActive.set(this.router.url.endsWith('my-applications'));
  }

  ngOnInit(): void {
    this.statusId = getValueFromName(this.status)?.toString() ?? '-1';
    this.userInfo = this.auth.getUserInfo()
    this.getData();
  }


  onSort({ column, direction }: SortEvent) {
    this.sortBy = column;
    this.sortOrder = direction;
    this.applyFilters();
  }

  search() {
    this.applyFilters();
  }

  applyFilters() {
    this.getData();
  }

  //::TODO Replace static Id with CRM_USER_ID (Heaiba)
  getData() {
    var query = `userId=${this.userInfo.crmUserId}`;
    // var query = `userId=${'5c4f074f-df92-ef11-b111-005056010908'}`;
    query += `&statusId=${this.statusId}`;
    query += `&page=${this.page}`;
    query += `&pageSize=${this.pageSize}`;
    query += `&sortColumn=${this.sortBy}`;
    query += `&sortDirection=${this.sortOrder}`;
    query += `&searchTerm=${this.searchTerm}`;

    this.dataService
      .get(`Global/GetUserRequests?${query}`)
      .subscribe((res) => {

        this.filteredData = res.data?.Applications;
        this.filteredCount = res?.data?.dataCount;
      });
  }

  async deleteApplication(id: string, entityName: string) {
    const confirmed = await this.alert.confirmSubmit('');
    if (confirmed) {
      this.dataService
        .delete(`Global/DeleteDraftRequest?requestId=${id}&entityName=${entityName}`)
        .subscribe((res) => {
          this.applyFilters();
        });
    }
  }

  viewApplication(app: any) {
    switch (app.ServiceCatalogue.toLocaleLowerCase()) {
      case ServiceCatalogue.LostCard.toLocaleLowerCase():
        this.router.navigate(['/e-services/pod-lost-card', app.Id]);
        break;
      case ServiceCatalogue.IssueCard.toLocaleLowerCase():
        this.router.navigate(['/e-services/pod-new-card', app.Id]);
        break;
      case ServiceCatalogue.MarriageGrant.toLowerCase():
        this.router.navigate(['/e-services/marriage-grant', app.Id]);
        break;
      case ServiceCatalogue.MassWedding.toLowerCase():
        this.router.navigate(['/e-services/mass-wedding', app.Id]);
        break;
      // case 'fd3677ba-35d5-ee11-b10d-005056010908':
      //   this.router.navigate(['/e-services/npo-declaration', app.Id]);
      //   break;
      case ServiceCatalogue.PodCenterRegistration.toLowerCase():
        this.router.navigate(['/e-services/pod-centers', app.Id]);
        break;
      case ServiceCatalogue.NPOLicenseDeclaration:
      case ServiceCatalogue.NPOLicenseDeclarationByDecree:
        window.location.href = `/e-services/npo-license-declaration/npo-request-details/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.EstablishmentEdit:
        window.location.href = `/e-services/npo-license-declaration/npo-request-details/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}&establishmentId=${app.EstablishmentID}&serviceCatalogueName=${app.ServiceCatalogueName}&serviceCatalogueNameAR=${app.ServiceCatalogueNameAR}`;
        break;
      case ServiceCatalogue.IssueLicenseNonMuslimWorshipPlace:
        window.location.href = `/e-services/non-muslims-worship-place-license/details/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.RequestAllocationWorshipRoom:
        window.location.href = `/e-services/non-muslims-worship-place-license/room-details/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.RequestToIssueFundraisingPermit:
        window.location.href = `/e-services/fund-raising-service/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.AffiliateSubscribeOrJoinAssociationsOrRegionalInternationalEntities:
        window.location.href = `/e-services/joining-and-affiliating-associations/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.ParticipateInActivitiesAndEvents:
        window.location.href = `/e-services/activities-and-events-participation/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;
      case ServiceCatalogue.OrganizeActivitiesAndEvents:
        window.location.href = `/e-services/organizing-events-and-activities/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;

      case ServiceCatalogue.RequestToExtendFundraisingPermit:
        window.location.href = `/e-services/fund-raising-service/extend-details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;


      case ServiceCatalogue.RequestForApprovalOfOpeningNPOBranch:
        window.location.href = `/e-services/request-for-approval-of-opening-npo-branch/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;

      case ServiceCatalogue.RequestForReceiveDonationsNOC:
        window.location.href = `/e-services/noc-request/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;

      case ServiceCatalogue.OpeningNewBankAccountRequest:
        window.location.href = `/e-services/request-to-open-new-bank-account-certificate/details/${app.EstablishmentID}/${app.Id}?serviceCatalouge=${app.ServiceCatalogue}`;
        break;



      default:
        break;
    }

  }


  get ServiceCatalogue() {
    return ServiceCatalogue;
  }

  onSortChange(sortBy: string) {
    if (sortBy === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'asc';
    }
    this.applyFilters();
  }
  onFilterChange() {
    this.applyFilters();
  }
  onPageChange() {
    this.applyFilters();
  }
  onSelectChange() {
    this.applyFilters();
  }
  onStatusChange() {
    this.applyFilters();
  }

  //::TODO change this is bad solution (Heaiba)
  redirectToNpoRequest = (requestId: string): void => {
    if (requestId !== null) {
      this.npoLicenseDeclarationService.GetNpoRequestForm(requestId).subscribe((res) => {
        if (res.Status == 2000) {
          this.router.navigate([`/e-services/npo-license-declaration/type/${res?.data[0]?.BasicInformationForm?.npoform}/${requestId}`]);
        }
      }, error => {

      });
    }
  }

  getPageSymbol(current: number) {
    return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'][current - 1];
  }
}
