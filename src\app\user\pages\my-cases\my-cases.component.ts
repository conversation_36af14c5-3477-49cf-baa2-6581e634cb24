import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-my-cases',
  templateUrl: './my-cases.component.html',
  styleUrl: './my-cases.component.scss'
})
export class MyCasesComponent {
  status: string;
  disabled: boolean = true;

  constructor(private route: ActivatedRoute) {
    var status = this.route.snapshot.paramMap.get('status');

    this.disabled = false;
    if (status) {
      this.status = status;
    }
    else {
      this.status = '-1';
    }
  }
}
