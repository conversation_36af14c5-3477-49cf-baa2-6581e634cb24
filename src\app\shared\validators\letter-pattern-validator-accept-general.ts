import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function letterPatternValidatorGeneral(language: 'en' | 'ar', isRequired: boolean = true): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    const englishPattern = /^[A-Za-z\s\d\n!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{2,500}$/;
    const arabicPattern = /^[\u0600-\u06FF\s\d\n!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{2,500}$/;

    if (!value) {
      if(isRequired){
        return { required: true };
      }
    } else {
      if (value.length < 2) {
        return { minLengthIs2: true };
      }
      
      if (value.length > 500) {
        return { maxLength: true };
      }
      // const containsNumbers = /\d/;
  
      if (language === 'en') {
        // if (containsNumbers.test(value)) {
        //   return { containsNumbers: true };
        // }
        if (!englishPattern.test(value)) {
          return { invalidPattern2En: true };
        }
      }
  
      if (language === 'ar') {
        // if (containsNumbers.test(value)) {
        //   return { containsNumbers: true };
        // }
        if (!arabicPattern.test(value)) {
          return { invalidPattern2Ar: true };
        }
      }
    }
    

    return null;
  };
}
