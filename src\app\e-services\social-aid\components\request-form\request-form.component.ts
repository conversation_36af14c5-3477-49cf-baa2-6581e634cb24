import { Component, ElementRef, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { LanguageService } from '../../../../shared/services/language.service';
import { NotifyService } from '../../../../shared/services/notify.service';
import { StepperService } from '../../../../shared/services/stepper.service';
import { DataService } from '../../../../shared/services/data.service';
import { ActivatedRoute, Router } from '@angular/router';
import { AuthService } from '../../../../shared/services/auth.service';
import { FormService } from '../../../../shared/services/form.service';
import { AlertService } from '../../../../shared/services/alert.service';
import { TranslateService } from '@ngx-translate/core';
import { SocialAidService } from '../../services/social-aid.service';
import { Lookup } from '../../../../shared/models/lookup.model';
import { SwpService } from '../../../../shared/services/swp.service';
import { LookupIds } from '../../../../shared/enums/lookupIds.enum';
import { InputType } from '../../../../shared/enums/input-type.enum';

@Component({
  selector: 'app-request-form',
  templateUrl: './request-form.component.html',
  styleUrl: './request-form.component.scss',
})
export class RequestFormComponent {
  @Input() form: FormGroup;
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  isUaeNational: boolean = true;
  //Lookups
  PortalCategory: Lookup[];
  SubCategory: Lookup[];
  filteredSubCategory: Lookup[];
  Areas: Lookup[];
  filteredAreas: Lookup[];
  Emirates: Lookup[];
  MaritalStatus: Lookup[];
  Centers: Lookup[];
  booleanList = [new Lookup(true, 'yes', 'نعم'), new Lookup(false, 'No', 'لا')];

  MilitaryServiceStatus = [
    new Lookup('1', 'Enrolled in Military service', 'مستمر بالخدمة الوطنية'),
    new Lookup('2', 'Completed Military service', 'تم اكمال الخدمة الوطنية '),
    new Lookup('3', 'Exempted from Military service', 'معفي من الخدمة الوطنية'),
    new Lookup('4', 'Not applicable for women', 'لا ينطبق لللاناث'),
  ];

  Terminated = [new Lookup('1', 'I have been terminated from Public/Semi-Government', 'تم انهاء خدمتي من جهة حكومية او جهة شبه حكومية'), new Lookup('2', 'I have been terminated from private sector', 'تم انهاء خدمتي من القطاع الخاص'), new Lookup('3', 'I did not work before', 'لم اعمل سابقاً')];

  ChildEligibilityforWomeninDifficultyMerged = [
    new Lookup('662410003', 'Not applicable', 'لا ينطبق'),
    new Lookup('662410000', 'At least 1 child less than 4 years old', 'طفل واحد على الأقل أقل من 4 سنوات'),
    new Lookup('662410001', 'At least 1 PoD child less than 21 years old', 'طفل واحد على الأقل من ذوي الإعاقة يقل عمره عن 21 عامًا'),
    new Lookup('662410002', 'At least 1 PoD child less than 25 years old if the same child is a qualified student', 'يجب أن يكون عمر طفل واحد على الأقل من فئة ذوي الإعاقة أقل من 25 عامًا إذا كان نفس الطفل طالبًا مؤهلاً'),
  ];

  utilityProvider = [new Lookup('662410001', 'DEWA', '(DEWA) هيئة كهرباء ومياه دبي'), new Lookup('662410003', 'EWE', '(EWE) الاتحاد للماء والكهرباء'), new Lookup('662410002', 'SEWA', '(SEWA) هيئة كهرباء ومياه الشارقة'), new Lookup('662410000', 'TAQA', '(TAQA) شركة أبو ظبي الوطنية للطاقة')];

  UNDER_45_AGE = LookupIds.UNDER_45_AGE;
  DIVORCED = LookupIds.DIVORCED;
  SPOUSE_INCAPACITATED_FOREIGNER = LookupIds.SPOUSE_INCAPACITATED_FOREIGNER;
  ABANDONED = LookupIds.ABANDONED;
  BORN_UNKNOWN_PARENTS = LookupIds.BORN_UNKNOWN_PARENTS;
  ORPHANS_ONLY = LookupIds.ORPHANS_ONLY;
  CHILD_OF_PRISONER = LookupIds.CHILD_OF_PRISONER;
  CHILD_IN_DIFFICULT_SITUATION = LookupIds.CHILD_IN_DIFFICULT_SITUATION;
  INFLATION_PROCESS_TEMPLATE_ID = '94b2a9e5-d2ae-ee11-a568-000d3a6c23a9';
  beneficiaryAge: any;
  userInfo: any;
  requestId: string | null;
  ContactId: string;


  constructor(
    protected lang: LanguageService,
    private notify: NotifyService,
    private elementRef: ElementRef,
    protected stepper: StepperService,
    private socialAidService: SocialAidService,
    private data: DataService,
    private router: Router,
    private auth: AuthService,
    private route: ActivatedRoute,
    private formService: FormService,
    private alert: AlertService,
    private translate: TranslateService,
    private swpService: SwpService
  ) {
    this.requestId = this.route.snapshot.paramMap.get('id');

    const userInfo = this.auth.getUserInfo();
    if (userInfo?.userInfo?.nationalityEN?.toUpperCase() != "UAE") {
      this.isUaeNational = false;
    }
    this.socialAidService.contactId$.subscribe(contactId => {
      this.ContactId = contactId || '';
    });

  }

  get f(): any {
    return this.form.controls;
  }

  ngOnInit() {
    this.socialAidService.lookupData$.subscribe((res) => {
      if (res && res.Data) {
        this.PortalCategory = this.swpService.translateLookup(res.Data.PortalCategory);
        this.SubCategory = this.swpService.translateLookup(res.Data.SubCategory);
        this.Areas = this.swpService.translateLookup(res.Data.Areas);
        this.Emirates = this.swpService.translateLookup(res.Data.Emirates);
        this.MaritalStatus = this.swpService.translateLookup(res.Data.MaritalStatus);
        this.Centers = this.swpService.translateLookup(res.Data.Center);
      }
      if (this.requestId) {
        this.checkRequest();
      }
    });
    //Form Validation
    this.setupFormValidation();

    const userInfo = this.auth.getUserInfo();

    //Get ICP Info
    this.socialAidService.beneficiaryDetails$.subscribe((res) => {
      if (res?.StatusCode === 200) {
        this.userInfo = res.Data;

        //BENEFICIARY AGE
        const dob = this.userInfo?.DateofBirth;
        const date = new Date(dob);
        const age = Math.floor((Date.now() - date.getTime()) / 3.15576e10);
        this.beneficiaryAge = age;
      }
    });


  }

  setupFormValidation() {
    this.form.get('SubCategory')?.valueChanges.subscribe((subCategory) => {
      this.updateValidationBasedOnSubCategory(subCategory);
    });

    this.form.get('Category')?.valueChanges.subscribe((value: Lookup) => {
      this.filteredSubCategory = this.SubCategory?.filter((p) => p.ParentID == value?.ID);
      const subCategory = this.form.get('SubCategory')?.value;
      this.updateValidationBasedOnSubCategory(subCategory);
    });

    // (this.form.get('personalInfo') as FormGroup).get('Emirates')?.valueChanges.subscribe((value: Lookup) => {
    //   this.filteredAreas = this.Areas.filter((a) => a.ParentID == value?.ID);
    // });
  }

  updateValidationBasedOnSubCategory(subCategory: Lookup) {
    const categoryControl = this.form.get('Category');
    const age = this.beneficiaryAge;

    if (subCategory?.ID === LookupIds.DIVORCED || subCategory?.ID === LookupIds.SPOUSE_INCAPACITATED_FOREIGNER || subCategory?.ID === LookupIds.ABANDONED) {
      this.form.get('ChildEligibilityforWomeninDifficulty')?.setValidators([Validators.required, Validators.minLength(1)]);
    } else {
      this.form.get('ChildEligibilityforWomeninDifficulty')?.clearValidators();
    }

    if (subCategory?.ID === LookupIds.UNDER_45_AGE) {
      this.form.get('IsActiveStudent')?.setValidators([Validators.required]);
      this.form.get('MilitaryServiceStatus')?.setValidators([Validators.required]);
      this.form.get('Terminated')?.setValidators([Validators.required]);
      this.form.get('ReceivedLocalSupport')?.setValidators([Validators.required]);
    } else {
      this.form.get('IsActiveStudent')?.clearValidators();
      this.form.get('MilitaryServiceStatus')?.clearValidators();
      this.form.get('Terminated')?.clearValidators();
      this.form.get('ReceivedLocalSupport')?.clearValidators();
    }

    if (subCategory?.ID === LookupIds.BORN_UNKNOWN_PARENTS) {
      this.form.get('GuardianEmiratesID')?.setValidators([Validators.required]);
    } else {
      this.form.get('GuardianEmiratesID')?.clearValidators();
    }

    if (
      (subCategory?.ID === LookupIds.DIVORCED || subCategory?.ID === LookupIds.SPOUSE_INCAPACITATED_FOREIGNER || subCategory?.ID === LookupIds.ABANDONED) &&
      this.form.get('ChildEligibilityforWomeninDifficulty')?.value.length > 0 &&
      !this.form.get('ChildEligibilityforWomeninDifficulty')?.value.find((item) => item.value === '662410003')
    ) {
      this.form.get('NumberOfChildren')?.setValidators([Validators.required, Validators.min(1), Validators.pattern(/^[0-9]{1,2}$/), this.validateNumberOfChildren.bind(this)]);
    } else {
      this.form.get('NumberOfChildren')?.clearValidators();
    }

    if (
      (subCategory?.ID === LookupIds.DIVORCED || subCategory?.ID === LookupIds.SPOUSE_INCAPACITATED_FOREIGNER || subCategory?.ID === LookupIds.ABANDONED) &&
      this.form.get('ChildEligibilityforWomeninDifficulty')?.value.length > 0 &&
      !this.form.get('ChildEligibilityforWomeninDifficulty')?.value.find((item) => item.value === '662410003') &&
      this.form.get('ChildEligibilityforWomeninDifficulty')?.value.find((item) => item.value === '662410002')
    ) {
      this.form.get('NumberOfChildrenLessThan25')?.setValidators([Validators.required, Validators.pattern(/^[0-9]{1,2}$/), Validators.min(1), Validators.max(Number(this.form.get('NumberOfChildren')?.value))]);
    } else {
      this.form.get('NumberOfChildrenLessThan25')?.clearValidators();
    }

    this.form.get('ChildEligibilityforWomeninDifficulty')?.updateValueAndValidity();
    this.form.get('IsActiveStudent')?.updateValueAndValidity();
    this.form.get('MilitaryServiceStatus')?.updateValueAndValidity();
    this.form.get('Terminated')?.updateValueAndValidity();
    this.form.get('ReceivedLocalSupport')?.updateValueAndValidity();
    this.form.get('GuardianEmiratesID')?.updateValueAndValidity();
    this.form.get('NumberOfChildren')?.updateValueAndValidity();
    this.form.get('NumberOfChildrenLessThan25')?.updateValueAndValidity();
  }

  validateNumberOfChildren(control) {
    const value = control.value;
    const childEligibilityLength = this.form.get('ChildEligibilityforWomeninDifficulty')?.value.length;
    if (value && value >= childEligibilityLength) {
      return null;
    } else {
      return { invalidNumberOfChildren: true };
    }
  }

  submit(saveDraft: boolean = true) {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.stepper.scrollToError(this.elementRef);
      this.notify.showError('notify.error', 'notify.invalidForm');
    } else if (saveDraft) {
      const userInfo = this.auth.getUserInfo();
      this.ContactId = localStorage.getItem('ContactId') ?? "";

      const request = {
        UpdateType: !this.requestId ? "CREATE" : saveDraft ? "DRAFT" : "SUBMIT",
        IdCase: this.requestId || undefined,
        CaseDetails: {
          // Education: this.f.Educations || "",
          // AccomodationType: this.f.accomadations || "",
          ChildEligibilityforWomeninDifficulty: this.s?.ChildEligibilityforWomeninDifficulty?.value?.ID || "",
          Category: this.s?.Category?.value?.ID || "",
          SubCategory: this.s?.SubCategory?.value?.ID || "",
          IsActiveStudent: this.s?.IsActiveStudent?.value?.NameEnglish == "yes",
          ReceivedLocalSupport:
            this.s?.ReceivedLocalSupport?.value?.NameEnglish == "yes"
              ? 662410000
              : this.s?.ReceivedLocalSupport?.value?.NameEnglish == "no"
                ? 662410001
                : 662410002,
          PursuingHigherEducation: this.s?.PursuingHigherEducation?.value?.NameEnglish == "yes",
          NumberOfChildren: this.s?.NumberOfChildren?.value || 0,
          NumberOfChildrenLessThan25: this.s?.NumberOfChildrenLessThan25?.value || 0,
          DraftedinMilitaryService: this.s?.PursuingMilitaryService?.value?.NameEnglish == "yes",
          MilitaryServiceStatus: this.s?.MilitaryServiceStatus?.value?.ID || "",
          Terminated: this.s?.Terminated?.value?.ID || "",
          GuardianEmiratesID: this.s?.GuardianEmiratesID?.value || "",
        },
        CaseType: 1,
        Testing: true,
        IdAllowanceCategory: LookupIds.ALLOWANCE_CATEGORY,
        IdBeneficiary: this.ContactId,
        IdProcessTempalte: LookupIds.PROCESS_TEMPLATE_ID
      }

      this.socialAidService.SaveRequest(request).subscribe((res) => {
        if (res.IsSuccess) {
          this.stepper.updateFormData(this.form.getRawValue());
          this.notify.showSuccess('notify.success', 'notify.draftSaved');

          if (!this.requestId) {
            this.stepper.setAutoStep(true);
            this.router.navigate(['/e-services/social-aid', res?.Data?.IdCase]);
          }
          else {
            this.next.emit();
          }
        } else {
          this.notify.showParsedError('notify.error', res.Errors);
        }
      })
    }
  }

  checkRequest() {
    this.requestId = this.route.snapshot.paramMap.get('id');
    if (!this.ContactId) {
      this.ContactId = localStorage.getItem('ContactId') || '';
    }
    // const userInfo = this.auth.getUserInfo();
    if (this.requestId) {

      this.socialAidService.getRequest(this.requestId, this.ContactId).subscribe((res) => {
        if (res.StatusCode === 200) {
          this.form.patchValue({
            ChildEligibilityforWomeninDifficulty: this.stepper.getLookup(this.ChildEligibilityforWomeninDifficultyMerged, res.Data.CaseDetails.ChildEligibilityforWomeninDifficulty),
            Category: this.stepper.getLookup(this.PortalCategory, res.Data.CaseDetails.Category),
            SubCategory: this.stepper.getLookup(this.SubCategory, res.Data.CaseDetails.SubCategory),
            IsActiveStudent: this.stepper.getLookup(this.booleanList, res.Data.CaseDetails.IsActiveStudent),
            ReceivedLocalSupport: this.stepper.getLookup(this.booleanList, res.Data.CaseDetails.ReceivedLocalSupport),
            PursuingHigherEducation: this.stepper.getLookup(this.booleanList, res.Data.CaseDetails.PursuingHigherEducation),
            NumberOfChildren: res.Data.CaseDetails.NumberOfChildren,
            NumberOfChildrenLessThan25: res.Data.CaseDetails.NumberOfChildrenLessThan25,
            PursuingMilitaryService: this.stepper.getLookup(this.booleanList, res.Data.CaseDetails.DraftedinMilitaryService),
            MilitaryServiceStatus: this.stepper.getLookup(this.MilitaryServiceStatus, res.Data.CaseDetails.MilitaryServiceStatus),
            Terminated: this.stepper.getLookup(this.Terminated, res.Data.CaseDetails.Terminated),
            GuardianEmiratesID: res.Data.CaseDetails.GuardianEmiratesID,
          });
          // this.stepper.updateFormData(res?.Data?.CaseDetails);
          // this.stepper.updateFormData({ data: res?.Data?.CaseDetails });
          if (this.form.valid) {
            this.stepper.updateFormData(this.form.getRawValue());
            // this.next.emit();
          }
        } else if (!res.IsSuccess) {
          this.notify.showParsedError('notify.error', res.Errors);
        }
      });
    }
  }

  //Get Form Controls Of Social Aid
  get s(): any {
    return this.form.controls;
  }

  public get InputType() {
    return InputType;
  }
}
