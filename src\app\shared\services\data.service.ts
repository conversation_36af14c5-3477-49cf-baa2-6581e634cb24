import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';
import { environment } from '../../../environments/environment';
import { BehaviorSubject, Observable, of } from 'rxjs';

const JWT_TOKEN_KEY = 'jwtToken';
const USER_INFO_KEY = 'userInfo';


@Injectable({
  providedIn: 'root',
})
export class DataService {

  constructor(private http: HttpClient) { }

  /**
    *  GET request
    * @param path url path
    * @param apiSecretKey  generated api Secret Key
    * @returns
    * m.heaiba
    */
  get(path: string, apiSecretKey: string = environment.apiSecretKey): Observable<any> {
    const headers = new HttpHeaders({
      'APIKey': apiSecretKey
    });
    return this.http.get<any>(`${environment.apiBaseUrl}/${path}`, { headers });
  }


  /**
   * POST request
   * @param path url path
   * @param data  posted data
   * @param apiSecretKey  generated api Secret Key
   * @returns
   * m.heaiba
   */
  post(path: string, data: any, apiSecretKey: string = environment.apiSecretKey): Observable<any> {
    const headers = new HttpHeaders({
      'APIKey': apiSecretKey
    });
    return this.http.post<any>(`${environment.apiBaseUrl}/${path}`, data, { headers });
  }

  /**
   * PUT request
   * @param path url path
   * @param id identitiy
   * @param data update data
   * @param apiSecretKey  generated api Secret Key
   * @returns
   *  m.heaiba
   */
  put(path: string, id: number, data: any, apiSecretKey: string = environment.apiSecretKey): Observable<any> {
    const headers = new HttpHeaders({
      'APIKey': apiSecretKey
    });
    return this.http.put<any>(`${environment.apiBaseUrl}/${path}/${id}`, data, { headers });
  }

  /**
   * DELETE request
   * @param path  url path
   * @param apiSecretKey generated api Secret Key
   * @returns
   *  m.heaiba
   */
  delete(path: string, apiSecretKey: string = environment.apiSecretKey): Observable<any> {
    const headers = new HttpHeaders({
      'APIKey': apiSecretKey
    });
    return this.http.delete<any>(`${environment.apiBaseUrl}/${path}`, { headers });
  }

  getIcpData(eid: string): Observable<any> {
    return this.http.get<any>(`${environment.authBaseURL}api/ICA/ICAPersonDetailWithDOB?emiratesID=${eid}`);
  }
  getIcpDataWithDoB(eid: string, dob: string): Observable<any> {
    return this.http.get<any>(`${environment.apiBaseUrl}/PODCard/VerifyEIDWithDOB?EmiratesId=${eid}&DateOfBirth=${dob}`);
  }
  getPodCard(eid: string, cardNo: string): Observable<any> {
    var url = `${environment.apiBaseUrl}/PODCard/SearchForCard?emiratesId=${eid}&cardNo=${cardNo}`;
    return this.http.get<any>(url);
  }

  checkEligibility(eid: string, dob: string): Observable<any> {
    return this.http.get<any>(`${environment.apiBaseUrl}/PODCenter/CheckEligibility?emiratesId=${eid}&DateOfBirth=${dob}`);
  }

}

