import {
	But<PERSON>,
	<PERSON>lex,
	Show,
	Table,
	Table<PERSON>ontainer,
	Tbody,
	Text,
	Th,
	Thead,
	Tr,
	VStack,
} from "@chakra-ui/react";
import { ButtonArrowIcon, EditIcon, ExlamationMark, PencilIcon } from "components/Icons";
import { useTranslation } from "next-i18next";
import { IFamilyMember } from "interfaces/SocialAidForm.interface";
import StatusPill from "components/StatusPill";
import { getLookupLabel } from "utils/helpers";
import { useFormContext } from "context/FormContext";
import { WIFE_LOOKUP_ID, HUSBAND_LOOKUP_ID } from "config";
import { useRouter } from "next/router";

interface Props {
	members: IFamilyMember[];
	setDeleteMemberId: Function;
	setEditMember: Function;
	mb?: number;
	readOnly?: boolean;
	caseType?: number;
}

function FamilyMembersTable({
	members,
	setDeleteMemberId,
	setEditMember,
	mb = 0,
	readOnly = false,
	caseType = 1,
}: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const { locale } = useRouter();
	const { lookups } = useFormContext();
	let headers = [
		{
			id: "Fullname",
		},
		{
			id: "numberOfHousehold",
			width: "25%",
		},
		{
			id: "relationship",
			width: "15%",
		},
		{
			id: "status",
			width: "25%",
		},
		{
			id: "Action",
		},
	];
	return (
		<>
			<Show above="md">
				<TableContainer
					mb={mb}
					rounded={"lg"}
					border="1px"
					borderBottom={"0px"}
					borderColor="#BBBCBD"
				>
					<Table variant="simple">
						<Thead>
							<Tr fontWeight="medium" mb={4}>
								{headers.map((header, idx) => {
									return (
										<Th
											color="brand.tableTextColor"
											key={idx}
											textTransform="none"
											lineHeight="150%"
											fontSize="0.9375rem"
											fontWeight="bold"
											letterSpacing="unset"
											py={4}
											px={4}
											borderColor="brand.tableBorderColor"
											width={header.width || "unset"}
										>
											{t(header.id)}
										</Th>
									);
								})}
							</Tr>
						</Thead>
						<Tbody>
							{members?.map((member, memberRow) => {
								return (
									<Tr key={member.Id}>
										{headers.map((header, idx) => {
											return (
												<Th
													key={idx}
													textTransform="none"
													color="brand.familyTableRowColor"
													fontWeight="normal"
													fontSize="0.9375rem"
													letterSpacing="unset"
													lineHeight="150%"
													p={4}
													borderColor="brand.tableBorderColor"
												>
													{header.id === "numberOfHousehold" ? (
														memberRow + 1
													) : header.id === "actions" ? (
														<Flex>
															{(member.Relationship === WIFE_LOOKUP_ID || member.Relationship === HUSBAND_LOOKUP_ID) && !readOnly && (
																<PencilIcon
																	mr={8}
																	color="brand.gray.400"
																	transform="scale(1.25, 1.25)"
																	onClick={() => {
																		if (!readOnly) setEditMember(member);
																	}}
																	cursor="pointer"
																/>
															)}
															{/* <TrashIcon
														color="brand.gray.400"
														transform="scale(1.25, 1.25)"
														onClick={() => setDeleteMemberId(member.Id)}
													/> */}
														</Flex>
													) : header.id === "status" ? (
														member.IsInformationUpdated ? (
															<Flex flexDirection={"row"}>
																<StatusPill
																	customText={t("complete")!}
																	customStatus={"requestApproved"}
																	onClick={() => {
																		if (!readOnly || !member.IsInformationUpdated) {
																			setEditMember(member);
																		}
																	}}
																/>
															</Flex>
														) : (
															<Button
																onClick={() => {
																	setEditMember(member);
																}}
																rightIcon={
																	<ButtonArrowIcon
																		w={"24px"}
																		h={"24px"}
																		transform={locale === "ar" ? "scale(-1, 1)" : ""}
																		marginInlineStart={1}
																	/>
																}
																variant={"outline"}
																minHeight={"0.2rem"}
																h={"2rem"}
																px={"1px !important"}
															>
																{t("completeInfo")}
															</Button>
														)
													) : header.id === "relationship" ? (
														getLookupLabel(lookups, "FamilyRelationship", member?.Relationship)
													) : header.id === "Action" ? (
														member.IsInformationUpdated &&
														caseType === 2 && (
															<EditIcon
																w={"40px"}
																height={"40px"}
																transform="scale(1.25, 1.25)"
																onClick={() => {
																	if (!readOnly || !member.IsInformationUpdated) {
																		setEditMember(member);
																	}
																}}
																cursor="pointer"
															/>
														)
													) : (
														<Flex alignItems={"center"} gap={2}>
															{!member.IsInformationUpdated && (
																<ExlamationMark color="brand.mainGold" />
															)}
															{header.id === "Fullname" ? (
																<Text>
																	{locale === "en" ? member["FullnameEN"] : member["FullnameAR"]}
																</Text>
															) : (
																<Text>{member[header.id]}</Text>
															)}
														</Flex>
													)}
												</Th>
											);
										})}
									</Tr>
								);
							})}
						</Tbody>
					</Table>
				</TableContainer>
			</Show>

			<Show below="md">
				<VStack align={"start"}>
					{members.map((member, index) => {
						return (
							<Flex
								key={index}
								w="full"
								p={4}
								justifyContent={"space-between"}
								borderBottom="1px solid #BBBCBD"
								_hover={{ cursor: "pointer" }}
							>
								<VStack flex={2} align={"start"}>
									<Flex alignItems={"center"} gap={2}>
										{!member.IsInformationUpdated && <ExlamationMark color="brand.mainGold" />}
										<Text fontSize={"1rem"} fontWeight={"bold"}>
											{locale === "en" ? member.FullnameEN : member.FullnameAR} (
											{getLookupLabel(lookups, "FamilyRelationship", member?.Relationship)})
										</Text>
									</Flex>
									<Text fontSize={"0.875rem"}>
										{t("numberOfHousehold")} : {index + 1}
									</Text>
								</VStack>
								{member.IsInformationUpdated ? (
									<StatusPill
										customText={t("complete")!}
										customStatus={"requestApproved"}
										onClick={() => {
											if (!readOnly) setEditMember(member);
										}}
									/>
								) : (
									<Button
										onClick={() => {
											setEditMember(member);
										}}
										rightIcon={
											<ButtonArrowIcon
												w={"24px"}
												h={"24px"}
												transform={locale === "ar" ? "scale(-1, 1)" : ""}
												marginInlineStart={1}
											/>
										}
										variant={"outline"}
										minHeight={"0.2rem"}
										h={"2rem"}
										px={"1px !important"}
									>
										{t("completeInfo")}
									</Button>
								)}
							</Flex>
						);
					})}
				</VStack>
			</Show>
		</>
	);
}

export default FamilyMembersTable;

// members.map((row, rowIdx) => {
// 	console.log(row.id, t(row.id));
// 	return (
// 		<Tr key={rowIdx} bg={rowIdx % 2 !== 0 ? "#F8F8F8" : "unset"}>
// 			<Td
// 				borderColor="brand.tableBorderColor"
// 				fontSize="0.9375rem"
// 				fontWeight="normal"
// 				lineHeight="150%"
// 			>
// 				{t(row.id)}
// 			</Td>
// 			<Td
// 				borderColor="brand.tableBorderColor"
// 				fontSize="0.9375rem"
// 				fontWeight="bold"
// 				lineHeight="150%"
// 			>
// 				{row.value}
// 			</Td>
// 		</Tr>
// 	);
// })}
