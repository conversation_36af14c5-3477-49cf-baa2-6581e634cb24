<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="col-md-12 d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center">
        {{(messageTranslationPrefix+'title') | translate}}
      </h1>
    </div>


    <div class="row section-separator">
      <div class="col-md-12">
        <app-select [label]="messageTranslationPrefix+'donationDateAndDuration' | translate"
          [control]="fb?.DonationDateOrDuration" [data]="donationDateOrDurationLookups"
          (onValueChange)="selectChange($event)" />
      </div>
      @if(fb?.DonationDateOrDuration?.value?.ID == 1){
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'dateOfReceivingDonations' | translate"
          [control]="fb?.DateOfReceivingDonations" [min]="minDate" />
      </div>
      }

      @if(fb?.DonationDateOrDuration?.value?.ID == 2){
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'fromDate' | translate" [control]="fb?.FromDate"
          [min]="minDate" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [label]="messageTranslationPrefix+'toDate' | translate" [control]="fb?.ToDate"
          [min]="minDate" />
      </div>
      }
    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
        (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()"> {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
