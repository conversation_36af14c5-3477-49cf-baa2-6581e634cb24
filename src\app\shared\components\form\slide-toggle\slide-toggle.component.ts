import { Component, Input, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-slide-toggle',
  templateUrl: './slide-toggle.component.html',
  styleUrls: ['./slide-toggle.component.scss'],
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class SlideToggleComponent {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() hint: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Input() displayHintInAllSituations = false;


  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  constructor(private validationService: ValidationService) { }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

}
