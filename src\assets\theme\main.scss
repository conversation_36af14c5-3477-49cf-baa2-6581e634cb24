// * {
//   [dir="ltr"] {
//     font-family: "Roboto" !important;
//   }

//   [dir="rtl"] {
//     font-family: "Noto Kufi Arabic" !important;
//   }

//   [lang="en"] {
//     font-family: "Roboto" !important;
//   }

//   [lang="ar"] {
//     font-family: "Noto Kufi Arabic" !important;
//   }
// }
[dir="ltr"]:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons):not(textarea:lang(ar):not(:placeholder-shown)):not(input:lang(ar):not(:placeholder-shown)):not(td:lang(ar)) {
  font-family: 'Roboto', sans-serif !important;
}

[dir="rtl"]:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons):not(textarea:lang(en):not(:placeholder-shown)):not(input:lang(en):not(:placeholder-shown)):not(td:lang(en)) {
  font-family: 'Noto <PERSON> Arabic', 'Roboto', sans-serif !important;
}

[dir="ltr"] *:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons):not(textarea:lang(ar):not(:placeholder-shown)):not(input:lang(ar):not(:placeholder-shown)):not(td:lang(ar)) {
  font-family: 'Roboto', sans-serif !important;
}

[dir="rtl"] *:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons):not(textarea:lang(en):not(:placeholder-shown)):not(input:lang(en):not(:placeholder-shown)):not(td:lang(en)) {
  font-family: 'Noto Kufi Arabic', 'Roboto', sans-serif !important;
}

[lang="en"]:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons) {
  font-family: 'Roboto', sans-serif !important;
}

[lang="ar"]:not(.fa-classic, .fa-regular, .fa-solid, .far, .fas, .material-icons) {
  font-family: 'Noto Kufi Arabic', 'Roboto', sans-serif !important;
}

[lang="en"] * {
  font-family: "Roboto" !important;
}

[lang="ar"] * {
  font-family: "Noto Kufi Arabic" !important;
}

body {
  .material-icons {
    font-family: "Material Icons" !important;
  }

  & * {

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: $aeblack-900;
    }
  }

  // & *:dir(rtl){
  //   h1,
  //   h2,
  //   h3,
  //   h4,
  //   h5,
  //   h6 {
  //     font-family: "Noto Kufi Arabic" !important;
  //   }
  // }


}

.fa-classic,
.fa-regular,
.fa-solid,
.far,
.fas {
  font-family: "Font Awesome 6 Free" !important;
}

// #content-wrapper {
//   background-color: #F7F7F7;
// }
.navbar {
  background-color: #f8f9fc;
}

.container {
  &--dashboard {

    // background-color: $aeblack-50;
    .section {
      padding: 16px 0px;

      &--no-paddingTop {
        padding-top: 0px;
      }

      &__header {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-bottom: 16px;

        @media (min-width: 1024px) {
          flex-direction: row;
          margin-bottom: 24px;
        }

        h2 {
          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 26px;
          margin-bottom: 8px;

          @media (min-width: 1024px) {
            font-size: 26px;
            line-height: 32px;
          }
        }

        .section__action {
          a {
            font-size: 16px;
            font-weight: 500;
            line-height: 22px;
          }
        }
      }
    }

    .col-md-6,
    section {
      padding-top: 0em;
    }
  }
}

section {
  padding-top: 0;
}

.offset-lg-3 {
  @media (min-width: 992px) {

    [ng-reflect-dir="rtl"] &,
    [dir="rtl"] & {
      margin-left: 0px;
      margin-right: 25%;
    }
  }
}

// bg colors classes
.bg-mocdyellow-gradient {
  background: linear-gradient(0deg, $aeblack-50 0%, #f3ebdc 74.5%);
}

.bg-mocdyellow {
  background-color: $mocdyellow;
}

// font style classes
.step-title {
  font-size: 32px !important;
  font-weight: 700 !important;
  line-height: 38px !important;
  margin: 32px 0px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 62px !important;
    line-height: 70px !important;
  }
}

.subtitle {
  font-size: 24px !important;
  font-weight: 700 !important;
  line-height: 32px !important;
}

.service-sub-heading {
  color: $aeblack-800 !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  line-height: 32px !important;
}

.info-desc {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;

  // margin: 16px 0px;
  @media only screen and (min-width: 1024px) {
    font-size: 20px;
    line-height: 26px;
  }
}

.info-desc-xs {
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 18px !important;
    line-height: 28px !important;
  }
}

.info-desc-sm {
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 22px !important;
  margin-bottom: 8px !important;
}

.info-desc-sm--semi-bold {
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 22px !important;
}

.info-desc-sm--bold {
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22px !important;
}

.info-desc-md {
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 20px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 24px !important;
    line-height: 32px !important;
  }
}

.info-desc-md-bold {
  font-size: 20px !important;
  font-weight: 700 !important;
  line-height: 28px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 26px !important;
    line-height: 32px !important;
    font-weight: 600 !important;
  }
}

.info-desc-md-thin {
  font-size: 20px !important;
  font-weight: 400 !important;
  line-height: 28px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 24px !important;
    line-height: 32px !important;
  }
}

.info-desc-md-extra-thin {
  font-size: 18px !important;
  font-weight: 400 !important;
  line-height: 28px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 24px !important;
    line-height: 32px !important;
  }
}

.info-desc-lg-small-mobile {
  font-size: 24px !important;
  font-weight: 700 !important;
  line-height: normal !important;

  @media only screen and (min-width: 1024px) {
    font-size: 40px !important;
    line-height: normal !important;
  }
}

.info-desc-lg {
  font-size: 32px !important;
  font-weight: 700 !important;
  line-height: normal !important;

  @media only screen and (min-width: 1024px) {
    font-size: 40px !important;
    line-height: normal !important;
  }
}

.info-desc-xl {
  font-size: 26px !important;
  font-weight: 700 !important;
  line-height: 32px !important;

  @media only screen and (min-width: 1024px) {
    font-size: 48px !important;
    line-height: normal !important;
  }
}

.info-desc-2xl {
  font-size: 24px !important;
  font-weight: 700 !important;
  line-height: 32px !important;
  margin-bottom: 8px !important;
  color: $aeblack-800;
}

// font-colors classes
.text-red {
  color: $aered-600;
}

.text-green {
  color: $aegreen-600;
}

.text-gold {
  color: $aegold-900;
}

// negative spacing
.mt-n10 {
  margin-top: -40px;
}

.mt-n40 {
  margin-top: -160px;
}

.tooltip {
  z-index: 10001;
  background-color: black;
  border-radius: 4px;
  padding: 6px 18px;
}

.cdk-live-announcer-element {
  display: none;
}

.mat-mdc-select-panel.mdc-menu-surface {
  box-shadow: 0px 20px 25px -5px rgba(27, 29, 33, 0.1),
    0px 8px 10px -6px rgba(27, 29, 33, 0.1) !important;
}

.flex-sm-column {
  @media (max-width: 1023px) {
    flex-direction: column;
  }
}

.figma-review-card-container {
  margin: 24px 0 !important;
  display: grid;
  gap: 16px !important;
  grid-template-columns: repeat(2, 1fr);

  .figma-review-card {
    background-color: rgba(249, 247, 237, 1);
    padding: 24px !important;
    border-radius: 8px !important;
  }
}

@media (max-width: 1024px) {
  .figma-review-card-container {
    display: flex;
    gap: 16px !important;
    width: 100% !important;
    justify-content: center;
    flex-direction: column;

    .figma-review-card {
      background-color: rgba(249, 247, 237, 1);
      padding: 24px !important;
      border-radius: 8px !important;
    }
  }
}

.dashboard-padding {
  padding: 40px 0;

  @media (max-width: 1024px) {
    padding: 24px 0 32px 0 !important;
  }
}

.m-sm-auto {
  @media (max-width:1024px) {
    margin: auto !important;
  }
}

// [dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
//   padding-left: 16px !important;
// }

@media (max-width: 1024px){
  .mobileView {
    .mat-mdc-form-field-flex {
      height: 38px !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

app-radio {
  input:focus, input:hover, .mdc-radio:hover {
    ~.mdc-radio__background::before {
      transform: scale(0, 0) !important;
    }
  }


  .mat-mdc-radio-button .mdc-radio:hover .mdc-radio__native-control:not([disabled]):not(:focus)~.mdc-radio__background::before {
    transform: scale(0, 0) !important;
  }

  .radio-mobile-view {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
}