import { SwpService } from "../services/swp.service";

export enum ServiceCatalogue {
  LostCard = "904EBD96-7D78-EE11-B10C-************",
  IssueCard = "38E3F60B-1861-EE11-B10B-************",
  NPODeclaration = "FD3677BA-35D5-EE11-B10D-************",
  PodCenterRegistration = "F9CB6C71-DB65-EF11-B110-************",
  SwpService = "FD3677BA-35D5-EE11-B10D-************",
  Complaint = "FD3677BA-35D5-EE11-B10D-************",
  ThankYou = "FD3677BA-35D5-EE11-B10D-************",
  Inquiry = "FD3677BA-35D5-EE11-B10D-************",
  Suggestion = "FD3677BA-35D5-EE11-B10D-************",
  NPOLicenseDeclaration = "fd3677ba-35d5-ee11-b10d-************",
  NPOLicenseDeclarationByDecree = "93e0148b-50a7-ef11-b111-************",

  MarriageGrant = "8670E00C-53A6-EF11-B111-************",
  MassWedding = "8FA01460-53A6-EF11-B111-************",
  EarlyInterventionCenter = "38e3f60b-1861-ee11-b10b-************",
  Issue971 = "bdbaf11a-bdf2-ef11-b112-************",
  // reportingAbuse = "02e3f60b-1861-ee11-b10b-************",
  licensingNonGovPodCenter = "fbec84fe-bef2-ef11-b112-************",
  RequestAd = "a704dec8-bdf2-ef11-b112-************",
  renewfamilyCounselingLicense = "a6e5c96b-bdf2-ef11-b112-************",
  renewNonGovPodCenter = "4a01979d-bef2-ef11-b112-************",
  licensingPrivateFamilyCounselingCenter = "aa1e4039-bef2-ef11-b112-************",
  EstablishmentEdit = "481be108-86fd-ef11-b112-************",
  ToWhomApply = "f54116ec-22e4-ed11-8847-6045bd6a528f",
  IssueLicenseNonMuslimWorshipPlace = "0a6f27a6-7fe8-ef11-b112-************",
  RequestAllocationWorshipRoom = "8979dcde-eb12-f011-b112-************",

  RequestToIssueFundraisingPermit = "8bc94fb2-ba33-f011-b112-************",
  RequestToExtendFundraisingPermit = "ea9c9497-fb4c-f011-b112-************",
  AffiliateSubscribeOrJoinAssociationsOrRegionalInternationalEntities = "87ca4c70-d545-f011-b112-************",
  ParticipateInActivitiesAndEvents = "a765722d-033e-f011-b112-************",
  OrganizeActivitiesAndEvents = "dbdc5fa2-f140-f011-b112-************",

  RequestForApprovalOfOpeningNPOBranch = "f91e1d95-8e56-f011-b112-************",
  RequestForReceiveDonationsNOC = "0e099d40-f558-f011-b112-************",

  OpeningNewBankAccountRequest = "732030c4-895c-f011-b112-************"

}

