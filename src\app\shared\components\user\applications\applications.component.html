<!-- <div class="container" *ngIf="auth.isAuthenticated()">
  <div class="card m-2">
  <div class="card-body text-center table-container">
    <div class="container">
      <p></p>
      <div class="mb-3 row d-flex justify-content-between p-2">
        <div class="col-xs-3 col-sm-auto">
            <form class="form">
            <table>
              <td>
                <input
                id="table-complete-search"
                type="text"
                class="form-control"
                name="searchTerm"
                [placeholder]="'Search' | translate"
                [(ngModel)]="searchTerm"

              />
              </td>
              <td><span><button mat-button color="primary" (click)="search()" class="btn basic-filled-button btn-sm"><i class="fa fa-magnifying-glass"></i></button></span></td>
            </table>
          </form>
          </div>
          <div class="col-xs-3 col-sm-auto">
            <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="statusId" [disabled]="disabled" (change)="onStatusChange()">
              <option value="-1">{{ "status.all" | translate }}</option>
              <option value="100">{{ "status.Completed" | translate }}</option>
              <option value="100000001">{{ "status.InProgress" | translate }}</option>
              <option value="100000000">{{ "status.Submitted" | translate }}</option>
              <option value="2">{{ "status.OnHold" | translate }}</option>
              <option value="1">{{ "status.Draft" | translate }}</option>
              <option value="100000004">{{ "status.Rejected" | translate }}</option>
              <option value="100000003">{{ "status.Approved" | translate }}</option>
              <option value="100000002">{{ "status.Returned" | translate }}</option>
              <option value="100000005">{{ "status.PendingPayment" | translate }}</option>
            </select>
          </div>

        </div>
    </div>
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th scope="col" class="text-center" sortable="ApplicationNumber" (sort)="onSort($event)">{{'applications.ApplicationNumber' | translate}}</th>
            @if(lang.IsArabic)
            {
              <th scope="col" class="text-center" sortable="'ServiceCatalogueNameAR'" (sort)="onSort($event)">{{'applications.ServiceName' | translate}}</th>
            }
            @else
            {
              <th scope="col" class="text-center" sortable="'ServiceCatalogueName'" (sort)="onSort($event)">{{'applications.ServiceName' | translate}}</th>
            }
            <th scope="col" class="text-center" sortable="StatusName" (sort)="onSort($event)">{{'applications.Status' | translate}}</th>
            <th scope="col" class="text-center" sortable="SubmissionDate" (sort)="onSort($event)">{{'applications.SubmissionDate' | translate}}</th>
            <th scope="col" class="text-center">{{'applications.FinalComment' | translate}}</th>
            <th scope="col">{{'applications.Actions' | translate}}</th>
          </tr>
        </thead>
        <tbody>
          @for (app of filteredData; track app.name; let i = $index) {
            <tr>
              <td class="align-middle">{{ app.ApplicationNumber }}</td>
              <td class="align-middle">{{ lang.IsArabic?app.ServiceCatalogueNameAR:app.ServiceCatalogueName }}</td>
              <td class="align-middle">{{ lang.IsArabic?app.StatusNameAR:app.StatusName }}</td>
              <td class="align-middle">{{ app.SubmissionDate | date:'dd/MM/yyyy' }}</td>
              <td class="align-middle col-wrap">{{ app.FinalComment }}</td>
              <td class="align-middle">
                @if(app.StatusName == 'Draft' || app.StatusName == 'Pending Payment'|| app.StatusName == 'Returned'){
                  <button mat-button color="primary" (click)="viewApplication(app)"><i class="fa fa-edit"></i></button>
                }
                @if(app.StatusName == 'Draft' || app.StatusName == 'Pending Payment'){
                  <button mat-button color="primary" (click)="deleteApplication(app.Id, app.EntityName)"><i class="fa fa-trash"></i></button>
                }
              </td>
            </tr>
          }
        </tbody>
      </table>
</div>
    <div class="d-flex justify-content-between p-2">
      <span class="col-2">{{'Total count' | translate}}: {{filteredCount}}</span>
      <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize" [collectionSize]="filteredCount" (pageChange)="onPageChange()"></ngb-pagination>

      <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSize" (change)="onSelectChange()">
        <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
        <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
        <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
      </select>
    </div>

  </div>
</div>
</div>

 -->


<!-- <div class="container" style="margin-top: 40px !important;" *ngIf="auth.isAuthenticated()"> -->
<!-- new application.component -->
<div *ngIf="auth.isAuthenticated()" class="application-overview">
  <div class="application-overview__header">
    <div>
      <h2>{{'userPages.applications.title'|translate}}</h2>
      <p>{{'Keep track of your applications and tasks'|translate}}</p>
    </div>
    <div class="application-overview__header-action">
      <a href="#" class="aegov-link " title="View All ">
        {{'View All' | translate}}
        <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z">
          </path>
        </svg>
        <span class="sr-only"> {{'View All' | translate}}</span>
      </a>
    </div>
  </div>
  <div class="application-overview__filters">
    <div class="d-flex gap-3 order-lg-1">
      <div class="d-none">
        <mat-form-field>
          <mat-label>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
              <path
                d="M3.33334 15C3.09723 15 2.89945 14.92 2.74 14.76C2.58056 14.6 2.50056 14.4022 2.5 14.1667C2.49945 13.9311 2.57945 13.7333 2.74 13.5733C2.90056 13.4133 3.09834 13.3333 3.33334 13.3333H6.66667C6.90278 13.3333 7.10084 13.4133 7.26084 13.5733C7.42084 13.7333 7.50056 13.9311 7.5 14.1667C7.49945 14.4022 7.41945 14.6003 7.26 14.7608C7.10056 14.9214 6.90278 15.0011 6.66667 15H3.33334ZM3.33334 10.8333C3.09723 10.8333 2.89945 10.7533 2.74 10.5933C2.58056 10.4333 2.50056 10.2356 2.5 10C2.49945 9.76444 2.57945 9.56667 2.74 9.40667C2.90056 9.24667 3.09834 9.16667 3.33334 9.16667H11.6667C11.9028 9.16667 12.1008 9.24667 12.2608 9.40667C12.4208 9.56667 12.5006 9.76444 12.5 10C12.4994 10.2356 12.4194 10.4336 12.26 10.5942C12.1006 10.7547 11.9028 10.8344 11.6667 10.8333H3.33334ZM3.33334 6.66667C3.09723 6.66667 2.89945 6.58667 2.74 6.42667C2.58056 6.26667 2.50056 6.06889 2.5 5.83333C2.49945 5.59778 2.57945 5.4 2.74 5.24C2.90056 5.08 3.09834 5 3.33334 5H16.6667C16.9028 5 17.1008 5.08 17.2608 5.24C17.4208 5.4 17.5006 5.59778 17.5 5.83333C17.4994 6.06889 17.4194 6.26694 17.26 6.4275C17.1006 6.58806 16.9028 6.66778 16.6667 6.66667H3.33334Z" />
            </svg>
            sort By:
          </mat-label>
          <mat-select>
            <mat-option value="ss"> Most Relevant</mat-option>
            <mat-option value="ee">Oldest First</mat-option>
            <mat-option value="rr">Newest First</mat-option>
          </mat-select>
          <i class="icon-toggle fa-solid fa-chevron-down"></i>
        </mat-form-field>
      </div>
      <div>
        <form>
          <div class="aegov-form-control">
            <div class="form-control-input">
              <span class="control-suffix">
                <button class="" (click)="search()" type="submit">
                  <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 28 28">
                    <path
                      d="M25.1191 23.8809L19.6427 18.4056C21.2299 16.5 22.0214 14.0558 21.8525 11.5814C21.6836 9.10709 20.5672 6.79313 18.7357 5.12091C16.9041 3.4487 14.4984 2.54697 12.0189 2.60332C9.53944 2.65967 7.17715 3.66976 5.42345 5.42345C3.66976 7.17715 2.65967 9.53944 2.60332 12.0189C2.54697 14.4984 3.4487 16.9041 5.12091 18.7357C6.79313 20.5672 9.10709 21.6836 11.5814 21.8525C14.0558 22.0214 16.5 21.2299 18.4056 19.6427L23.8809 25.1191C23.9622 25.2004 24.0588 25.2649 24.165 25.3088C24.2712 25.3528 24.385 25.3755 24.5 25.3755C24.615 25.3755 24.7288 25.3528 24.835 25.3088C24.9413 25.2649 25.0378 25.2004 25.1191 25.1191C25.2004 25.0378 25.2649 24.9413 25.3088 24.835C25.3528 24.7288 25.3755 24.615 25.3755 24.5C25.3755 24.385 25.3528 24.2712 25.3088 24.165C25.2649 24.0588 25.2004 23.9622 25.1191 23.8809ZM4.37501 12.25C4.37501 10.6925 4.83687 9.16993 5.70218 7.87489C6.5675 6.57985 7.79741 5.57049 9.23637 4.97445C10.6753 4.37841 12.2587 4.22246 13.7863 4.52632C15.3139 4.83018 16.7171 5.5802 17.8185 6.68154C18.9198 7.78288 19.6698 9.18607 19.9737 10.7137C20.2775 12.2413 20.1216 13.8247 19.5256 15.2636C18.9295 16.7026 17.9202 17.9325 16.6251 18.7978C15.3301 19.6631 13.8075 20.125 12.25 20.125C10.1621 20.1227 8.16044 19.2923 6.6841 17.8159C5.20775 16.3396 4.37732 14.3379 4.37501 12.25Z" />
                  </svg>
                </button>
              </span>
              <input type="text" id="table-complete-search" name="searchTerm" [placeholder]="'Search' | translate"
                [(ngModel)]="searchTerm">

            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="overflow-x-auto application-overview__filters-tabs-parent">
      <!-- radio tabs status filter -->

      <form class="overflow-x-auto application-overview__filters-tabs">
        <div class="form-check" (click)="statusId='-1';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault1"
            [checked]="statusId=='-1'">
          <label class="form-check-label" for="flexRadioDefault11">
            {{ "status.all" | translate }}
          </label>
        </div>
        <div class="form-check" (click)="statusId='100';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault2"
            [checked]="statusId=='100'">
          <label class="form-check-label" for="flexRadioDefault21">
            {{ "status.Completed" | translate }}
          </label>
        </div>
        <div class="form-check" (click)="statusId='100000001';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault3"
            [checked]="statusId=='100000001'">
          <label class="form-check-label" for="flexRadioDefault31">
            {{ "status.InProgress" | translate }}
          </label>
        </div>
        <!-- <div class="form-check" (click)="statusId='100000000';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault4" [checked]="statusId=='100000000'">
            <label class="form-check-label" for="flexRadioDefault4">
              {{ "status.Submitted" | translate }}
            </label>
          </div> -->
        <div class="form-check" (click)="statusId='2';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault5"
            [checked]="statusId=='2'">
          <label class="form-check-label" for="flexRadioDefault51">
            {{ "status.OnHold" | translate }}
          </label>
        </div>
        <div class="form-check" (click)="statusId='1';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault6"
            [checked]="statusId=='1'">
          <label class="form-check-label" for="flexRadioDefault61">
            {{ "status.Draft" | translate }}
          </label>
        </div>
        <!-- <div class="form-check" (click)="statusId='100000004';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault7" [checked]="statusId=='100000004'">
            <label class="form-check-label" for="flexRadioDefault7">
              {{ "status.Rejected" | translate }}
            </label>
          </div>
          <div class="form-check" (click)="statusId='100000003';onStatusChange()">
            <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault8" [checked]="statusId=='100000003'">
            <label class="form-check-label" for="flexRadioDefault8">
              {{ "status.Approved" | translate }}
            </label>
          </div> -->
        <div class="form-check" (click)="statusId='100000002';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault9"
            [checked]="statusId=='100000002'">
          <label class="form-check-label" for="flexRadioDefault91">
            {{ "status.Returned" | translate }}
          </label>
        </div>
        <div class="form-check" (click)="statusId='100000005';onStatusChange()">
          <input class="form-check-input" type="radio" name="flexRadioDefault" id="flexRadioDefault10"
            [checked]="statusId=='100000005'">
          <label class="form-check-label" for="flexRadioDefault101">
            {{ "status.PendingPayment" | translate }}
          </label>
        </div>


      </form>

    </div>
  </div>
  <div class="application-overview__content">
    <!-- empty state need to remove d-none class to be shown while need to hide or remove table and footer actions"paggination , total count" -->
    <div class="application-overview__empty-state text-center d-none">
      <h4>{{'You have no applications'|translate}}</h4>
      <p>{{'Once you complete an application it will available here.'|translate}}</p>
      <button class="aegov-btn btn-outline">
        {{'All Services'|translate}}
        <span class="sr-only">{{'All Services'|translate}}</span>
      </button>
    </div>
    <!--/end empty state -->

    <!-- Application table -->
    <div class="table-listing-full-width">
      <div>
        <table class="table">
          <thead>
            <tr>
              <th scope="col" sortable="ApplicationNumber" (sort)="onSort($event)">{{'applications.ApplicationNumber' |
                translate}}</th>
              @if(lang.IsArabic)
              {
              <th scope="col" class="text-center" sortable="'ServiceCatalogueNameAR'" (sort)="onSort($event)">
                {{'applications.ServiceName' | translate}}</th>
              }
              @else
              {
              <th scope="col" class="text-center" sortable="'ServiceCatalogueName'" (sort)="onSort($event)">
                {{'applications.ServiceName' | translate}}</th>
              }

              <th scope="col" class="text-center" sortable="SubmissionDate" (sort)="onSort($event)">
                {{'applications.SubmissionDate' | translate}}</th>
              <th scope="col" class="text-center">{{'applications.FinalComment' | translate}}</th>
              <th scope="col" class="text-center" sortable="StatusName" (sort)="onSort($event)">{{'applications.Status'
                | translate}}</th>
              <th scope="col">{{'applications.Actions' | translate}}</th>
            </tr>
          </thead>
          <tbody>

            @for (app of filteredData; track app.Id; let i = $index) {
            <tr>
              <td class="mobile-only">
                <div class="table-listing-full-width__inner-title-container">
                  <div class="fw-bold text-primary">
                    {{'applications.ApplicationNumber' | translate}}:
                  </div>
                  <div>
                    <span class="table-listing-full-width__inner-title">{{ app.ApplicationNumber }}</span>
                  <div class="table-listing-full-width__new" *ngIf="i==1"><label>{{'New'|translate}}:</label></div>
                  </div>
                </div>
                <div class="table-listing-full-width__inner-title-container">
                  <div class="fw-bold text-primary">
                    {{'applications.ServiceName' | translate}}:
                  </div>
                  <div>
                    {{ lang.IsArabic?app.ServiceCatalogueNameAR:app.ServiceCatalogueName }}
                  </div>
                </div>
                <div class="table-listing-full-width__inner-title-container">
                  <div class="fw-bold text-primary">
                    {{'applications.SubmissionDate' | translate}}:
                  </div>
                  <div>
                    {{ app.SubmissionDate | date:'dd/MM/yyyy' }}
                  </div>
                </div>
                <div class="table-listing-full-width__inner-title-container">
                  <div class="fw-bold text-primary">
                    {{'applications.FinalComment' | translate}}:
                  </div>
                  <div>
                    {{ app.FinalComment }}
                  </div>
                </div>
                <div class="table-listing-full-width__inner-title-container">
                  <div class="fw-bold text-primary">
                    {{'applications.Status'| translate}}:
                  </div>
                  <div class="table-listing-full-width__status">
                    <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                      <circle cx="4.75" cy="4" r="4" fill="#F8C027" *ngIf="app.StatusName=='In Progress'" />
                      <circle cx="4.75" cy="4" r="4" fill="#C3C6CB" *ngIf="app.StatusName=='Draft'" />
                      <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Submitted'" />
                      <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="app.StatusName=='Rejected'" />
                      <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Approved'" />
                      <circle cx="4.75" cy="4" r="4" fill="#D83731" *ngIf="app.StatusName=='Returned'" />
                      <circle cx="4.75" cy="4" r="4" fill="#286CFF" *ngIf="app.StatusName=='Pending Payment'" />
                      <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Approved'" />
                    </svg>
                    <span>{{ lang.IsArabic?app.StatusNameAR:app.StatusName }}</span>
                  </div>
                </div>
              </td>

              <td class="desktop-only ">
                <span class="table-listing-full-width__inner-title">{{ app.ApplicationNumber }}</span>
                <div class="table-listing-full-width__new" *ngIf="i==0"><label>{{'New'|translate}}</label></div>
              </td>
              <td class="desktop-only text-center maxWidth">
                {{ lang.IsArabic?app.ServiceCatalogueNameAR:app.ServiceCatalogueName}}
              </td>
              <td class="desktop-only text-center">{{ app.SubmissionDate | date:'dd/MM/yyyy' }}</td>
              <td class="desktop-only text-center maxWidth">{{ app.FinalComment }}</td>
              <td class="desktop-only text-center table-listing-full-width__status">
                <svg xmlns="http://www.w3.org/2000/svg" width="9" height="8" viewBox="0 0 9 8" fill="none">
                  <circle cx="4.75" cy="4" r="4" fill="#F8C027" *ngIf="app.StatusName=='In Progress'" />
                  <circle cx="4.75" cy="4" r="4" fill="#C3C6CB" *ngIf="app.StatusName=='Draft'" />
                  <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Submitted'" />
                  <circle cx="4.75" cy="4" r="4" fill="#eb0505" *ngIf="app.StatusName=='Rejected'" />
                  <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Approved'" />
                  <circle cx="4.75" cy="4" r="4" fill="#D83731" *ngIf="app.StatusName=='Returned'" />
                  <circle cx="4.75" cy="4" r="4" fill="#286CFF" *ngIf="app.StatusName=='Pending Payment'" />
                  <circle cx="4.75" cy="4" r="4" fill="#4A9D5C" *ngIf="app.StatusName=='Approved'" />
                </svg>
                <span> {{ lang.IsArabic?app.StatusNameAR:app.StatusName }} </span>
              </td>
              <td class="text-center">
                @if(app.StatusName == 'Draft' || app.StatusName == 'Pending Payment' || app.StatusName == 'Returned'
                  ||app.ServiceCatalogue == ServiceCatalogue.NPOLicenseDeclaration || app.ServiceCatalogue == ServiceCatalogue.NPOLicenseDeclarationByDecree
                  ||app.ServiceCatalogue == ServiceCatalogue.IssueLicenseNonMuslimWorshipPlace || app.ServiceCatalogue == ServiceCatalogue.RequestAllocationWorshipRoom
                  ||app.ServiceCatalogue == ServiceCatalogue.RequestToIssueFundraisingPermit || app.ServiceCatalogue == ServiceCatalogue.AffiliateSubscribeOrJoinAssociationsOrRegionalInternationalEntities
                  ||app.ServiceCatalogue == ServiceCatalogue.ParticipateInActivitiesAndEvents || app.ServiceCatalogue == ServiceCatalogue.OrganizeActivitiesAndEvents
                  ||app.ServiceCatalogue == ServiceCatalogue.RequestToExtendFundraisingPermit || app.ServiceCatalogue == ServiceCatalogue.RequestForApprovalOfOpeningNPOBranch
                  || app.ServiceCatalogue == ServiceCatalogue.RequestForReceiveDonationsNOC || app.ServiceCatalogue == ServiceCatalogue.OpeningNewBankAccountRequest
                  || app.ServiceCatalogue ==  ServiceCatalogue.EstablishmentEdit
                  )
                {
                <button class="action-button dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path
                      d="M10.9375 10C10.9375 10.1854 10.8825 10.3667 10.7795 10.5208C10.6765 10.675 10.5301 10.7952 10.3588 10.8661C10.1875 10.9371 9.99896 10.9557 9.8171 10.9195C9.63525 10.8833 9.4682 10.794 9.33709 10.6629C9.20598 10.5318 9.11669 10.3648 9.08051 10.1829C9.04434 10.001 9.06291 9.81254 9.13386 9.64123C9.20482 9.46993 9.32498 9.32351 9.47915 9.2205C9.63332 9.11748 9.81458 9.0625 10 9.0625C10.2486 9.0625 10.4871 9.16127 10.6629 9.33709C10.8387 9.5129 10.9375 9.75136 10.9375 10ZM10 5.625C10.1854 5.625 10.3667 5.57002 10.5208 5.467C10.675 5.36399 10.7952 5.21757 10.8661 5.04627C10.9371 4.87496 10.9557 4.68646 10.9195 4.5046C10.8833 4.32275 10.794 4.1557 10.6629 4.02459C10.5318 3.89348 10.3648 3.80419 10.1829 3.76801C10.001 3.73184 9.81254 3.75041 9.64123 3.82136C9.46993 3.89232 9.32351 4.01248 9.2205 4.16665C9.11748 4.32082 9.0625 4.50208 9.0625 4.6875C9.0625 4.93614 9.16127 5.1746 9.33709 5.35041C9.5129 5.52623 9.75136 5.625 10 5.625ZM10 14.375C9.81458 14.375 9.63332 14.43 9.47915 14.533C9.32498 14.636 9.20482 14.7824 9.13386 14.9537C9.06291 15.125 9.04434 15.3135 9.08051 15.4954C9.11669 15.6773 9.20598 15.8443 9.33709 15.9754C9.4682 16.1065 9.63525 16.1958 9.8171 16.232C9.99896 16.2682 10.1875 16.2496 10.3588 16.1786C10.5301 16.1077 10.6765 15.9875 10.7795 15.8333C10.8825 15.6792 10.9375 15.4979 10.9375 15.3125C10.9375 15.0639 10.8387 14.8254 10.6629 14.6496C10.4871 14.4738 10.2486 14.375 10 14.375Z"
                      fill="#92722A" />
                  </svg>
                </button>
                <div class="action-popup dropdown-menu">
                  <ul>
                    @if(app.StatusName == 'Draft' || app.StatusName == 'Pending Payment' || app.StatusName == 'Returned'
                     || app.ServiceCatalogue == ServiceCatalogue.NPOLicenseDeclaration || app.ServiceCatalogue == ServiceCatalogue.NPOLicenseDeclarationByDecree
                     || app.ServiceCatalogue == ServiceCatalogue.IssueLicenseNonMuslimWorshipPlace || app.ServiceCatalogue == ServiceCatalogue.RequestAllocationWorshipRoom
                     || app.ServiceCatalogue == ServiceCatalogue.RequestToIssueFundraisingPermit|| app.ServiceCatalogue == ServiceCatalogue.AffiliateSubscribeOrJoinAssociationsOrRegionalInternationalEntities
                     || app.ServiceCatalogue == ServiceCatalogue.ParticipateInActivitiesAndEvents  || app.ServiceCatalogue == ServiceCatalogue.OrganizeActivitiesAndEvents
                     || app.ServiceCatalogue == ServiceCatalogue.RequestToExtendFundraisingPermit || app.ServiceCatalogue == ServiceCatalogue.RequestForApprovalOfOpeningNPOBranch
                     || app.ServiceCatalogue == ServiceCatalogue.RequestForReceiveDonationsNOC || app.ServiceCatalogue ==  ServiceCatalogue.OpeningNewBankAccountRequest
                     || app.ServiceCatalogue ==  ServiceCatalogue.EstablishmentEdit
                     ){
                    <li class="dropdown-item" (click)="viewApplication(app)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                        <path
                          d="M19.3211 6.74688C19.2937 6.68516 18.632 5.21719 17.1609 3.74609C15.2008 1.78594 12.725 0.75 9.99999 0.75C7.27499 0.75 4.79921 1.78594 2.83905 3.74609C1.36796 5.21719 0.703118 6.6875 0.678899 6.74688C0.643362 6.82681 0.625 6.91331 0.625 7.00078C0.625 7.08826 0.643362 7.17476 0.678899 7.25469C0.706243 7.31641 1.36796 8.78359 2.83905 10.2547C4.79921 12.2141 7.27499 13.25 9.99999 13.25C12.725 13.25 15.2008 12.2141 17.1609 10.2547C18.632 8.78359 19.2937 7.31641 19.3211 7.25469C19.3566 7.17476 19.375 7.08826 19.375 7.00078C19.375 6.91331 19.3566 6.82681 19.3211 6.74688ZM9.99999 12C7.5953 12 5.49452 11.1258 3.75546 9.40234C3.0419 8.69273 2.43483 7.88356 1.95312 7C2.4347 6.11636 3.04179 5.30717 3.75546 4.59766C5.49452 2.87422 7.5953 2 9.99999 2C12.4047 2 14.5055 2.87422 16.2445 4.59766C16.9595 5.307 17.5679 6.11619 18.0508 7C17.4875 8.05156 15.0336 12 9.99999 12ZM9.99999 3.25C9.25831 3.25 8.53329 3.46993 7.9166 3.88199C7.29992 4.29404 6.81927 4.87971 6.53544 5.56494C6.25162 6.25016 6.17735 7.00416 6.32205 7.73159C6.46674 8.45902 6.82389 9.1272 7.34834 9.65165C7.87279 10.1761 8.54097 10.5333 9.2684 10.6779C9.99583 10.8226 10.7498 10.7484 11.4351 10.4645C12.1203 10.1807 12.7059 9.70007 13.118 9.08339C13.5301 8.4667 13.75 7.74168 13.75 7C13.749 6.00576 13.3535 5.05253 12.6505 4.34949C11.9475 3.64645 10.9942 3.25103 9.99999 3.25ZM9.99999 9.5C9.50554 9.5 9.02219 9.35338 8.61107 9.07867C8.19994 8.80397 7.87951 8.41352 7.69029 7.95671C7.50107 7.49989 7.45157 6.99723 7.54803 6.51227C7.64449 6.02732 7.88259 5.58186 8.23222 5.23223C8.58186 4.8826 9.02731 4.6445 9.51227 4.54804C9.99722 4.45157 10.4999 4.50108 10.9567 4.6903C11.4135 4.87952 11.804 5.19995 12.0787 5.61107C12.3534 6.0222 12.5 6.50555 12.5 7C12.5 7.66304 12.2366 8.29893 11.7678 8.76777C11.2989 9.23661 10.663 9.5 9.99999 9.5Z"
                          fill="#92722A" />
                      </svg>
                      <span>
                        @if(app.Status === 100000003 || app.Status === 100000004 || app.Status === 100000000 || app.Status === 100000001){
                          {{'View Application' | translate}}
                        } @else {
                         {{'ViewAndEditApplication' | translate}}
                        }
                      </span>
                    </li>
                    }
                    <li class="dropdown-item d-none">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                          d="M17.5 3.75H2.5C2.16848 3.75 1.85054 3.8817 1.61612 4.11612C1.3817 4.35054 1.25 4.66848 1.25 5V15C1.25 15.3315 1.3817 15.6495 1.61612 15.8839C1.85054 16.1183 2.16848 16.25 2.5 16.25H17.5C17.8315 16.25 18.1495 16.1183 18.3839 15.8839C18.6183 15.6495 18.75 15.3315 18.75 15V5C18.75 4.66848 18.6183 4.35054 18.3839 4.11612C18.1495 3.8817 17.8315 3.75 17.5 3.75ZM17.5 5V6.875H2.5V5H17.5ZM17.5 15H2.5V8.125H17.5V15ZM16.25 13.125C16.25 13.2908 16.1842 13.4497 16.0669 13.5669C15.9497 13.6842 15.7908 13.75 15.625 13.75H13.125C12.9592 13.75 12.8003 13.6842 12.6831 13.5669C12.5658 13.4497 12.5 13.2908 12.5 13.125C12.5 12.9592 12.5658 12.8003 12.6831 12.6831C12.8003 12.5658 12.9592 12.5 13.125 12.5H15.625C15.7908 12.5 15.9497 12.5658 16.0669 12.6831C16.1842 12.8003 16.25 12.9592 16.25 13.125ZM11.25 13.125C11.25 13.2908 11.1842 13.4497 11.0669 13.5669C10.9497 13.6842 10.7908 13.75 10.625 13.75H9.375C9.20924 13.75 9.05027 13.6842 8.93306 13.5669C8.81585 13.4497 8.75 13.2908 8.75 13.125C8.75 12.9592 8.81585 12.8003 8.93306 12.6831C9.05027 12.5658 9.20924 12.5 9.375 12.5H10.625C10.7908 12.5 10.9497 12.5658 11.0669 12.6831C11.1842 12.8003 11.25 12.9592 11.25 13.125Z"
                          fill="#92722A" />
                      </svg>
                      <span>Make Payment</span>
                    </li>
                    <li class="dropdown-item  d-none">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                          d="M16.6922 6.43281L12.3172 2.05781C12.2591 1.99979 12.1902 1.95378 12.1143 1.92241C12.0384 1.89105 11.9571 1.87494 11.875 1.875H4.375C4.04348 1.875 3.72554 2.0067 3.49112 2.24112C3.2567 2.47554 3.125 2.79348 3.125 3.125V16.875C3.125 17.2065 3.2567 17.5245 3.49112 17.7589C3.72554 17.9933 4.04348 18.125 4.375 18.125H15.625C15.9565 18.125 16.2745 17.9933 16.5089 17.7589C16.7433 17.5245 16.875 17.2065 16.875 16.875V6.875C16.8751 6.7929 16.859 6.71159 16.8276 6.63572C16.7962 6.55985 16.7502 6.4909 16.6922 6.43281ZM12.5 4.00859L14.7414 6.25H12.5V4.00859ZM15.625 16.875H4.375V3.125H11.25V6.875C11.25 7.04076 11.3158 7.19973 11.4331 7.31694C11.5503 7.43415 11.7092 7.5 11.875 7.5H15.625V16.875Z"
                          fill="#92722A" />
                      </svg>
                      <span>Finish Draft</span>
                    </li>
                    <li class="dropdown-item  d-none">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                          d="M18.5 14.6946L11.668 2.82974C11.4972 2.53906 11.2535 2.29803 10.9609 2.13057C10.6684 1.9631 10.3371 1.875 9.99998 1.875C9.66287 1.875 9.33161 1.9631 9.03904 2.13057C8.74647 2.29803 8.50274 2.53906 8.33202 2.82974L1.49998 14.6946C1.33571 14.9757 1.24915 15.2955 1.24915 15.6211C1.24915 15.9468 1.33571 16.2665 1.49998 16.5477C1.66852 16.8401 1.91184 17.0825 2.20495 17.2498C2.49807 17.4172 2.83044 17.5036 3.16795 17.5001H16.832C17.1693 17.5033 17.5013 17.4168 17.7941 17.2494C18.0869 17.0821 18.33 16.8399 18.4984 16.5477C18.6629 16.2667 18.7498 15.947 18.75 15.6214C18.7503 15.2957 18.664 14.9759 18.5 14.6946ZM17.4164 15.9219C17.3568 16.0235 17.2713 16.1075 17.1686 16.1651C17.0659 16.2228 16.9498 16.2521 16.832 16.2501H3.16795C3.0502 16.2521 2.93402 16.2228 2.83133 16.1651C2.72865 16.1075 2.64314 16.0235 2.58358 15.9219C2.52963 15.8306 2.50117 15.7264 2.50117 15.6204C2.50117 15.5143 2.52963 15.4101 2.58358 15.3188L9.41561 3.45396C9.47637 3.35284 9.56227 3.26917 9.66494 3.21108C9.76762 3.153 9.88358 3.12247 10.0015 3.12247C10.1195 3.12247 10.2355 3.153 10.3381 3.21108C10.4408 3.26917 10.5267 3.35284 10.5875 3.45396L17.4195 15.3188C17.473 15.4104 17.5009 15.5147 17.5004 15.6208C17.4998 15.7269 17.4708 15.8309 17.4164 15.9219ZM9.37498 11.2501V8.12505C9.37498 7.95929 9.44083 7.80032 9.55804 7.68311C9.67525 7.5659 9.83422 7.50005 9.99998 7.50005C10.1657 7.50005 10.3247 7.5659 10.4419 7.68311C10.5591 7.80032 10.625 7.95929 10.625 8.12505V11.2501C10.625 11.4158 10.5591 11.5748 10.4419 11.692C10.3247 11.8092 10.1657 11.8751 9.99998 11.8751C9.83422 11.8751 9.67525 11.8092 9.55804 11.692C9.44083 11.5748 9.37498 11.4158 9.37498 11.2501ZM10.9375 14.0626C10.9375 14.248 10.8825 14.4292 10.7795 14.5834C10.6765 14.7376 10.5301 14.8577 10.3587 14.9287C10.1874 14.9996 9.99894 15.0182 9.81709 14.982C9.63523 14.9459 9.46818 14.8566 9.33707 14.7255C9.20596 14.5944 9.11667 14.4273 9.0805 14.2454C9.04432 14.0636 9.06289 13.8751 9.13385 13.7038C9.2048 13.5325 9.32497 13.3861 9.47914 13.283C9.63331 13.18 9.81456 13.1251 9.99998 13.1251C10.2486 13.1251 10.4871 13.2238 10.6629 13.3996C10.8387 13.5755 10.9375 13.8139 10.9375 14.0626Z"
                          fill="#92722A" />
                      </svg>
                      <span>Make Complaint</span>
                    </li>
                    @if(app.StatusName == 'Draft' || app.StatusName == 'Pending Payment'){
                    <li class="delete" (click)="deleteApplication(app.Id, app.EntityName)">
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path
                          d="M16.875 3.75H13.75V3.125C13.75 2.62772 13.5525 2.15081 13.2008 1.79917C12.8492 1.44754 12.3723 1.25 11.875 1.25H8.125C7.62772 1.25 7.15081 1.44754 6.79917 1.79917C6.44754 2.15081 6.25 2.62772 6.25 3.125V3.75H3.125C2.95924 3.75 2.80027 3.81585 2.68306 3.93306C2.56585 4.05027 2.5 4.20924 2.5 4.375C2.5 4.54076 2.56585 4.69973 2.68306 4.81694C2.80027 4.93415 2.95924 5 3.125 5H3.75V16.25C3.75 16.5815 3.8817 16.8995 4.11612 17.1339C4.35054 17.3683 4.66848 17.5 5 17.5H15C15.3315 17.5 15.6495 17.3683 15.8839 17.1339C16.1183 16.8995 16.25 16.5815 16.25 16.25V5H16.875C17.0408 5 17.1997 4.93415 17.3169 4.81694C17.4342 4.69973 17.5 4.54076 17.5 4.375C17.5 4.20924 17.4342 4.05027 17.3169 3.93306C17.1997 3.81585 17.0408 3.75 16.875 3.75ZM7.5 3.125C7.5 2.95924 7.56585 2.80027 7.68306 2.68306C7.80027 2.56585 7.95924 2.5 8.125 2.5H11.875C12.0408 2.5 12.1997 2.56585 12.3169 2.68306C12.4342 2.80027 12.5 2.95924 12.5 3.125V3.75H7.5V3.125ZM15 16.25H5V5H15V16.25ZM8.75 8.125V13.125C8.75 13.2908 8.68415 13.4497 8.56694 13.5669C8.44973 13.6842 8.29076 13.75 8.125 13.75C7.95924 13.75 7.80027 13.6842 7.68306 13.5669C7.56585 13.4497 7.5 13.2908 7.5 13.125V8.125C7.5 7.95924 7.56585 7.80027 7.68306 7.68306C7.80027 7.56585 7.95924 7.5 8.125 7.5C8.29076 7.5 8.44973 7.56585 8.56694 7.68306C8.68415 7.80027 8.75 7.95924 8.75 8.125ZM12.5 8.125V13.125C12.5 13.2908 12.4342 13.4497 12.3169 13.5669C12.1997 13.6842 12.0408 13.75 11.875 13.75C11.7092 13.75 11.5503 13.6842 11.4331 13.5669C11.3158 13.4497 11.25 13.2908 11.25 13.125V8.125C11.25 7.95924 11.3158 7.80027 11.4331 7.68306C11.5503 7.56585 11.7092 7.5 11.875 7.5C12.0408 7.5 12.1997 7.56585 12.3169 7.68306C12.4342 7.80027 12.5 7.95924 12.5 8.125Z"
                          fill="#D83731" />
                      </svg>
                      <span>{{'Delete Application' | translate}}</span>
                    </li>
                    }
                  </ul>
                </div>
                }
              </td>
            </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
    <!-- end Application table -->



  </div>
  <div class="application-overview__footer">
    <div class="application-overview__footer-total-count d-flex justify-content-between align-items-center flex-sm-column">
      <span class="w-sm-100 text-start">{{'Total count' | translate}}: {{filteredCount}}</span>
      <div class="mobile-only d-flex gap-2 align-items-center w-sm-100 justify-content-sm-between">
        <span>{{'Show per page' | translate}}:</span>
        <mat-form-field>
          <mat-select placeholder="5" [(ngModel)]="pageSize" (change)="onSelectChange()">
            <mat-option [value]="5">5 {{'itemsPerPage' | translate}}</mat-option>
            <mat-option [value]="10">10 {{'itemsPerPage' | translate}}</mat-option>
            <mat-option [value]="50">50 {{'itemsPerPage' | translate}}</mat-option>
          </mat-select>
          <i class="icon-toggle fa-solid fa-chevron-down"></i>
        </mat-form-field>

      </div>
    </div>
    <div class="aegov-pagination">
      <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize"
        [collectionSize]="filteredCount" (pageChange)="onPageChange()" aria-label="Custom pagination">
        <ng-template ngbPaginationPrevious>
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
            <path
              d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
          </svg>
          <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
        </ng-template>
        <ng-template ngbPaginationNext>
          <span class="d-none d-lg-block">{{'Next' | translate}}</span>
          <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
            <path
              d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
          </svg>
        </ng-template>
        <!-- <ng-template ngbPaginationNumber let-p>{{ getPageSymbol(p) }}</ng-template> -->
      </ngb-pagination>

      <!-- <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="page" [pageSize]="pageSize" [collectionSize]="filteredCount" (pageChange)="onPageChange()"></ngb-pagination> -->
    </div>


  </div>
</div>
<!--/end new application.component -->



<!-- </div> -->
