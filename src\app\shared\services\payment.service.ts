import { Injectable } from '@angular/core';
import { NotifyService } from './notify.service';
import { LanguageService } from './language.service';
import { DataService } from './data.service';

@Injectable({
  providedIn: 'root',
})
export class PaymentService {
  constructor(private notify: NotifyService,
    private lang: LanguageService,
    private dataService:DataService,
  ) {}

  pay(requestId: string) {
    this.dataService
      .get(
        `Payment/CreatePayment?requestId=${requestId}&lang=${
          this.lang.IsArabic ? 'AR' : 'EN'
        }`
      )
      .subscribe(
        (response) => {
          if (response.Status == 2000) {
            window.location.href = response.PaymentUrl;
          } else {
            if (this.lang.IsArabic) {
              this.notify.showError('notify.error', response.MessageAR);
            } else {
              this.notify.showError('notify.error', response.MessageEN);
            }
          }
        },
        (e) => {
          this.notify.showError('notify.error', e.error.Message);
        }
      );
  }
}
