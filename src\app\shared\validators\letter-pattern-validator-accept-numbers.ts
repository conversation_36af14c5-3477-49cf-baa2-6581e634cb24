import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function letterPatternValidatorAcceptNumbers(language: 'en' | 'ar'): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return { required: true };
    }

    if (value.length < 2) {
      return { minLengthIs2: true };
    }

    if (value.length > 500) {
      return { maxLength: true };
    }

    const englishPattern = /^[A-Za-z\s]+$/;
    const arabicPattern = /^[\u0600-\u06FF\s]+$/;
    // const containsNumbers = /\d/;

    if (language === 'en') {
      // if (containsNumbers.test(value)) {
      //   return { containsNumbers: true };
      // }
      if (!englishPattern.test(value)) {
        return { invalidPatternEn: true };
      }
    }

    if (language === 'ar') {
      // if (containsNumbers.test(value)) {
      //   return { containsNumbers: true };
      // }
      if (!arabicPattern.test(value)) {
        return { invalidPatternAr: true };
      }
    }

    return null;
  };
}
