import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { GridActionTypesEnum } from '../../enums/grid-action-types-enum';

@Component({
  selector: 'app-type-of-donations',
  templateUrl: './type-of-donations.component.html',
  styleUrls: ['./type-of-donations.component.scss'],
})
export class TypeOfDonationsComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges
{
  requestId: any;
  countries: Lookup[];
  currencies: Lookup[];
  localCurrency: Lookup | undefined;
  minDate: Date;
  requestTypes: Lookup[];
  entityLegalForms: Lookup[];
  cashDonationTypeLookups: Lookup[] = [];
  inkindDonationTypeLookups: Lookup[] = [];
  editRows: string[] = [];

  cashDonationTypesForm: FormGroup;
  inKindDonationTypesForm: FormGroup;

  cashDonationTypesEditIndex: number;
  inKindDonationTypesEditIndex: number;

  get fb(): any {
    return this.form.controls;
  }

  get CashDonationTypes(): FormArray {
    return this.form.get('CashDonationTypes') as FormArray;
  }

  get InKindDonationTypes(): FormArray {
    return this.form.get('InKindDonationTypes') as FormArray;
  }

  get cControls(): any {
    return this.cashDonationTypesForm.controls;
  }

  get iControls(): any {
    return this.inKindDonationTypesForm.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() selectedLegalFormType: number;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() typeOfDonationEmitter: EventEmitter<boolean> =
    new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.typeOfDonations.';

    this.intiCashDonationTypesForm();
    this.intiInkindDonationTypesForm();

    const today = new Date();
    today.setDate(today.getDate() + 30);
    this.minDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.countries = data?.Countries;
        this.currencies = data?.Currencies;
        this.requestTypes = data?.RequestType;
        this.entityLegalForms = data?.EntityLegalForm;
        this.cashDonationTypeLookups = data?.CashDonationType;
        this.inkindDonationTypeLookups = data?.InkindDonationType;

        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.TypeOfDonations);
          } else if (_ && _.isFullDetails == false) {
            this.CashDonationTypes.clear();
            this.InKindDonationTypes.clear();
            this.mapData(_?.TypeOfDonations);
          }
        });
      }
    });

    this.cashDonationTypePagination();
    this.CashDonationTypes.valueChanges.subscribe((_) =>
      this.cashDonationTypePagination()
    );

    this.inkindDonationTypePagination();
    this.InKindDonationTypes.valueChanges.subscribe((_) =>
      this.inkindDonationTypePagination()
    );

    this.form.get('TotalCashDonations')?.disable();
    this.form.get('TotalInKindDonations')?.disable();

    this.form.get('TotalCashDonations')?.valueChanges.subscribe((value) => {
      if (value && value > 0) {
        this.calculateGrandTotal();
      }
    });

    this.form.get('TotalInKindDonations')?.valueChanges.subscribe((value) => {
      if (value && value > 0) {
        this.calculateGrandTotal();
      }
    });
  }

  onCashDonationSelectChange(event: any) {
    this.typeOfDonationEmitter.emit(event.checked);
    if (event.checked) {
      this.FormService.addValidators(
        this.form,
        ['TotalCashDonations'],
        [Validators.required]
      );
      if (
        this.form.get('IsInKindDonations')?.value === false ||
        this.form.get('IsInKindDonations')?.value === '' ||
        this.form.get('IsInKindDonations')?.value === undefined ||
        this.form.get('IsInKindDonations')?.value === null
      ) {
        this.FormService.clearValidators(this.form, ['IsInKindDonations']);
      }
    } else {
      this.FormService.clearFields(
        this.form,
        ['TotalCashDonations', 'IsCashDonations'],
        true
      );

      this.CashDonationTypes.clear();
    }

    if (event.checked === false && !this.form.get('IsInKindDonations')?.value) {
      this.FormService.addValidators(
        this.form,
        ['IsCashDonations', 'IsInKindDonations'],
        [Validators.required]
      );
    }
  }

  onInkindDonationSelectChange(event: any) {
    if (event.checked) {
      this.FormService.addValidators(
        this.form,
        ['TotalInKindDonations'],
        [Validators.required]
      );
      if (
        this.form.get('IsCashDonations')?.value === false ||
        this.form.get('IsCashDonations')?.value === '' ||
        this.form.get('IsCashDonations')?.value === undefined ||
        this.form.get('IsCashDonations')?.value === null
      ) {
        this.FormService.clearValidators(this.form, ['IsCashDonations']);
      }
    } else {
      this.FormService.clearFields(
        this.form,
        ['TotalInKindDonations', 'IsInKindDonations'],
        true
      );

      this.InKindDonationTypes.clear();
    }

    if (event.checked === false && !this.form.get('IsCashDonations')?.value) {
      this.FormService.addValidators(
        this.form,
        ['IsCashDonations', 'IsInKindDonations'],
        [Validators.required]
      );
    }
  }

  calculateGrandTotal() {
    const totalCashDonations = this.form.get('TotalCashDonations')?.value ?? 0;
    const totalInkindDonations =
      this.form.get('TotalInKindDonations')?.value ?? 0;

    const grandTotal =
      Number(totalCashDonations) + Number(totalInkindDonations);
    this.form.get('GrandTotalAmount')?.disable();
    this.form.get('GrandTotalAmount')?.setValue(grandTotal);
  }

  intiCashDonationTypesForm = (): void => {
    this.cashDonationTypesForm = this.FormBuilder.group({
      recordId: new FormControl(''),
      cashDonationType: new FormControl('', [Validators.required]),
      currencyType: new FormControl('', [Validators.required]),
      approximateValueAed: new FormControl('', [Validators.required, Validators.min(1)]),
    });
  };

  intiInkindDonationTypesForm = (): void => {
    this.inKindDonationTypesForm = this.FormBuilder.group({
      recordId: new FormControl(''),
      inkindDonationType: new FormControl('', [Validators.required]),
      countOrQuantity: new FormControl('', [Validators.required]),
      approximateValueAed: new FormControl('', [Validators.required, Validators.min(1)]),
    });
  };

  isValidForm = (): boolean => {
    const isCashDonationsSelected = this.form.get('IsCashDonations')?.value;
    const isInkindDonationsSelected = this.form.get('IsInKindDonations')?.value;

    let cashDonationTypesLengh = this.CashDonationTypes.length > 0;
    let inkindDonationTypesLengh = this.InKindDonationTypes.length > 0;

    if (isCashDonationsSelected && isInkindDonationsSelected) {
      return cashDonationTypesLengh && inkindDonationTypesLengh;
    } else if (isCashDonationsSelected && !isInkindDonationsSelected) {
      return cashDonationTypesLengh;
    } else if (!isCashDonationsSelected && isInkindDonationsSelected) {
      return inkindDonationTypesLengh;
    } else {
      return false;
    }
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'TypeOfDonations',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'TypeOfDonations',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      IsCashDonations: this.fb?.IsCashDonations?.value ?? false,
      IsInKindDonations: this.fb?.IsInKindDonations?.value ?? false,
      TotalCashDonationsAmountinAED: this.fb?.TotalCashDonations?.value ?? '',
      TotalInkindDonationsAmountinAED:
        this.fb?.TotalInKindDonations?.value ?? '',
      GrandTotalDonationAmountinAED: this.fb?.GrandTotalAmount?.value ?? '',
      cashDonations: (this.CashDonationTypes.controls as Array<any>).map(
        (control) => {
          const cashDonationType$ = control?.value;
          // local currency manual override in currency type
          if (cashDonationType$.cashDonationType.ID === '100000000') {
            cashDonationType$.currencyType = 'AED'
          }
          return {
            recordId:
              (cashDonationType$.recordId ?? this.EMPTY_GUID).length == 3
                ? this.EMPTY_GUID
                : cashDonationType$.recordId ?? this.EMPTY_GUID,
            CashDonationType: cashDonationType$?.cashDonationType?.ID ?? '',
            CurrencyType: cashDonationType$?.currencyType ?? '',
            ApproximateValueinAED: cashDonationType$?.approximateValueAed ?? '',
          };
        }
      ),
      inkindDonations: (this.InKindDonationTypes.controls as Array<any>).map(
        (inKindDonationType$) => {
          return {
            recordId:
              (inKindDonationType$.value.recordId ?? this.EMPTY_GUID).length == 3
                ? this.EMPTY_GUID
                : inKindDonationType$.value.recordId ?? this.EMPTY_GUID,
            InkindDonationType:
              inKindDonationType$.value?.inkindDonationType?.ID ?? '',
            Count: inKindDonationType$.value?.countOrQuantity ?? '',
            ApproximateValueinAED:
              inKindDonationType$.value?.approximateValueAed ?? '',
          };
        }
      ),
    };
  };

  mapData = (data: any): void => {
    if (!data) return;
    this.fb?.IsCashDonations?.setValue(data?.IsCashDonations);
    this.fb?.IsInKindDonations?.setValue(data?.IsInKindDonations);

    this.fb?.TotalCashDonations?.setValue(data?.TotalCashDonationsAmountinAED);
    this.fb?.TotalInKindDonations?.setValue(
      data?.TotalInkindDonationsAmountinAED
    );
    this.fb?.GrandTotalAmount?.setValue(data?.GrandTotalDonationAmountinAED);
    data.cashDonations.forEach((donation: any) => {
      this.CashDonationTypes.push(
        this.FormBuilder.group({
          recordId: [donation?.recordId],
          cashDonationType: [
            this.cashDonationTypeLookups.find(
              (_) => _.ID == donation?.CashDonationType
            ),
          ],
          currencyType: [donation?.CurrencyType],
          approximateValueAed: [donation?.ApproximateValueinAED],
        })
      );
    });
    data.inkindDonations.forEach((donation: any) => {
      this.InKindDonationTypes.push(
        this.FormBuilder.group({
          recordId: [donation?.recordId],
          inkindDonationType: [
            this.inkindDonationTypeLookups.find(
              (_) => _.ID == donation?.InkindDonationType
            ),
          ],
          countOrQuantity: [donation?.Count],
          approximateValueAed: [donation?.ApproximateValueinAED],
        })
      );
    });
    if (data?.IsCashDonations) {
      this.typeOfDonationEmitter.emit(true);
    } else {
      this.typeOfDonationEmitter.emit(false);
    }
  };

  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(
      this.isReturnForUpdate ?? false,
      this.feedbackList,
      'typeOfDonations',
      this.fb
    );
  };

  cashDonationTypePage = 1;
  cashDonationTypePageSize = 5;
  cashDonationTypeDataTable: FormArray = this.FormBuilder.array([]);

  get cashDonationTypeReprsentedDataTable(): FormArray {
    return this.cashDonationTypeDataTable;
  }
  get cashDonationTypeTableIndex(): number {
    return (this.cashDonationTypePage - 1) * this.cashDonationTypePageSize;
  }

  cashDonationTypePagination = (): void => {
    this.cashDonationTypeDataTable = this.FormBuilder.array([]);
    let data$ = this.CashDonationTypes.controls.slice(
      (this.cashDonationTypePage - 1) * this.cashDonationTypePageSize,
      (this.cashDonationTypePage - 1) * this.cashDonationTypePageSize +
        this.cashDonationTypePageSize
    );
    data$.forEach((_) => this.cashDonationTypeDataTable.push(_));
  };

  inkindhDonationTypePage = 1;
  inkindDonationTypePageSize = 5;
  inkindDonationTypeDataTable: FormArray = this.FormBuilder.array([]);

  get inkindDonationTypeReprsentedDataTable(): FormArray {
    return this.inkindDonationTypeDataTable;
  }
  get inkindDonationTypeTableIndex(): number {
    return (this.inkindhDonationTypePage - 1) * this.inkindDonationTypePageSize;
  }

  inkindDonationTypePagination = (): void => {
    this.inkindDonationTypeDataTable = this.FormBuilder.array([]);
    let data$ = this.InKindDonationTypes.controls.slice(
      (this.inkindhDonationTypePage - 1) * this.inkindDonationTypePageSize,
      (this.inkindhDonationTypePage - 1) * this.inkindDonationTypePageSize +
        this.inkindDonationTypePageSize
    );
    data$.forEach((_) => this.inkindDonationTypeDataTable.push(_));
  };

  checkIsEditId = (id: undefined): boolean =>
    id && id != null && id != undefined && id != '' && id != ' '
      ? this.editRows.findIndex((_) => _ == id) > -1
      : false;

  onCashDonationTypeSelect(event: any) {
    if (event.value.ID === '100000000') {
      this.cashDonationTypesForm.get('currencyType')?.disable();
      this.cashDonationTypesForm.get('currencyType')?.setValue('AED');
    } else {
      this.cashDonationTypesForm.get('currencyType')?.enable();
      this.cashDonationTypesForm.get('currencyType')?.reset();
    }
  }

  getCashDonationType(cashDonationTypeId: string): Lookup | undefined {
    const typeFound = this.cashDonationTypeLookups.find(
      (type) => type.ID === cashDonationTypeId
    );
    return typeFound!;
  }

  getInkindDonationType(inkindDonationTypeId: string): Lookup | undefined {
    const typeFound = this.inkindDonationTypeLookups.find(
      (type) => type.ID === inkindDonationTypeId
    );
    return typeFound!;
  }

  manageCashDonationTypesForm = (
    item: any,
    type: GridActionTypesEnum
  ): void => {
    if (type != GridActionTypesEnum.EDIT) {
      if (this.validateAddCashDonationType(item)) {
        this.NotifyService.showError(
          'notify.error',
          'notify.cashDonationTypeExistsError'
        );
        return;
      }
  
      const newCashDonationType = this.FormBuilder.group({
        recordId: [this.generateDistinctId()],
        cashDonationType: [item.cashDonationType],
        currencyType: [
          item.cashDonationType?.ID === '100000000' ? 'AED' : item.currencyType,
        ],
        approximateValueAed: [item.approximateValueAed],
      });

      this.CashDonationTypes.push(newCashDonationType);
    } else {
      if (this.validateEditCashDonationType(item)) {
        this.NotifyService.showError(
          'notify.error',
          'notify.cashDonationTypeExistsError'
        );
        return;
      }

      this.CashDonationTypes.at(this.cashDonationTypesEditIndex).setValue({
        recordId: item?.recordId,
        cashDonationType: item.cashDonationType,
        currencyType: item.cashDonationType?.ID === "100000000" ? 'AED' : item.currencyType,
        approximateValueAed: item.approximateValueAed,
      });
    }

    this.calculateTotalCashDonations();

    this.cashDonationTypesForm.reset();
    this.modalService.dismissAll();
  };

  validateAddCashDonationType = (item: any): Boolean => {
    if (!this.CashDonationTypes || !this.CashDonationTypes.controls)
      return false;

    const matchingName = this.CashDonationTypes.controls.find((control) => {
      const cashDonationType = control.get('cashDonationType')?.value;
      const currencyType = control.get('currencyType')?.value;
      const localCurrencyType = control.get('cashDonationType')?.value?.ID === "100000000";
      
      if(localCurrencyType){
        return cashDonationType === item.cashDonationType;
      }

      return cashDonationType === item.cashDonationType && currencyType === item.currencyType;
    });
    return !!matchingName;
  };

  validateEditCashDonationType = (item: any): Boolean => {
    if (!this.CashDonationTypes || !this.CashDonationTypes.controls)
      return false;

    const matchingName = this.CashDonationTypes.controls.find((control) => {
      const cashDonationType = control.get('cashDonationType')?.value;

      return (
        cashDonationType === item.cashDonationType &&
        control.get('currencyType')?.value === item.approximateValueAed &&
        control.get('approximateValueAed')?.value === item.approximateValueAed
      );
    });
    return !!matchingName;
  };

  handleEditCashDonationType = (data: any, idx: number): void => {
    this.cashDonationTypesEditIndex = idx;
    this.cashDonationTypesForm.patchValue({
      recordId: data?.value?.recordId,
      cashDonationType: data?.value?.cashDonationType,
      currencyType: data?.value?.currencyType,
      approximateValueAed: data?.value?.approximateValueAed,
    });
  };

  removeCashDonationType = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let cashDonationType = this.CashDonationTypes?.value[idx];
      if (cashDonationType) {
        this.removeFromCRM(cashDonationType?.id, 'mocd_cashdonationresource');
      }

      this.CashDonationTypes.removeAt(idx);
      this.calculateTotalCashDonations();
    }
  };

  manageInkindDonationTypesForm = (
    item: any,
    type: GridActionTypesEnum
  ): void => {
    if (type != GridActionTypesEnum.EDIT) {
      if (this.validateInkindDonationType(item)) {
        this.NotifyService.showError(
          'notify.error',
          'notify.inkindDonationTypeExistsError'
        );
        return;
      }

      const newInkindDonationType = this.FormBuilder.group({
        recordId: [this.generateDistinctId()],
        inkindDonationType: [item.inkindDonationType],
        countOrQuantity: [item.countOrQuantity],
        approximateValueAed: [item.approximateValueAed],
      });

      this.InKindDonationTypes.push(newInkindDonationType);
    } else {
      this.InKindDonationTypes.at(this.inKindDonationTypesEditIndex).setValue({
        recordId: item?.recordId,
        inkindDonationType: item.inkindDonationType,
        countOrQuantity: item.countOrQuantity,
        approximateValueAed: item.approximateValueAed,
      });
    }

    this.calculateTotalInkindDonations();

    this.inKindDonationTypesForm.reset();
    this.modalService.dismissAll();
  };

  validateInkindDonationType = (item: any): Boolean => {
    if (!this.InKindDonationTypes || !this.InKindDonationTypes.controls)
      return false;

    const matchingName = this.InKindDonationTypes.controls.find((control) => {
      const inkindDonationType = control.get('inkindDonationType')?.value;

      return inkindDonationType === item.inkindDonationType;
    });
    return !!matchingName;
  };

  handleEditInkindDonationType = (data: any, idx: number): void => {
    this.inKindDonationTypesEditIndex = idx;
    this.inKindDonationTypesForm.patchValue({
      recordId: data?.value?.recordId,
      inkindDonationType: data?.value?.inkindDonationType,
      countOrQuantity: data?.value?.countOrQuantity,
      approximateValueAed: data?.value?.approximateValueAed,
    });
  };

  removeInkindonationType = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let inkindDonationType = this.InKindDonationTypes?.value[idx];
      if (inkindDonationType) {
        this.removeFromCRM(
          inkindDonationType?.id,
          'mocd_inkinddonationresource'
        );
      }

      this.InKindDonationTypes.removeAt(idx);
      this.calculateTotalInkindDonations();
    }
  };

  calculateTotalCashDonations() {
    let totalCashDonations = 0;

    this.CashDonationTypes.controls.forEach((control) => {
      totalCashDonations += control.get('approximateValueAed')?.value;
    });

    this.form.get('TotalCashDonations')?.disable();
    this.form.get('TotalCashDonations')?.setValue(totalCashDonations);
  }

  calculateTotalInkindDonations() {
    let totalInkindDonations = 0;

    this.InKindDonationTypes.controls.forEach((control) => {
      totalInkindDonations += control.get('approximateValueAed')?.value;
    });

    this.form.get('TotalInKindDonations')?.disable();
    this.form.get('TotalInKindDonations')?.setValue(totalInkindDonations);
  }

  checkFormValidations(isOpened: boolean) {
    Object.keys(this.cashDonationTypesForm.controls).forEach((control) => {
      if (
        control === 'currencyType' &&
        this.cashDonationTypesForm.get('cashDonationType')?.value?.ID ===
          '100000000'
      ) {
        this.cashDonationTypesForm.get(control)?.setValue('AED');
        this.cashDonationTypesForm.get(control)?.disable();
      }
    });
  }
}
