import { Component, OnInit } from '@angular/core';
import { TranslateService, LangChangeEvent } from '@ngx-translate/core';
import { environment } from '../../../../environments/environment';
import { AuthService } from '../../../shared/services/auth.service';
import { LanguageService } from '../../../shared/services/language.service';
import { DataService } from '../../../shared/services/data.service';

@Component({
  selector: 'app-my-documents',
  templateUrl: './my-documents.component.html',
  styleUrl: './my-documents.component.scss'
})
export class MyDocumentsComponent implements OnInit {
  cols: number = 3;
  protected lang = 'en';
  documents: any;

  constructor(
    protected translate: TranslateService,
    protected auth: AuthService,
    private dataService: DataService,
    protected languageService: LanguageService
  ) {
    this.lang = languageService.IsArabic ? 'ar' : 'en';
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this.lang = event.lang;
    });
  }
  ngOnInit(): void {
    var userInfo = this.auth.getUserInfo()

    this.dataService
      .get(`Document/MyDocuments?userId=${userInfo?.crmUserId}`)
      .subscribe((res) => {
        this.documents = res.data;
      });
  }

  handleChoice(e: any) {
    switch (e.target.id) {
      case 'btnCards':
        this.cols = 3;
        break;
      case 'btnList':
        this.cols = 12;
        break;
      default:
        this.cols = 3;
        break;
    }
  }
}
