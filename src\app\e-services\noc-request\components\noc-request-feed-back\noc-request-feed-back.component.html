<div class="container">
  <div class="row table-responsive d-block_mobile">
    <table class="my-table">
      <thead>
        <tr>
          <th class="align-middle" scope="col">{{messageTranslationPrefix+'index' | translate}}</th>
          <th class="align-middle" scope="col">{{messageTranslationPrefix+'fieldName' |
            translate}}</th>
          <th class="align-middle" scope="col">{{messageTranslationPrefix+'sectionName' |
            translate}}</th>
          <th class="align-middle" scope="col">{{messageTranslationPrefix+'reviewerComments' |
            translate}}</th>
          <th class="align-middle" scope="col">{{messageTranslationPrefix+'action' | translate}}</th>
        </tr>
      </thead>
      <tbody>
        @for (feedback of feedbackList; track $index) {
        <tr>
          <td class="align-middle">{{ $index + 1 }}</td>
          <td class="align-middle">
            @if(LanguageService.IsArabic){
            <span lang="ar">{{ feedback?.FieldorGridNamearabic }}</span>
            } @else {
            <span lang="en">{{ feedback?.FieldorGridName }}</span>
            }
          </td>
          <td class="align-middle">
            @if(LanguageService.IsArabic){
            <span lang="ar">{{ feedback?.SectionAr }}</span>
            } @else {
            <span lang="en">{{ feedback?.Section }}</span>
            }
          </td>
          <td class="align-middle">
            <span>{{feedback?.Comments}}</span>
          </td>
          <td class="align-middle">
            <span class="btn basic-filled-button" (click)="openSection(feedback?.SectionTechnicalname??'')">
              {{messageTranslationPrefix+'goToSection' | translate}}</span>
          </td>
        </tr>
        }
      </tbody>
    </table>
  </div>

  <div class="figma-card-container">
    @for (feedback of feedbackList; track $index){
    <div class="figma-card">
      <button type="button" mat-icon-button [matMenuTriggerFor]="menu" class="figma-actions-menu-trigger"
        aria-label="Actions menu">
        <mat-icon>more_vert</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <span class="goToSection-btn" (click)="openSection(feedback?.SectionTechnicalname??'')">
          {{messageTranslationPrefix+'goToSection' | translate}}
        </span>
      </mat-menu>
      <div class="figma-card-content">
        <div class="figma-card-field">
          <span class="static-value">#</span>
          <span class="dynamic-value">{{ $index + 1 }}</span>
        </div>
        <div class="figma-card-field">
          <div class="static-value">{{ messageTranslationPrefix +'fieldName' | translate }}:</div>
          @if(LanguageService.IsArabic) {
          <div class="dynamic-value" lang="en">{{feedback.FieldorGridNamearabic }}</div>
          } @else {
          <div class="dynamic-value" lang="en">{{feedback.FieldorGridName }}</div>
          }
        </div>
        <div class="figma-card-field">
          <div class="static-value">{{ messageTranslationPrefix +'sectionName' | translate }}:</div>
          @if(LanguageService.IsArabic) {
          <div class="dynamic-value" lang="ar">{{feedback.SectionAr }}</div>
          } @else {
          <div class="dynamic-value" lang="en">{{feedback.Section }}</div>
          }
        </div>
        <div class="figma-card-field">
          <div class="static-value">{{ messageTranslationPrefix +'reviewerComments' | translate }}:</div>
          <div class="dynamic-value">{{feedback.Comments }}</div>
        </div>
      </div>
    </div>
    }
  </div>
</div>
