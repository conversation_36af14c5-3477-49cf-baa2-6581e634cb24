import { NgModule } from '@angular/core';
import { MyFinancialTransactionComponent } from './pages/my-financial-transaction/my-financial-transaction.component';
import { MyDraftsComponent } from './pages/my-drafts/my-drafts.component';
import { MyDocumentsComponent } from './pages/my-documents/my-documents.component';
import { UserRoutingModule } from './user-routing.module';
import { MyApplicationsComponent } from './pages/my-applications/my-applications.component';
import { ApplicationsReadyToPayComponent } from './pages/applications-ready-to-pay/applications-ready-to-pay.component';
import { MyCasesComponent } from './pages/my-cases/my-cases.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { SharedModule } from '../shared/shared.module';
import { LoginComponent } from './pages/login/login.component';
import { PaymentSuccessComponent } from './pages/payment/payment-success/payment-success.component';
import { PaymentFailedComponent } from './pages/payment/payment-failed/payment-failed.component';
import { SubmitSummaryComponent } from './pages/application/submit-summary/submit-summary.component';
import { LoginWebsiteComponent } from './pages/login-website/login-website.component';
import { MySocialCasesComponent } from './pages/my-social-cases/my-social-cases.component';


@NgModule({
  declarations: [
    MyFinancialTransactionComponent,
    MyDraftsComponent,
    MySocialCasesComponent,
    MyDocumentsComponent,
    MyApplicationsComponent,
    ApplicationsReadyToPayComponent,
    MyCasesComponent,
    DashboardComponent,
    LoginComponent,
    PaymentSuccessComponent,
    PaymentFailedComponent,
    SubmitSummaryComponent,
    LoginWebsiteComponent,

  ],
  imports: [
    UserRoutingModule,
    SharedModule
  ]
})
export class UserModule { }
