@if(control)
{
<label class="form-label" [ngClass]="{'requird':isRequired}">{{label | translate}}</label>
<mat-form-field appearance="outline" class="appFullWidth">
  <!-- <mat-label>{{label | translate}}</mat-label> -->
  @switch (type) {
  @case (InputType.Default) {
  <input style="text-overflow: ellipsis;" matInput [appMaxLength]="allowMaxLength" [formControl]="control" [placeholder]="Placeholder">
  }
  @case (InputType.UnifiedNumber) {
  <input style="text-overflow: ellipsis; direction: ltr;" [style.text-align]="lang.IsArabic ? 'end' : 'start'" matInput [appMaxLength]="allowMaxLength" [formControl]="control" [placeholder]="Placeholder">
  }
  @case (InputType.PoBox) {
  <input matInput [appMaxLength]="allowMaxLength" [formControl]="control" [placeholder]="Placeholder">
  }
  @case (InputType.Email) {
  <input matInput dir="ltr" type="email" lang="en" [appMaxLength]="false" [formControl]="control"
    [placeholder]="'<EMAIL>'">
  }
  @case (InputType.Mobile) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false" mask="(000) 000000000" [showMaskTyped]="false"
    [formControl]="control" [placeholder]="'(971) 5000000000'">
  }
  @case (InputType.MobileWithoutMask) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false"
    [formControl]="control" [placeholder]="'(971) 5000000000'">
  }
  @case (InputType.InternationalMobileWithoutMask) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false"
    [formControl]="control" [placeholder]="'(+00 or 000) 0000000000'">
  }
  @case (InputType.LandLine) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false" mask="(000) 0 000 0000" [showMaskTyped]="false"
    [formControl]="control" [placeholder]="'(971) 6 000 0000 or (971) 9 000 0000'">
  }
  @case (InputType.LandLineWithoutMask) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false" [formControl]="control"
    [placeholder]="'(971) 6 000 0000 or (971) 9 000 0000'">
  }
  @case (InputType.InternationalLandLineWithoutMask) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false" [formControl]="control"
    [placeholder]="'(+00 or 000) 0000000000'">
  }
  @case (InputType.EmiratesId) {
  <input matInput dir="ltr" lang="en" [appMaxLength]="false" mask="000-0000-0000000-0" [showMaskTyped]="false"
    [formControl]="control" [placeholder]="'784-0000-0000000-0'">
  }
  @case (InputType.Date) {
  <input matInput type="date" dir="ltr" lang="en" [formControl]="control">
  }
  @case (InputType.WebSite) {
  <input matInput type="text" placeholder="www.domain.com | https://domain.com" dir="ltr" lang="en"
    [formControl]="control">
  }
  @case (InputType.PodCardNo) {
  <input dir="ltr" lang="en" matInput [appMaxLength]="false" mask="0000-00000000" [showMaskTyped]="true"
    [formControl]="control" [placeholder]="'0000-00000000'">
  }
  @case (InputType.Currency) {
  <div class="d-flex gap-1 input-currency">
    <input dir="ltr" lang="en" type="number" [min]="min" matInput [appMaxLength]="false" [formControl]="control"
      [placeholder]="placeholder | translate">
    <span matTextSuffix class="currency-span"> &nbsp; {{'AED' | translate}} </span>
  </div>
  }
  @case (InputType.Number) {
  <input type="number" lang="en" [min]="min" matInput [appMaxLength]="false" [formControl]="control"
    [placeholder]="placeholder | translate">
  }
  @case (InputType.CurrencyFormatted) {
  <div dir="ltr" lang="en" class="d-flex gap-1 input-currency" [ngClass]="translate.currentLang === 'ar' ? 'flex-row-reverse':''">
    <input type="text" [attr.dir]="translate.currentLang === 'ar' ? 'rtl' : 'ltr'" [appNumberOnly]="true" [min]="min" matInput [appMaxLength]="false" [formControl]="control"
      [placeholder]="placeholder | translate">
    <span matTextSuffix class="currency-span"> &nbsp; {{'AED' | translate}} </span>
  </div>
  }
  @case (InputType.Percentage) {
  <div class="input-container">
    <input type="number" lang="en" [min]="min" [max]="max" matInput [appMaxLength]="false" [formControl]="control" [placeholder]="placeholder | translate" />
    <span class="input-suffix">%</span>
  </div>
  }

  @default {
  Default case.
  }
  }
  <!-- @if (!control.touched) { -->

  <mat-hint [ngClass]="(!control.value && !hint && !label) ? 'd-none':''" *ngIf="control.enabled">
    <svg width="15" height="15" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
        fill="#92722A" />
    </svg>

    @if(hint) {
    {{hint | translate}}
    } @else {
    {{'Please enter' | translate}} {{label | translate}}
    }
  </mat-hint>
  <!-- } -->

  @if (control.touched && control.invalid) {
  <mat-error *ngIf="errorMessage !== null">{{ errorMessage }}</mat-error>
  }
</mat-form-field>

@if (showGoogleTranslate) {
<app-google-translate [target]="googleTranslateTarget" [control]="control"
  [isRelated]="showGoogleTranslateToRelatedCompoent" [relatedCompoent]="googleTranslateToRelatedCompoent">
</app-google-translate>
}

}
