import { Component, Input, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormControl, Validators } from '@angular/forms';
import { InputType } from '../../../enums/input-type.enum';
import { ValidationService } from '../../../services/validation.service';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { LanguageService } from '../../../services/language.service';

@Component({
  selector: 'app-input',
  templateUrl: './input.component.html',
  styleUrl: './input.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  },
  encapsulation: ViewEncapsulation.None
})
export class InputComponent {
  @Input() isAutoFill: boolean = false;
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() hint: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() type: InputType = InputType.Default;
  @Input() columns: number = 6;
  @Input() min: number = 0;
  @Input() max: number = 0;
  @Input() isReadOnly: boolean = false;
  @Input() allowMaxLength: boolean = false;


  @Input() showGoogleTranslate: boolean = false;
  @Input() showGoogleTranslateToRelatedCompoent: boolean = false;
  @Input() googleTranslateTarget: string;
  @Input() googleTranslateToRelatedCompoent: FormControl | undefined | null;

  _placeholder: string = '';

  constructor(private validationService: ValidationService, protected translate: TranslateService, protected lang: LanguageService) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
          this._placeholder='';
        });
   }

  public get InputType() {
    return InputType;
  }
  public get Placeholder() {
    if (this._placeholder) {
      return this._placeholder;
    }
    this._placeholder = this.placeholder ? `${this.translate.instant(this.placeholder)}` : `${this.translate.instant('Please enter')}${this.translate.instant(this.label)}`;
    return this._placeholder;
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }
  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }
  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
