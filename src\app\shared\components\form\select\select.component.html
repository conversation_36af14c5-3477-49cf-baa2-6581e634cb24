@if(control)
{
<label class="form-label" [ngClass]="{'requird':isRequired}">{{label | translate}}</label>
<mat-form-field appearance="outline" class="appFullWidth select-with-caret">
  <!-- <mat-label>{{label | translate}}</mat-label> -->
  <mat-select (selectionChange)="onSelectionChange($event)" [formControl]="control" [placeholder]="Placeholder">
    <mat-option *ngFor="let item of data" [value]="item"
      [disabled]="item?.disabled">{{lang.IsArabic?item.NameArabic:item.NameEnglish}}</mat-option>
  </mat-select>
  <!-- @if (!control.touched && control.value=='') { -->
  <mat-hint *ngIf="control.enabled">
    <svg width="15" height="15" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
        fill="#92722A" />
    </svg>

    <!-- {{'Please select...' | translate}} <strong>{{label | translate}}</strong></mat-hint> -->
    {{'Please select...' | translate}} {{label | translate}}</mat-hint>
  <!-- } -->
  @if (control.touched && control.invalid) {
  <mat-error *ngIf="errorMessage !== null">{{ errorMessage }}</mat-error>
  }
</mat-form-field>
}
