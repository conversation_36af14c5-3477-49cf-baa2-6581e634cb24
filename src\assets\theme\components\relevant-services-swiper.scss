.relevant-services-swiper{
  padding-bottom: 50px;
  .swiper-slide{
    height: 100% !important;
  }
  .swiper-controls-wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    position: absolute;
    bottom: 10px;
    width: 100%;
  }
  .swiper-button-next , .swiper-button-prev{
    position: static;
    height: auto;
    margin: 0;
    &:after{
      display: none;
    }
    svg{
      fill: $aegold-600;
      width: 24px;
      height: 24px;
      [ng-reflect-dir=rtl] &, [dir=rtl] &{
        transform: rotate(180deg);
      }
    }
  }
  .swiper-pagination{
    position: static;
    width: auto;
    .swiper-pagination-bullet {
      width: 8px;
      height: 8px;
      background-color: $aegold-500;
      opacity: 1;
      &.swiper-pagination-bullet-active{
        background-color: $aegold-800;
      }
    }
  }
  .service-card{
    height: 100% !important;
    border-radius: 16px;
    border: 2px solid $aegold-800 !important;
    border: 2px $mocdyellow;
    background: white;
    padding: 16px;
    display: grid;
    gap: 16px;
    @media only screen and (min-width: 1024px) {
      padding: 24px;
    }
    &__title{
      // font-family:"Inter";
      font-size: 26px !important;
      font-weight: 600 !important;
      line-height: 32px !important;
      margin-top: 0px !important;
      @media only screen and (min-width: 1024px) {
        font-size: 20px !important;
        line-height: 26px !important;
      }
    }
    button{
      height: 40px;
      padding: 0px 16px;
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
      margin-top: 8px;
      @media only screen and (min-width: 1024px) {
        margin-top: 16px;
      }
      svg{
        [ng-reflect-dir=rtl] &, [dir=rtl] &{
          transform: rotate(180deg);
        }
      }
    }
  }
  .swiper-slide{
    height: auto !important;
  }
}