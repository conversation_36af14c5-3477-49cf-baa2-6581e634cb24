import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function customIbanValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const iban = control.value;
        if (!iban) {
            return null;
        }

        const uaeIbanPattern = /^AE\d{21}$/;
        const isValid = uaeIbanPattern.test(iban);

        return isValid ? null : { invalidIban: true };
    };
}
