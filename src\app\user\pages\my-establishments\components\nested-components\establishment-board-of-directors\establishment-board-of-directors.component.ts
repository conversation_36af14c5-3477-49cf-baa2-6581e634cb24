import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { LangChangeEvent } from '@ngx-translate/core';
import { filter, tap } from 'rxjs';
import { ActionFormType } from '../../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { LegalFormTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/legal-form-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { FREQUENCY_OF_BOARD_MEETINGS_LIST, BOARD_ELECTION_CYCLE_LIST, RENOMINATION_LIST, NUMBER_OF_PERMISSIBLE_MEMBERS_LIST, ELECTION_METHOD_LIST } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { letterPatternValidatorGeneral } from '../../../../../../shared/validators/letter-pattern-validator-accept-general';

@Component({
  selector: 'app-establishment-board-of-directors',
  templateUrl: './establishment-board-of-directors.component.html',
  styleUrls: ['./establishment-board-of-directors.component.scss']
})
export class EstablishmentBoardOfDirectorsComponent extends MyEstablishmentComponentBase implements OnInit, OnChanges {
  frequencyOfBoardMettingsList: Lookup[];
  boardElectionCycleList: Lookup[];
  renomintationList: Lookup[];
  noofPermissableMembersList: Lookup[];
  positions: any[];
  conditions: any[];
  electionMethodList: Lookup[];

  conditionFormEditIndex: number;
  memberPositionsEditIndex: number;
  includeConditionIds: boolean = false;
  includePositionIds: boolean = false;

  get fbControls(): any {
    return this.form.controls;
  }
  boardConditionForm: FormGroup;
  get bcControls(): any {
    return this.boardConditionForm.controls;
  }
  get boardConditions(): FormArray<any> {
    return this.form.get("Conditions") as FormArray;
  }

  boardPositionsForm: FormGroup;
  get bpControls(): any {
    return this.boardPositionsForm.controls;
  }
  get boardPositions(): FormArray {
    return this.form.get("Positions") as FormArray;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.boardOfDirectors.";
    this.intiBoardConditionForm();
    this.intiBoardPositionsForm();

    this.StepperService.formData$.subscribe((data: any) => {
      if (this.form) {
        this.addValidationlocalBoardMembersPercentage();
      }
      const members = data?.FoundingMembersForm?.members || [];
      const hasUAEBoardMember = members.some(member => member.nationalityType?.ID === '1');

      if (members.length > 0) {
        if (hasUAEBoardMember) {
          this.updatePercentageOfUAELocalBoardMember(true);
        } else {
          this.updatePercentageOfUAELocalBoardMember(false);
        }
      }
    });
  }
  addValidationlocalBoardMembersPercentage = (): void => this.FormService.addValidators(this.form, ['localBoardMembersPercentage'], [Validators.min(70), Validators.max(100), Validators.pattern('^[0-9]{1,10}$')]);
  updatePercentageOfUAELocalBoardMember(hasUAEBoardMember: boolean): void {
    const percentageControl = this.form?.get('localBoardMembersPercentage');
    if (!percentageControl) return;

    if (hasUAEBoardMember) {
      if (percentageControl.value == '0')
        percentageControl.setValue('');

      if (!this.isNotAllowedToEdit && !this.isReturnForUpdate)
        percentageControl.enable();

      if (this.form) {
        this.addValidationlocalBoardMembersPercentage();
      }
    } else {
      percentageControl.setValue('0');
      percentageControl.disable();
      this.FormService.clearValidators(this.form, ['localBoardMembersPercentage']);
    }
  }


  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.frequencyOfBoardMettingsList = FREQUENCY_OF_BOARD_MEETINGS_LIST;
    this.boardElectionCycleList = BOARD_ELECTION_CYCLE_LIST;
    this.renomintationList = RENOMINATION_LIST;
    this.noofPermissableMembersList = NUMBER_OF_PERMISSIBLE_MEMBERS_LIST;
    this.electionMethodList = ELECTION_METHOD_LIST;

    this.MyEstablishmentService.lookupData$
      .pipe(
        filter(data => !!data),
        tap(data => {
          this.positions = data.BoardPosition;
          this.filterPositionsBasedOnType();
          this.conditions = data.GeneralConditionBoardofdirection;
          this.filterConditionsBasedOnType();



          if (this.MyEstablishmentService.FORM_MODE == ActionFormType.CREATE) {
            this.fillPredefinePositions();
            this.fillPredefineConditions();
          }
          this.StepperService.requestData$.subscribe(_ => {
            if (_ && (_.isFullDetails == true)) { this.mapData(_?.BoardOfDirectorsForm); }
            else if (_ && (_.isFullDetails == false) && _.BoardOfDirectorsForm) {
              this.boardPositions.clear();
              this.boardConditions.clear();
              this.mapData(_?.BoardOfDirectorsForm);
            }
            this.changeValidations(this.form, this.fbControls?.MemberIsExceeds?.value, 'ExceptionReasonAr', 'ExceptionReasonEn')
            this.updatePercentageOfUAELocal(_);
            this.updateBoardNumberValidators(this.fbControls?.MemberIsExceeds?.value);
          });
        })
      )
      .subscribe();

    this.Translation.onLangChange.subscribe((event: LangChangeEvent) => {
      this.changeValidations(this.form, this.fbControls?.MemberIsExceeds?.value, 'ExceptionReasonAr', 'ExceptionReasonEn');
    });

    this.resetFromValidation(this.form);

    this.positionPagination();
    this.conditionPagination();
    this.boardPositions.valueChanges.subscribe(_ => this.positionPagination());
    this.boardConditions.valueChanges.subscribe(_ => this.conditionPagination());
  }

  filterPositionsBasedOnType = (): void => {
    const legalFormMapping = {
      [LegalFormTypesEnum.Association]: 'Association',
      [LegalFormTypesEnum.Union]: 'Union',
      [LegalFormTypesEnum.SocialSolidarityFunds]: 'Social Solidarity Fund'
    };

    const selectedForm = legalFormMapping[this.fbControls.LegalFormId?.value];

    if (selectedForm) {
      this.positions = this.positions.filter(position => position.LegalForm === selectedForm);
    } else {
      this.positions = [];
    }

    this.positions = this.positions.sort((a, b) => a.Priority - b.Priority);
  };

  get countOfPredefindConditions(): number {
    return this.conditions?.length ?? -1;
  }

  filterConditionsBasedOnType = (): void => {
    const legalFormMapping = {
      [LegalFormTypesEnum.Association]: 'Association',
      [LegalFormTypesEnum.Union]: 'Union',
      [LegalFormTypesEnum.SocialSolidarityFunds]: 'Social Solidarity Fund'
    };

    const selectedForm = legalFormMapping[this.fbControls.LegalFormId?.value];

    if (selectedForm) {
      this.conditions = this.conditions.filter(condition => condition.LegalForm === selectedForm);
    } else {
      this.conditions = [];
    }

    this.conditions = this.conditions.sort((a, b) => a.Priority - b.Priority);
  };

  updatePercentageOfUAELocal = (data: any): void => {
    const members = data?.FoundingMembersForm?.FounderMember || [];
    const hasUAEBoardMember = members.some(member => member.localmember === 1);

    if (members.length > 0) {
      if (hasUAEBoardMember) {
        this.updatePercentageOfUAELocalBoardMember(true);
      } else {
        this.updatePercentageOfUAELocalBoardMember(false);
      }
    }
  }

  private fillPredefineConditions(): void {
    this.conditions.forEach(_ => {
      if (!this.checkConditionsExist(_?.Id)) {
        this.boardConditions.push(this.FormBuilder.group({
          id: [_.Id],
          nominationEn: [_.Name],
          nominationAr: [_.NameAr],
          canEdit: [false],
          SatusReason: [_?.SatusReason?.trim() || 'draft']
        }));
      }
    });
  }

  private fillPredefinePositions(): void {
    if (this.positions.length > 0) {
      this.positions.forEach((item) => {
        if (!this.checkPositionsExist(item?.ID)) {
          this.boardPositions.push(this.FormBuilder.group({
            id: [item?.ID],
            positionEn: [item?.NameEnglish],
            positionAr: [item?.NameArabic],
            canEdit: [false],
            SatusReason: [item?.SatusReason?.trim() || 'draft']
          }));
        }
      });
    }
  }


  intiBoardConditionForm = (): void => {
    this.boardConditionForm = this.FormBuilder.group({
      id: [''],
      nominationEn: new FormControl("", [letterPatternValidatorGeneral('en', true)]),
      nominationAr: new FormControl("", [letterPatternValidatorGeneral('ar', true)]),
      canEdit: new FormControl(''),
      SatusReason: ['']
    });
  }

  intiBoardPositionsForm = (): void => {
    this.boardPositionsForm = this.FormBuilder.group({
      id: [''],
      positionEn: new FormControl("", [letterPatternValidatorGeneral('en', true)]),
      positionAr: new FormControl("", [letterPatternValidatorGeneral('ar', true)]),
      canEdit: new FormControl(''),
      SatusReason: ['']
    });
  }

  updateBoardNumberValidators = (memberIsExceedsThan11: boolean): void => {
    this.changeValidations(this.form, this.fbControls?.MemberIsExceeds?.value, 'ExceptionReasonAr', 'ExceptionReasonEn');

    this.FormService.clearValidators(this.form, ['BoardNumbers']);

    if (memberIsExceedsThan11)
      this.FormService.addValidators(this.form, ['BoardNumbers'], [Validators.min(12)]);
    else
      this.FormService.addValidators(this.form, ['BoardNumbers'], [Validators.min(5), Validators.max(11)]);
  };

  // updateNumberOfPermissibleTermsValidators = (): void => {
  //   if (this.fbControls?.CanBeRenominated?.value?.ID === 1)
  //     this.FormService.addValidators(this.form, ['NumberOfPermissibleTerms'], [Validators.required]);
  //   else
  //     this.FormService.clearValidators(this.form, ['NumberOfPermissibleTerms']);

  //   this.fbControls.updateValueAndValidity();
  // };

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.valid;
    });

    return result;
    // return this.form.valid && (this.fbControls?.BoardNumbers.value - 1 == this.boardPositions.length);
    // return true;
  }

  isItemDuplicated = (item: any): boolean => {
    if (!this.boardConditions?.controls) return false;

    return this.boardConditions.controls.some(control => {
      const { conditionEn, conditionAr, id } = {
        conditionEn: control.get('nominationEn')?.value,
        conditionAr: control.get('nominationAr')?.value,
        id: control.get('id')?.value
      };

      const isSameContent = conditionEn === item.nominationEn || conditionAr === item.nominationAr;
      const isDifferentOrEmptyId = id !== item.id;

      return isSameContent && isDifferentOrEmptyId;
    });
  };

  manageConditionForm(item: any, type: GridActionTypesEnum) {
    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError('notify.error', 'notify.conditionExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.boardConditions.push(
        this.FormBuilder.group({
          id: [this.generateDistinctId()],
          nominationEn: [item.nominationEn],
          nominationAr: [item.nominationAr],
          SatusReason: [item.SatusReason?.trim() || 'draft'],
          canEdit: true,
        })
      );
    else
      this.boardConditions
        .at(this.conditionFormEditIndex)
        .patchValue({
          id: item.id,
          nominationEn: item.nominationEn,
          nominationAr: item.nominationAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
          canEdit: true,
        });

    this.boardConditionForm.reset();
    this.modalService.dismissAll();
  }

  handleEditConditions = (data: any, idx: number): void => {
    this.conditionFormEditIndex = idx;
    this.boardConditionForm.patchValue({
      id: data?.value?.id,
      nominationEn: data?.value?.nominationEn,
      nominationAr: data?.value?.nominationAr,
      canEdit: true,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
    this.FormService.enableFields(this.boardConditionForm, ['nominationEn', 'nominationAr'])
  }

  handleCloneConditions = (data: any): void => {
    this.boardConditionForm.patchValue({
      nominationEn: data?.value?.nominationEn,
      nominationAr: data?.value?.nominationAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.boardConditionForm, ['nominationEn', 'nominationAr'])
  }
  removeConditions = async (data: any, idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let condition = this.boardConditions?.value[idx];
      if (condition)
        this.removeFromCRM(condition?.id, this.MEMBERSHIP_CONDITION_GRID_IN_CRM);

      this.boardConditions.removeAt(idx);
    }
  }

  managePositions = (item: any, type: GridActionTypesEnum): void => {
    if (this.boardPositions?.value?.some((_: any) => (_.positionEn === item.positionEn || _.positionAr === item.positionAr) && _.id !== item.id)) {
      this.NotifyService.showError('notify.error', 'notify.positionExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.boardPositions.push(
        this.FormBuilder.group({
          id: [this.generateDistinctId()],
          positionEn: [item.positionEn],
          positionAr: [item.positionAr],
          SatusReason: [item.SatusReason?.trim() || 'draft'],
        })
      );
    else
      this.boardPositions
        .at(this.memberPositionsEditIndex)
        .patchValue({
          id: item.id,
          positionEn: item.positionEn,
          positionAr: item.positionAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
        });

    this.boardPositionsForm.reset();
    this.modalService.dismissAll();
  }


  handleEditPositions = (data: any, idx: number): void => {
    this.memberPositionsEditIndex = idx;
    this.boardPositionsForm.patchValue({
      id: data?.value?.id,
      positionEn: data?.value?.positionEn,
      positionAr: data?.value?.positionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
    this.FormService.enableFields(this.boardPositionsForm, ['positionEn', 'positionAr'])
  }

  handleClonePositions = (data: any): void => {
    this.boardPositionsForm.patchValue({
      positionEn: data?.value?.positionEn,
      positionAr: data?.value?.positionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft'
    });
    this.FormService.enableFields(this.boardPositionsForm, ['positionEn', 'positionAr'])
  }
  removePositions = async (data: any, idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.boardPositions?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.POSITION_GRID_IN_CRM);

      this.boardPositions.removeAt(idx);
    }
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("BoardOfDirectorsForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("BoardOfDirectorsForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("BoardOfDirectorsForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }


  getMappingObject = (): any => {
    return {
      nbrboardmemberexceed11code: this.fbControls?.MemberIsExceeds.value ? 1 : 0,
      MembershipConditions: (this.boardConditions.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: (this.includeConditionIds && _$.id) ? _$.id : (_$.canEdit == false ? this.EMPTY_GUID : _$.id ?? this.EMPTY_GUID),
          Name: _$.nominationEn,
          NameAr: _$.nominationAr,
          Position: "string",
          PositionId: this.EMPTY_GUID,
          Priority: index + 1,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$.SatusReason?.trim() || 'draft'
        }
      }),
      Positions: (this.boardPositions.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: (this.includePositionIds && _$.id) ? _$.id : (_$.canEdit == false ? this.EMPTY_GUID : _$.id ?? this.EMPTY_GUID),
          Name: _$.positionEn,
          NameAr: _$.positionAr,
          GeneralPosition: "",
          GeneralPositionCode: 0,
          Priority: index + 1,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$.SatusReason?.trim() || 'draft'
        }
      }),
      ExceptionsRequest: {
        Id: this.fbControls?.ExceptionReasonId?.value ?? this.EMPTY_GUID,
        Name: "",
        Description: this.fbControls?.ExceptionReasonEn?.value ?? '',
        DescriptionAr: this.fbControls?.ExceptionReasonAr?.value ?? ''
      },
      nbrofboardmembers: this.fbControls?.BoardNumbers.value,
      Frequencyofmonthlyboardmeetings: this.fbControls?.FrequencyOfMonthlyBoardMeetings.value?.ID,
      percentageofuaelocalboardmembers: this.fbControls?.localBoardMembersPercentage.value,
      Electionmethod: this.fbControls?.ElectionMethod?.value?.ID,
      NbrofpermissibleTerms: this.fbControls?.CanBeRenominated?.value.ID === 0 ? null : this.fbControls?.NumberOfPermissibleTerms?.value?.ID,
      Boardelectioncycle: this.fbControls?.BoardElectionCycle?.value?.ID,
      canboardmemberberenomatedforotherterm: this.fbControls?.CanBeRenominated?.value?.ID,
    }
  }

  getValueFromList = (list: any[] | null | undefined, index: number | null | undefined): any =>
    list && index ? list[index - 1] ?? '' : '';

  getElectionMethodValueFromList = (list: any[] | null | undefined, id: number | null | undefined): any =>
    list?.find(value => value.ID === id) ?? '';
  //list && index ? list[index] ?? '' : '';

  mapData = (data: any): void => {
    if (!data) return;

    this.fbControls.MemberIsExceeds.setValue(data?.nbrboardmemberexceed11code === 1);
    this.fbControls.localBoardMembersPercentage.setValue(data?.percentageofuaelocalboardmembers ?? '');
    this.fbControls.BoardNumbers.setValue(data?.nbrofboardmembers ?? '');
    const matchedRenomination = this.renomintationList?.find(_ => _.ID === data?.canboardmemberberenomatedforotherterm) ?? '';
    this.fbControls.CanBeRenominated.setValue(matchedRenomination);

    this.fbControls.FrequencyOfMonthlyBoardMeetings.setValue(
      this.getValueFromList(this.frequencyOfBoardMettingsList, data?.Frequencyofmonthlyboardmeetings)
    );
    this.fbControls.ElectionMethod.setValue(
      this.getElectionMethodValueFromList(this.electionMethodList, data?.Electionmethod)
    );
    this.fbControls.NumberOfPermissibleTerms.setValue(
      this.getValueFromList(this.noofPermissableMembersList, data?.NbrofpermissibleTerms)
    );
    this.fbControls.BoardElectionCycle.setValue(
      this.getValueFromList(this.boardElectionCycleList, data?.Boardelectioncycle)
    );

    this.fbControls.ExceptionReasonId.setValue(data?.ExceptionsRequest?.Id ?? '');
    this.fbControls.ExceptionReasonEn.setValue(data?.ExceptionsRequest?.Description ?? '');
    this.fbControls.ExceptionReasonAr.setValue(data?.ExceptionsRequest?.DescriptionAr ?? '');

    data.MembershipConditions = data.MembershipConditions.sort((a, b) => a.Priority - b.Priority);
    data.MembershipConditions?.forEach((item: any) => {
      this.boardConditions.push(this.FormBuilder.group({
        id: item.Id ?? '',
        nominationEn: item.Name ?? '',
        nominationAr: item.NameAr ?? '',
        canEdit: [true],
        SatusReason: item?.SatusReason?.trim() || 'draft'
      }));
    });

    data.Positions = data.Positions.sort((a, b) => a.Priority - b.Priority);
    data.Positions?.forEach((item: any) => {
      this.boardPositions.push(this.FormBuilder.group({
        id: item.Id ?? '',
        positionEn: item.Name ?? '',
        positionAr: item.NameAr ?? '',
        canEdit: [true],
        SatusReason: item?.SatusReason?.trim() || 'draft'
      }));
    });

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.boardConditions?.value || this.boardConditions?.value?.length == 0)) {
      this.fillPredefineConditions();
    } else {
      this.includeConditionIds = true;
    }

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.boardPositions?.value || this.boardPositions?.value?.length == 0)) {
      this.fillPredefinePositions();
    } else {
      this.includePositionIds = true;
    }
  };

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "boardofdirectors", this.fbControls);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  checkPositionsExist = (id: string): boolean => this.boardPositions.controls.findIndex(_ => _.get('id')?.value == id) > -1;
  checkConditionsExist = (id: string): boolean => this.boardConditions.controls.findIndex(_ => _.get('id')?.value == id) > -1;

  conditionPage = 1;
  conditionPageSize = 10;
  conditionDataTable: FormArray = this.FormBuilder.array([]);
  get conditionReprsentedDataTable(): FormArray { return this.conditionDataTable; }
  get conditionTableIndex(): number { return (this.conditionPage - 1) * (this.conditionPageSize) }
  conditionPagination = (): void => {
    this.conditionDataTable = this.FormBuilder.array([]);
    let data$ = this.boardConditions.controls.slice((this.conditionPage - 1) * this.conditionPageSize, (this.conditionPage - 1) * this.conditionPageSize + this.conditionPageSize);
    data$.forEach(_ => this.conditionDataTable.push(_));
  }

  positionPage = 1;
  positionPageSize = 10;
  positionDataTable: FormArray = this.FormBuilder.array([]);
  get positionReprsentedDataTable(): FormArray { return this.positionDataTable; }
  get positionTableIndex(): number { return (this.positionPage - 1) * (this.positionPageSize) }
  positionPagination = (): void => {
    this.positionDataTable = this.FormBuilder.array([]);
    let data$ = this.boardPositions.controls.slice((this.positionPage - 1) * this.positionPageSize, (this.positionPage - 1) * this.positionPageSize + this.positionPageSize);
    data$.forEach(_ => this.positionDataTable.push(_));
  }
}
