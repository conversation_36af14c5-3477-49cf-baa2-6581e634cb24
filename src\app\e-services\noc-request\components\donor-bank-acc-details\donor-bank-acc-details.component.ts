import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { LegalFormTypesEnum } from '../../enums/legal-form-types-enum';
import { accountMatchesIban } from '../../../../shared/validators/account-match-iban.validator';

@Component({
  selector: 'app-donor-bank-acc-details',
  templateUrl: './donor-bank-acc-details.component.html',
  styleUrls: ['./donor-bank-acc-details.component.scss'],
})
export class DonorBankAccountDetailsComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  countries: Lookup[];
  minDate: Date;
  UnitedArabEmiratesCountryID = '20778e6e-6975-ed11-81ad-002248cbd873';

  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() selectedLegalFormType: LegalFormTypesEnum | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.donorBankAccountDetails.';

    const today = new Date();
    today.setDate(today.getDate() + 30);
    this.minDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.countries = data?.Countries;
        if (this.selectedLegalFormType == LegalFormTypesEnum.OutsideUae) {
          this.countries = this.countries.filter(_ => _.ID?.toLocaleLowerCase().trim() !== this.UnitedArabEmiratesCountryID.toLocaleLowerCase().trim())
        }

        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.DonorBankAccountDetails);
          } else if (_ && _.isFullDetails == false) {
            this.mapData(_?.DonorBankAccountDetails);
          }

          if (this.selectedLegalFormType == LegalFormTypesEnum.InsideUae) {
            this.fb.Country.setValue(this.countries?.find((_) => _.ID?.toLocaleLowerCase().trim() === this.UnitedArabEmiratesCountryID.toLocaleLowerCase().trim()));
            this.fb.Country?.disable();
          }

        });
      }
    });

    this.form.get('IBANNumber')?.valueChanges.subscribe(value => {
      if(value && value !== '' && value !== undefined && value !== null){
        this.form.get('AccountNumber')?.addValidators(accountMatchesIban(this.form.get('IBANNumber')!));
      }
    })
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );
    return result;
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'DonorBankAccountDetails',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'DonorBankAccountDetails',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      Country: this.fb?.Country?.value?.ID ?? '',
      BankName: this.fb?.BankName?.value ?? '',
      AccountNumber: this.fb?.AccountNumber?.value ?? '',
      IBANNumber: this.fb?.IBANNumber?.value ?? '',
    };
  };

  mapData = (data: any): void => {
    if (!data) return;

    this.fb.Country.setValue(
      this.countries?.find((_) => _.ID === data?.Country)
    );

    this.fb.BankName.setValue(data?.BankName);
    this.fb.AccountNumber.setValue(data?.AccountNumber);
    this.fb.IBANNumber.setValue(data?.IBANNumber);
  };

  
  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "donorBankAccountDetails", this.fb);
  }
}
