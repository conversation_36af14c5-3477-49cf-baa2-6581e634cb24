// .custom-download-buttons {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     column-gap: 15px;
//     padding: 20px;

//     @media(max-width: 1024px) {
//         flex-direction: column !important;
//         column-gap: unset !important;
//         row-gap: 15px;
//         padding: 10px !important;
//     }

//     button {
//         text-wrap: nowrap;
//     }
// }

.download-buttons-container {
    @media (min-width: 1024px) {
      column-gap: 1rem !important;
    }
  
    @media (max-width: 1024px) {
      flex-direction: column !important;
      row-gap: 0.5rem !important;
    }
  }