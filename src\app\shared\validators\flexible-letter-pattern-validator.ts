import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

// accept any thing
// required
// min 2
// max 500

export function flexibleletterPatternValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return { required: true };
    }

    if (value.length < 2) {
      return { minLengthIs2: true };
    }

    if (value.length > 500) {
      return { maxLength: true };
    }

    return null;
  };
}
