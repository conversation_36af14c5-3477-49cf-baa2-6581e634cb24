import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function letterPatternValidatorGeneralNotNumberOnly( isRequired: boolean = true): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;
    const pattern = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFA-Za-z\s\d\n!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{2,500}$/;




    if (!value) {
      if (isRequired) {
        return { required: true };
      }
    } else {
      if (value.length < 2) {
        return { minLengthIs2: true };
      }

      if (value.length > 500) {
        return { maxLength: true };
      }

      if (!pattern.test(value) || !isNaN(value)) {
        return { invalidPatternNotNumOnly: true };
      }
    }


    return null;
  };
}
