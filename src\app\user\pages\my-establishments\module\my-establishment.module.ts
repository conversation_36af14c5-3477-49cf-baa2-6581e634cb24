import { NgModule } from '@angular/core';
import { EstablishmentMainPageComponent } from '../pages/establishment-main-page/establishment-main-page.component';
import { EstablishmentDetailsPageComponent } from '../pages/establishment-details-page/establishment-details-page.component';
import { MyEstablishmentRoutingModule } from './my-establishment-routing.module';
import { SharedModule } from '../../../../shared/shared.module';
import { EstablishmentListComponent } from '../components/establishment-list/establishment-list.component';
import { EstablishmentRequestStepperComponent } from '../components/establishment-request-stepper/establishment-request-stepper.component';
import { EstablishmentBasicInformationComponent } from '../components/nested-components/establishment-basic-information/establishment-basic-information.component';
import { EstablishmentBoardOfDirectorsComponent } from '../components/nested-components/establishment-board-of-directors/establishment-board-of-directors.component';
import { EstablishmentBoardOfTrusteesComponent } from '../components/nested-components/establishment-board-of-trustees/establishment-board-of-trustees.component';
import { EstablishmentFoundingMembersComponent } from '../components/nested-components/establishment-founding-members/establishment-founding-members.component';
import { EstablishmentFundServicesComponent } from '../components/nested-components/establishment-fund-services/establishment-fund-services.component';
import { EstablishmentInterimCommitteeComponent } from '../components/nested-components/establishment-interim-committee/establishment-interim-committee.component';
import { EstablishmentMembershipEnrollmentComponent } from '../components/nested-components/establishment-membership-enrollment/establishment-membership-enrollment.component';
import { EstablishmentObjectiveComponent } from '../components/nested-components/establishment-objective/establishment-objective.component';
import { EstablishmentUploadDocumentsComponent } from '../components/nested-components/establishment-upload-documents/establishment-upload-documents.component';
import { EstablishmentAllocationOfFoundationFundsByDecreeComponent } from '../components/nested-components/nested-by-decree-components/e-allocation-of-foundation-funds-by-decree/e-allocation-of-foundation-funds-by-decree.component';
import { EstablishmentBoardOfDirectorsByDecreeComponent } from '../components/nested-components/nested-by-decree-components/establishment-board-of-directors-by-decree/establishment-board-of-directors-by-decree.component';
import { EstablishmentBoardOfTrusteesByDecreeComponent } from '../components/nested-components/nested-by-decree-components/establishment-board-of-trustees-by-decree/establishment-board-of-trustees-by-decree.component';
import { EstablishmentFoundingMembersByDecreeComponent } from '../components/nested-components/nested-by-decree-components/establishment-founding-members-by-decree/establishment-founding-members-by-decree.component';
import { EstablishmentFundActivitiesAndObjectivesComponent } from '../components/nested-components/nested-declaration-components/establishment-fund-activities-and-objectives/establishment-fund-activities-and-objectives.component';
import { EstablishmentUnionObjectivesComponent } from '../components/nested-components/nested-declaration-components/establishment-union-objectives/establishment-union-objectives.component';
import { EstablishmentAllocationOfFoundationFundsComponent } from '../components/nested-components/establishment-allocation-of-foundation-funds/establishment-allocation-of-foundation-funds.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EstablishmentReviewAndSubmitComponent } from '../components/nested-components/establishment-review-and-submit/establishment-review-and-submit.component';
import { EstablishmentDetailsManagementComponent } from '../pages/establishment-details-management/establishment-details-management.component';
import { NmwpEstablishmentDetailsComponent } from '../pages/nmwp-establishment-details/nmwp-establishment-details.component';
import { FundraisingPermitListComponent } from '../pages/establishment-details-management/nested-components/fundraising-permit-list/fundraising-permit-list.component';
import { MembersListComponent } from '../pages/establishment-details-management/nested-components/members-list/members-list.component';
import { EstablishmentFeedBackComponent } from '../components/nested-components/establishment-feed-back/establishment-feed-back.component';

const EXPORT_COMPOENTS = [
  EstablishmentListComponent,
  EstablishmentRequestStepperComponent,

  EstablishmentAllocationOfFoundationFundsComponent,
  EstablishmentBasicInformationComponent,
  EstablishmentBoardOfDirectorsComponent,
  EstablishmentBoardOfTrusteesComponent,
  EstablishmentFoundingMembersComponent,
  EstablishmentFundServicesComponent,
  EstablishmentInterimCommitteeComponent,
  EstablishmentMembershipEnrollmentComponent,
  EstablishmentObjectiveComponent,
  EstablishmentUploadDocumentsComponent,
  EstablishmentReviewAndSubmitComponent,
  /* nested-by-decree-components */
  EstablishmentAllocationOfFoundationFundsByDecreeComponent,
  EstablishmentBoardOfDirectorsByDecreeComponent,
  EstablishmentBoardOfTrusteesByDecreeComponent,
  EstablishmentFoundingMembersByDecreeComponent,

  /* nested-declaration-components */
  EstablishmentFundActivitiesAndObjectivesComponent,
  EstablishmentUnionObjectivesComponent,
  FundraisingPermitListComponent,
  MembersListComponent,
  EstablishmentFeedBackComponent
];

const COMPOENTS = [
  EstablishmentMainPageComponent,
  EstablishmentDetailsPageComponent,
  EstablishmentDetailsManagementComponent,
  NmwpEstablishmentDetailsComponent,
  ...EXPORT_COMPOENTS,
];

@NgModule({
  declarations: [...COMPOENTS],
  exports: [...EXPORT_COMPOENTS],
  imports: [MyEstablishmentRoutingModule, SharedModule, MatSlideToggleModule],
})
export class MyEstablishmentModule { }
