.appform h1 {
    color: $aeblack-800;
    font-size: 24px;
    font-weight: $font-bold;
    margin-bottom: 32px;

    @media(max-width: 1024px) {
        overflow: hidden;
        overflow-wrap: break-word;
        word-break: auto-phrase;

        display: flex !important;
        align-items: start !important;
        justify-content: center !important;
        flex-direction: column !important;
        // margin: 14px 0 !important;
        margin: 7px 0 !important;
        font-size: 20px;

        .pro_names {
            margin-left: 0 !important;
            margin-right: 0 !important;
            margin-top: 10px !important;
        }
    }
}

.appform  > .row {
    @media (max-width:1024px) {
        row-gap: 18px !important;
        margin-bottom: 0 !important;


        .col-sm-12,
        .col-md-6 {
            padding-bottom: 0 !important;
        }
    }
}

.section-separator {
    // @media (min-width:1024px){
    //     margin-bottom: 48px !important;
    // }
    
    @media (max-width:1023px){
        margin-bottom: 16px !important;
    }


}
