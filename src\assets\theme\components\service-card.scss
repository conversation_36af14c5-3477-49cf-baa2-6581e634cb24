
.service-card{
  // border-radius: 16px;
  // border: 2px $mocdyellow;
  // background: white;
  padding: 16px;
  display: grid;
  gap: 16px;
  // @media only screen and (min-width: 1024px) {
  //   padding: 24px;
  // }
  &__eligiblity-lbl , &__time-lbl{
    padding: 4px 12px;
    font-size: 18px;
    font-weight: 500;
    line-height: 28px; 
    border-radius: 30px;
    border: 1px solid;
  }
  &__eligiblity-lbl{
    color: $aegreen-600;
    border-color: $aegreen-600;
    background-color: $aegreen-100;
    &.error{
      color: $aered-600;
      border-color: $aered-600;
      background-color: $aered-100;
    }
  }
  &__time-lbl{
    color: $aegold-600;
    border-color: $aegold-600;
  }
  &__title{
    font-size: 20px !important;
    font-weight: 700 !important;
    line-height: 26px !important;
    margin-top: 0px !important;
    @media only screen and (min-width: 1024px) {
      font-size: 24px !important;
      line-height: 30px !important;
    }
  }
  &__desc{
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 0px !important;
  }
  button{
    height: 40px;
    padding: 0px 16px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    margin-top: 8px;
    @media only screen and (min-width: 1024px) {
      margin-top: 16px;
    }
    svg{
      [ng-reflect-dir=rtl] &, [dir=rtl] &{
       transform: rotate(180deg);
      }
    }
  }
}

.service-suggestions-swiper{
  // padding-bottom: 50px;
  .swiper-controls-wrapper{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    position: absolute;
    bottom: 10px;
    width: 100%;
  }
  .swiper-button-next , .swiper-button-prev{
    position: static;
    height: auto;
    margin: 0;
    &:after{
      display: none;
    }
    svg{
      fill: $aegold-600;
      width: 24px;
      height: 24px;
      [ng-reflect-dir=rtl] &, [dir=rtl] &{
        transform: rotate(180deg);
       }
    }
  }
  .swiper-pagination{
    position: static;
    width: auto;
    .swiper-pagination-bullet {
      width: 8px;
      height: 8px;
      background-color: $aegold-500;
      opacity: 1;
      &.swiper-pagination-bullet-active{
        background-color: $aegold-800;
      }
    }
  }
}