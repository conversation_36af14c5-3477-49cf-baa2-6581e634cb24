.input-currency {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Firefox */
  input[type="number"] {
    -moz-appearance: textfield;
  }

  .currency-span {
    text-align: center;
    justify-content: center;
    align-content: center;
    align-items: center;
    margin-top: 0px;
    color: #848484;
    width: 10rem;
    text-align: right;
    text-wrap: nowrap !important;
  }
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
}

// mat-form-field {
//   &:dir(rtl) {
//     mat-hint ,
//     .currency-span,
//     .mdc-label {
//       font-family: "Noto Kufi Arabic" !important;
//     }
//   }
// }