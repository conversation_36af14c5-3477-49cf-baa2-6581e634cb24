import {
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Attachment } from '../../../../shared/models/attachment.model';
import { Feedback } from '../../models/feedback';
import { SubmitType } from '../../models/submit-type';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { LegalFormTypesEnum } from '../../enums/legal-form-types-enum';

@Component({
  selector: 'app-upload-documents',
  templateUrl: './upload-documents.component.html',
  styleUrls: ['./upload-documents.component.scss'],
})
export class UploadDocumentsComponent
  extends NocRequestComponentBase
  implements OnInit, OnChanges {
  get f(): any {
    return this.form.controls;
  }
  attachments: Attachment[] = [];
  attachmentsData: any[] = [];
  documentList: any;
  filterDocumentList: any;
  requestId: any;
  establishmentId: any;
  serviceCatalogues: any[] = [];
  NPO_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO DeclarationServiceCatalogue';
  RequestType = LegalFormTypesEnum;
  @Input() form: FormGroup;
  @Input() mode?: string;
  @Input() legalFormType: any;
  @Input() npoLegalFormType: any;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.uploadDocuments.';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      (changes['feedbackList']?.currentValue &&
        changes['feedbackList']?.currentValue?.length > 0) ||
      changes['isReturnForUpdate']?.currentValue === true
    ) {
      this.checkEditableFildes();
    }
  }

  ngOnInit() {
    this.form = this.FormBuilder.group({});
    this.NocRequestService.lookupData$.subscribe(
      (data) => {
        if (data) {
          this.documentList = data?.DocumentType ?? [];
          if (this.documentList?.length > 0) {
            this.documentList = this.documentList.filter((doc) => doc != null && doc.InsideOutsideUAE == this.legalFormType)[0]?.DocumentType ?? [];
            this.filterDocumentList = this.documentList;
          }

          this.setupFormControls();

          this.StepperService.requestData$.subscribe((_) => {
            this.requestId = this.StepperService.requestId;

            if (_ && _.isFullDetails == true) {
              this.mapData(_?.UploadDocumentForm);
              this.checkAttachmentRequest();
            } else if (_ && _.isFullDetails == false) {
              this.attachments = [];
              this.attachmentsData = [];
              this.mapData(_?.UploadDocumentForm);
              this.checkAttachmentRequest();
            }

            if (_ && _?.DonorDetails && _?.DonorDetails?.DonorType) {
              if (this.legalFormType == this.RequestType.InsideUae) {
                switch (_?.DonorDetails?.DonorType) {
                  case 1:
                    //<!-- Individual 1-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == '16229105-155d-f011-b112-005056010908' || _.ID == 'a0967f41-155d-f011-b112-005056010908' || _.ID == 'bce29a60-155d-f011-b112-005056010908');
                    break;
                  case 2:
                    //<!-- Public Sector 2-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'f91f64d6-155d-f011-b112-005056010908');
                    break;
                  case 3:
                    //<!-- Private Sector 3-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'd8697e78-155d-f011-b112-005056010908' || _.ID == 'befaf8ba-155d-f011-b112-005056010908');
                    break;
                  case 4:
                    //<!-- NPO Sector 4-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'f91f64d6-155d-f011-b112-005056010908');
                    break;

                  default:
                    break;
                }
              } else {
                switch (_?.DonorDetails?.DonorType) {
                  case 1:
                    //<!-- Individual 1-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'a0967f41-155d-f011-b112-005056010908' || _.ID == 'bce29a60-155d-f011-b112-005056010908');
                    break;
                  case 2:
                    //<!-- Public Sector 2-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'f91f64d6-155d-f011-b112-005056010908');
                    break;
                  case 3:
                    //<!-- Private Sector 3-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == 'd8697e78-155d-f011-b112-005056010908' || _.ID == 'befaf8ba-155d-f011-b112-005056010908');
                    break;
                  case 4:
                    //<!-- NPO Sector 4-->
                    this.documentList = this.filterDocumentList.filter(_ => _.ID == '8664ca15-3e62-f011-b112-005056010908' || _.ID == 'f91f64d6-155d-f011-b112-005056010908');
                    break;

                  default:
                    break;
                }
              }

              this.setupFormControls();
            }
          });
        }
      }
    );

    setInterval(() => {
      this.checkAllowUpload();
      this.checkEditableFildes();
    }, 2000);
  }



  isValidForm = (): boolean => {
    if ((this.documentList?.length ?? 0) == 0) return false;

    if (this.documentList?.every((doc) => doc.Requirement === 'Optional'))
      return true;

    const requiredDocs = this.documentList.filter(
      (doc) => doc.Requirement === 'Required'
    );
    if (requiredDocs.length === 0) return this.attachments.length > 0;
    const hasInvalidRequiredDocs = requiredDocs.some(
      (doc) => this.form.get(doc.ID)?.invalid
    );
    return !hasInvalidRequiredDocs;
  };

  getFormControl(id: string): FormControl {
    return this.form.get(id) as FormControl;
  }

  checkAllowUpload = (): void => {
    this.documentList?.forEach((doc) => {
      if (this.isNotAllowedToEdit) {
        this.form.get(doc.ID)?.disable();
      } else {
        this.form.get(doc.ID)?.enable();
      }
    });
  };

  setupFormControls() {
    this.documentList?.forEach((doc) => {
      const isRequired = doc.Requirement === 'Required';
      this.form.addControl(
        doc.ID,
        this.FormBuilder.control('', isRequired ? Validators.required : null)
      );
    });
  }

  onFileUploaded(attachment: Attachment) {
    this.uploadFile(attachment);
  }

  onFileDeleted(attachment: Attachment) {
    const index = this.attachments.findIndex(
      (item) => item.docTypeId === attachment.docTypeId
    );
    if (index !== -1) {
      this.NocRequestService.deleteAttachment(
        this.attachments[index].id
      ).subscribe((res) => {
        this.NotifyService.showSuccess(
          'notify.success',
          'notify.fileDeletedSuccess'
        );
        const id = this.attachments[index].id;
        this.attachments = this.attachments.filter((_) => _.id !== id);
        this.attachmentsData = this.attachmentsData.filter((_) => _.id !== id);
        this.removeFromCRM(id, this.DOCUMENTS_GRID_IN_CRM);
        this.saveAsDraft(true);
      });
    }
  }

  uploadFile(attachment: Attachment) {
    const reader = new FileReader();
    reader.readAsDataURL(attachment.file);
    reader.onload = () => {
      let base64 = reader.result?.toString();
      if (base64) {
        base64 = base64.split(',')[1];
      }
      var userInfo = this.AuthService.getUserInfo();
      this.requestId = this.StepperService.requestId;

      if (this.requestId && !attachment.isEditMode) {
        const request = {
          Applicant: userInfo.crmUserId,
          DocumentType: attachment.docTypeId,
          EstablishmentId: this.establishmentId,
          RecieveDonationRequest: this.requestId,
          mimeType: attachment.file.type,
          FileName: attachment.file.name,
          Base64: base64,
        };
        this.NocRequestService.saveAttachment(
          request
        ).subscribe((res) => {
          attachment.id = res.Id;
          this.attachments.push(attachment);
          this.attachmentsData.push({
            id: res.Id,
            ...request,
          });
        });
      } else if (this.requestId && attachment.isEditMode) {
        this.attachments.push(attachment);
      } else {
        this.attachments.push(attachment);
        this.attachmentsData.push({
          id: this.EMPTY_GUID,
          Applicant: userInfo.crmUserId,
          DocumentType: attachment.docTypeId,
          EstablishmentId: this.establishmentId,
          mimeType: attachment.file.type,
          FileName: attachment.file.name,
          Base64: base64,
        });
      }
    };
  }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'UploadDocumentForm',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (!this.isValidForm()) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'UploadDocumentForm',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      Document: this.attachmentsData.map((_) => {
        return {
          Id: _.id,
          Establishment: _.EstablishmentId,
          Applicant: this.currentApplicantId,
          DocumentType: _.DocumentType,
          mimeType: _.mimeType,
          FileName: _.FileName,
          Base64: _.Base64,
        };
      }),
    };
  };

  mapData = (data: any): void => {
    if (!data || !data.Document) return;

    this.attachments = [];
    this.attachmentsData = [];
    data.Document.forEach((item: any) => {
      const attachment: any = {
        id: item.Id,
        docTypeId: item.DocumentType,
        file: null,
        mimeType: item.mimeType ?? item.MimeType,
        FileName: item.FileName,
        isEditMode: true,
        Base64: item.Base64,
      };

      this.attachments.push(attachment);
      this.attachmentsData.push({
        id: item.Id,
        EstablishmentId: item.Establishment,
        currentApplicantId: item.Applicant,
        DocumentType: item.DocumentType,
        mimeType: item.mimeType ?? item.MimeType,
        FileName: item.FileName,
        Base64: item.Base64,
      });
    });
  };

  checkEditableFildes = (): void => {
    if (this.isReturnForUpdate) {
      this.documentList?.forEach((doc) => {
        this.form.get(doc.ID)?.enable();
      });
    }
  };

  checkAttachmentRequest = (): void => {
    if (this.requestId) {
      this.NocRequestService.fetchAttachments(
        this.requestId
      ).subscribe((res) => {
        var data = res?.data;
        this.mapData({ Document: data });
      });
    }
  };

  getexternalInjectFile = (docTypeId: string): any =>
    this.attachments.find((_) => _.docTypeId == docTypeId);
}
