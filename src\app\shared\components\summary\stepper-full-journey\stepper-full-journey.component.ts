import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { SummaryStepsFullJourny } from '../../../models/summry-steps-full-journey';
import { MatStepper } from '@angular/material/stepper';
import { LanguageService } from '../../../services/language.service';

@Component({
  selector: 'app-stepper-full-journey',
  templateUrl: './stepper-full-journey.component.html',
  styleUrls: ['./stepper-full-journey.component.scss']
})
export class StepperFullJourneyComponent implements OnInit, OnChanges {

  @Input() steps: SummaryStepsFullJourny[] | undefined;
  @Input() lastIndexActiveStep: number = 1;
  @ViewChild('journeyStepper') stepper: MatStepper;

  constructor(protected lang: LanguageService) { }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['steps']?.currentValue &&
      changes['steps']?.currentValue?.length > 0) {
      setTimeout(() => {
        if (this.steps?.length == this.lastIndexActiveStep) {
          this.stepper.selectedIndex = this.lastIndexActiveStep - 1;
          this.stepper.selected!.completed = true;
          this.stepper.selected!.state = 'done';

        } else {
          this.stepper.selectedIndex = this.lastIndexActiveStep;
        }
      }, 500);
    }
  }

  ngOnInit() { }

}
