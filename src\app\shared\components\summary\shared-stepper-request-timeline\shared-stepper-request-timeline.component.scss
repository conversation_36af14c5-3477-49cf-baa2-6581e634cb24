@import '../../../../../assets/theme/variables.scss';
@import '../../../../../assets/theme/main.scss';
@import '../../../../../assets/theme/fonts.scss';

.custom-stepper {
    position: relative;
    width: 100%; // Adjust width as needed

    .mat-step-header {
        display: flex;
        align-items: center;
        gap: 20px;
        width: 100%;
        position: relative; // For the line positioning
        padding-left: calc(41px + 62px);
    }

    /* Make mat-step-icon a circle and hide the number */
    .mat-step-icon {
        background-color: $aegold-500; // Custom color for the circle
        color: transparent !important;
        border-radius: 50%;
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0; // Hide the number inside the circle
        transform: translateX(6px);
    }

    .mat-step-label {
        display: grid;
        grid-template-columns: 1fr auto; // Left side (Date & Clock) | Vertical line and Status
        gap: 10px;
        align-items: center;
        width: 100%;
    }

    .step-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }

    .left-side {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        position: absolute;
        left: 0;
    }

    .step-date {
        color: $aeblack-900;
        font-size: 14px;
        // font-family: "Roboto";
    }

    .step-clock {
        color: $aeblack-900;
        font-size: 14px;
        // font-family: "Roboto";
    }

    .step-status {
        color: $aeblack-900;
        min-width: 109px;
        text-align: center;
    }

    /* Style the vertical line between Date & Status */
    .mat-step-header .mat-stepper-vertical-line::before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 50%;
        /* Position the line in the middle */
        width: 5px;
        height: 100%;
        background-color: $aeblack-200;
        /* Line color */
        transform: translateX(-50%);
        /* Center the line */
    }

    // Ensuring all step content is always visible, even for inactive steps
    .mat-step-content {
        display: block !important;
    }

    .mat-stepper-vertical .mat-step-header {
        display: flex;
        flex-direction: column; // Adjust for vertical stepper
        align-items: flex-start;
        position: relative; // Ensure absolute positioning of the line works
    }

    .mat-stepper-vertical-line::before {
        left: -.5px;
        border-left-width: 3px;
        border-left-color: $aeblack-200;
    }

    .mat-vertical-content-container {
        margin-left: calc(41px + 62px + 12px);
    }

    .mat-vertical-content-container {
        height: 0;
    }
}