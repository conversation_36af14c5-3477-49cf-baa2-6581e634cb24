@import './variables.scss';
@import './main.scss';
@import './fonts.scss';

// -- plugins
// @import 'swiper/scss';
// @import 'swiper/scss/navigation';
// @import 'swiper/scss/pagination';

// --components
@import './components//form.scss';
@import './components/button.scss';
@import './components/accordion.scss';
@import './components/alert.scss';
@import './components/cta-block.scss';
@import './components/stepper.scss';
@import './components/stepper-vertical-form.scss';
@import './components/service-apply-Card.scss';
@import './components/info-card.scss';
@import './components/submit-result.scss';
@import './components/footer.scss';
@import './components/header.scss';
@import './components/user-card.scss';
@import './components/service-status-card.scss';
@import './components/hyperlinks.scss';
@import './components/service-card.scss';
@import './components/table-listing-card.scss';
@import './components/application-overview.scss';
@import './components/pagination.scss';
@import './components/UAE-pass-panel.scss';
@import './components/help-FAB.scss';
@import './components/eligibility-criteria-modal.scss';
@import './components/info.scss';
@import './components/npo-service.scss';
@import './components/feedback.scss';
@import './components/relevant-services-swiper.scss';
@import './components/typography.scss';
@import './components/modal.scss';
@import './components/table.scss';
@import './components/select.scss';
@import './components/tooltip.scss';
@import './components/application-summary.scss';
@import './components/stepper-vertical-calendar.scss';
@import './components/stepper-horizontal-calendar.scss';