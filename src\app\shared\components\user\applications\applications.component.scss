.col-wrap {
  word-wrap: break-word;
  // max-width: 160px;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 10px;
  // white-space: nowrap;
}

/* Works on Firefox */
* {
  scrollbar-width: none;
  // scrollbar-width: thin;
  // scrollbar-color: #f2eccf #ffffff;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 12px;
}

*::-webkit-scrollbar-track {
  background: #ffffff;
}

*::-webkit-scrollbar-thumb {
  background-color: #f2eccf;
  border-radius: 20px;
  border: 3px solid #ffffff;
}


.application-overview {
  background: #fff !important;
  padding: 12px 24px !important;
  border-radius: 16px !important;
}

.total-count {
  @media (max-width:1024px) {
    display: none !important;
  }
}

.maxWidth {
  max-width: 300px;
  @media (max-width: 1024px){
    max-width: unset !important;
  }
}