.mat-step-header {
    pointer-events: all !important;
  }
  
  .showSuccessMessage {
    // visibility: hidden !important;
    display: none;
    height: 0 !important;
    overflow: hidden !important;
    transition: height 0.3s ease;
  }
  
  .congratulation-message {
    margin: 0 0 32px;

    > h1 {
      font-size: 62px;
      font-weight: 700;
      
      >.result-icon {
        margin-right: 24px !important;
        margin-left: 0 !important;
        
        &:dir(rtl){
         margin-right: 0 !important;
         margin-left: 24px !important;
       }
      }

      @media (max-width: 1024px) {
        font-size: 40px !important;
        flex-wrap: wrap;
        
        > .result-icon {
          width: 40px;
          height: 40px;
        }
      }

      @media (max-width: 768px) {
        // font-size: 32px !important;
        
        > .result-icon {
          width: 32px;
          height: 32px;
        }
      }
    }
  }
  
  .submition-message {
    line-height: 48.41px;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 24px !important;
  }
  
  .actions-container {
    display: flex;
    gap: 24px !important;
    margin-top: 40px;
  }
  
  .review-message {
    font-size: 24px;
    font-weight: 400;
    line-height: 32px;
  }
  
  .application-reference-number {
    padding: 4rem;
    border-radius: 8px;
    background-color: #f9f7ed;
    margin-bottom: 40px;
  }
  
  .application-reference-number p {
    font-size: 26px;
    font-weight: 600;
    line-height: 31.47px;
  }
  
  .application-reference-number .eService_sub-title {
    font-size: 24px;
    font-weight: 400;
    line-height: 32px;
    color: #7c5e24;
  }
  
