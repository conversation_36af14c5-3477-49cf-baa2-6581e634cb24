.service-apply-Card{
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: $aegold-50;
  border-radius: 8px;
  @media only screen and (min-width: 1024px) {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 32px;
  }
  .service-apply-Card__lbl{
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
    @media only screen and (min-width: 1024px) {
      font-size: 20px;
      font-weight: 500;
      line-height: 28px; 
    }
    svg{
      width: 32px;
      height: 32px;
    }
  }
  .service-apply-Card__info{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    // padding: 16px;
  }
  .service-apply-Card__time{
    padding: 4px 12px;
    border-radius: 30px;
    border: 1px solid $aegold-600;
    font-size: 18px;
    font-weight: 500;
    line-height: 28px;
    color: $aegold-600;
  }
  .service-apply-Card__title{
    font-size: 26px !important;
    font-weight: 600 !important;
    line-height: 32px !important;
    margin-top: 0px !important;
    color: $aeblack-800;
    // font-family: 'Inter' !important;
    // [ng-reflect-dir=rtl] & , [dir=rtl] & {
    //   font-family: 'Alexandria' !important;
    // }
    @media only screen and (min-width: 1024px) {
      font-size: 32px;
      line-height: 38px;
    }
  }
  .service-apply-Card__sub-title{
    font-size: 20px !important;
    font-weight: 700 !important;
    line-height: 26px !important;
    margin-bottom: 0px !important;
    @media only screen and (min-width: 1024px) {
      font-size: 24px !important;
      line-height: 32px !important;
    }
  }
  .service-apply-Card__desc{
    font-size: 14px !important;
    font-weight: 400 !important;
    line-height: 20px !important;
    margin-top: 4px !important;
    margin-bottom: 0px !important;
    @media only screen and (min-width: 1024px) {
      font-size: 16px !important;
      line-height: 24px !important;
    }
  }
  .service-apply-Card__action{
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    margin: 0px;
    @media only screen and (min-width: 1024px) {
      font-size: 18px !important;
      line-height: 24px !important;
    }
  }

  &.error{
    background-color: $aered-50;
    color: $aered-600;
    .service-apply-Card__lbl{
      color: $aered-600;
      svg{
        fill: $aered-600;
      }
    }
    .service-apply-Card__title{
      color: $aered-600;
    }
    .service-apply-Card__sub-title{
      color: $aered-600;
    }
  }
}