import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  Renderer2,
  forwardRef,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { DecimalPipe } from '@angular/common';

@Directive({
  selector: '[appNumberOnly]',
  providers: [
    DecimalPipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => NumberOnlyDirective),
      multi: true,
    },
  ],
})
export class NumberOnlyDirective implements ControlValueAccessor {
  @Input() appNumberOnly: boolean = false;
  private maxValue: number = 1_000_000_000_000;

  private onChange: (value: number | null) => void = () => {};
  private onTouched: () => void = () => {};

  constructor(
    private el: ElementRef,
    private decimalPipe: DecimalPipe,
    private renderer: Renderer2
  ) {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    if (!this.appNumberOnly) return;

    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Escape',
      'Enter',
      '.',
      'ArrowLeft',
      'ArrowRight',
    ];

    if (allowedKeys.includes(event.key) || /^[0-9]$/.test(event.key)) {
      return;
    }

    event.preventDefault();
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent): void {
    if (!this.appNumberOnly) return;

    const pasted = event.clipboardData?.getData('text') ?? '';
    if (isNaN(Number(pasted))) {
      event.preventDefault();
    }
  }

  @HostListener('blur')
  onBlur(): void {
    let inputValue: string = this.el.nativeElement.value;
    inputValue = inputValue.replace(/,/g, '');
    const numericValue = parseFloat(inputValue);

    if (isNaN(numericValue)) {
      this.setValue('');
      this.onChange(null);
    } else {
      const limitedValue = Math.min(numericValue, this.maxValue);
      const formatted = this.decimalPipe.transform(limitedValue, '1.0-2') || '';
      this.setValue(formatted);
      this.onChange(limitedValue);
    }

    this.onTouched();
    this.el.nativeElement.dispatchEvent(new Event('change'));
  }

  @HostListener('focus')
  onFocus(): void {
    const value = this.el.nativeElement.value;
    this.setValue(value.replace(/,/g, ''));
  }

  writeValue(value: any): void {
    if (typeof value === 'number' && !isNaN(value)) {
      const formatted = this.decimalPipe.transform(value, '1.0-2') || '';
      this.setValue(formatted);
    } else {
      this.setValue('');
    }
  }

  registerOnChange(fn: (value: number | null) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {
    this.renderer.setProperty(this.el.nativeElement, 'disabled', isDisabled);
  }

  private setValue(val: string): void {
    this.renderer.setProperty(this.el.nativeElement, 'value', val);
  }
}
