import { DatePipe } from '@angular/common';
import { Injectable } from '@angular/core';
import { FormGroup, ValidatorFn } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class FormService {

  constructor(private datePipe: DatePipe) { }

  clearFields(form: FormGroup, fields: string[], clearValidators: boolean = false) {
    fields.forEach(field => {
      form.get(field)?.setValue('');
      if (clearValidators) {
        form.get(field)?.clearValidators();
        form.get(field)?.updateValueAndValidity();
      }
    });
  }
  clearValidators(form: FormGroup, fields: string[]) {
    fields.forEach(field => {
      form.get(field)?.clearValidators();
      form.get(field)?.updateValueAndValidity();
    });
  }
  addValidators(form: FormGroup, fields: string[], validators: ValidatorFn[]) {
    fields.forEach(field => {
      form.get(field)?.clearValidators();
      form.get(field)?.addValidators(validators);
      form.get(field)?.updateValueAndValidity();
    });
  }
  disableFields(form: FormGroup, fields: string[]) {
    fields.forEach(field => {
      form.get(field)?.disable();
    });
  }

  convertToDate(dateInput: string) {
    var date_components = dateInput.split("/");
    var day = parseInt(date_components[0]);
    var month = parseInt(date_components[1]);
    var year = parseInt(date_components[2]);
    return new Date(year, month - 1, day);
  }

  enableFields(form: FormGroup, fields: string[]) {
    fields.forEach(field => {
      form.get(field)?.enable();
    });
  }
  cleanText(text: string) {
    var resText = text?.replaceAll(',', ' ');
    return resText;
  }
  formatDate(input: string | null, format: string = 'dd/MM/yyyy') {
    var result = this.datePipe.transform(input, format);
    return result;
  }

  formatMobileNumber(input: string | null): string | null {
    if (!input) return null;

    if (input.startsWith('05')) {
      return '971' + input.slice(1);
    }
    return input;
  }
}
