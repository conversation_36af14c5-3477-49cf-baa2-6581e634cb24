$breakpoints: (
  xs: 0,
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  xxl: 1536px
);

/////////////////
// Colors
////////////////

$mocdyellow : #F4ECDD;

$aegold-50 : #F9F7ED;
$aegold-100 : #F2ECCF;
$aegold-200 : #E6D7A2;
$aegold-300 : #D7BC6D;
$aegold-400 : #CBA344;
$aegold-500 : #B68A35;
$aegold-600 : #92722A;
$aegold-700 : #7C5E24;
$aegold-800 : #6C4527;
$aegold-900 : #5D3B26;
$aegold-950 : #361E12;

$aered-50 : #FEF2F2;
$aered-100 : #FDE4E3;
$aered-200 : #FDCDCB;
$aered-300 : #FAAAA7;
$aered-400 : #F47A75;
$aered-500 : #EA4F49;
$aered-600 : #D83731;
$aered-700 : #B52520;
$aered-800 : #95231F;
$aered-900 : #7C2320;
$aered-950 : #430E0C;

$aegreen-50 : #F3FAF4;
$aegreen-100 : #E4F4E7;
$aegreen-200 : #CAE8CF;
$aegreen-300 : #A0D5AB;
$aegreen-400 : #6FB97F;
$aegreen-500 : #4A9D5C;
$aegreen-600 : #3F8E50;
$aegreen-700 : #2F663C;
$aegreen-800 : #2A5133;
$aegreen-900 : #24432B;
$aegreen-950 : #0F2415;

$aeblack-50 : #F7F7F7;
$aeblack-100 : #E1E3E5;
$aeblack-200 : #C3C6CB;
$aeblack-300 : #9EA2A9;
$aeblack-400 : #797E86;
$aeblack-500 : #5F646D;
$aeblack-600 : #4B4F58;
$aeblack-700 : #3E4046;
$aeblack-800 : #232528;
$aeblack-900 : #1B1D21;
$aeblack-950 : #0E0F12;

// secondary Palette

$techblue-50 : #E7F5FF;
$techblue-100 : #D3EDFF;
$techblue-200 : #B0DBFF;
$techblue-300 : #81C1FF;
$techblue-400 : #4F98FF;
$techblue-500 : #286CFF;
$techblue-600 : #043DFF;
$techblue-700 : #003CFF;
$techblue-800 : #002DC2;
$techblue-900 : #0B32A4;
$techblue-950 : #071C5F;

$seablue-50 : #EFFAFF;
$seablue-100 : #DEF3FF;
$seablue-200 : #B6EAFF;
$seablue-300 : #75DBFF;
$seablue-400 : #2CCAFF;
$seablue-500 : #00ABEB;
$seablue-600 : #0090D4;
$seablue-700 : #0073AB;
$seablue-800 : #00608D;
$seablue-900 : #065074;
$seablue-950 : #04334D;

$camelyellow-50 : #FFFBEB;
$camelyellow-100 : #FDF4C8;
$camelyellow-200 : #FBE68C;
$camelyellow-300 : #FAD44F;
$camelyellow-400 : #F8C027;
$camelyellow-500 : #F29F0E;
$camelyellow-600 : #D67909;
$camelyellow-700 : #B2550B;
$camelyellow-800 : #904110;
$camelyellow-900 : #773610;
$camelyellow-950 : #441B04;

$desertorange-50 : #FEF5EE;
$desertorange-100 : #FCE9D8;
$desertorange-200 : #F9CFAF;
$desertorange-300 : #F5AC7C;
$desertorange-400 : #EF8048;
$desertorange-500 : #EB5F24;
$desertorange-600 : #E54B1D;
$desertorange-700 : #B73417;
$desertorange-800 : #922B1A;
$desertorange-900 : #762518;
$desertorange-950 : #3F100B;

$fuchsia-50 : #FDF4FF;
$fuchsia-100 : #FAE8FF;
$fuchsia-200 : #F5D0FE;
$fuchsia-300 : #F0ABFC;
$fuchsia-400 : #E879F9;
$fuchsia-500 : #D946EF;
$fuchsia-600 : #C026D3;
$fuchsia-700 : #A21CAF;
$fuchsia-800 : #86198F;
$fuchsia-900 : #701A75;
$fuchsia-950 : #4A044E;

$slate-50 : #F8FAFC;
$slate-100 : #F1F5F9;
$slate-200 : #E2E8F0;
$slate-300 : #CBD5E1;
$slate-400 : #94A3B8;
$slate-500 : #64748B;
$slate-600 : #475569;
$slate-700 : #334155;
$slate-800 : #1E293B;
$slate-900 : #0F172A;
$slate-950 : #020617;

$white: #fff;

/////////////////
// font
////////////////
// $base-font-size: 16px;
// $base-line-height: 1px;
// $font: "Cairo", sans-serif;
// $font-normal: 400;
// $font-semi : 600;
// $font-bold : 700;

//- Semantic
// $sterling-medium-emphasis:rgba(187, 187, 187, 0.2);



////////////////////////
// Bootstrap Overrides
////////////////////////

$body-bg: $white;
$body-color: $aeblack-900;
// $theme-colors: (
//   "primary": $primary,
//   "secondary": $secondary,
// );
// $hr-border-color: $light-grey;
$container-max-widths: (
  sm: 100% ,
  md: 740px,
  lg: 980px,
  xl: 1240px,
  xxl: 1480px) !default;

$spacers: (
  0: 0,
  1: 0.25rem,
  2: 0.5rem,
  3: 0.75rem,
  4: 1rem,
  5: 1.25rem,
  6: 1.5rem,
  7: 1.75rem,
  8: 2rem,
  9: 2.25rem,
  10: 2.5rem,
  11: 2.75rem,
  12: 3rem,
  13: 3.25rem,
  14: 3.5rem,
  16: 4rem,
  20: 4.5rem,
  24: 5rem,
  26: 6rem,
  28: 7rem,
  32: 8rem,
  36: 9rem,
  40: 10rem,
);

/////////////////
// Fonts
////////////////

// Font weight
$font-extralight: 200;
$font-light: 300;
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
$font-extrabold: 800;
//////
