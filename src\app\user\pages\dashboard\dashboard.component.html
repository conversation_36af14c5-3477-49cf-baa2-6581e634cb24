
<!-- gradient for dashboard screen: bg-mocdyellow-gradient -->
<div class="container-fluid bg-mocdyellow-gradient dashboard-padding">
  <div class="container--dashboard container" *ngIf="auth.isAuthenticated()">
    <!-- need to remove this not in figma  -->
    <div class="alert alert-info d-none">
      <img src="assets/images/celebrate.gif" width="50">
      <div> {{'Welcome' | translate}}
        <span><a routerLink="/e-services">{{'dashboard.link' | translate}}</a></span>
      </div>
    </div>
    <!--/end need to remove this not in figma  -->

    <!-- <section class="pt-10 pb-40 "> -->
      <!-- user welcome card  -->
      <!-- <div class="section section--no-paddingTop"> -->
        <!-- <div class="container"> -->
          <div class="user-card desktop-only">
            <div class="d-flex flex-column flex-lg-row gap-6 ">
              <div class="user-card__img">
                <img src="../../../../assets/images/uae-placholder-image.png" />
              </div>
              <div>
                <h1 [lang]="lang.IsArabic ? 'ar':'en'" class="user-card__title">{{'Welcome'|translate}}
                  <ng-container *ngIf="lang.IsArabic; else englishFallback">
                    <ng-container *ngIf="userInfo.userInfo.firstnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameAR)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.firstnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameEN)}}
                    </span></ng-container>
                    <!-- <ng-container *ngIf="userInfo.userInfo.fullnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameAR)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.fullnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameEN)}}
                    </span></ng-container> -->
                </ng-container>
                <ng-template #englishFallback>
                    <ng-container *ngIf="userInfo.userInfo.firstnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameEN)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.firstnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameAR)}}
                    </span></ng-container>
                    <!-- <ng-container *ngIf="userInfo.userInfo.fullnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameEN)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.fullnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameAR)}}
                    </span></ng-container> -->
                </ng-template>

                </h1>
                <p class="user-card__desc">{{'dashboard-description'|translate}}</p>
              </div>
            </div>

            <div class="search-input-container">
              <form #searchForm="ngForm" (ngSubmit)="onSubmit(searchForm)" novalidate>
                <div class="aegov-form-control">
                  <div class="form-control-input">
                    <span class="">
                      <app-voice-search (speakResult)="onSpeakComplete($event)"></app-voice-search>
                    </span>
                    <!-- <span class="">
                      <a href="#" title="Some link text related description">
                        <svg class="" width="28" height="28" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 19.25C15.3919 19.2486 16.7265 18.695 17.7107 17.7107C18.695 16.7265 19.2486 15.3919 19.25 14V7C19.25 5.60761 18.6969 4.27226 17.7123 3.28769C16.7277 2.30312 15.3924 1.75 14 1.75C12.6076 1.75 11.2723 2.30312 10.2877 3.28769C9.30312 4.27226 8.75 5.60761 8.75 7V14C8.75145 15.3919 9.30504 16.7265 10.2893 17.7107C11.2735 18.695 12.6081 19.2486 14 19.25ZM10.5 7C10.5 6.07174 10.8687 5.1815 11.5251 4.52513C12.1815 3.86875 13.0717 3.5 14 3.5C14.9283 3.5 15.8185 3.86875 16.4749 4.52513C17.1313 5.1815 17.5 6.07174 17.5 7V14C17.5 14.9283 17.1313 15.8185 16.4749 16.4749C15.8185 17.1313 14.9283 17.5 14 17.5C13.0717 17.5 12.1815 17.1313 11.5251 16.4749C10.8687 15.8185 10.5 14.9283 10.5 14V7ZM14.875 22.7063V25.375C14.875 25.6071 14.7828 25.8296 14.6187 25.9937C14.4546 26.1578 14.2321 26.25 14 26.25C13.7679 26.25 13.5454 26.1578 13.3813 25.9937C13.2172 25.8296 13.125 25.6071 13.125 25.375V22.7063C10.9677 22.4867 8.96849 21.4751 7.51389 19.8669C6.05929 18.2588 5.25266 16.1684 5.25 14C5.25 13.7679 5.34219 13.5454 5.50628 13.3813C5.67038 13.2172 5.89294 13.125 6.125 13.125C6.35706 13.125 6.57962 13.2172 6.74372 13.3813C6.90781 13.5454 7 13.7679 7 14C7 15.8565 7.7375 17.637 9.05025 18.9497C10.363 20.2625 12.1435 21 14 21C15.8565 21 17.637 20.2625 18.9497 18.9497C20.2625 17.637 21 15.8565 21 14C21 13.7679 21.0922 13.5454 21.2563 13.3813C21.4204 13.2172 21.6429 13.125 21.875 13.125C22.1071 13.125 22.3296 13.2172 22.4937 13.3813C22.6578 13.5454 22.75 13.7679 22.75 14C22.7473 16.1684 21.9407 18.2588 20.4861 19.8669C19.0315 21.4751 17.0323 22.4867 14.875 22.7063Z"></path>
                        </svg>
                      </a>
                    </span> -->
                    <input
                      type="text"
                      class="px-0 h-[52px] lg:h-[60px]"
                      id="search"
                      name="searchQuery"
                      [(ngModel)]="searchQuery"
                      [placeholder]="'Search' | translate"
                      required
                    />
                    <span class="control-suffix">
                      <button class="aegov-btn btn-icon btn-lg inputLarge" type="submit">
                        <svg class="" width="24" height="20" viewBox="0 0 24 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M23.7075 10.7076L14.7075 19.7076C14.5199 19.8952 14.2654 20.0006 14 20.0006C13.7346 20.0006 13.4801 19.8952 13.2925 19.7076C13.1049 19.5199 12.9994 19.2654 12.9994 19.0001C12.9994 18.7347 13.1049 18.4802 13.2925 18.2926L20.5863 11.0001H1C0.734784 11.0001 0.48043 10.8947 0.292893 10.7072C0.105357 10.5196 0 10.2653 0 10.0001C0 9.73485 0.105357 9.4805 0.292893 9.29296C0.48043 9.10542 0.734784 9.00007 1 9.00007H20.5863L13.2925 1.70757C13.1049 1.51993 12.9994 1.26543 12.9994 1.00007C12.9994 0.734704 13.1049 0.480208 13.2925 0.292568C13.4801 0.104927 13.7346 -0.000488281 14 -0.000488281C14.2654 -0.000488281 14.5199 0.104927 14.7075 0.292568L23.7075 9.29257C23.8005 9.38544 23.8742 9.49573 23.9246 9.61713C23.9749 9.73853 24.0008 9.86865 24.0008 10.0001C24.0008 10.1315 23.9749 10.2616 23.9246 10.383C23.8742 10.5044 23.8005 10.6147 23.7075 10.7076Z"></path>
                        </svg>
                      </button>
                    </span>
                  </div>
                </div>
              </form>

            </div>
          </div>

          <!-- mobile user card -->
          <div class="user-card mobile-only">
            <div class="d-flex flex-column flex-lg-row gap-6 col-sm-6">
              <div class="user-card__img">
                <img src="../../../../assets/images/uae-placholder-image.png" />
              </div>
              <div>
                <h1 [lang]="lang.IsArabic ? 'ar':'en'" class="user-card__title">{{'Welcome'|translate}}
                  <ng-container *ngIf="lang.IsArabic; else englishFallback">
                    <ng-container *ngIf="userInfo.userInfo.firstnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameAR)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.firstnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameEN)}}
                    </span></ng-container>
                    <!-- <ng-container *ngIf="userInfo.userInfo.fullnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameAR)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.fullnameAR"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameEN)}}
                    </span></ng-container> -->
                </ng-container>
                <ng-template #englishFallback>
                    <ng-container *ngIf="userInfo.userInfo.firstnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameEN)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.firstnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.firstnameAR)}}
                    </span></ng-container>
                    <!-- <ng-container *ngIf="userInfo.userInfo.fullnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameEN)}}
                    </span></ng-container>
                    <ng-container *ngIf="!userInfo.userInfo.fullnameEN"><span class="text-capitalize">
                      {{ cleanText(userInfo.userInfo.fullnameAR)}}
                    </span></ng-container> -->
                </ng-template>

                </h1>
                <p class="user-card__desc">{{'dashboard-description'|translate}}</p>
              </div>
            </div>

            <div class="search-input-container col-sm-6">
              <form #searchForm="ngForm" (ngSubmit)="onSubmit(searchForm)" novalidate>
                <div class="aegov-form-control">
                  <div class="form-control-input">
                    <span class="">
                      <app-voice-search (speakResult)="onSpeakComplete($event)"></app-voice-search>
                    </span>
                    <!-- <span class="">
                      <a href="#" title="Some link text related description">
                        <svg class="" width="28" height="28" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">
                        <path d="M14 19.25C15.3919 19.2486 16.7265 18.695 17.7107 17.7107C18.695 16.7265 19.2486 15.3919 19.25 14V7C19.25 5.60761 18.6969 4.27226 17.7123 3.28769C16.7277 2.30312 15.3924 1.75 14 1.75C12.6076 1.75 11.2723 2.30312 10.2877 3.28769C9.30312 4.27226 8.75 5.60761 8.75 7V14C8.75145 15.3919 9.30504 16.7265 10.2893 17.7107C11.2735 18.695 12.6081 19.2486 14 19.25ZM10.5 7C10.5 6.07174 10.8687 5.1815 11.5251 4.52513C12.1815 3.86875 13.0717 3.5 14 3.5C14.9283 3.5 15.8185 3.86875 16.4749 4.52513C17.1313 5.1815 17.5 6.07174 17.5 7V14C17.5 14.9283 17.1313 15.8185 16.4749 16.4749C15.8185 17.1313 14.9283 17.5 14 17.5C13.0717 17.5 12.1815 17.1313 11.5251 16.4749C10.8687 15.8185 10.5 14.9283 10.5 14V7ZM14.875 22.7063V25.375C14.875 25.6071 14.7828 25.8296 14.6187 25.9937C14.4546 26.1578 14.2321 26.25 14 26.25C13.7679 26.25 13.5454 26.1578 13.3813 25.9937C13.2172 25.8296 13.125 25.6071 13.125 25.375V22.7063C10.9677 22.4867 8.96849 21.4751 7.51389 19.8669C6.05929 18.2588 5.25266 16.1684 5.25 14C5.25 13.7679 5.34219 13.5454 5.50628 13.3813C5.67038 13.2172 5.89294 13.125 6.125 13.125C6.35706 13.125 6.57962 13.2172 6.74372 13.3813C6.90781 13.5454 7 13.7679 7 14C7 15.8565 7.7375 17.637 9.05025 18.9497C10.363 20.2625 12.1435 21 14 21C15.8565 21 17.637 20.2625 18.9497 18.9497C20.2625 17.637 21 15.8565 21 14C21 13.7679 21.0922 13.5454 21.2563 13.3813C21.4204 13.2172 21.6429 13.125 21.875 13.125C22.1071 13.125 22.3296 13.2172 22.4937 13.3813C22.6578 13.5454 22.75 13.7679 22.75 14C22.7473 16.1684 21.9407 18.2588 20.4861 19.8669C19.0315 21.4751 17.0323 22.4867 14.875 22.7063Z"></path>
                        </svg>
                      </a>
                    </span> -->
                    <input
                      type="text"
                      class="px-0 h-[52px] lg:h-[60px]"
                      id="search"
                      name="searchQuery"
                      [(ngModel)]="searchQuery"
                      [placeholder]="'Search' | translate"
                      required
                    />
                    <span class="control-suffix">
                      <button class="aegov-btn btn-icon btn-lg inputLarge" type="submit">
                        <svg class="" width="24" height="20" viewBox="0 0 24 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M23.7075 10.7076L14.7075 19.7076C14.5199 19.8952 14.2654 20.0006 14 20.0006C13.7346 20.0006 13.4801 19.8952 13.2925 19.7076C13.1049 19.5199 12.9994 19.2654 12.9994 19.0001C12.9994 18.7347 13.1049 18.4802 13.2925 18.2926L20.5863 11.0001H1C0.734784 11.0001 0.48043 10.8947 0.292893 10.7072C0.105357 10.5196 0 10.2653 0 10.0001C0 9.73485 0.105357 9.4805 0.292893 9.29296C0.48043 9.10542 0.734784 9.00007 1 9.00007H20.5863L13.2925 1.70757C13.1049 1.51993 12.9994 1.26543 12.9994 1.00007C12.9994 0.734704 13.1049 0.480208 13.2925 0.292568C13.4801 0.104927 13.7346 -0.000488281 14 -0.000488281C14.2654 -0.000488281 14.5199 0.104927 14.7075 0.292568L23.7075 9.29257C23.8005 9.38544 23.8742 9.49573 23.9246 9.61713C23.9749 9.73853 24.0008 9.86865 24.0008 10.0001C24.0008 10.1315 23.9749 10.2616 23.9246 10.383C23.8742 10.5044 23.8005 10.6147 23.7075 10.7076Z"></path>
                        </svg>
                      </button>
                    </span>
                  </div>
                </div>
              </form>

            </div>
          </div>

        <!-- </div> -->

      <!-- </div> -->
      <!--/end user welcome card  -->

      <!-- services status cards  -->
      <!-- <div class="section"  *ngIf="data"> -->
        <!-- <div class="container"> -->
          <!-- <div> -->


            <!-- please add attribute dir="rtl" to  <div class="swiper service-status-card-swiper">  in arabic -->
            <!-- <div class="swiper service-status-card-swiper" [dir]="lang.IsArabic?'rtl':'ltr'"> -->
              <!-- <div class="swiper-wrapper"> -->
              <div *ngIf="data" class="d-flex gap-4 flex-wrap">
                <!-- <div class="swiper-slide"> -->
                  <div class="service-status-card flex-fill">
                    <div class="d-flex justify-content-between flex-column-reverse flex-sm-row gap-2">
                      <div class="service-status-card__status-wrapper">
                        <label class="service-status-card__lbl">{{'In progress' | translate}}</label>
                        <label class="service-status-card__status">{{data.InProgressRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-progress">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                          <path d="M12 22.5C17.5228 22.5 22 18.0228 22 12.5C22 6.97715 17.5228 2.5 12 2.5C6.47715 2.5 2 6.97715 2 12.5C2 18.0228 6.47715 22.5 12 22.5Z" stroke="#F29F0E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M12 6.5V12.5L8 10.5" stroke="#F29F0E" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/InProgress" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>
                <!-- </div> -->
                <!-- <div class="swiper-slide"> -->
                  <div class="service-status-card flex-fill">
                    <div class="d-flex justify-content-between flex-column-reverse flex-sm-row gap-2">
                      <div class="service-status-card__status-wrapper">
                        <label class="service-status-card__lbl">{{'Completed' | translate}}</label>
                        <label class="service-status-card__status">{{data.CompletedRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-completed">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                          <path d="M12 22.5C17.523 22.5 22 18.023 22 12.5C22 6.977 17.523 2.5 12 2.5C6.477 2.5 2 6.977 2 12.5C2 18.023 6.477 22.5 12 22.5Z" stroke="#4A9D5C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          <path d="M9 12.5L11 14.5L15 10.5" stroke="#4A9D5C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                          </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/Completed" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>
                <!-- </div> -->
                <!-- <div class="swiper-slide"> -->
                  <div class="service-status-card flex-fill">
                    <div class="d-flex justify-content-between flex-column-reverse flex-sm-row gap-2">
                      <div class="service-status-card__status-wrapper">
                        <label class="service-status-card__lbl"> {{'Returned' | translate}}</label>
                        <label class="service-status-card__status">{{data.ReturnedRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-returned">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                          <path d="M21.7501 14.0001C21.7484 15.5908 21.1157 17.1159 19.9908 18.2408C18.866 19.3656 17.3409 19.9983 15.7501 20.0001H7.5001C7.30119 20.0001 7.11042 19.921 6.96977 19.7804C6.82912 19.6397 6.7501 19.449 6.7501 19.2501C6.7501 19.0511 6.82912 18.8604 6.96977 18.7197C7.11042 18.5791 7.30119 18.5001 7.5001 18.5001H15.7501C16.9436 18.5001 18.0882 18.0259 18.9321 17.182C19.776 16.3381 20.2501 15.1935 20.2501 14.0001C20.2501 12.8066 19.776 11.662 18.9321 10.8181C18.0882 9.97416 16.9436 9.50005 15.7501 9.50005H4.81041L8.03073 12.7194C8.17146 12.8602 8.25052 13.051 8.25052 13.2501C8.25052 13.4491 8.17146 13.6399 8.03073 13.7807C7.89 13.9214 7.69912 14.0005 7.5001 14.0005C7.30108 14.0005 7.11021 13.9214 6.96948 13.7807L2.46948 9.28068C2.39974 9.21102 2.34442 9.1283 2.30668 9.03726C2.26894 8.94621 2.24951 8.84861 2.24951 8.75005C2.24951 8.65149 2.26894 8.55389 2.30668 8.46285C2.34442 8.3718 2.39974 8.28908 2.46948 8.21943L6.96948 3.71943C7.11021 3.5787 7.30108 3.49963 7.5001 3.49963C7.69912 3.49963 7.89 3.5787 8.03073 3.71943C8.17146 3.86016 8.25052 4.05103 8.25052 4.25005C8.25052 4.44907 8.17146 4.63995 8.03073 4.78068L4.81041 8.00005H15.7501C17.3409 8.00179 18.866 8.63449 19.9908 9.75933C21.1157 10.8842 21.7484 12.4093 21.7501 14.0001Z" fill="#D83731"/>
                        </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/Returned" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>
                <!-- </div> -->
                <!-- <div class="swiper-slide"> -->
                  <div class="service-status-card flex-fill">
                    <div class="d-flex justify-content-between flex-column-reverse flex-sm-row gap-2">
                      <div class="service-status-card__status-wrapper">
                        <label class="service-status-card__lbl">{{'Ready To Pay' | translate}}</label>
                        <label class="service-status-card__status">{{data.PendingPaymentRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-ready">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                          <path d="M21 5H3C2.60218 5 2.22064 5.15804 1.93934 5.43934C1.65804 5.72064 1.5 6.10218 1.5 6.5V18.5C1.5 18.8978 1.65804 19.2794 1.93934 19.5607C2.22064 19.842 2.60218 20 3 20H21C21.3978 20 21.7794 19.842 22.0607 19.5607C22.342 19.2794 22.5 18.8978 22.5 18.5V6.5C22.5 6.10218 22.342 5.72064 22.0607 5.43934C21.7794 5.15804 21.3978 5 21 5ZM21 6.5V8.75H3V6.5H21ZM21 18.5H3V10.25H21V18.5ZM19.5 16.25C19.5 16.4489 19.421 16.6397 19.2803 16.7803C19.1397 16.921 18.9489 17 18.75 17H15.75C15.5511 17 15.3603 16.921 15.2197 16.7803C15.079 16.6397 15 16.4489 15 16.25C15 16.0511 15.079 15.8603 15.2197 15.7197C15.3603 15.579 15.5511 15.5 15.75 15.5H18.75C18.9489 15.5 19.1397 15.579 19.2803 15.7197C19.421 15.8603 19.5 16.0511 19.5 16.25ZM13.5 16.25C13.5 16.4489 13.421 16.6397 13.2803 16.7803C13.1397 16.921 12.9489 17 12.75 17H11.25C11.0511 17 10.8603 16.921 10.7197 16.7803C10.579 16.6397 10.5 16.4489 10.5 16.25C10.5 16.0511 10.579 15.8603 10.7197 15.7197C10.8603 15.579 11.0511 15.5 11.25 15.5H12.75C12.9489 15.5 13.1397 15.579 13.2803 15.7197C13.421 15.8603 13.5 16.0511 13.5 16.25Z" fill="#286CFF"/>
                        </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/PendingPayment" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>
                <!-- </div> -->
                <!-- <div class="swiper-slide"> -->
                  <div class="service-status-card flex-fill draft-service-status-card desktop-only">
                    <div class="d-flex justify-content-between flex-column-reverse flex-sm-row gap-2">
                      <div class="service-status-card__status-wrapper">
                        <label class="service-status-card__lbl">{{'Draft' | translate}}</label>
                        <label class="service-status-card__status">{{data.DraftRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-draft">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
                          <path d="M20.0306 8.21938L14.7806 2.96938C14.7109 2.89975 14.6282 2.84454 14.5371 2.8069C14.4461 2.76926 14.3485 2.74992 14.25 2.75H5.25C4.85218 2.75 4.47064 2.90804 4.18934 3.18934C3.90804 3.47064 3.75 3.85218 3.75 4.25V20.75C3.75 21.1478 3.90804 21.5294 4.18934 21.8107C4.47064 22.092 4.85218 22.25 5.25 22.25H18.75C19.1478 22.25 19.5294 22.092 19.8107 21.8107C20.092 21.5294 20.25 21.1478 20.25 20.75V8.75C20.2501 8.65148 20.2307 8.55391 20.1931 8.46286C20.1555 8.37182 20.1003 8.28908 20.0306 8.21938ZM15 5.31031L17.6897 8H15V5.31031ZM18.75 20.75H5.25V4.25H13.5V8.75C13.5 8.94891 13.579 9.13968 13.7197 9.28033C13.8603 9.42098 14.0511 9.5 14.25 9.5H18.75V20.75Z" fill="#3E4046"/>
                        </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/Draft" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>

                  <!-- mobile draft status card -->
                  <div class="service-status-card flex-fill draft-service-status-card mobile-only">
                    <div class="d-flex flex-sm-row-reverse gap-2">
                      <div class="service-status-card__status-wrapper draft-status-wrapper">
                        <label class="service-status-card__lbl">{{'Draft' | translate}}</label>
                        <label class="service-status-card__status">{{data.DraftRequests}}</label>
                      </div>
                      <div class="service-status-card__icon success-draft">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none">
                          <path d="M20.0306 8.21938L14.7806 2.96938C14.7109 2.89975 14.6282 2.84454 14.5371 2.8069C14.4461 2.76926 14.3485 2.74992 14.25 2.75H5.25C4.85218 2.75 4.47064 2.90804 4.18934 3.18934C3.90804 3.47064 3.75 3.85218 3.75 4.25V20.75C3.75 21.1478 3.90804 21.5294 4.18934 21.8107C4.47064 22.092 4.85218 22.25 5.25 22.25H18.75C19.1478 22.25 19.5294 22.092 19.8107 21.8107C20.092 21.5294 20.25 21.1478 20.25 20.75V8.75C20.2501 8.65148 20.2307 8.55391 20.1931 8.46286C20.1555 8.37182 20.1003 8.28908 20.0306 8.21938ZM15 5.31031L17.6897 8H15V5.31031ZM18.75 20.75H5.25V4.25H13.5V8.75C13.5 8.94891 13.579 9.13968 13.7197 9.28033C13.8603 9.42098 14.0511 9.5 14.25 9.5H18.75V20.75Z" fill="#3E4046"/>
                        </svg>
                      </div>
                    </div>
                    <div class="service-status-card__action">
                      <a routerLink="/user-pages/my-applications/Draft" class="aegov-link " title="View All">
                        {{'View All' | translate}}
                         <svg class="" width="22" height="22" viewBox="0 0 24 24" fill="#92722A" xmlns="http://www.w3.org/2000/svg">
                          <path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.2806 11.4694C16.3504 11.539 16.4057 11.6217 16.4434 11.7128C16.4812 11.8038 16.5006 11.9014 16.5006 12C16.5006 12.0986 16.4812 12.1962 16.4434 12.2872C16.4057 12.3783 16.3504 12.461 16.2806 12.5306L13.2806 15.5306C13.1399 15.6714 12.949 15.7504 12.75 15.7504C12.551 15.7504 12.3601 15.6714 12.2194 15.5306C12.0786 15.3899 11.9996 15.199 11.9996 15C11.9996 14.801 12.0786 14.6101 12.2194 14.4694L13.9397 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H13.9397L12.2194 9.53063C12.0786 9.38989 11.9996 9.19902 11.9996 9C11.9996 8.80098 12.0786 8.61011 12.2194 8.46937C12.3601 8.32864 12.551 8.24958 12.75 8.24958C12.949 8.24958 13.1399 8.32864 13.2806 8.46937L16.2806 11.4694Z"></path>
                        </svg>
                        <span class="sr-only">{{'View All' | translate}}</span>
                      </a>

                    </div>
                  </div>
                <!-- </div> -->
              </div>
              <!-- </div> -->
              <!-- <div class="swiper-pagination"></div> -->
            <!-- </div> -->
          <!-- </div> -->

        <!-- </div> -->

      <!-- </div> -->
      <!--/end services status cards   -->
    <!-- </section> -->


    <!-- <section class="mt-n40"> -->
      <!-- Application Overview table -->
      <!-- <div class="section" > -->
      <!-- <div class="" > -->
        <!-- <app-applications [status]="'-1'" [disabled]="false" *ngIf="auth.isAuthenticated()"></app-applications>
        @if(allowOnDevOnly || allowOnTestOnly)
        {
          <app-inquiry-tracker [status]="'-1'" [disabled]="false" *ngIf="auth.isAuthenticated()"></app-inquiry-tracker>
        } -->
        
      <div class="container">
  <section class="eServices">
    <!-- <div class="row">
      <div class="col col-12">
        <h1 class="eServices_title">
          {{ "legalTypePage.title" | translate }}
        </h1>
        <p class="eServices_sub-title">
          {{
          "legalTypePage.selectNpoLegalForm"
          | translate
          }}
        </p>
 
      </div>
    </div> -->
    <div class="row eService_links" [class.is-arabic]="lang.IsArabic">
      @for (appType of applicationsTypes; track appType.ID) {
      <div class="col col-12">
        <div [ngStyle]="
            lang.IsArabic
              ? {
                  'border-right': '8px solid #B68A35',
                  'border-radius': '0 8px 8px 0'
                }
              : {
                  'border-left': '8px solid #B68A35',
                  'border-radius': '8px 0 0 8px'
                }
          " class="card text-left" (click)="redirectToType(appType.ID)">
          <div class="card-body d-flex justify-content-between align-items-center p-0">
            <h5 [ngStyle]="
                lang.IsArabic
                  ? {
                      'padding-right': '40px',
                      '': ''
                    }
                  : {
                      'padding-left': '40px'
                    }
              " class="card-title m-0">
              {{
              lang.IsArabic
              ? appType.NameArabic
              : appType.NameEnglish
              }}
            </h5>
            <span class="go-to_lnk"><i [ngClass]="
                  lang.IsArabic
                    ? 'fa-circle-chevron-left'
                    : 'fa-circle-chevron-right'
                " class="fa-solid"></i></span>
          </div>
        </div>
      </div>
      }
      <ng-container></ng-container>
    </div>
  </section>
</div>


      <!-- </div> -->
      <!-- </div> -->
      <!--/end Application Overview table -->
      <app-modal [form]="modalForm" [title]="'Add Profile'" [viewContentOnly]="false" [buttonEnabled]="false"
        [buttonClass]="'btn btn-primary'" (submitAction)="submitModal($event)">
        <app-input [label]="'First Name'" [control]="f.firstName"></app-input>
      </app-modal>



</div>
</div>










