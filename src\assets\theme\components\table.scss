.my-table {
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  width: 100%;
  border: 1px solid $aegold-400;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
  padding: 0;
  // margin-bottom: 28px !important;

  &>tbody>tr:nth-of-type(even) {
    background-color: $aeblack-50;
    border-top: 1px solid #d7d7d7;
    border-bottom: 1px solid #d7d7d7;
  }

  th {
    height: 68px;
    // text-wrap: nowrap;
  }

  td {
    background-color: transparent;
    height: 62px;
    text-wrap: wrap;
  }

  th,
  td {
    border: none;
    color: $aeblack-800;
    padding: 10px;
    text-align: center;
  }

  thead th {
    background-color: $aegold-50;
    font-weight: $font-medium;
    font-size: 16px;
  }

  tbody td {
    font-size: 14px;
    font-weight: 400;
  }

  &.table_vertical-mobile {
    @media (max-width: 767px) {
      display: flex;
      flex-direction: column;
      width: 100%;
      background-color: $white;
      border: 0;

      /* Header styles */
      thead {
        flex: 0 0 40%;
        max-width: 40%;

        tr {
          display: flex;
          flex-direction: column;

          th {
            color: $aegold-600;
            font-weight: $font-bold;
            text-align: left;
            /* Ensure text aligns properly */
            padding: 8px 12px;
            background-color: #f4f4f4;
            /* Optional: Light background for header */
          }
        }
      }

      /* Body styles */
      tbody {
        flex: 1 0 60%;
        max-width: 60%;

        tr {
          display: flex;
          width: 100%;
          margin-bottom: 16px;
          background-color: $aegold-50;
          border-radius: 6px;
          padding: 16px 12px;
          flex-wrap: wrap;
          align-items: center;
          /* Ensure alignment of content */
        }

        td {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 5px 0;
          flex: 1;
          width: 100%;
        }

        td:first-child {
          max-width: 50%;
          flex: 1 0 50%;
          justify-content: flex-start;
        }

        td:nth-child(2),
        td:nth-child(3) {
          flex: 1 0 100%;
          width: 100%;
        }

        td:last-child {
          max-width: 50%;
          flex: 1 0 50%;
          justify-content: flex-end;
        }

        /* Align td and th heights */
        th,
        td {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        /* Optional: Icon button styles */
        .mat-mdc-icon-button.mat-mdc-button-base {
          padding: 0;
          height: max-content;
          text-align: right;
        }
      }
    }
  }
}

/////////////////////
/// actions cell
/* Hide the 3-dots button on larger screens */
@media (min-width: 992px) {
  .actions-menu-trigger {
    display: none !important;
  }

  .actions-direct-buttons {
    display: inline-flex;
    // gap: 10px;
    align-items: center;
    justify-content: center;

    .mat-mdc-menu-item {

      &:hover,
      &:focus {
        background: none !important;
      }

      .mat-icon-no-color,
      .mat-mdc-menu-item .mat-mdc-menu-submenu-icon {
        color: $aegold-600;
        margin: 0;
        width: 22px;
        height: 22px;
        font-size: 22px;

        &:hover,
        &:focus {
          color: $aegold-400;
        }
      }

      &-text {
        display: none;
      }

      &:has(.material-icons, mat-icon, [matButtonIcon]) {
        padding: 0 !important;
        width: max-content !important;

        &+p {
          display: none;
        }
      }
    }
  }
}

/* Show the 3-dots button on smaller screens */
@media (max-width: 991px) {
  .actions-menu-trigger {
    display: inline-flex;
  }

  .actions-direct-buttons {
    display: none;
  }
}

/******** Responsive Table *********/
@media (max-width: 1024px) {
  .d-block_mobile {
    display: none !important;
    border: 0;
    box-shadow: none;

    th,
    td {
      display: block;
      width: 100%;
    }

    thead {
      display: none;
      /* Hide headers on mobile */
    }

    tbody {
      display: block;
      margin-top: 20px;
    }

    tr {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      border: 0;
      border-radius: 6px;
      padding: 16px 12px;
      background-color: $aegold-50;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;
    }

    td {
      // display: flex;
      padding: 0;

      &.ar_txt {
        text-align: right !important;
      }
    }


    /* Add labels for each field (vertical layout) */
    td::before {
      content: attr(data-label);
      // content: attr(data-label) ":";
      font-size: 14px;
      margin-right: 4px;
      color: $aegold-600;
      width: 100%;
      display: block;
      text-align: left;

    }
  }
}

////////////////////////////////
/// /** table sort and search **/
/* Container for the search and sort options */
.table-controls {
  display: flex;
  gap: 16px;
  // margin-bottom: 16px;
  align-items: center;
  justify-content: end;

  /* Search input container */
  .search-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    max-width: calc(100% - (16px + 114px));

    .search-input {
      padding: 0 16px;
      padding-left: calc(12px + 28px + 16px);
      /* Adjust for icon space */
      border: 1px solid $aegold-400;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
      height: 52px;
      width: 294px;
      gap: 12px;
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1);
      max-width: 100%;

      &:focus {
        border-color: $aegold-600;
      }

      &::placeholder {
        color: $aeblack-200;
        font-size: 14px;
      }
    }

    /* Position the search icon */
    .search-icon {
      position: absolute;
      left: 16px;
      color: $aegold-600;
      pointer-events: none;
      font-size: 30px;
    }
  }

  /* Sort dropdown container */
  .sort-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: $aegold-50;
    height: 52px;
    width: 114px;
    min-width: 114px;

    .sort-select {
      appearance: none;
      padding: 0 12px;
      padding-left: calc(8px + 12px + 16px);
      /* Space for icon */
      border: 1px solid $aegold-400;
      border-radius: 6px;
      font-size: 14px;
      outline: none;
      color: $aegold-500;
      height: 100%;
      width: 100%;

      &:focus {
        border-color: $aegold-600;
      }
    }

    /* Position the sort icon */
    .sort-icon {
      position: absolute;
      left: 12px;
      color: $aegold-500;
      pointer-events: none;
      font-size: 23px;
    }

    .down_arrow {
      position: absolute;
      right: 12px;
      color: $aegold-500;
      font-size: 15px;
    }
  }

  @media (max-width: 767px) {
    justify-content: start;
  }
}

.cursor-pointer {
  cursor: pointer;
}

body [ng-reflect-dir=rtl],
body [dir=rtl] {
  .table-controls .sort-container {
    .sort-icon {
      right: 12px;
      left: auto;
    }

    .down_arrow {
      left: 12px;
      right: auto;
    }

    .sort-select {
      padding: 0 12px;
      padding-right: 36px;
    }
  }

  .table {

    td,
    th {
      text-align: right;
    }
  }
}

.figma-card-container {
  @media(min-width: 1024px) {
    display: none;
  }

  margin: 10px 0 24px 0 !important;

  display: flex;
  gap: 16px;
  flex-direction: column;

  .figma-card {
    position: relative;
    width: 100%;
    border: 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 10px;
    background-color: rgba(249, 247, 237, 1);
  }

  .figma-actions-menu-trigger {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    color: #B68A35 !important;

    &:dir(rtl){
      left: 0 !important;
      right: unset !important;
    }
  }

  .figma-card-field {
    margin-bottom: 0.5rem;
  }

  .static-value {
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    color: rgba(146, 114, 42, 1);
  }

  .dynamic-value {
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: rgba(35, 37, 40, 1);
  }

  .figma-card-content {
    overflow: hidden !important;
  }

}

.mat-mdc-menu-content {
  display: flex;
  align-items: center;
  justify-content: center;
}