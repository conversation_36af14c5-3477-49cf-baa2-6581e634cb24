<div class="container-fluid">
  <mat-accordion>
    <!-- NPO Details -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'npoDetails') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoNameEn'" [isPlain]="true"
          [value]="currentEstablishment?.BasicInformationForm?.name"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoNameAr'" [isPlain]="true"
          [value]="currentEstablishment?.BasicInformationForm?.namear"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'npoLegalForm'" [isPlain]="true"
          [value]="getLegalFormType(currentEstablishment?.BasicInformationForm?.npoform)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoDeclarationDecision'"
          [isPlain]="true" [value]="formData?.NPODetails?.NpoDeclarationDecisionLink"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MainCategory'" [isPlain]="true"
          [value]="currentEstablishment?.BasicInformationForm?.MainCategory"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'HeadquarterEmirate'"
          [isPlain]="true" [value]="getEmirate(currentEstablishment?.BasicInformationForm?.emirate)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'LicensingEntity'" [isPlain]="true"
          [value]="getLicensingEntity(currentEstablishment?.BasicInformationForm?.LicensingAuthority)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'LicenseNumber'" [isPlain]="true"
          [value]="currentEstablishment?.BasicInformationForm?.LicenseNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'LicenseIssuanceDate'"
          [isDate]="true" [value]="currentEstablishment?.BasicInformationForm?.IssuanceDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'LicenseExpiryDate'"
          [isDate]="true" [value]="currentEstablishment?.BasicInformationForm?.Licenseexpirydate"></div>
      </div>
    </mat-expansion-panel>

    <!-- Donor Details -->
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{ messageTranslationPrefix+'donorDetails' | translate }}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display" [label]="messageTranslationPrefix+'donorType'"
          [isPlain]="true"
          [value]="LanguageService.IsArabic ? getDonorType(formData?.DonorDetails?.DonorType)?.NameArabic : getDonorType(formData?.DonorDetails?.DonorType)?.NameEnglish">
        </div>

        @if(formData?.DonorDetails?.DonorType === donorTypesEnum.Individual){
        @if(selectedLegalFormType === requestType.OutsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'fullName'" [isPlain]="true"
          [value]="formData?.DonorDetails?.FullName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'nationality'" [isPlain]="true"
          [value]="getNationality(formData?.DonorDetails?.Nationality)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'placeOfBirth'" [isPlain]="true"
          [value]="getCountry(formData?.DonorDetails?.PlaceofBirth)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'currentResidence'"
          [isPlain]="true" [value]="getCountry(formData?.DonorDetails?.CurrentResidence)">
        </div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'mobile'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber">
        </div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'passportNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PassportNumber">
        </div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'passportType'" [isPlain]="true"
          [value]="getPassportType(formData?.DonorDetails?.PassportType)"></div>
        @if(formData?.DonorDetails?.PassportType?.value?.ID === 4){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'otherPassportType'"
          [isPlain]="true" [value]="formData?.DonorDetails?.OtherPassportType"></div>
        }
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'passportExpiryDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.PassportExpiryDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'jobTitle'" [isPlain]="true"
          [value]="formData?.DonorDetails?.JobTitle"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'employer'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Employer"></div>
        } 
        
        @if(selectedLegalFormType === requestType.InsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'dateOfBirth'" [isDate]="true"
          [value]="formData?.DonorDetails?.individualDonorDetails?.DateofBirth"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'emiratesId'" [isPlain]="true"
          [value]="formData?.DonorDetails?.individualDonorDetails?.EmirateId"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'jobTitle'" [isPlain]="true"
          [value]="formData?.DonorDetails?.JobTitle"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'employer'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Employer"></div>
        }
        }

        @if(formData?.DonorDetails?.DonorType === donorTypesEnum.Government_Sector){
        @if(selectedLegalFormType === requestType.OutsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityName'" [isPlain]="true"
          [value]="formData?.DonorDetails?.CompanyOrEntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'country'" [isPlain]="true"
          [value]="getCountry(formData?.DonorDetails?.Country)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'city'" [isPlain]="true"
          [value]="formData?.DonorDetails?.City"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'businessDomain'" [isPlain]="true"
          [value]="formData?.DonorDetails?.BusinessDomain"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'phoneNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        } 
        
        @if(selectedLegalFormType === requestType.InsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityName'" [isPlain]="true"
          [value]="formData?.DonorDetails?.CompanyOrEntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityType'" [isPlain]="true"
          [value]="getEntityLegalFormType(formData?.DonorDetails?.EntityType)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'emirate'" [isPlain]="true"
          [value]="getEmirate(formData?.DonorDetails?.Emirate)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'phoneNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        }
        }

        @if(formData?.DonorDetails?.DonorType === donorTypesEnum.Private_Sector){
        @if(selectedLegalFormType === requestType.OutsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityOrCompanyName'"
          [isPlain]="true" [value]="formData?.DonorDetails?.CompanyOrEntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'country'" [isPlain]="true"
          [value]="getCountry(formData?.DonorDetails?.Country)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'city'" [isPlain]="true"
          [value]="formData?.DonorDetails?.City"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licensingAuthority'"
          [isPlain]="true" [value]="formData?.DonorDetails?.OutsideLicensingAuthority"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'commercialLicenseNumber'"
          [isPlain]="true" [value]="formData?.DonorDetails?.CommercialLicenseNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseIssuanceDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseIssuanceDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseExpiryDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseExpiryDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'phoneNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        } 
        
        @if(selectedLegalFormType === requestType.InsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityOrCompanyName'"
          [isPlain]="true" [value]="formData?.DonorDetails?.CompanyOrEntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'emirate'" [isPlain]="true"
          [value]="getEmirate(formData?.DonorDetails?.Emirate)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licensingAuthority'"
          [isPlain]="true" [value]="getLicensingEntity(formData?.DonorDetails?.LicensingAuthority)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'commercialLicenseNumber'"
          [isPlain]="true" [value]="formData?.DonorDetails?.CommercialLicenseNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseIssuanceDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseIssuanceDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseExpiryDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseExpiryDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'phoneNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        }

        }

        @if(formData?.DonorDetails?.DonorType === donorTypesEnum.NPO_Sector){
        @if(selectedLegalFormType === requestType.OutsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'entityName'" [isPlain]="true"
          [value]="formData?.DonorDetails?.EntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'country'" [isPlain]="true"
          [value]="getCountry(formData?.DonorDetails?.Country)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'city'" [isPlain]="true"
          [value]="formData?.DonorDetails?.City"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licensingAuthority'"
          [isPlain]="true" [value]="getLicensingEntity(formData?.DonorDetails?.LicensingAuthority)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.LicenseNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseIssuanceDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseIssuanceDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseExpiryDate'"
          [isDate]="true" [value]="formData?.DonorDetails?.LicenseExpiryDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'mainCategory'" [isPlain]="true"
          [value]="getMainCategory(formData?.DonorDetails?.MainCategory)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'phoneNumber'" [isPlain]="true"
          [value]="formData?.DonorDetails?.PhoneNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'email'" [isPlain]="true"
          [value]="formData?.DonorDetails?.Email"></div>
        }
        
        @if(selectedLegalFormType === requestType.InsideUae){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoNameAr'" [isPlain]="true"
          [value]="npoEstablishmentDonor?.NPONameArabic"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoNameEn'" [isPlain]="true"
          [value]="npoEstablishmentDonor?.NPONameEnglish"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoLegalForm'" [isPlain]="true"
          [value]="getLegalFormType(npoEstablishmentDonor?.NPOLegalForm)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NpoDeclarationDecision'"
          [isPlain]="true" [value]="npoEstablishmentDonor?.DeclarationDecisionLink"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'mainCategory'" [isPlain]="true"
          [value]="getMainCategory(npoEstablishmentDonor?.MainCategory)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'headquartersEmirate'"
          [isPlain]="true" [value]="getEmirate(npoEstablishmentDonor?.HeadquarterEmirate)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licensingEntity'" [isPlain]="true"
          [value]="getLicensingEntity(npoEstablishmentDonor?.LicensingEntity)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseNumber'" [isPlain]="true"
          [value]="npoEstablishmentDonor?.LicenseNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseIssuanceDate'"
          [isDate]="true" [value]="npoEstablishmentDonor?.LicenseIssuanceDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'licenseExpiryDate'"
          [isDate]="true" [value]="npoEstablishmentDonor?.LicenseExpiryDate"></div>
        }
        }
      </div>
    </mat-expansion-panel>

    <!-- Type Of Donations -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'typeOfDonations') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        @if(LanguageService.IsArabic){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'typeOfCashDonations'"
          [isPlain]="true" [value]="formData?.TypeOfDonations?.IsCashDonations === true ? 'نعم' : 'لا'"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'typeOfInkindDonations'"
          [isPlain]="true" [value]="formData?.TypeOfDonations?.IsInKindDonations === true ? 'نعم' : 'لا'"></div>
        } @else {
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'typeOfCashDonations'"
          [isPlain]="true" [value]="formData?.TypeOfDonations?.IsCashDonations === true ? 'Yes' : 'No'"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'typeOfInkindDonations'"
          [isPlain]="true" [value]="formData?.TypeOfDonations?.IsInKindDonations === true ? 'Yes' : 'No'"></div>
        }
      </div>

      @if(formData?.TypeOfDonations?.IsCashDonations === true){
      <div class="not_card">
        <h4 class="title_wrapper">
          {{messageTranslationPrefix+'typeOfCashDonations' | translate}}
        </h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"cashDonationTypeEn" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"cashDonationTypeAr" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"currencyType" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"approximateValueAed" |translate}}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let cashDonationType of paginateDataCashDonationTypes; let i = index">
                <td class="align-middle text-center">
                  {{ (pageCashDonationTypes - 1) * pageSizeCashDonationTypes + i + 1 }}
                </td>
                <td class="align-middle text-center">
                  {{ getCashDonationType(cashDonationType?.CashDonationType)?.NameEnglish }}
                </td>
                <td class="align-middle text-center">
                  {{ getCashDonationType(cashDonationType?.CashDonationType)?.NameArabic }}
                </td>
                <td class="align-middle text-center">
                  {{ cashDonationType?.CurrencyType }}
                </td>
                <td class="align-middle text-center">
                  {{ cashDonationType?.ApproximateValueinAED }}
                </td>

              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div class="figma-review-card-container">
        <div class="figma-card-container">
          @for (cashDonationType of paginateDataCashDonationTypes; track $index){
          <div class="figma-card">
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ $index + 1 }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'cashDonationTypeEn' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ getCashDonationType(cashDonationType?.CashDonationType) }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'cashDonationTypeAr' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ getCashDonationType(cashDonationType?.CashDonationType) }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'currencyType' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ cashDonationType?.CurrencyType }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'approximateValueAed' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ cashDonationType?.ApproximateValueinAED }}
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>

      <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
        <span class="col-2 d-l">
          {{'Total count' | translate}}:{{filteredCountCashDonationTypes}}
        </span>
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageCashDonationTypes"
          [pageSize]="pageSizeCashDonationTypes" [collectionSize]="filteredCountCashDonationTypes"
          (pageChange)="getPremiumDataCashDonationTypes()">
          <ng-template ngbPaginationPrevious>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
            </svg>
            <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
          </ng-template>
          <ng-template ngbPaginationNext>
            <span class="d-none d-lg-block">{{'Next' | translate}}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
            </svg>
          </ng-template>
        </ngb-pagination>
        <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeCashDonationTypes"
          (change)="getPremiumDataCashDonationTypes()">
          <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
        </select>
      </div>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display"
          [label]="messageTranslationPrefix+'totalCashDonationsAmount'" [isPlain]="true"
          [value]="formData?.TypeOfDonations?.TotalCashDonationsAmountinAED"></div>
      </div>
      }

      @if(formData?.TypeOfDonations?.IsInKindDonations === true){
      <div class="not_card">
        <h4 class="title_wrapper">
          {{messageTranslationPrefix+'typeOfInkindDonations' | translate}}
        </h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"inkindDonationTypeEn" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"inkindDonationTypeAr" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"countOrQuantity" |translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"approximateValueAed" |translate}}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let inkindDonationType of paginateDataInkindDonationTypes; let i = index">
                <td class="align-middle text-center">
                  {{ (pageInkindDonationTypes - 1) * pageSizeInkindDonationTypes + i + 1 }}
                </td>
                <td class="align-middle text-center">
                  {{ getInkindDonationType(inkindDonationType?.InkindDonationType)?.NameEnglish }}
                </td>
                <td class="align-middle text-center">
                  {{ getInkindDonationType(inkindDonationType?.InkindDonationType)?.NameArabic }}
                </td>
                <td class="align-middle text-center">
                  {{ inkindDonationType?.Count }}
                </td>
                <td class="align-middle text-center">
                  {{ inkindDonationType?.ApproximateValueinAED }}
                </td>

              </tr>
            </tbody>
          </table>
        </div>

      </div>
      <div class="figma-review-card-container">
        <div class="figma-card-container">
          @for (inkindDonationType of paginateDataInkindDonationTypes; track $index){
          <div class="figma-card">
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ $index + 1 }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'inkindDonationTypeEn' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ getInkindDonationType(inkindDonationType?.InkindDonationType)?.NameEnglish }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'inkindDonationTypeAr' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ getInkindDonationType(inkindDonationType?.InkindDonationType)?.NameArabic }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'countOrQuantity' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ inkindDonationType?.Count }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'approximateValueAed' | translate }}:
                </div>
                <div class="dynamic-value">
                  {{ inkindDonationType?.ApproximateValueinAED }}
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>

      <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
        <span class="col-2 d-l">
          {{'Total count' | translate}}:{{filteredCountInkindDonationTypes}}
        </span>
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageInkindDonationTypes"
          [pageSize]="pageSizeInkindDonationTypes" [collectionSize]="filteredCountInkindDonationTypes"
          (pageChange)="getPremiumDataInkindDonationTypes()">
          <ng-template ngbPaginationPrevious>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
            </svg>
            <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
          </ng-template>
          <ng-template ngbPaginationNext>
            <span class="d-none d-lg-block">{{'Next' | translate}}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
            </svg>
          </ng-template>
        </ngb-pagination>
        <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeInkindDonationTypes"
          (change)="getPremiumDataInkindDonationTypes()">
          <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
        </select>
      </div>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display"
          [label]="messageTranslationPrefix+'totalInkindDonationsAmount'" [isPlain]="true"
          [value]="formData?.TypeOfDonations?.TotalInkindDonationsAmountinAED"></div>
      </div>
      }


      @if(formData?.TypeOfDonations?.IsCashDonations === true && formData?.TypeOfDonations?.IsInKindDonations === true)
      {
      <div class="not_card">
        <h4 class="title_wrapper">
          {{messageTranslationPrefix+'grandTotalDonations' | translate}}
        </h4>
      </div>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display"
          [label]="messageTranslationPrefix+'grandTotalDonationsAmount'" [isPlain]="true"
          [value]="formData?.TypeOfDonations?.GrandTotalDonationAmountinAED"></div>
      </div>
      }
    </mat-expansion-panel>

    @if(isCashTypSelected){
    <!-- NPO Bank Account Details -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'npoBankAccountDetails') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'bank'" [isPlain]="true"
          [value]="getBank(formData?.NPOBankAccountDetails?.bank)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'bankAccount'" [isPlain]="true"
          [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.AccountNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'accountOwnerNameEn'"
          [isPlain]="true" [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.AccountOwnerNameEnglish"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'accountOwnerNameAr'"
          [isPlain]="true" [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.AccountOwnerNameArabic"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'branchName'" [isPlain]="true"
          [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.BranchName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'bankAddress'" [isPlain]="true"
          [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.BankAddress"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ibanNumber'" [isPlain]="true"
          [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.IBANNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'accountType'" [isPlain]="true"
          [value]="getBankAccountType(formData?.NPOBankAccountDetails?.bankAccountDetails?.AccountType)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'accountOpenDate'" [isDate]="true"
          [value]="formData?.NPOBankAccountDetails?.bankAccountDetails?.AccountOpenDate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'currencyType'" [isPlain]="true"
          [value]="getCurrency(formData?.NPOBankAccountDetails?.bankAccountDetails?.CurrencyType)"></div>
      </div>
    </mat-expansion-panel>

    <!-- Donor Bank Account Details -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'donorBankAccountDetails') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'country'" [isPlain]="true"
          [value]="getCountry(formData?.DonorBankAccountDetails?.Country)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'bankName'" [isPlain]="true"
          [value]="formData?.DonorBankAccountDetails?.BankName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'accountNumber'" [isPlain]="true"
          [value]="formData?.DonorBankAccountDetails?.AccountNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ibanNumber'" [isPlain]="true"
          [value]="formData?.DonorBankAccountDetails?.IBANNumber"></div>
      </div>
    </mat-expansion-panel>
    }

    <!-- Donation Purpose -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'donationPurpose') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display" [label]="messageTranslationPrefix+'donationPurpose'"
          [isPlain]="true" [value]="formData?.DonationPurpose?.donationPurpose"></div>
      </div>
    </mat-expansion-panel>

    <!-- Donation Date or Duration -->
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          {{(messageTranslationPrefix+'donationDateAndDuration') | translate}}
        </mat-panel-title>
      </mat-expansion-panel-header>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display"
          [label]="messageTranslationPrefix+'donationDateAndDuration'" [isPlain]="true"
          [value]="LanguageService.IsArabic ? getDonationDateAndDurationType(formData?.DonationDateAndDuration?.DonationDateAndDurationType)?.NameArabic : getDonationDateAndDurationType(formData?.DonationDateAndDuration?.DonationDateAndDurationType)?.NameEnglish">
        </div>
      </div>

      @if(formData?.DonationDateAndDuration?.DonationDateAndDurationType === 1){
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card special-display"
          [label]="messageTranslationPrefix+'donationDateAndDuration'" [isDate]="true"
          [value]="formData?.DonationDateAndDuration?.DateofReceivingtheDonations"></div>
      </div>
      }

      @if(formData?.DonationDateAndDuration?.DonationDateAndDurationType === 2){
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'fromDate'" [isDate]="true"
          [value]="formData?.DonationDateAndDuration?.Fromdate"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'fromDate'" [isDate]="true"
          [value]="formData?.DonationDateAndDuration?.Todate"></div>
      </div>
      }
    </mat-expansion-panel>

    <!-- documents -->
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'uploadDocuments') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      @for (file of documents; track $index) {
      <div class="figma-review-card-container">
        <span class="figma-review-card">
          <h4>
            <strong [lang]="LanguageService.IsArabic ? 'ar':'en'">{{getDocTypeName(file?.DocumentType)}} : </strong>
          </h4>
          <a [href]="generateDownloadLink(file)" [download]="file?.FileName">{{file?.FileName}}</a>
        </span>
      </div>
      }
    </mat-expansion-panel>
  </mat-accordion>
</div>

<div class="button_gp">
  <ng-container *ngIf="!isNotAllowedToEdit">
    <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
      (click)="previous.emit()">
      {{'Previous' | translate}}
    </button>
    <button type="button" class="btn basic-button" (click)="submit(true)">
      {{ messageTranslationPrefix+"saveAsDraft" | translate }}
    </button>
  </ng-container>
  <button type="button" class="btn btn-primary basic-filled-button"
    *ngIf="!formData?.IsSubmitted && formData?.StatusCode!=100000000 && formData?.StatusCode!=100000003 && showAction"
    (click)="submit(false)">
    {{messageTranslationPrefix+"submitRequest" |translate}}
  </button>

  <!-- @if(this.formData?.Id){ -->
  <ng-container *ngFor="let button of downloadButtons">
    <button type="button" class="btn download-button" (click)="download(button)">
      <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
          fill="#92722A" />
      </svg>
      {{LanguageService.IsArabic?button?.ButtonLabelAr: button?.ButtonLabelEn}}
    </button>
  </ng-container>
  <!-- } -->
</div>