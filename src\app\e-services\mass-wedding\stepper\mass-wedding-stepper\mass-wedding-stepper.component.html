<div style="margin-top: 40px !important;" class="container dashboard-padding">
  <div class="row m-sm-auto">
    <h1 class="eService_title">
      {{ messageTranslationPrefix + "title" | translate }}
    </h1>
  </div>

  <div class="row m-sm-auto">
    <app-stepper-full-journey [steps]="journeySteps"
      [lastIndexActiveStep]="journeyIndexActiveStep"></app-stepper-full-journey>
  </div>

  <div class="row progress_wrapper">
    <mat-progress-bar mode="determinate" style="border-radius: 8px !important;" [value]="progress"></mat-progress-bar>
    <span class="progress_txt">{{ "Progress" | translate }} ({{ progress }}%)</span>
  </div>
  <app-personal-details></app-personal-details>

  <div class="row gap-service-32 flex-md-nowrap m-sm-auto">
    @if(isEligible == false)
    {


    <section class="section" >
    
      <div class="service-apply-Card error">
          <div class="service-apply-Card__info">
              <label class="service-apply-Card__lbl">
                  <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                      <path d="M16 3C13.4288 3 10.9154 3.76244 8.77759 5.1909C6.63975 6.61935 4.97351 8.64968 3.98957 11.0251C3.00563 13.4006 2.74819 16.0144 3.2498 18.5362C3.75141 21.0579 4.98953 23.3743 6.80762 25.1924C8.6257 27.0105 10.9421 28.2486 13.4638 28.7502C15.9856 29.2518 18.5995 28.9944 20.9749 28.0104C23.3503 27.0265 25.3807 25.3603 26.8091 23.2224C28.2376 21.0846 29 18.5712 29 16C28.9964 12.5533 27.6256 9.24882 25.1884 6.81163C22.7512 4.37445 19.4467 3.00364 16 3ZM16 27C13.8244 27 11.6977 26.3549 9.88873 25.1462C8.07979 23.9375 6.66989 22.2195 5.83733 20.2095C5.00477 18.1995 4.78693 15.9878 5.21137 13.854C5.63581 11.7202 6.68345 9.7602 8.22183 8.22183C9.76021 6.68345 11.7202 5.6358 13.854 5.21136C15.9878 4.78692 18.1995 5.00476 20.2095 5.83733C22.2195 6.66989 23.9375 8.07979 25.1462 9.88873C26.3549 11.6977 27 13.8244 27 16C26.9967 18.9164 25.8367 21.7123 23.7745 23.7745C21.7123 25.8367 18.9164 26.9967 16 27ZM18 22C18 22.2652 17.8946 22.5196 17.7071 22.7071C17.5196 22.8946 17.2652 23 17 23C16.4696 23 15.9609 22.7893 15.5858 22.4142C15.2107 22.0391 15 21.5304 15 21V16C14.7348 16 14.4804 15.8946 14.2929 15.7071C14.1054 15.5196 14 15.2652 14 15C14 14.7348 14.1054 14.4804 14.2929 14.2929C14.4804 14.1054 14.7348 14 15 14C15.5304 14 16.0391 14.2107 16.4142 14.5858C16.7893 14.9609 17 15.4696 17 16V21C17.2652 21 17.5196 21.1054 17.7071 21.2929C17.8946 21.4804 18 21.7348 18 22ZM14 10.5C14 10.2033 14.088 9.91332 14.2528 9.66665C14.4176 9.41997 14.6519 9.22771 14.926 9.11418C15.2001 9.00065 15.5017 8.97094 15.7926 9.02882C16.0836 9.0867 16.3509 9.22956 16.5607 9.43934C16.7704 9.64912 16.9133 9.91639 16.9712 10.2074C17.0291 10.4983 16.9994 10.7999 16.8858 11.074C16.7723 11.3481 16.58 11.5824 16.3334 11.7472C16.0867 11.912 15.7967 12 15.5 12C15.1022 12 14.7206 11.842 14.4393 11.5607C14.158 11.2794 14 10.8978 14 10.5Z"/>
                  </svg>
                  {{ messageTranslationPrefix + "NotEligible" | translate }}
              </label>
              <h3 class="service-apply-Card__title">{{ messageTranslationPrefix + "title" | translate }}</h3>
              <div>
                  <!-- <h4 class="service-apply-Card__sub-title">Previous Marriage Grant</h4> -->
                  <p class="service-apply-Card__desc">{{ lang.IsArabic?service_uae_national_only_ar:service_uae_national_only }}&nbsp;<span *ngIf="showMyApplicationsLink"><a routerLink="/user-pages/my-applications"  mat-flat-button class="basic-button m-2">{{'services.marriageGrant.gotoMyApplications' | translate}}</a></span></p>
              </div>

          </div>
      </div>

  </section>
    }
    @else{
    <div class="col-12 col-md-4 col-lg-4 col-xl-3 d-flex flex-column align-items-center scrollSteps">
      <section class="horizontal-scroll w-100">
        <mat-stepper linear #stepper="matVerticalStepper" [selectedIndex]="lastselectedTabIndex"
          (selectedIndexChange)="updateCurrentPortalStep($event)" orientation="vertical" class="w-100">
          <mat-step [stepControl]="requestForm">
            <ng-template matStepLabel>{{"Applicant Information" | translate}}</ng-template>
          </mat-step>
          <mat-step [stepControl]="attachmentForm">
            <ng-template matStepLabel>{{"Attachments" | translate}}</ng-template>
          </mat-step>
          <mat-step [stepControl]="summaryForm">
            <ng-template matStepLabel>{{"Summary & Submit" | translate}}</ng-template>
          </mat-step>
        </mat-stepper>
      </section>
    </div>

    <div class="col-12 col-md-8 col-lg-8 col-xl-9 stepper_vertical-content">
      @if(stepper.selectedIndex == ServiceSteps.ApplicantInfo)
      {
      <app-request-form #ApplicantInfoComponentTemplate [form]="requestForm" (next)="goNext()"
        (previous)="goPrevious()"></app-request-form>
      }
      @if(stepper.selectedIndex == ServiceSteps.Attachment)
      {
      <app-attachments #AttachmentsComponentTemplate [form]="attachmentForm" (next)="goNext()"
        (previous)="goPrevious()"></app-attachments>
      }
      @if(stepper.selectedIndex == ServiceSteps.Summary)
      {
      <app-summary #SummaryComponentTemplate [form]="summaryForm" (next)="goNext()"
        (previous)="goPrevious()"></app-summary>
      }
    </div>
    }
  </div>
</div>




























<!-- <div class="container">
  <h1>{{ "services.massWedding.title" | translate }}</h1>
</div>
<div class="row">
  <div class="col-md-2">
    <app-helper></app-helper>
  </div>

  <div class="col-md">
    <app-personal-details></app-personal-details>
    <div class="card">
      <div class="card-body">
        <mat-progress-bar mode="determinate" [value]="progress"></mat-progress-bar>
        <span>{{ "Progress" | translate }} ({{ progress }}%)</span>
        <mat-vertical-stepper #stepper (selectionChange)="selectionChanged($event)">
          <mat-step [stepControl]="requestForm">
            <ng-template matStepLabel>{{ "Service Form" | translate }}</ng-template>
            <app-request-form [form]="requestForm" (next)="goNext()" (previous)="goPrevious()"></app-request-form>
          </mat-step>
          <mat-step [stepControl]="attachmentForm">
            <ng-template matStepLabel>{{ "Attachments" | translate }}</ng-template>
            <app-attachments [form]="attachmentForm" (next)="goNext()" (previous)="goPrevious()"></app-attachments>
          </mat-step>
          <mat-step [stepControl]="form">
            <ng-template matStepLabel>{{ "Summary & Submit" | translate }}</ng-template>
            <app-summary [form]="form" (next)="goNext()" (previous)="goPrevious()"></app-summary>
          </mat-step>
        </mat-vertical-stepper>
      </div>
    </div>
  </div>
</div> -->