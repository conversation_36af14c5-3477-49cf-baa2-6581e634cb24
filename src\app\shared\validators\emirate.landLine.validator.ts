import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function emirateLandLineValidator(
  startWith6: boolean = false,
  startWith9: boolean = false
): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const phoneNumber: string = control.value?.trim();

    if (!phoneNumber) {
      return null;
    }

    const isMaxLength = phoneNumber.length === 11;

    const isValid = startWith6
      ? /^9716\d{3}\d{4}$/.test(phoneNumber)
      : startWith9
      ? /^9719\d{3}\d{4}$/.test(phoneNumber)
      : /^9715\d{3}\d{4}$/.test(phoneNumber);

    if (isValid) {
      return null;
    }

    if (startWith6) {
      if(!isMaxLength) return { invalidLandlineNumberLength: true };
      return { invalidLandlineNumberStartWith6: true };
    } else if (startWith9) {
      if(!isMaxLength) return { invalidLandlineNumberLength: true };
      return { invalidLandlineNumberStartWith9: true };
    } else {
       if(!isMaxLength) return { invalidLandlineNumberLength: true };
      return { invalidLandlineNumberStartWith5: true };
    }

    
  };
}
