// * {
//   &:lang(ar) {
//     font-family: 'Noto <PERSON> Arabic' !important;
//   }

//   &:lang(en) {
//     font-family: 'Roboto' !important;
//   }
// }

.fa-paperclip {
  color: gray;
}

.fa-trash {
  color: red !important;
}


.table-container {
  overflow-x: auto;
  width: 100%;
  max-width: 100%;
}

.table {
  color: gray;
  table-layout: fixed;
  width: 100%;
}

.table th,
.table td {
  width: 25%;
  padding: 8px;
  text-align: center;
  // border: 1px solid #ddd;
}

.upload-btn {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  column-gap: 5px !important;

  height: 48px !important;
  line-height: 22px;
  font-weight: 500;
  font-size: 16px;
  text-align: center;
  padding: 12px 16px;
}