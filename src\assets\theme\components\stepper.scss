.mat-typography .eService_title{
    font-weight: $font-bold;
        color: $aeblack-800;
        font-size: 48px;
        text-transform: capitalize;
        margin-bottom: 60px;
        line-height: 1;
        // padding-top: 1rem;
        @media (max-width: 1024px) {
            font-size: 24px !important;
            margin-bottom: 16px !important;
            text-transform:capitalize !important;
            margin-bottom: 24px !important;
            margin-top: 20px !important;
            line-height: 1.3 !important;
        }
}
.mat-stepper-horizontal {
    background: transparent !important;
}

.mat-horizontal-stepper-wrapper {
    .mat-horizontal-stepper-header-container {
        align-items: self-start;
        justify-content: center;

        .mat-stepper-horizontal-line {
            transform: translateY(26px);
        }

        .mat-step-header {
            overflow: visible;
            width: 40px;
            flex: 1 0 0;
            padding: 8px;
            background-color: transparent !important;
            height: auto !important;
            flex-direction: column;

            [ng-reflect-dir=rtl] &,
            [dir=rtl] & {
                margin-left: 5px;
                padding-right: 0;
                margin-right: 0;
                padding-left: 0;
            }

            .mat-step-label {
                font-size: 16px;
                font-weight: $font-medium;
                // max-width: 100%;
                max-width: 65%;
                white-space: pre-line;
                text-align: center;
                min-width: 160px;
                padding-top: 10px;

                .stepper-number {
                    display: none;
                }
            }

            .mat-step-icon {
                width: 40px;
                height: 40px;
                font-weight: $font-bold;
                font-size: 16px;
                border: 1px solid $slate-200;
                color: $aeblack-800;
                margin-bottom: 8px;
                background-color: transparent;

                [ng-reflect-dir=rtl] &,
                [dir=rtl] & {
                    margin-left: 12px;
                    margin-right: 0;
                }
            }

            .mat-step-icon-selected {
                background-color: $aegold-600;
                border-color: $aegold-600;
                color: $white;
                outline: 6px solid $aegold-200;
            }

            .mat-step-label-selected {
                .stepper-number {
                    font-size: 12px !important;
                    color: $aegold-600;
                    display: block !important;
                    margin-bottom: 4px;
                }
            }

            .mat-step-icon-state-edit {
                background-color: $aegold-500;
                color: $white;
            }

            .mat-step-icon-state-done {
              background-color: #92722A !important;
              border-color: #92722A !important;
              color: #fff !important;
            }
        }

        // stepper arrow separator
        // .mat-step-header::after { 
        //     content: " "; 
        //     display: block; 
        //     width: 0; 
        //     height: 0;
        //     border-top: 50px solid transparent; /* Go big on the size, and let overflow hide */
        //     border-bottom: 50px solid transparent;
        //     border-left: 30px solid rgba(249, 247, 237, .5);
        //     position: absolute;
        //     top: 50%;
        //     margin-top: -50px; 
        //     left: 100%;
        //     z-index: 2; 
        //     [ng-reflect-dir=rtl] & , [dir=rtl] & {
        //         border-right: 30px solid rgba(249, 247, 237, .5);
        //         border-left: none;
        //         right: 100%;
        //     }
        // }
        // .mat-step-header::before { 
        //     content: " "; 
        //     display: block; 
        //     width: 0; 
        //     height: 0;
        //     border-top: 50px solid transparent;       
        //     border-bottom: 50px solid transparent;
        //     border-left: 30px solid $white;
        //     position: absolute;
        //     top: 50%;
        //     margin-top: -50px; 
        //     margin-left: 5px;
        //     left: 100%;
        //     z-index: 1; 
        //     [ng-reflect-dir=rtl] & , [dir=rtl] & {
        //         border-right: 30px solid $white;
        //         border-left: none;
        //         right: 100%;
        //         margin-right: 5px;
        //         margin-left: 0;
        //     }
        // }
        // .mat-step-header:last-child:before{
        //     display: none !important;
        // }
        // .mat-step-header:last-child:after{
        //     display: none !important;
        // }
        // .mat-step-header:last-child{
        //     margin: 0 !important;
        // }
        // .mat-step-header:first-child{
        //     padding-left: 24px;
        //     [ng-reflect-dir=rtl] & , [dir=rtl] & {
        //         padding-right: 24px;
        //         padding-left: 0;
        //     }
        // }
    }

    // /* Hide scrollbar for Chrome, Safari and Opera */
    // .mat-horizontal-stepper-header-container::-webkit-scrollbar {
    //     display: none;
    // }
    // /* Hide scrollbar for IE, Edge and Firefox */
    // .mat-horizontal-stepper-header-container {
    //     -ms-overflow-style: none;  /* IE and Edge */
    //     scrollbar-width: none;  /* Firefox */
    // }
    // progress bar
    .mat-horizontal-content-container {
        padding: 0px;

        .progress-bar {
            // width: calc(100% + 43px);
            // margin-left: -24px;
            text-align: left;
            float: none;

            [ng-reflect-dir=rtl] &,
            [dir=rtl] & {
                // margin-right: -24px;
                // margin-left: 0;
                text-align: right;
            }

            .progress-text {
                font-weight: $font-bold;
                margin-top: 4px;
            }

            .mdc-linear-progress__bar-inner {
                border-color: $aegold-500;
            }

            .mat-mdc-progress-bar {
                --mdc-linear-progress-track-color: #F2ECCF;
            }
        }
    }
}

/** Progressbar **/
html {
    --mdc-linear-progress-active-indicator-height: 10px !important;
    --mdc-linear-progress-track-height: 10px !important;
    --mdc-linear-progress-track-shape: 0;
}

.mdc-linear-progress__bar-inner {
    border-color: $aegold-500 !important;
    border-radius: 6px 0px 0px 6px;
}

.mdc-linear-progress__buffer-bar {
    background-color: $aegold-50;
    border-radius: 6px;
}

.mdc-linear-progress__buffer {
    @media (max-width: 1024px){
        position: relative !important;
    }
}

.progress_wrapper {
    margin: 40px auto;
    
    @media (max-width:1024px) {
        margin: 20px auto !important;
    }
}

.progress_txt {
    display: block;
    text-align: right;
    margin-top: 12px;
    font-size: 14px;
    color: $aeblack-500;
}

/** !!Progressbar **/
@media (min-width: 992px) {
    .mat-step-text-label {
        padding-inline: 8px;
    }
}
@media (max-width: 991px) {
    .mat-horizontal-stepper-wrapper {
        .mat-horizontal-stepper-header-container {
            overflow-x: scroll;
            justify-content: start;
            scrollbar-width: none;
        }

        .mat-step-header {
            width: auto !important;

            // [ng-reflect-dir=rtl] &,
            // [dir=rtl] & {
            //     padding-right: 24px !important;
            //     padding-left: 0 !important;
            //     margin-left: 24px !important;
            // }
        }


    }
}