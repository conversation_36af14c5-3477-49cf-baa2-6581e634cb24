import { Injectable, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class LanguageService {



  constructor(public translate: TranslateService, private route: ActivatedRoute) {

    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      localStorage.setItem('lang', event.lang);
    });

  }

  public get IsArabic() {
    return this.translate.currentLang === 'ar';
  }

  public get ArrowDirection() {
    return this.translate.currentLang === 'ar' ? 'left' : 'right';
  }
  public get ArrowDirectionBack() {
    return this.translate.currentLang === 'en' ? 'left' : 'right';
  }


  switchLanguage() {
    if (this.IsArabic) {
      this.translate.use('en');
      this.changePageDirection('en');
    }
    else {
      this.translate.use('ar');
      this.changePageDirection('ar');
    }
  }

  changeLang(lang: string) {
    this.translate.use(lang);
    this.changePageDirection(lang);
  }

  changePageDirection(lang: string) {
    const rootBodyTag: HTMLElement = document.getElementById('app-body') as HTMLElement;
    if (lang === 'ar') {
      rootBodyTag.setAttribute('dir', 'rtl');
      rootBodyTag.setAttribute('lang', 'ar');
    } else {

      rootBodyTag.setAttribute('dir', 'ltr');
      rootBodyTag.setAttribute('lang', 'en-US');
    }
  }

  setDefaultLang() {
    this.route.queryParams.subscribe(params => {
      var langParam = params['lang'];
      if (langParam && (langParam === 'en' || langParam === 'ar')) {
        this.translate.setDefaultLang(langParam);
        this.translate.use(langParam);
      }
      else {
        var lang = localStorage.getItem('lang');
        if (lang && lang.length > 0 && (lang === 'en' || lang === 'ar')) {
          this.translate.setDefaultLang(lang);
          this.translate.use(lang);
        }
      else {
          this.translate.setDefaultLang('ar');
          this.translate.use('ar');
        }
      }
      this.changePageDirection(this.IsArabic ? 'ar' : 'en');
    });


  }
  // setDefaultLang() {
  //   this.route.queryParams.subscribe(params => {
  //     var lang = params['lang'];
  //     if (lang) {
  //       this.translate.use(lang);
  //     } else {
  //       lang = localStorage.getItem('lang');
  //       if (lang && lang.length > 0 && (lang === 'en' || lang === 'ar')) {
  //         this.translate.setDefaultLang(lang);
  //         this.translate.use(lang);
  //       }
  //       else {
  //         this.translate.setDefaultLang('ar');
  //         this.translate.use('ar');
  //       }
  //     }
  //   });
  // }

}
