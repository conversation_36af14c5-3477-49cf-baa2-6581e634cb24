import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import Swal from 'sweetalert2';
import { AlertType } from '../../e-services/npo-license-declaration/models/alert-type';

@Injectable({
  providedIn: 'root'
})
export class AlertService {

  constructor(private translate: TranslateService) { }

  confirmSubmit(messageKey: string): Promise<boolean> {
    var translations = this.translate.instant('alert');

    if (!messageKey) {
      return Swal.fire({
        title: translations.title,
        // text: translations.text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#92722A',
        cancelButtonColor: '#d33',
        confirmButtonText: translations.confirmButtonText,
        cancelButtonText: translations.cancelButtonText,
        reverseButtons: true
      }).then((result) => {
        return result.isConfirmed;
      });
    }
    else {
      var description = this.translate.instant(messageKey);

      return Swal.fire({
        title: translations.title,
        text: description,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#92722A',
        cancelButtonColor: '#d33',
        confirmButtonText: translations.confirmButtonText,
        cancelButtonText: translations.cancelButtonText,
        reverseButtons: true
      }).then((result) => {
        return result.isConfirmed;
      });
    }
  }

  async showAlert(alertType: AlertType): Promise<boolean> {
    try {
      const result = await Swal.fire({
        title: alertType.title,
        text: alertType.message,
        icon: alertType.icon,
        showCancelButton: alertType.showCancelButton,
        confirmButtonColor: alertType.confirmButtonColor,
        cancelButtonColor: alertType.cancelButtonColor,
        confirmButtonText: alertType.confirmButtonText,
        cancelButtonText: alertType.cancelButtonText,
        reverseButtons: true
      });
      return result.isConfirmed;
    } catch (error) {
      console.error('Error displaying confirmation dialog:', error);
      return false;
    }
  }

  showMessage(icon: 'success' | 'error' | 'warning' | 'info', message: string): void {
    var description = this.translate.instant(message);
    let title = '';
    if (icon === 'warning') {
      title = this.translate.instant('notify.warning');
    } else if (icon === 'info') {
      title = this.translate.instant('notify.info');
    } else if (icon === 'error') {
      title = this.translate.instant('notify.error');
    } else if (icon === 'success') {
      title = this.translate.instant('notify.success');
    }

    Swal.fire(title, description, icon);
  }

  showHtmlAlert = async (alertType: AlertType): Promise<boolean> => {
    try {
      const result = await Swal.fire({
        title: alertType.title,
        html: alertType.message,
        icon: alertType.icon,
        showCancelButton: alertType.showCancelButton,
        confirmButtonColor: alertType.confirmButtonColor,
        cancelButtonColor: alertType.cancelButtonColor,
        confirmButtonText: alertType.confirmButtonText,
        cancelButtonText: alertType.cancelButtonText,
        width: alertType.width,
        reverseButtons: true
      });
      return result.isConfirmed;
    } catch (error) {
      console.error('Error displaying confirmation dialog:', error);
      return false;
    }
  }
}
