import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from './language.service';
import { Lookup } from '../models/lookup.model';

@Injectable({
  providedIn: 'root'
})
export class SwpService {

  constructor() { }

  translateLookup(data:any)
  {
    var result:Lookup[]=[];
    data.forEach(item => {
      result.push(new Lookup(item.Id, item.Name, item.NameAR, item.RelatedId))
    });
    return result;
  }

}
