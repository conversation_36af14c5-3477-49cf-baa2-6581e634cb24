import { Attachment } from './../../../models/attachment.model';
import { TranslateService } from '@ngx-translate/core';
import { Component, EventEmitter, inject, input, Input, OnInit, Output, ViewChild, OnChanges, SimpleChanges } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { AlertService } from '../../../services/alert.service';
import { ValidationService } from '../../../services/validation.service';
import { NotifyService } from '../../../services/notify.service';
import { StepperService } from '../../../services/stepper.service';
import { NpoLicenseStepperService } from '../../../../e-services/npo-license-declaration/services/npo-license-stepper.service';
import { Observable } from 'rxjs';
import { LanguageService } from '../../../services/language.service';


@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrl: './file-upload.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class FileUploadComponent implements OnInit, OnChanges {

  @Input() label: string;
  @Input() description: string;
  @Input() docTypeId: string;
  @Input() placeholder: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() columns: number = 6;
  @Input() isPhoto: boolean = false;
  @Input() isNpoServices: boolean = false;
  @Input() useCustomExtensions: boolean = false;
  @Input() allowedExtensions: string = "";
  @Input() maxFileSize: number = 5;
  @Output() fileUploaded: EventEmitter<Attachment> = new EventEmitter<Attachment>();
  @Output() fileDeleted: EventEmitter<Attachment> = new EventEmitter<Attachment>();


  file: File | null = null;

  isValidFormat: boolean = false;
  isValidSize: boolean = false;
  private isProgrammaticLoad: boolean = false;

  @Input() externalInjectFile: any;
  constructor(private alert: AlertService,
    protected stepper: StepperService,
    protected npoStepper: NpoLicenseStepperService,
    private notify: NotifyService,
    private validationService: ValidationService,
    private translateService: TranslateService) { }

  base64ToFile(base64: string, fileName: string, contentType: string): File {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new File([new Blob([byteArray], { type: contentType })], fileName, { type: contentType });
  }

  handleFileInput(f: any) {
    var files = f.target.files as FileList;
    this.file = files.item(0);

    this.isValidSize = this.checkValidSize();
    this.isValidFormat = this.checkFileType();

    if (!this.isValidSize) {
      this.notify.showError('notify.error', 'notify.invalidFileSize');
      this.file = null;
      this.control.setValue('');
    }
    else if (!this.isValidFormat) {
      this.notify.showError('notify.error', 'notify.invalidFileFormat');
      this.file = null;
      this.control.setValue('');
    }

    if (this.file && this.isValidSize && this.isValidFormat) {
      this.fileUploaded.emit(new Attachment(this.label, this.docTypeId, this.file));
    }
  }
  async deleteFile() {

    const translationTitle = this.translateService.instant('alert.deleteTitle');
    const translationMessage = this.translateService.instant('notify.deleteMessage');
    const translations = this.translateService.instant('alert');

    const confirmed = await this.alert.showAlert({
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#b52520',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmButtonText,
      cancelButtonText: translations.cancelButtonText
    });

    if (confirmed && this.file !== null) {
      this.fileDeleted.emit(new Attachment(this.label, this.docTypeId, this.file));
      this.softDeleteValue();
    }
  }

  softDeleteValue = (): void => {
    this.file = null;
    this.control.setValue('');
    if (this.required) {
      this.control.addValidators([Validators.required]);
      this.control.updateValueAndValidity();
    }
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  checkValidSize() {
    const file = this.file;
    if (file && file.size <= (this.maxFileSize * 1024 * 1024)) {
      return true;
    }
    return false;
  }

  checkFileType() {
    const file = this.file;
    if (file) {
      const splitArray = file.name.split('.');
      const fileType = file.name.split('.')[splitArray.length - 1]?.toLowerCase();

      if (this.useCustomExtensions) {
        const extensions = this.allowedExtensions.trim().toLocaleLowerCase().split(',');
        if (fileType && extensions.includes(fileType)) {
          return true;
        } else {
          return false;
        }
      } else {
        if (!this.isPhoto && fileType && ['pdf', 'png', 'jpg', 'jpeg'].includes(fileType)) {
          return true;
        } else if (this.isPhoto && fileType && ['png', 'jpg', 'jpeg'].includes(fileType)) {
          return true;
        }
        else {
          return false;
        }
      }

    }
    return false;
  }


  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  ngOnInit(): void {
    if (this.isNpoServices) {
      this.subscribeToFormData(this.npoStepper.formData$, 'requestAttachments');
      this.subscribeToFormData(this.npoStepper.requestData$, 'UploadDocumentForm.Document');
    } else {
      this.subscribeToFormData(this.stepper.formData$, 'requestAttachments');
    }

  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes && changes["externalInjectFile"]?.currentValue) {
      this.handleFileUpload(this.externalInjectFile);
    }
  }

  private subscribeToFormData(source$: Observable<any>, documentKey: string): void {
    source$.subscribe(res => {
      const documents = this.getDocumentsFromResponse(res, documentKey);
      if (documents) {
        const fileItem = documents?.find(p => p.DocumentType === this.docTypeId);
        if (fileItem && fileItem.Base64.length > 0) {
          // this.isProgrammaticLoad = true;
          this.handleFileUpload(fileItem);
          // this.isProgrammaticLoad = false;
        }
      }
    });
  }

  private getDocumentsFromResponse(res: any, documentKey: string): any[] | null {
    if (res && documentKey.includes('.')) {
      const keys = documentKey.split('.');
      return keys.reduce((acc, key) => acc?.[key], res) || null;
    }
    return res?.[documentKey] || null;
  }

  private handleFileUpload(fileItem: any): void {
    this.file = this.base64ToFile(fileItem.Base64, fileItem.FileName, (fileItem.MimeType ?? fileItem.mimeType));
    this.control.clearValidators();
    this.control.updateValueAndValidity();
    // if (!this.isProgrammaticLoad) {
    this.fileUploaded.emit(new Attachment(this.label, this.docTypeId, this.file, true, fileItem.Id));
    // }
  }

  isMobileScreen(): boolean {
    // Check if the viewport matches the `lg` breakpoint (Bootstrap default is ≥ 992px)
    return window.matchMedia('(max-width: 1023px)').matches;
  }

  downloadFile = (file: any): void => {

  }
}
