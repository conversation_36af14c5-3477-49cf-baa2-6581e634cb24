import { Component, Injector, OnInit } from '@angular/core';
import { MyEstablishmentComponentBase } from '../../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-members-list',
  templateUrl: './members-list.component.html',
  styleUrl: './members-list.component.scss',
})
export class MembersListComponent extends MyEstablishmentComponentBase implements OnInit {


  previousPermitsArray: any[];
  constructor(injector: Injector) {
    super(injector);
  }

  ngOnInit(): void {
    this.getSubmittingFundraisingPermitException(this.ActivatedRoute.snapshot.paramMap.get('establishmentId'));
  }

  fundraisingPermitList: any[] = [];

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'active':
        return 'green';
      case 'rejected':
        return 'red';
      default:
        return '';
    }
  }

  getStatusBgColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'active':
        return '#E7F5FF';
      case 'rejected':
      default:
        return '';
    }
  }



  openPermit(mode: string, id: string) {
    window.location.href = `e-services/members-list/${this.ActivatedRoute.snapshot.paramMap.get('establishmentId')}/${id}`;
  }



  getSubmittingFundraisingPermitException = (id: string | null): void => {
    this.MyEstablishmentService.checkSubmittingFundraisingPermit(
      id
    ).subscribe(
      (val$) => {
        this.previousPermitsArray = val$?.data[0] ?? [];
        this.pagination();
      },
      (error) => { }
    );
  };

  page = 1;
  pageSize = 10;
  dataTable: any[] = [];
  get reprsentedDataTable(): Array<any> {
    return this.dataTable;
  }
  get tableIndex(): number {
    return (this.page - 1) * this.pageSize;
  }
  pagination = (): void => {
    this.dataTable = [];
    let data$ = this.previousPermitsArray?.slice(
      (this.page - 1) * this.pageSize,
      (this.page - 1) * this.pageSize + this.pageSize
    );
    data$?.forEach((_) => this.dataTable.push(_));
  };

  getStatusByNameSTG = (status: string): string => {
    const trimmedStatus = status
      ?.trim()
      ?.toLocaleLowerCase()
      ?.replace(/\s+/g, '');

    const statuses = {
      en: {
        active: 'Active',
        proposed: 'Proposed',
        pendingconfirmation: 'Pending Confirmation',
        draft: 'Draft',
        confirmed: 'Confirmed',
        refused: 'Refused',
        approved: 'Approved',
        rejected: 'Rejected',
        requestupdate: 'Request Update',
      },
      ar: {
        active: 'نشط',
        proposed: 'مقترح',
        pendingconfirmation: 'في انتظار التأكيد',
        draft: 'مسودة',
        confirmed: 'تم التأكيد',
        refused: 'مرفوض',
        approved: 'موافق',
        rejected: 'مرفوض',
        requestupdate: 'طلب تحديث',
      },
    };

    const language = this.LanguageService?.IsArabic ? 'ar' : 'en';

    return statuses[language][trimmedStatus] || statuses[language].draft;
  };

  getPermitStatus = (status: string): string => {
    const statuses = {
      en: {
        100000000: 'Active',
        100000001: 'Expired',
        100000002: 'Not Started',
        0: '-'
      },
      ar: {
        100000000: 'نشط',
        100000001: 'منتهي الصلاحية',
        100000002: 'لم يتم البدء',
        0: '-'
      },
    }
    const language = this.LanguageService?.IsArabic ? 'ar' : 'en';
    return statuses[language][status] || '-'
  }


}
