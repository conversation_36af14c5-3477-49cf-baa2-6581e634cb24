.aegov-link {
  margin-left: -.625rem;
  display: inline-flex;
  align-items: center;
  gap: .5rem;
  background-color: transparent;
  padding: .5rem .625rem;
  vertical-align: middle;
  font-weight: 600;
  text-decoration: none;
  text-decoration-line: none;
  color: $aegold-600;
  transition: color 0.3s;

  &:hover {
    color: $aegold-500;

    svg {
      fill: $aegold-500;
    }
  }

  svg {
    fill: $aegold-600;
    transition: color 0.3s;

    [ng-reflect-dir=rtl] &,
    [dir=rtl] & {
      transform: rotate(180deg);
    }
  }

  @media (max-width:1024px) {
    text-wrap: nowrap !important;
  }
}