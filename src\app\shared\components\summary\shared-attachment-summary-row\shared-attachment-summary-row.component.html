  <!-- <td class="labelSummary"><strong>{{ label | translate}}</strong> : </td>
  <td *ngIf="isPlain">{{ value }}</td>
  <td *ngIf="isDate">{{ value | date:'dd/MM/yyyy' }}</td>
  <td *ngIf="isLookup">{{ valueLookup }}</td>
  <td *ngIf="isFile && file"><a [href]="fileUrl" [download]="fileName">{{ fileName }}</a></td> -->
  <h4 *ngIf="isFile && value?.file" class="card-title"><strong>{{label | translate}}: </strong>
    <span><a [href]="value?.file?.fileUrl" [download]="value?.file?.name">{{ value?.file?.name }}</a></span>
   </h4>
