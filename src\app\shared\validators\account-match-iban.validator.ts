import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function accountMatchesIban(ibanControl: AbstractControl): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const account = control.value;
    const iban = ibanControl?.value || '';

    if (!account || !iban) return null; // skip if empty

    // Extract only digits from IBAN
    const numericIban = iban.replace(/\D/g, '');

    return numericIban.includes(account) ? null : { accountMismatch: true };
  };
}
