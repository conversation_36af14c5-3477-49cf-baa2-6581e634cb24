import { Component, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { StepperService } from '../../../../shared/services/stepper.service';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from '../../../../shared/services/language.service';
import { customEmailValidator } from '../../../../shared/validators/custom.email.validator';
import { StepsFullJournyEnum, SummaryStepsFullJourny } from '../../../../shared/models/summry-steps-full-journey';
import { RequestFormComponent } from '../../components/request-form/request-form.component';
import { AttachmentsComponent } from '../../components/attachments/attachments.component';
import { SummaryComponent } from '../../components/summary/summary.component';
import { JOURNEY_STEPS } from '../../constants/journey_steps';
import { ServiceSteps } from '../../enums/steps.enum';
import { MassWeddingService } from '../../services/mass-wedding.service';
import { AuthService } from '../../../../shared/services/auth.service';
import { ServiceCatalogue } from '../../../../shared/enums/service-catalogue.enum';
import { Lookup } from '../../../../shared/models/lookup.model';
import { FormService } from '../../../../shared/services/form.service';
import { customIbanValidator } from '../../../../shared/validators/custom.iban.validator';
import { ActivatedRoute } from '@angular/router';
import { customMobileValidator } from '../../../../shared/validators/custom.mobile.validator';
import { CustomTextValidator } from '../../../../shared/validators/custom.text.validator';

@Component({
  selector: 'app-mass-wedding-stepper',
  templateUrl: './mass-wedding-stepper.component.html',
  styleUrl: './mass-wedding-stepper.component.scss'
})
export class MassWeddingStepperComponent {
  @ViewChild("stepper") stepper: MatStepper;
  @ViewChild('ApplicantInfoComponentTemplate') ApplicantInfoComponentTemplate: RequestFormComponent;
  @ViewChild('AttachmentsComponentTemplate') AttachmentsComponentTemplate: AttachmentsComponent;
  @ViewChild('SummaryComponentTemplate') SummaryComponentTemplate: SummaryComponent;
  form: FormGroup;


  messageTranslationPrefix: string = 'services.massWedding.';
  journeySteps: SummaryStepsFullJourny[];
  journeyIndexActiveStep: number;
  lastselectedTabIndex: unknown;
  isReturnForUpdate: boolean = false;
  isEligible: boolean ;
  service_uae_national_only: string = '';
  service_uae_national_only_ar: string = '';
  shariaCourtList: Lookup[];
  educationLevelList: Lookup[];
  emirates: Lookup[];
  employerCategories: Lookup[];
  placesOfWork: Lookup[];
  banksData: Lookup[];
  showMyApplicationsLink: boolean=false;

  constructor(
    private formBuilder: FormBuilder,
    protected translate: TranslateService,
    protected stepperService: StepperService,
    private toastr: ToastrService,
    protected lang: LanguageService,
    private MassWeddingService: MassWeddingService,
    private auth: AuthService,
    private formService: FormService,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    this.intiJourneySteps(JOURNEY_STEPS);

    this.MassWeddingService.lookupData$.subscribe(data => {
      if (data && data.EmploymentStatus) {
        // this.shariaCourtList = data.WorkingSector;
        this.educationLevelList = data.EducationLevel;
        this.emirates = data.Emirates;
        // this.employerCategories = data.Emirates;
        this.placesOfWork = data.Emirates;
        this.banksData = data.Emirates;
      }
    });


    this.form = this.formBuilder.group({
      applicantInfo: this.formBuilder.group({
        FamilyBookDataId: new FormControl(''),
        FamilyBookMemberId: new FormControl(''),
        ApplicationFamilyBookDataId: new FormControl(''),
        husbandPersonalInfoForm: this.formBuilder.group({
          Id: new FormControl(''),
          dateOfMarriageContract: new FormControl({ value: '', disabled: true }),
          shariaCourtName: new FormControl('', [Validators.required]),
          NameAr: new FormControl({ value: '', disabled: true }),
          NameEn: new FormControl({ value: '', disabled: true }),
          dob: new FormControl({ value: '', disabled: true }),
          emiratesId: new FormControl({ value: '', disabled: true }),
          educationLevel: new FormControl('', [Validators.required]),
          email: new FormControl('', [Validators.required, customEmailValidator()]),
          mobile1: new FormControl('', [Validators.required, Validators.minLength(12), customMobileValidator()]),
        }),
        wifePersonalInfoForm: this.formBuilder.group({
          Id: new FormControl(''),
          NameAr: new FormControl({ value: '', disabled: true }),
          NameEn: new FormControl({ value: '', disabled: true }),
          dob: new FormControl({ value: '', disabled: true }),
          emiratesId: new FormControl({ value: '', disabled: true }),
          educationLevel: new FormControl('', [Validators.required]),
          email: new FormControl('', [Validators.required, customEmailValidator()]),
          mobile: new FormControl('', [Validators.required, Validators.minLength(12), customMobileValidator()]),
        }),
        abstractEnrollmentInfoForm: this.formBuilder.group({
          familyBookNumber: new FormControl({ value: '', disabled: true }),
          townNumber: new FormControl({ value: '', disabled: true }),
          placeOfIssuanceFamilyBook: new FormControl(''),
        }),
        statementOfWorkForm: this.formBuilder.group({
          employerCategory: new FormControl('', [Validators.required]),
          employer: new FormControl(''),
          placeOfWork: new FormControl(''),
        }),
        incomeStatementForm: this.formBuilder.group({
          totalMonthlyIncome: new FormControl('', [Validators.required]),
          bankName: new FormControl('', [Validators.required]),
          iban: new FormControl('', [Validators.required, customIbanValidator()]),
        }),
      }),
      attachmentForm: this.formBuilder.group({
        // commercialLicense: new FormControl(''),
        monthlyIncomeCertificate: new FormControl(''),
        // proofOfResidence: new FormControl(''),
        ibanLetter: new FormControl(''),
        // bankStatement: new FormControl(''),
      }),
      summaryForm: this.formBuilder.group({
        consent: ['', [Validators.required, Validators.requiredTrue]],
      }),
    });

    const requestId = this.route.snapshot.paramMap.get('id');
    if (!requestId) {
      this.isEligibleCheck();
    } else {
      this.isEligible = true;
    }

    // var regexEmployer = this.lang.IsArabic ? Validators.pattern(/^[\u0600-\u06FF\s]{3,}$/) : Validators.pattern(/^[A-Za-z\s]{3,}$/);
    // this.formService.addValidators(((this.form.get('applicantInfo') as FormGroup).get('statementOfWorkForm') as FormGroup), ['employer'], [regexEmployer]);

    // this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
    //   var regexEmployer = this.lang.IsArabic ? Validators.pattern(/^[\u0600-\u06FF\s]{3,}$/) : Validators.pattern(/^[A-Za-z\s]{3,}$/);
    //   this.formService.addValidators(((this.form.get('applicantInfo') as FormGroup).get('statementOfWorkForm') as FormGroup), ['employer'], [regexEmployer]);

    // });
  }


  //Get Form Components Methods

  get requestForm() {
    return this.form.get('applicantInfo') as FormGroup;
  }

  get summaryForm() {
    return this.form.get('summaryForm') as FormGroup;
  }
  get attachmentForm() {
    return this.form.get('attachmentForm') as FormGroup;
  }

  get ServiceSteps() {
    return ServiceSteps;
  }


  goNext() {
    this.stepper.next();
  }

  goPrevious() {
    this.stepper.previous();
  }

  intiJourneySteps(data: any) {
    this.journeySteps = [];
    (data as Array<any>).map(_ => {
      this.journeySteps.push({
        nameEn: _?.nameEn,
        nameAr: _?.nameAr,
        status: _?.IsCompleted == 'No' ? StepsFullJournyEnum.INPROGRESS : StepsFullJournyEnum.COMPLETE
      })
    });
    const lastIndex = [...this.journeySteps].reverse().findIndex(step => step.status === StepsFullJournyEnum.COMPLETE);
    const originalIndex = lastIndex !== -1 ? this.journeySteps.length - 1 - lastIndex : -1;
    this.journeyIndexActiveStep = originalIndex < 0 ? 0 : originalIndex + 1;
  }

  updateCurrentPortalStep = (index: number): void => {
    window.scrollTo(0, 0);
  }

  isEligibleCheck() {
    const userInfo = this.auth.getUserInfo();
    this.MassWeddingService.getEligibilityData(userInfo.crmUserId, ServiceCatalogue.MassWedding).subscribe((response) => {
      if (response.Status == 2000) {
        this.isEligible = true;

        const husbandData = response.data.Husband;
        const husbandForm = this.form.get('applicantInfo.husbandPersonalInfoForm');

        if (husbandData) {
          husbandForm?.setValue({
            Id: husbandData.Id,
            dateOfMarriageContract: this.formService.formatDate(response.data.MarriageContractDate, 'yyyy-MM-dd'),
            shariaCourtName: response.data.ShariaCourt != 0 ? this.stepperService.getLookup(this.shariaCourtList, response.data.ShariaCourt) : null,
            NameAr: husbandData.FullNameArabic,
            NameEn: husbandData.FullNameEnglish,
            dob: this.formService.formatDate(husbandData.DateOfBirth, 'yyyy-MM-dd'),
            emiratesId: husbandData.EmiratesId,
            educationLevel: husbandData.EducationLevel !== '00000000-0000-0000-0000-000000000000'
              ? this.stepperService.getLookup(this.educationLevelList, husbandData.EducationLevel)
              : null,
            email: husbandData?.PreferedEmail,
            mobile1: husbandData?.PreferedMobileNo?.startsWith('05') ? '971' + husbandData?.PreferedMobileNo.slice(1) : husbandData?.PreferedMobileNo
          });

          // Enable/Disable specific fields based on data presence


          husbandForm?.get('dateOfMarriageContract')?.value ? husbandForm?.get('dateOfMarriageContract')?.disable() : husbandForm?.get('dateOfMarriageContract')?.enable();
          husbandData.FullNameArabic ? husbandForm?.get('NameAr')?.disable() : husbandForm?.get('NameAr')?.enable();
          husbandData.FullNameEnglish ? husbandForm?.get('NameEn')?.disable() : husbandForm?.get('NameEn')?.enable();
          husbandData.DateOfBirth ? husbandForm?.get('dob')?.disable() : husbandForm?.get('dob')?.enable();
          husbandData.EmiratesId ? husbandForm?.get('emiratesId')?.disable() : husbandForm?.get('emiratesId')?.enable();

        }

        // For Wife Personal Info Form
        const wifeData = response.data.Wife;
        const wifeForm = this.form.get('applicantInfo.wifePersonalInfoForm');

        if (wifeData) {
          wifeForm?.setValue({
            Id: wifeData.Id,
            NameAr: wifeData.FullNameArabic,
            NameEn: wifeData.FullNameEnglish,
            dob: this.formService.formatDate(wifeData.DateOfBirth, 'yyyy-MM-dd'),
            emiratesId: wifeData.EmiratesId,
            educationLevel: wifeData.EducationLevel !== '00000000-0000-0000-0000-000000000000'
              ? this.stepperService.getLookup(this.educationLevelList, wifeData.EducationLevel)
              : null,
            email: wifeData?.PreferedEmail,
            mobile: wifeData?.PreferedMobileNo?.startsWith('05') ? '971' + wifeData?.PreferedMobileNo.slice(1) : wifeData?.PreferedMobileNo,
          });

          // Enable/Disable specific fields based on data presence
          wifeData.FullNameArabic ? wifeForm?.get('NameAr')?.disable() : wifeForm?.get('NameAr')?.enable();
          wifeData.FullNameEnglish ? wifeForm?.get('NameEn')?.disable() : wifeForm?.get('NameEn')?.enable();
          wifeData.DateOfBirth ? wifeForm?.get('dob')?.disable() : wifeForm?.get('dob')?.enable();
          wifeData.EmiratesId ? wifeForm?.get('emiratesId')?.disable() : wifeForm?.get('emiratesId')?.enable();

          const abstractEnrollmentInfoData = response.data;
          const abstractEnrollmentInfoForm = this.form.get('applicantInfo.abstractEnrollmentInfoForm');

          if (abstractEnrollmentInfoData) {
            abstractEnrollmentInfoForm?.setValue({
              familyBookNumber: abstractEnrollmentInfoData.FamilyBookNo,
              townNumber: abstractEnrollmentInfoData.Town,
              placeOfIssuanceFamilyBook: abstractEnrollmentInfoData.FamilyBookIssuePlace !== '00000000-0000-0000-0000-000000000000'
                ? this.stepperService.getLookup(this.emirates, abstractEnrollmentInfoData.FamilyBookIssuePlace)
                : null
            });

            // Enable/Disable specific fields based on data presence
            abstractEnrollmentInfoData.FamilyBookNo ? abstractEnrollmentInfoForm?.get('familyBookNumber')?.disable() : abstractEnrollmentInfoForm?.get('familyBookNumber')?.enable();
            // abstractEnrollmentInfoData.TownNumber ? abstractEnrollmentInfoForm?.get('townNumber')?.disable() : abstractEnrollmentInfoForm?.get('townNumber')?.enable();
            // abstractEnrollmentInfoData.PlaceOfIssuanceFamilyBook ? abstractEnrollmentInfoForm?.get('placeOfIssuanceFamilyBook')?.disable() : abstractEnrollmentInfoForm?.get('placeOfIssuanceFamilyBook')?.enable();
          }

        }
        this.form.get('applicantInfo')?.patchValue({
          FamilyBookDataId: response.data.FamilyBookDataId,
          FamilyBookMemberId: response.data.FamilyBookMemberId,
          ApplicationFamilyBookDataId: response.data.ApplicationFamilyBookDataId
        });

        this.showMyApplicationsLink = false;
      } 
      else if (response.Status == 4011) {
        this.isEligible = false;
        this.service_uae_national_only = response.MessageEN;
        this.service_uae_national_only_ar = response.MessageAR;
        this.showMyApplicationsLink = true;
      }
      else {
        this.isEligible = false;
        this.service_uae_national_only = response.MessageEN;
        this.service_uae_national_only_ar = response.MessageAR;
        this.showMyApplicationsLink = false;

      }
    });
  }


  get progress() {
    var progressToal = 0;
    if (this.requestForm.valid) {
      progressToal += 50;
    }
    // if (this.attachmentForm.valid) {
    //   progressToal += 40;
    // }
    if (this.summaryForm.valid) {
      progressToal += 10;
    }
    return progressToal;
  }

  selectionChanged(event: any) {
    const currentStep = event.previouslySelectedIndex;
    if (currentStep === 0) {
      if (this.requestForm.invalid) {
        this.requestForm.markAllAsTouched();
        return;
      }
      // } else if (currentStep === 1) {
      //   if (this.attachmentForm.invalid) {
      //     this.attachmentForm.markAllAsTouched();
      //     return;
      //   }
    } else if (currentStep === 1) {
      if (this.summaryForm.invalid) {
        this.summaryForm.markAllAsTouched();
        return;
      }
    }
  }

  submit() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.toastr.error("Invalid Form!", "Failure");
    } else {
      this.toastr.success("Valid Form!", "Success");
    }
  }
}
