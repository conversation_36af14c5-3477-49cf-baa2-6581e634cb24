<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="row align-items-center justify-content-between" style="row-gap: 1rem;">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <h1>{{messageTranslationPrefix+'title' | translate}}</h1>

          <div class="d-block to-conditions to-tooltip">

          </div>
        </div>
      </div>

      <ng-container *ngFor="let document of documentList">
        <div class="col-md-12">
          <app-file-upload [label]="LanguageService.IsArabic ? document.NameArabic : document.NameEnglish"
            [docTypeId]="document.ID"
            [description]="LanguageService.IsArabic ? document.descriptionArabic : document.descriptionEnglish"
            [control]="getFormControl(document.ID)" [columns]="12" (fileUploaded)="onFileUploaded($event)"
            (fileDeleted)="onFileDeleted($event)" [required]="document.Requirement === 'Required'"
            [isNpoServices]="true" [useCustomExtensions]="true"
            [allowedExtensions]="document?.AllowedExtensions??'PNG,JPG,JPEG,PDF'"
            [maxFileSize]="document?.AllowedSize??5" [lang]="LanguageService.IsArabic ? 'ar':'en'"
            [externalInjectFile]="getexternalInjectFile(document.ID)">
          </app-file-upload>
        </div>
      </ng-container>
    </div>
    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" class="btn basic-filled-button" (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button">
        {{"saveAsDraft" | translate }}
      </button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()">
        {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
