import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup, Validators } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';

@Component({
  selector: 'app-donation-date-and-duration',
  templateUrl: './donation-date-and-duration.component.html',
  styleUrls: ['./donation-date-and-duration.component.scss'],
})
export class DonationDateAndDurationComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  countries: Lookup[];
  minDate: Date;
  donationDateOrDurationLookups: Lookup[];


  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.donationDateAndDuration.';

    const today = new Date();
    today.setDate(today.getDate() + 30);
    this.minDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.countries = data?.Countries;
        this.donationDateOrDurationLookups = data?.DonationDateAndDurationType


        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.DonationDateAndDuration);
          } else if (_ && _.isFullDetails == false) {
            this.mapData(_?.DonationDateAndDuration);
          }
        });
      }
    });
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );
    return result;
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'DonationDateAndDuration',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'DonationDateAndDuration',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      DonationDateAndDurationType: this.fb?.DonationDateOrDuration?.value?.ID ?? '',
      DateofReceivingtheDonations: this.fb?.DateOfReceivingDonations?.value ?? '',
      Fromdate: this.fb?.FromDate?.value ?? '',
      Todate: this.fb?.ToDate?.value ?? '',
    };
  };

  mapData = (data: any): void => {
    if (!data) return;

    this.fb.DonationDateOrDuration.setValue(this.donationDateOrDurationLookups?.find((_) => _.ID == data?.DonationDateAndDurationType));

    if (data?.DateofReceivingtheDonations && data?.DateofReceivingtheDonations !== '0001-01-01T00:00:00') {
      this.fb.DateOfReceivingDonations.setValue(data?.DateofReceivingtheDonations);
    }
    if (data?.Fromdate && data?.Fromdate !== '0001-01-01T00:00:00') {
      this.fb.FromDate.setValue(data?.Fromdate);
    }

    if (data?.Todate && data?.Todate !== '0001-01-01T00:00:00') {
      this.fb.ToDate.setValue(data?.Todate);
    }
  };

  selectChange(event: any) {
    if (event.value.ID == 1) {
      this.FormService.addValidators(this.form, ['DateOfReceivingDonations'], [Validators.required]);
      this.FormService.clearFields(this.form, ['FromDate', 'ToDate'], true);
    } else {
      this.FormService.clearFields(this.form, ['DateOfReceivingDonations'], true);

      this.FormService.addValidators(this.form, ['FromDate', 'ToDate'], [Validators.required]);
      this.form.get('FromDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('FromDate')!, this.form.get('ToDate')!);
        if (this.form.get('FromDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('FromDate')?.setErrors(null);
        }
      });
      this.form.get('ToDate')?.valueChanges.subscribe(() => {
        this.validateDateRange(this.form.get('FromDate')!, this.form.get('ToDate')!);
        if (this.form.get('ToDate')?.hasError('matDatetimePickerMin')) {
          this.form.get('ToDate')?.setErrors(null);
        }
      });
    }
  }

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "donationDateAndDuration", this.fb);
  }
}
