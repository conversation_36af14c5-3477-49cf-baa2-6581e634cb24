import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';

@Component({
  selector: 'app-npo-bank-acc-details',
  templateUrl: './npo-bank-acc-details.component.html',
  styleUrls: ['./npo-bank-acc-details.component.scss'],
})
export class NpoBankAccountDetailsComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  accountTypes: Lookup[];
  currencyTypes: Lookup[];
  minDate: Date;

  banks: Lookup[] = [];
  bankAccounts: Lookup[] = [];
  filterBankAccounts: any[] = [];
  establishmentId: string = '';
  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() selectedLegalFormType: number;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.npoBankAccountDetails.';

    const today = new Date();
    today.setDate(today.getDate() + 30);
    this.minDate = today;

    this.establishmentId =
      this.ActivatedRoute.snapshot.paramMap.get('establishmentId') ?? '';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.accountTypes = data?.AccountType;
        this.currencyTypes = data?.Currencies;
        this.getBankList();
        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.NPOBankAccountDetails);
          } else if (_ && _.isFullDetails == false) {
            this.mapData(_?.NPOBankAccountDetails);
          }
        });

        Object.keys(this.form.controls).forEach((control) => {
          if (control !== 'Bank' && control !== 'BankAccount') {
            this.form.get(control)?.disable();
          }
        });
      }
    });

    this.form.get("BankAccount")?.valueChanges.subscribe({
      next: (value) => {
        if (value && value?.ID) {
          this.selectedBankAccountDetails(value?.ID);
        }
      }
    })
  }

  getBankList = (): void => {
    this.NocRequestService.getBankList(this.establishmentId).subscribe((_) => {
      this.banks = [];
      (_.Banks as any[]).forEach((_) => {
        this.banks.push({
          ID: _.recordId,
          NameArabic: _.BankArabicName,
          NameEnglish: _.BankEnglishName,
          ParentID: '',
        });
      });
    });
  };

  selectedBank = (event: any): void => {
    this.NocRequestService.getListOfBankAccounts(
      this.establishmentId,
      event.value.ID
    ).subscribe({
      next: (_) => {
        this.bankAccounts = [];
        this.filterBankAccounts = _.BankAccounts;
        (_.BankAccounts as any[]).forEach((_) => {
          this.bankAccounts.push({
            ID: _.recordId,
            NameArabic: _.AccountNumber,
            NameEnglish: _.AccountNumber,
            ParentID: '',
          });
        });

        if (event?.value?.accountID) {
          this.fb.BankAccount.setValue(
            this.bankAccounts?.find((_) => _.ID === event?.value?.accountID)
          );
          this.selectedBankAccountDetails(event?.value?.accountID);
        }
      },
    });
  };

  selectedBankAccountDetails = (accountId: string): void => {
    if (accountId) {
      const bankAccount: any = this.filterBankAccounts.find(
        (_) => _.recordId == accountId
      );

      if (bankAccount) {
        this.fb.CurrencyType.setValue(
          this.currencyTypes?.find((_) => _.ID == bankAccount?.CurrencyType)
        );
        this.fb.AccountType.setValue(
          this.accountTypes?.find((_) => _.ID == bankAccount?.AccountType)
        );

        if (
          bankAccount?.AccountOpenDate &&
          bankAccount?.AccountOpenDate !== '0001-01-01T00:00:00'
        )
          this.fb.AccountOpenDate.setValue(bankAccount?.AccountOpenDate);

        this.fb.AccountOwnerNameEn.setValue(bankAccount?.AccountOwnerNameEnglish);
        this.fb.AccountOwnerNameAr.setValue(bankAccount?.AccountOwnerNameArabic);
        this.fb.BranchName.setValue(bankAccount?.BranchName);
        this.fb.BankAddress.setValue(bankAccount?.BankAddress);
        this.fb.IBANNumber.setValue(bankAccount?.IBANNumber);
      }
    }
  };

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );
    return result;
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'NPOBankAccountDetails',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'NPOBankAccountDetails',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      bank: this.fb?.Bank?.value?.ID ?? '',
      BankAccount: this.fb?.BankAccount?.value?.ID ?? '',
    };
  };

  mapData = (data: any): void => {
    if (!data) return;
    if (data.bank !== '' && data.bank !== this.EMPTY_GUID) {
      this.fb.Bank.setValue(
        this.banks && this.banks?.length > 0
          ? this.banks?.find((_) => _.ID === data?.bank)
          : data?.bank
      );

      this.selectedBank({
        value: {
          ID: data?.bank,
          accountID: data?.BankAccount,
        },
      });
    }

    if (data.BankAccount !== '' && data.BankAccount !== this.EMPTY_GUID) {
      const selectedBankAccount = this.filterBankAccounts.find(bankAcc => bankAcc.recordId === data?.BankAccount);
      if (selectedBankAccount) {
        this.fb.BankAccount.setValue({
          ID: selectedBankAccount.recordId,
          NameArabic: selectedBankAccount.AccountNumber,
          NameEnglish: selectedBankAccount.AccountNumber,
          ParentID: ''
        });
      }
    }
    this.fb.bankAccountDetails.setValue(data?.bankAccountDetails);
  };

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "npoBankAccountDetails", this.fb);
  }
}
