import { Component, Input, OnInit, OnChanges, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { ValidationService } from '../../../services/validation.service';
import { LanguageService } from '../../../services/language.service';

@Component({
  selector: 'app-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrl: './checkbox.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class CheckboxComponent implements OnInit, OnChanges {
  @Input() label: string;
  @Input() placeholder: string='';
  @Input() hint: string='';
  @Input() control: FormControl;
  @Input() required: boolean=false;
  @Input() columns: number=6;
  @Input() isDisabled: boolean=false;

  @Input() linkLabel?: string;
  @Input() linkClick?: () => void;
  @Input() showHint: boolean=true;

  @Output() onSelectChange: EventEmitter<boolean> = new EventEmitter();

  ngOnInit() {
    if (this.isDisabled) {
      this.control.disable();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isDisabled'] && this.control) {
      if (this.isDisabled) {
        this.control.disable();
      } else {
        this.control.enable();
      }
    }
  } 


  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  constructor(private validationService: ValidationService, protected lang: LanguageService){}

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  change(event:any){
    this.onSelectChange.emit(event);
  }
}
