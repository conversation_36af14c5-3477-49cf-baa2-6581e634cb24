/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EstablishmentBoardOfTrusteesByDecreeComponent } from './establishment-board-of-trustees-by-decree.component';

describe('EstablishmentBoardOfTrusteesByDecreeComponent', () => {
  let component: EstablishmentBoardOfTrusteesByDecreeComponent;
  let fixture: ComponentFixture<EstablishmentBoardOfTrusteesByDecreeComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EstablishmentBoardOfTrusteesByDecreeComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EstablishmentBoardOfTrusteesByDecreeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
