import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormArray, FormGroup, FormControl, Validators } from '@angular/forms';
import { filter, tap } from 'rxjs';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';

@Component({
  selector: 'app-establishment-interim-committee',
  templateUrl: './establishment-interim-committee.component.html',
  styleUrls: ['./establishment-interim-committee.component.scss']
})
export class EstablishmentInterimCommitteeComponent extends MyEstablishmentComponentBase implements OnInit, OnChanges {

  emirates: Lookup[];
  fixedPositions: Lookup[];
  positions: Lookup[];
  membersOptions: Lookup[] = [];
  foundingMemberList: any[] = [];
  maxDate: Date;
  minDate: Date;
  get fbControls(): any {
    return this.form.controls;
  }
  get committeeMembers(): FormArray {
    return this.form.get("members") as FormArray;
  }

  committeeMemberForm: FormGroup;
  get cmControls(): any {
    return this.committeeMemberForm.controls;
  }



  @Input() form: FormGroup;
  @Input() formStatusCode: number = 1;
  @Input() meetingDate: Date;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.interimCommittee.";
    this.intiForm();

    this.StepperService.formData$
      .subscribe((data: any) => {
        this.membersOptions = [];
        this.foundingMemberList = data?.FoundingMembersForm?.members;

        data?.FoundingMembersForm?.members?.forEach(member => {
          if (!this.checkMemberOptions(member?.emiratesId)) {
            this.membersOptions.push(new Lookup(member?.emiratesId, member?.emiratesId, member?.emiratesId, member?.dateOfBirth));
          }
        });

        const totalOfMember = data?.FoundingMembersForm?.members?.length ?? -1;
        if (totalOfMember >= 0)
          this.removeDeletedMemberFromFormData(data?.FoundingMembersForm?.members);
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }

    if (changes['meetingDate']?.currentValue) {
      this.minDate = new Date(this.meetingDate);
    }
  }

  ngOnInit(): void {
    this.loadLookupData();
    this.resetFromValidation(this.form);

    this.pagination();
    this.committeeMembers.valueChanges.subscribe(_ => this.pagination());

    const today = new Date();
    today.setHours(23, 59, 59, 999);
    this.maxDate = today;

    this.form.get('Date')?.valueChanges.subscribe((value) => {
      const inputDate = new Date(value);
      const comparisonDate = new Date(this.meetingDate);

      if (inputDate.getTime() < comparisonDate.getTime()) {
        this.form.get('Date')?.setErrors({
          matDatetimePickerMin: {
            requiredDateTime: comparisonDate.toISOString(),
            actualDateTime: inputDate.toISOString()
          }
        });
      } else {
        this.form.get('Date')?.setErrors(null);
      }
    });


  }

  private removeDeletedMember(members: any[]): void {
    this.committeeMembers?.controls?.forEach((element, index) => {
      const eid = element?.get('name')?.value?.ID;
      const isMemberExist = members.find(member => member?.Contact?.EmirateId === eid);
      if (!isMemberExist) {
        this.committeeMembers.removeAt(index);
        this.removeFromCRM(element?.get('id')?.value, this.RELATIONSHIP_GRID_IN_CRM);
      }
    })

    this.committeeMembers.updateValueAndValidity();
    this.pagination();
  }

  private removeDeletedMemberFromFormData(members: any[]): void {
    if (members?.length > 0) {
      this.committeeMembers?.controls?.forEach((element, index) => {
        const eid = element?.get('name')?.value?.ID;
        const isMemberExist = members.find(member => member?.emiratesId === eid);
        if (!isMemberExist) {
          this.committeeMembers.removeAt(index);
          this.removeFromCRM(element?.get('id')?.value, this.RELATIONSHIP_GRID_IN_CRM);
          this.saveAsDraft(true);
        }
      })


    } else {
      if (this.committeeMembers?.controls?.length > 0) {
        this.committeeMembers?.controls?.forEach((element, index) => {
          this.committeeMembers.removeAt(index);
          this.removeFromCRM(element?.get('id')?.value, this.RELATIONSHIP_GRID_IN_CRM);
          this.saveAsDraft(true);
        })
      }
    }

    this.committeeMembers.updateValueAndValidity();
    this.pagination();
  }


  private loadLookupData(): void {
    this.MyEstablishmentService.lookupData$
      .pipe(
        filter(data => !!data),
        tap(data => {
          this.emirates = data.Emirates;
          this.positions = data.InteriimCommiteePosition;
          this.fixedPositions = data.InteriimCommiteePosition;
          this.subscribeToNpoRequestData();
        })
      )
      .subscribe();
  }

  private subscribeToNpoRequestData(): void {
    this.StepperService.requestData$.subscribe(_ => {
      if (_ && (_.isFullDetails == true)) {
        this.mapData(_.InterimCommitteeForm);
        this.updateMembers(_.FoundingMembersForm);
        // Object.keys(this.form.controls).forEach((control) => {
        //   if (
        //     (this.form.get(control)?.value === null ||
        //       this.form.get(control)?.value === '' ||
        //       !this.form.get(control)?.value) &&
        //     this.isNotAllowedToEdit === false
        //   ) {
        //     this.form.get(control)?.enable();
        //   } else {
        //     this.form.get(control)?.disable();
        //   }
        // });
      }
      else if (_ && (_.isFullDetails == false)) {
        this.committeeMembers.clear();
        this.mapData(_?.InterimCommitteeForm);
        this.updateMembers(_.FoundingMembersForm);
      }
    });

  }


  updateMembers = (data: any): void => {
    this.membersOptions = [];
    this.foundingMemberList = data?.FounderMember;

    data?.FounderMember?.forEach(member => {
      if (!this.checkMemberOptions(member?.Contact?.EmirateId)) {
        this.membersOptions.push(new Lookup(member?.Contact?.EmirateId, member?.Contact?.EmirateId, member?.Contact?.EmirateId, member?.Contact?.EmirateId));
      }
    });
    if (data?.FounderMember?.length > 0)
      this.removeDeletedMember(data?.FounderMember);
  }

  intiForm = (): void => {
    this.committeeMemberForm = this.FormBuilder.group({
      id: [''],
      name: new FormControl("", [Validators.required]),
      position: new FormControl("", [Validators.required]),
      committeeMemberContactId: new FormControl(""),
      committeeMemberName: new FormControl(""),
      SatusReason: [''],
      canEdit: new FormControl('')
    });
  }


  manageMember(item: any, type: GridActionTypesEnum) {
    if (type != GridActionTypesEnum.EDIT)
      this.committeeMembers.push(this.FormBuilder.group({
        id: [item.id],
        name: [item.name],
        position: [item.position],
        committeeMemberContactId: [item.committeeMemberContactId],
        committeeMemberName: [item.committeeMemberName],
        SatusReason: [item.SatusReason?.trim() || 'draft'],
        canEdit: [true]
      }));
    else
      this.committeeMembers.at(this.memberEditIndex).patchValue({
        id: item.id,
        name: item.name,
        position: item.position,
        committeeMemberName: item.committeeMemberName,
        SatusReason: item.SatusReason?.trim() || 'draft',
        canEdit: item.canEdit
      });

    this.committeeMembers.updateValueAndValidity();
  }
  memberEditIndex: number;
  handleEditMember = (data: any, idx: number): void => {
    this.removeSelectedDataFromList();

    this.memberEditIndex = idx;
    const ctrP$ = this.positions.find(_ => _.ID == data?.value?.position?.ID);
    const ctrMP$ = this.membersOptions.find(_ => _.ID == data?.value?.name?.ID);
    if (!ctrP$) {
      this.positions.push(data?.value?.position);
    }
    if (!ctrMP$) {
      this.membersOptions.push(data?.value?.name);
    }

    this.committeeMemberForm.patchValue({
      id: data?.value?.id,
      name: data?.value?.name,
      position: data?.value?.position,
      committeeMemberContactId: data?.value?.committeeMemberContactId,
      committeeMemberName: data?.value?.committeeMemberName,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: data?.value?.canEdit,
    });
    this.FormService.enableFields(this.committeeMemberForm, ['name', 'position']);

    this.removeDublication();
  }

  async removeMember(data: any, idx: number) {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.committeeMembers?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.RELATIONSHIP_GRID_IN_CRM);

      this.addChairman(data);
      this.addEID(data);
      this.committeeMembers.removeAt(idx);
    }
  }

  addChairman = (item: any): void => {
    if (item?.value?.position?.NameEnglish === 'Committee Chairman') {
      const chairmanPosition = this.fixedPositions?.find(position => position.NameEnglish === 'Committee Chairman');
      this.positions.push(new Lookup(chairmanPosition?.ID, 'Committee Chairman', 'رئيس اللجنة', ''));
    }
  }

  addEID = (data: any): void => {
    this.membersOptions.push(new Lookup(data?.value?.name?.NameEnglish, data?.value?.name?.NameEnglish, data?.value?.name?.NameEnglish, ''));
  }

  onModelOpening = (event: any): void => {
    this.removeSelectedDataFromList();
    this.addChairmanIfNotExist();
    this.removeDublication();
  }

  removeSelectedDataFromList() {
    this.committeeMembers?.value?.forEach(element => {
      if (element?.position?.NameEnglish === 'Committee Chairman')
        this.positions = this.positions.filter(_ => _.NameEnglish !== 'Committee Chairman');

      if (this?.membersOptions?.find(_ => _.NameEnglish === element?.name?.NameEnglish))
        this.membersOptions = this.membersOptions.filter(_ => _.NameEnglish !== element?.name?.NameEnglish);
    });
  }

  addChairmanIfNotExist = (): void => {
    const isChairmanExist = this.form?.value?.members.find(member => member?.position?.NameEnglish === 'Committee Chairman');
    const isChairmanExistInPosition = this.positions.find(_ => _?.NameEnglish === 'Committee Chairman');
    const chairmanPosition = this.fixedPositions?.find(position => position.NameEnglish === 'Committee Chairman');

    if (!isChairmanExist && !isChairmanExistInPosition)
      this.positions.push(new Lookup(chairmanPosition?.ID, 'Committee Chairman', 'رئيس اللجنة', ''));
  }

  removeDublication() {
    this.positions = this.positions.filter((value, index, self) => {
      return value && index === self.findIndex(_ => _.NameEnglish === value?.NameEnglish);
    });

    this.membersOptions = this.membersOptions.filter((value, index, self) => {
      return value && index === self.findIndex(_ => _.NameEnglish === value?.NameEnglish);
    });
  }


  isValidForm = (): boolean => {
    // let result: boolean = Object.keys(this.form.controls).every(controlName => {
    //   const control = this.form.get(controlName);
    //   return control?.disabled || control?.valid;
    // });

    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.valid;
    });

    // let committeeMembersLength = this.committeeMembers.length >= 3 && this.committeeMembers.length <= 7;
    // let chairmanExists = !!this.committeeMembers?.controls?.find(c => {
    //   return c.get('position')?.value?.NameEnglish === 'Committee Chairman';
    // });

    return  result;
    // return  result && committeeMembersLength && chairmanExists;
    return true;
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("InterimCommitteeForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("InterimCommitteeForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("InterimCommitteeForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  getMappingObject = (): any => {
    return {
      Npointerimcommitteeandfoundersmeeting: {
        Id: this.fbControls?.MeetingId?.value ?? this.EMPTY_GUID,
        MeetingPlace: this.fbControls?.MeetingPlace?.value ?? '',
        Emirate: this.fbControls?.Emirate?.value?.ID ?? '',

        Definetheadministrativepositions: this.fbControls?.Definetheadministrativepositions?.value ?? '',
        Appointthecommissioner: this.fbControls?.Appointthecommissioner?.value ?? '',

        Meetingdate: this.fbControls?.Date?.value ?? ''
      },
      InterimCommitee: (this.committeeMembers.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: _$.id ?? this.EMPTY_GUID,
          Contact: {
            Id: _$.committeeMemberContactId ?? this.EMPTY_GUID,
            EmirateId: _$.name?.NameEnglish ?? '',
            Dob: ''
          },
          PositionId: _$.position?.ID,
          Name: "",
          Description: "",
          Establishment: this.EMPTY_GUID,
          EstablishmentId: this.EMPTY_GUID,
          Email: "",
          Priority: index + 1,
          SatusReason: _$.SatusReason?.trim() || 'draft'
        }
      }),
    }
  }

  mapData = (data: any): void => {
    if (!data) return;

    this.fbControls.MeetingId.setValue(data?.Npointerimcommitteeandfoundersmeeting?.Id);
    this.fbControls.MeetingPlace.setValue(data?.Npointerimcommitteeandfoundersmeeting?.MeetingPlace);
    this.fbControls.Emirate.setValue(this.emirates?.find(_ => _.ID === data?.Npointerimcommitteeandfoundersmeeting?.Emirate) ?? '');

    this.fbControls.Definetheadministrativepositions.setValue(data?.Npointerimcommitteeandfoundersmeeting?.Definetheadministrativepositions);
    this.fbControls.Appointthecommissioner.setValue(data?.Npointerimcommitteeandfoundersmeeting?.Appointthecommissioner);

    this.fbControls.Date.setValue(data?.Npointerimcommitteeandfoundersmeeting?.Meetingdate);

    data.InterimCommitee = data.InterimCommitee.sort((a, b) => a.Priority - b.Priority);
    data.InterimCommitee.forEach((item: any) => {
      this.committeeMembers.push(this.FormBuilder.group({
        id: item?.Id,
        position: this.fixedPositions?.find(_ => _.ID === item?.PositionId),
        name: new Lookup(item?.Contact?.EmirateId, item?.Contact?.EmirateId, item?.Contact?.EmirateId, item?.Contact?.EmirateId),
        committeeMemberContactId: item?.Contact?.Id,
        committeeMemberName: this.LanguageService.IsArabic ? item?.Contact?.FullnameAr : item?.Contact?.FullName,
        SatusReason: item?.SatusReason?.trim() || 'draft',
        canEdit: true,
      }));
    });
  }

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "interimcommittee", this.fbControls);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  checkCommitteeMembersExist = (id: string): boolean => this.committeeMembers.controls.findIndex(_ => _.get('id')?.value == id) > -1;
  checkMemberOptions = (id: string): boolean => this.membersOptions.findIndex(_ => _.ID == id) > -1;

  page = 1;
  pageSize = 10;
  dataTable: FormArray = this.FormBuilder.array([]);
  get reprsentedDataTable(): FormArray { return this.dataTable; }
  get tableIndex(): number { return (this.page - 1) * (this.pageSize) }
  pagination = (): void => {
    this.dataTable = this.FormBuilder.array([]);
    let data$ = this.committeeMembers.controls.slice((this.page - 1) * this.pageSize, (this.page - 1) * this.pageSize + this.pageSize);
    data$.forEach(_ => this.dataTable.push(_));
  }


  get disableAddMember(): boolean {
    return this.committeeMembers.length >= 5;

  }

  canSeeName = (EID: any): boolean => {
    let member = this.foundingMemberList.find(member => member?.Contact?.EmirateId === EID.ID);
    if (!member) {
      member = this.foundingMemberList.find(member => member?.emiratesId === EID.ID);
    }

    const status = member?.SatusReason ?? member?.Status;

    if (status?.toLowerCase()?.trim() === 'pending confirmation' ||
      status?.toLowerCase()?.trim() === 'draft' ||
      status?.toLowerCase()?.trim() === 'rejected' ||
      status?.toLowerCase()?.trim() === 'refused')
      return false;

    return true;
  }

  checkMemberStatus = (): boolean => {
    const excludedStatusCodes = [1, 100000000];
    if (excludedStatusCodes.includes(this.formStatusCode)) {
      return false;
    }
    if (this.foundingMemberList && this.foundingMemberList?.length > 0) {
      return this.foundingMemberList?.some(member => {
        const status = member?.SatusReason?.trim() || 'draft';
        const normalizedStatus = status?.trim().toLocaleLowerCase().replace(/\s+/g, '');
        return normalizedStatus === "rejected" || normalizedStatus === "refused" || normalizedStatus === "draft" || normalizedStatus === "";
      });
    }

    return false;
  };
}
