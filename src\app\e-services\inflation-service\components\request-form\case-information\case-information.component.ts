import { Component, ElementRef, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Lookup } from '../../../../../shared/models/lookup.model';
import { InflationService } from '../../../services/inflation.service';
import { SwpService } from '../../../../../shared/services/swp.service';
import { LanguageService } from '../../../../../shared/services/language.service';
import { NotifyService } from '../../../../../shared/services/notify.service';
import { StepperService } from '../../../../../shared/services/stepper.service';
import { AuthService } from '../../../../../shared/services/auth.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-case-information',
  templateUrl: './case-information.component.html',
  styleUrl: './case-information.component.scss'
})
export class CaseInformationComponent {
  @Input() form: FormGroup;
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  isUaeNational: boolean = true;
  IsInflationNominatedCaseEdit = false;
  reasonForApplyList: Lookup[];
  beneficiaryAge: any;
  userInfo: any;
  requestId: string | null;
  ContactId: string;
  INFLATION_PROCESS_TEMPLATE_ID = '94b2a9e5-d2ae-ee11-a568-000d3a6c23a9';
  private idsToRemove = [
    'a5c77dbc-f173-ef11-a670-6045bd146374',
    '16f133ff-f173-ef11-a670-6045bd146374',
    'dc096fd9-f273-ef11-a670-6045bd146374',
  ];
  constructor(private inflationService: InflationService, private swpService: SwpService, protected lang: LanguageService,
    private notify: NotifyService,
    private elementRef: ElementRef,
    protected stepper: StepperService,
    private auth: AuthService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    this.requestId = this.route.snapshot.paramMap.get('id');

    const userInfo = this.auth.getUserInfo();
    if (userInfo?.userInfo?.nationalityEN?.toUpperCase() != "UAE") {
      this.isUaeNational = false;
    }
    this.inflationService.contactId$.subscribe(contactId => {
      this.ContactId = contactId || '';
    });

  }

  ngOnInit(): void {

    this.inflationService.lookupData$.subscribe(res => {

      if (res && res.Data && res.Data.InflationCategory) {
        this.reasonForApplyList = this.swpService.translateLookup(res.Data.InflationCategory);
        if (!this.IsInflationNominatedCaseEdit) {
          this.reasonForApplyList = this.reasonForApplyList.filter(
            item => !this.idsToRemove.includes(item.ID.toLowerCase())
          );
        }
        if (this.requestId) {
          this.checkRequest();
        }
      }
    });

    const userInfo = this.auth.getUserInfo();

    //Get ICP Info
    this.inflationService.beneficiaryDetails$.subscribe((res) => {
      if (res?.StatusCode === 200) {
        this.userInfo = res.Data;

        //BENEFICIARY AGE
        const dob = this.userInfo?.DateofBirth;
        const date = new Date(dob);
        const age = Math.floor((Date.now() - date.getTime()) / 3.15576e10);
        this.beneficiaryAge = age;
      }
    });
  }

  checkRequest() {
    this.requestId = this.route.snapshot.paramMap.get('id');
    if (!this.ContactId) {
      this.ContactId = localStorage.getItem('ContactId') || '';
    }
    // const userInfo = this.auth.getUserInfo();
    if (this.requestId) {

      this.inflationService.getRequest(this.requestId, this.ContactId).subscribe((res) => {
        if (res.StatusCode === 200) {
          if (!res.Data.CaseDetails.isInflationNominatedCaseEdit) {
            this.reasonForApplyList = this.reasonForApplyList.filter(
              item => !this.idsToRemove.includes(item.ID.toLowerCase())
            );
          }
          this.form.patchValue({
            reasonForApply: this.stepper.getLookup(this.reasonForApplyList, res.Data.CaseDetails.InflationCategory),
          });
          if (this.form.valid) {
            this.stepper.updateFormData(this.form.getRawValue());
            // this.next.emit();
          }
        } else if (!res.IsSuccess) {
          this.notify.showParsedError('notify.error', res.Errors);
        }
      });
    }
  }

  submit(saveDraft: boolean = true) {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.stepper.scrollToError(this.elementRef);
      this.notify.showError('notify.error', 'notify.invalidForm');
    } else if (saveDraft) {
      const userInfo = this.auth.getUserInfo();
      this.ContactId = localStorage.getItem('ContactId') ?? "";

      const request = {
        UpdateType: !this.requestId ? "CREATE" : saveDraft ? "DRAFT" : "SUBMIT",
        IdCase: this.requestId || undefined,
        CaseDetails: {
          Category: this.form.get("reasonForApply")?.value?.ID
        },
        CaseType: 1,
        Testing: true,
        IdBeneficiary: this.ContactId,
        IdProcessTempalte: this.INFLATION_PROCESS_TEMPLATE_ID
      }

      this.inflationService.SaveRequest(request).subscribe((res) => {
        if (res.IsSuccess) {
          this.stepper.updateFormData(this.form.getRawValue());
          this.notify.showSuccess('notify.success', 'notify.draftSaved');

          if (!this.requestId) {
            this.stepper.setAutoStep(true);
            this.router.navigate(['/e-services/inflation-service', res?.Data?.IdCase]);
          }
          else {
            this.next.emit();
          }
        } else {
          this.notify.showParsedError('notify.error', res.Errors);
        }
      })
    }
  }
  get f(): any {
    return this.form.controls;
  }
  get fb(): any {
    return (this.form as FormGroup).controls;
  }

}
