import { Grid, GridItem, HStack, Text } from "@chakra-ui/react";
import ModalDialog from "components/ModalDialog";
import { Form, Formik } from "formik";
import { IChildFamilyMember, IFamilyMember } from "interfaces/SocialAidForm.interface";
import { useTranslation } from "next-i18next";
import { useState } from "react";
import FamilyMembersTable from "../FamilyMembersTable";
import EditFamilyMemberFormModal from "./EditFamilyMemberFormModal";
import * as functions from "./functions";
import EditChildMemberForm from "./EditChildMemberFormModal";
import {
	CHILD_LIST,
	DUMMY_WIFE_RECORD_ID,
	DUMMY_HUSBAND_RECORD_ID,
	MARRIED_MARITAL_STATUS_ID,
	WIFE_LOOKUP_ID,
	HUSBAND_LOOKUP_ID,
	SPOUSE_INCAPACITATED_FOREIGNER,
	POD_CHILD_SUBCATEGORY_ID,
} from "config";
import { useRouter } from "next/router";

interface Props {
	onSubmit: any;
	members: any;
	setMembers: any;
	childMembers: IChildFamilyMember[];
	setChildMembers: any;
	khulasitQaidNumber: string;
	readOnly: boolean;
	caseType?: number;
	emiratesId?: string;
	maritalStatus?: string;
	gender?: string;
	subcategory?: string;
}

function FamilyMembersInfoForm({
	onSubmit,
	members,
	setMembers,
	childMembers,
	setChildMembers,
	khulasitQaidNumber,
	readOnly = false,
	caseType = 1,
	emiratesId = "",
	maritalStatus = "",
	gender = "",
	subcategory = "",
}: Props) {
	const { t } = useTranslation(["forms", "common"]);
	const router = useRouter();
	const { query } = router;
	const [deleteMemberId, setDeleteMemberId] = useState("");
	const [editMember, setEditMember] = useState<IFamilyMember | null>(null);
	const [editChildMember, setEditChildMember] = useState<{
		memberChildInfo?: IChildFamilyMember;
		member?: IFamilyMember;
	} | null>(null);

	var numberofWifes = 0;
	members.forEach((element) => {
		if (element.Relationship == WIFE_LOOKUP_ID) numberofWifes++;
	});

	if (numberofWifes < 1 && maritalStatus == MARRIED_MARITAL_STATUS_ID && gender == "Male") {
		var wifeObj = {
			IsInformationUpdated: false,
			Relationship: WIFE_LOOKUP_ID,
			dummyID: DUMMY_WIFE_RECORD_ID,
		};
		members.push(wifeObj);
		// createPersonalDocumentDoc(
		// 	emiratesId ? emiratesId : "",
		// 	query.requestId?.toString() ? query.requestId?.toString() : ""
		// );
	}

	// Add dummy husband for Spouse of Incapacitated Foreigner (female)
	var numberofHusbands = 0;
	members.forEach((element) => {
		if (element.Relationship == HUSBAND_LOOKUP_ID) numberofHusbands++;
	});

	if (
		numberofHusbands < 1 &&
		maritalStatus == MARRIED_MARITAL_STATUS_ID &&
		gender == "Female" &&
		subcategory == SPOUSE_INCAPACITATED_FOREIGNER
	) {
		var husbandObj = {
			IsInformationUpdated: false,
			Relationship: HUSBAND_LOOKUP_ID,
			dummyID: DUMMY_HUSBAND_RECORD_ID,
		};
		members.push(husbandObj);
	}
	const handleDeleteMember = () => {
		setMembers((state) => {
			return state.filter((member) => member.Id !== deleteMemberId);
		});
		setDeleteMemberId("");
	};

	const onEditMember = (edittedFamilyMember: IFamilyMember) => {
		setMembers((state) => {
			return state.map((member) => {
				if (
					member.Id === edittedFamilyMember.Id ||
					(edittedFamilyMember?.dummyID == member?.dummyID &&
						member?.dummyID == DUMMY_WIFE_RECORD_ID &&
						edittedFamilyMember?.dummyID == DUMMY_WIFE_RECORD_ID) ||
					(edittedFamilyMember?.dummyID == member?.dummyID &&
						member?.dummyID == DUMMY_HUSBAND_RECORD_ID &&
						edittedFamilyMember?.dummyID == DUMMY_HUSBAND_RECORD_ID)
				) {
					return edittedFamilyMember;
				}
				return member;
			});
		});
		console.log(members);
		setEditMember(null);
	};

	const onEditChildMember = (edittedChildMember: IChildFamilyMember) => {
		setChildMembers((state) => {
			return state.map((member) => {
				if (member.Id === edittedChildMember.Id) {
					return edittedChildMember;
				}
				return member;
			});
		});
		setMembers((state) => {
			return state.map((member) => {
				if (member.Id === edittedChildMember.Id) {
					return { ...member, IsInformationUpdated: true };
				}
				return member;
			});
		});
		setEditChildMember(null);
	};

	const showEditMemberSubForm = (member: IFamilyMember) => {
		// const NEW_POD_CHILD_IDS = [
		// 	"49fd9b8d-6a75-ed11-81ad-002248cbd873", // Father ID
		// 	"51fd9b8d-6a75-ed11-81ad-002248cbd873", // Mother ID
		// ];

		// const extendedChildList =
		// 	subcategory === POD_CHILD_SUBCATEGORY_ID ? [...CHILD_LIST, ...NEW_POD_CHILD_IDS] : CHILD_LIST;

		if (
			member.Relationship === WIFE_LOOKUP_ID ||
			member.Relationship === HUSBAND_LOOKUP_ID ||
			member.Relationship === "49fd9b8d-6a75-ed11-81ad-002248cbd873" ||
			member.Relationship === "51fd9b8d-6a75-ed11-81ad-002248cbd873"
		)
			return setEditMember(member);
		else if (CHILD_LIST.includes(member.Relationship))
			setEditChildMember({ member, memberChildInfo: childMembers.find((m) => m.Id === member.Id) });
	};

	const handleChangeEvent = (type, firstArg, secondArg, formik) => {
		if (type === "text") {
			handleTextChange(firstArg, secondArg, formik);
		}
	};
	const handleTextChange = (event, fieldName, formik) => {
		formik.setFieldValue(fieldName, event?.target?.value || "");
	};
	const { locale } = useRouter();
	return (
		<>
			<Formik
				enableReinitialize
				initialValues={functions.getInitialValues}
				validationSchema={functions.getValidationSchema}
				onSubmit={onSubmit}
			>
				{(formik) => (
					<Form
						onSubmit={(e) => {
							e.preventDefault();
							formik.handleSubmit(e);
						}}
						onChange={(e) => {
							e.preventDefault();
							functions.onChange(e, formik);
						}}
					>
						<Grid
							rowGap={{ base: 6, md: 6 }}
							columnGap={6}
							templateColumns="repeat(2, 1fr)"
							templateRows="auto"
						>
							<GridItem colSpan={{ base: 2, md: 2 }}>
								{members.length > 0 && (
									<>
										<HStack mb={6}>
											<Text>{t("FamilyHousholdName")}</Text>
											<Text>
												{locale === "en" ? members[0]?.FamilyHeadEN : members[0]?.FamilyHeadAR}
											</Text>
										</HStack>
										<HStack mb={6}>
											<Text>{t("khulasitQaidNumber")}</Text>
											<Text>{khulasitQaidNumber}</Text>
										</HStack>
										<FamilyMembersTable
											readOnly={readOnly}
											members={members}
											setDeleteMemberId={setDeleteMemberId}
											setEditMember={showEditMemberSubForm}
											caseType={caseType}
										/>
									</>
								)}
								{members.length === 0 && <Text>{t("noFamilyMembersData")}</Text>}
							</GridItem>
						</Grid>
					</Form>
				)}
			</Formik>
			<ModalDialog
				isModalShow={!!deleteMemberId}
				handleOnClick={handleDeleteMember}
				handleOnClose={() => {
					setDeleteMemberId("");
				}}
				confirmText={t("confirm", { ns: "common" })}
				cancelText={t("cancel", { ns: "common" })}
				imgSrc="/assets/images/deleteIcon.png"
				headerTitle={t("deleteFamilyMember")}
				confirmationMessage={t("deleteFamilyMemberText")}
				undoneAction={t("deleteCantBeUndone")}
			/>
			<EditFamilyMemberFormModal
				member={editMember}
				onEditMember={onEditMember}
				onClose={() => setEditMember(null)}
				readOnly={readOnly}
			/>
			<EditChildMemberForm
				memberInfo={editChildMember}
				onEditMember={onEditChildMember}
				onClose={() => setEditChildMember(null)}
				readOnly={readOnly}
			/>
		</>
	);
}

export default FamilyMembersInfoForm;
