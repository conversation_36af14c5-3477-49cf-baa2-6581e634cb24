.select-with-caret {
    .mat-mdc-select-trigger {
        position: relative;

        // Add a custom arrow using ::after
        &::after {
            content: "\f078"; // Font Awesome arrow
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            color: $aegold-500;
            font-size: 17px;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            transition: transform 0.3s ease; // Smooth rotation animation
        }

        [dir="ltr"] &::after {
            right: 0;
            left: unset;
          }
      
          [dir="rtl"] &::after {
            left: 0;
            right: unset;
          }
    }

    // Rotate the arrow when the dropdown is open
    [aria-expanded="true"] .mat-mdc-select-trigger::after {
        transform: translateY(-50%) rotate(180deg);
    }
}


body [ng-reflect-dir=rtl],
body [dir=rtl] {
    .select-with-caret .mat-mdc-text-field-wrapper.mdc-text-field::after {
        left: 12px;
        right: auto;
    }

}