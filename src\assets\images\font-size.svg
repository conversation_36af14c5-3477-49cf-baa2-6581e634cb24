<?xml version="1.0" encoding="utf-8"?>

<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.0//EN" "http://www.w3.org/TR/2001/REC-SVG-20010904/DTD/svg10.dtd">
<svg version="1.0" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="64px" height="64px" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve">
<g>
	<polyline fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" points="28,43 43,1 44,1 59,43 	"/>
	<line fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" x1="33" y1="29" x2="54" y2="29"/>
</g>
<g>
	<polyline fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" points="5,43 13,20 14,20 22,43 	"/>
	<line fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" x1="7" y1="36" x2="20" y2="36"/>
</g>
<polyline fill="none" stroke="#000000" stroke-width="2" stroke-linejoin="bevel" stroke-miterlimit="10" points="56,63 63,56 
	56,49 "/>
<polyline fill="none" stroke="#000000" stroke-width="2" stroke-linejoin="bevel" stroke-miterlimit="10" points="8,49 1,56 8,63 
	"/>
<g>
	<line fill="none" stroke="#000000" stroke-width="2" stroke-miterlimit="10" x1="63" y1="56" x2="1" y2="56"/>
</g>
</svg>
