import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

// required - min - max - Arabic - English - spaces
export function arabicEnglishSpaceValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (!value) {
      return { required: true };
    }

    if (value.length < 2) {
      return { minLengthIs2: true };
    }

    if (value.length > 500) {
      return { maxLength: true };
    }

    // const pattern = /^[\u0600-\u06FFa-zA-Z\s]*$/;
    //const pattern = /^[\s\u0600-\u06FFa-zA-Z(),\/.]*$/;   
    const pattern = /^[\u0600-\u06FFa-zA-Z0-9\s.,\/\-@!#$%^&*()_+=\[\]{}|:;"'<>\?~`\\]*$/;

    if (!pattern.test(value)) {
      return { invalidCharacters: true };
    }

    return null;
  };
}
