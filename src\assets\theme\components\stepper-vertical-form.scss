.horizontal-scroll {
  .mat-stepper-vertical {
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    padding: 0;

    .mat-step {
      &-icon {
        position: absolute;
        right: -6px;
        width: 20px;
        height: 20px;

        @media (max-width: 767px) {
          display: none;
        }

        &.mat-step-icon-state-edit {
          background-color: transparent;

          &::before {
            // content: "\f058";
            // font-family: "Font Awesome 6 Free";
            // font-weight: 900;
            // color: $aegold-500;
            // font-size: 17px;
          }

          .mat-icon {
            display: none;
          }
        }

        &-state-number {
          display: none;
        }

        &:dir(rtl) {
          right: unset;
          left: 0;
        }
      }

      .mat-step-header {
        padding: 0;
        height: 48px;
        border-bottom: 1px solid #fff;
      }

      &-label {
        width: 100%;

        .mat-step-text-label {
          color: #333;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          font-size: 16px;
          font-style: normal;
          font-weight: $font-medium;
          background-color: $aegold-50;
          border-bottom: 1px solid #fff;
          padding: 24px;
          text-wrap: wrap !important;
          @media (max-width: 1024px) {
            text-wrap: nowrap !important;
          }
        }

        &-selected {
          .mat-step-text-label {
            background-color: $aegold-500;
            color: #fff;
          }
        }
      }

      .mat-vertical-content-container {
        display: none;
      }
    }

    .mat-step-label-selected {
      .mat-step-text-label {
        .mat-step-complete {
          i {
            color: $aegold-500 !important; /* Icon color when selected and completed */
          }

          background-color: #fff !important; /* New background color for selected and completed step */
        }
      }
    }
  }

  .stepper_vertical-content {
    .mat-step {
      @media (max-width: 992px) {
        .mat-step-text-label {
          overflow-wrap: break-word; /* Modern */
          word-break: break-word; /* For compatibility */
          white-space: normal;
        }
      }
    }

    .appform {
      padding-top: 0;

      & > section {
        padding-top: 0;
      }
    }

    .container {
      padding-inline: 6px;

      @media screen and (min-width: 768px) {
        max-width: 100%;
        width: 100%;
        padding: 0;
      }
    }
  }
}
@media screen and (min-width: 768px) {
  .gap-service-32 {
    --bs-gutter-x: 32px;
  }
}

@media screen and (min-width: 768px) and (max-width: 991px) {
  .mat-stepper-vertical .mat-step-label .mat-step-text-label {
    font-size: 13px;
  }
}

.scrollSteps {
  // padding-bottom: 40px;

  // @media(max-width:1024px){
  //   padding-bottom: 32px !important;
  // }

  // @media (min-width: 992px){
  //   min-width: 30% !important;
  // }

  @media (max-width: 767px) {
    padding: 0;
    margin: 0;
    display: flex;
    width: 100%;
    align-items: self-start !important;

    .horizontal-scroll {
      padding: 0;
      margin: 0;
      display: flex;

      .mat-stepper-vertical {
        padding: 0;
        margin: 0;
        display: inline-flex;
        width: max-content !important;
        flex-direction: row;
        background-color: transparent;
        overflow-x: auto;
        flex-wrap: nowrap;
        white-space: nowrap;
        scrollbar-width: none;

        .mat-step-header {
          display: inline-block;
          box-sizing: border-box;
          height: 100%;
          margin: 0;
        }

        .mat-step-label {
          .mat-step-text-label {
            background-color: transparent;
            border-bottom: 1px solid $aeblack-100;
            padding: 12px;
          }

          &-selected {
            .mat-step-text-label {
              color: $aegold-500;
              background-color: transparent;
              border-bottom: 3px solid $aegold-500;
            }
          }
        }
      }
    }
  }
}

.mat-step-complete {
  background: $aegold-500;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  text-align: center;
  align-items: center;
  display: flex;
  justify-content: center;
  position: absolute;
  right: 1rem;
  i {
    font-weight: 900;
    color: #fff;
    font-size: 10px;
  }

  @media (max-width: 1024px) {
    display: none !important;
  }
}

body [ng-reflect-dir="rtl"],
body [dir="rtl"] {
  .mat-step-complete {
    left: 1rem;
    right: auto;
  }
}
