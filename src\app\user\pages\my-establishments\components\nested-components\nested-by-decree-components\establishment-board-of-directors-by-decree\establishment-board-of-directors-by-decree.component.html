<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <!-- <div class="d-flex justify-content-between align-items-center flex-wrap">
      <h1>{{(messageTranslationPrefix+'exceptionRequests') | translate}}</h1>

    </div> -->

    <!-- <div class="row"> -->
      <!-- <div (click)="updateBoardNumberValidators(this.fbControls?.MemberIsExceeds?.value)"> -->
      <!-- <div>
        <app-slide-toggle (click)="updateBoardNumberValidators(this.fbControls?.MemberIsExceeds?.value)" [displayHintInAllSituations]="true"
          [label]="messageTranslationPrefix+'boardMembersExceed11' | translate" [columns]="12"
          [control]="fbControls?.MemberIsExceeds">
        </app-slide-toggle><br />
      </div>

      @if(fbControls?.MemberIsExceeds?.value === true){
      @if(LanguageService.IsArabic) {
      <app-textarea [label]="messageTranslationPrefix+'exceptionReasonAr' | translate"
        [control]="fbControls?.ExceptionReasonAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
        [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fbControls?.ExceptionReasonEn"
        class="ar_lang"></app-textarea>
      <app-textarea [label]="messageTranslationPrefix+'exceptionReasonEn' | translate"
        [control]="fbControls?.ExceptionReasonEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="fbControls?.ExceptionReasonAr"></app-textarea>
      } @else {
      <app-textarea [label]="messageTranslationPrefix+'exceptionReasonEn' | translate"
        [control]="fbControls?.ExceptionReasonEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="fbControls?.ExceptionReasonAr"></app-textarea>
      <app-textarea [label]="messageTranslationPrefix+'exceptionReasonAr' | translate"
        [control]="fbControls?.ExceptionReasonAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
        [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fbControls?.ExceptionReasonEn"
        class="ar_lang"></app-textarea>
      }
      }

    </div> -->
    <div class="row d-flex align-items-lg-end">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
          <h1 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsInformation')
            | translate}}</h1>
        </div>
      </div>
      <div class="col-12 col-lg-6">
        <app-input [type]="InputType.Number" [label]="messageTranslationPrefix+'numberOfBoardMembers' | translate"
          [control]="fbControls?.BoardNumbers"> </app-input>
      </div>
      <div class="col-12 col-lg-6">
        <app-select [label]="messageTranslationPrefix+'frequencyOfBoardMeetings' | translate"
          [control]="fbControls?.FrequencyOfMonthlyBoardMeetings" [data]="frequencyOfBoardMettingsList"
          class="select-with-caret"></app-select>

      </div>
      <div class="col-12 col-lg-6">
        <app-input [label]="messageTranslationPrefix+'localBoardMembersPercentage' | translate"
          [type]="InputType.Percentage" [control]="fbControls?.localBoardMembersPercentage">
        </app-input>
      </div>
      <div class="col-12 col-lg-6">
        <app-select [label]="messageTranslationPrefix+'electionMethod' | translate"
          [control]="fbControls?.ElectionMethod" class="select-with-caret" [data]="electionMethodList">
        </app-select>
      </div>
      <div class="col-12 col-lg-6">
        <app-select (onValueChange)="updateNumberOfPermissibleTermsValidators()"
          [label]="messageTranslationPrefix+'boardMemberReNomination' | translate"
          [control]="fbControls?.CanBeRenominated" class="select-with-caret" [data]="renomintationList">
        </app-select>
      </div>
      <div class="col-12 col-lg-6">
        @if(fbControls?.CanBeRenominated?.value.ID === 1 ||
        !fbControls?.CanBeRenominated?.value)
        {
        <app-select class="select-with-caret"
          [label]="messageTranslationPrefix + 'numberOfPermissibleTerms' | translate"
          [control]="fbControls?.NumberOfPermissibleTerms" [data]="noofPermissableMembersList">
        </app-select>
        }
      </div>
      <div class="col-12">
        <app-select [label]="messageTranslationPrefix+'BoardElectionCycle' | translate"
          [control]="fbControls?.BoardElectionCycle" class="select-with-caret" [data]="boardElectionCycleList">
        </app-select>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
          <h1 class="title_wrapper">{{messageTranslationPrefix+'boardOfDirectorsConditions'
            | translate}}
            <span class="pro_names">{{boardConditions.length}}
              {{messageTranslationPrefix+'boardOfDirectorsConditions' |
              translate}}</span>
          </h1>

        </div>
      </div>
    </div>

    <div class="row section-separator">
      <div style="height: 64px !important;"
        class="col-12 col-lg-5 col-xl-4 d-flex align-items-center justify-content-start" *ngIf="!isNotAllowedToEdit">
        <app-modal
          *ngIf="isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001"
          [form]="boardConditionForm" [title]="messageTranslationPrefix+'addNominationCondition'| translate"
          [size]="'lg'" buttonIcon="fas fa-circle-plus" [viewContentOnly]="false" [buttonEnabled]="true"
          [buttonClass]="'btn btn-primary btn-plus'" [resetThenDismiss]="false"
          [buttonLabel]="messageTranslationPrefix+'addNominationCondition'| translate"
          (submitAction)="manageConditionForm($event,GRID_ACTION_TYPES.ADD)" [showCancelButton]="false">
          <div class="row">
            <app-textarea [label]="messageTranslationPrefix+'nominationConditionEnglish' | translate"
              [control]="bcControls?.nominationEn" [columns]="6" [showGoogleTranslate]="Page_View_Type === 2"
              [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
              [googleTranslateToRelatedCompoent]="bcControls?.nominationAr">
            </app-textarea>
            <app-textarea [label]="messageTranslationPrefix+'nominationConditionArabic' | translate"
              [control]="bcControls?.nominationAr" [columns]="6" [showGoogleTranslate]="Page_View_Type === 2"
              [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
              [googleTranslateToRelatedCompoent]="bcControls?.nominationEn" class="ar_lang"> </app-textarea>
          </div>
        </app-modal>
      </div>
      <!-- @if(boardConditions.length > 0){ -->
        <div formArrayName="Conditions">
          <div style="margin-top: 24px !important;" class="d-block_mobile">
            <div class="table-responsive">
              <table class="my-table">
                <thead>
                  <tr>
                    <th class="align-middle text-center" scope="col"></th>
                    <th class="align-middle text-center" scope="col">#</th>
                    <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+'nominationConditionEnglish'
                      |translate }}</th>
                    <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+'nominationConditionArabic'
                      |translate }}</th>
                    <!-- <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"status" | translate}}</th> -->
                    <th class="align-middle text-center text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                      {{messageTranslationPrefix+"Actions" | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  @for (condition of conditionReprsentedDataTable.controls; track
                  $index) {
                  @let order$=$index + conditionTableIndex + 1;
                  <tr [formGroupName]="$index">
                    <td class="align-middle text-center">
                      <!-- <div class="d-flex gap-1 align-items-center justify-content-center w-100 h-100"
                        *ngIf="condition.get('canEdit')?.value != false">
                        <i class="fas fa-arrow-down-long cursor-pointer" *ngIf="boardConditions.controls.length!==(order$)"
                          (click)="moveTableItem(order$-1,boardConditions.controls,MOVE_TYPE.Down,conditionPagination,countOfPredefindConditions)"></i>
                        <i class="fas fa-arrow-up-long cursor-pointer" *ngIf="(order$)!==(1)"
                          (click)="moveTableItem(order$-1,boardConditions.controls,MOVE_TYPE.Up,conditionPagination,countOfPredefindConditions)"></i>
                      </div> -->
                    </td>
                    <td class="align-middle text-center">{{ order$ }}</td>
                    <td class="align-middle text-center" lang="en">{{condition?.get("nominationEn")?.value }}</td>
                    <td class="align-middle text-center" lang="ar">{{condition?.get("nominationAr")?.value }}</td>
                    <!-- <td class="align-middle text-center">{{getStatusByName(condition.get("SatusReason")?.value) }}</td> -->
                    @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001){
                    @if(condition.get("canEdit")?.value != false){

                    <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit">
                      <button mat-icon-button type="button" class="actions-menu-trigger" [matMenuTriggerFor]="menu"
                        aria-label="Example icon-button with a menu">
                        <mat-icon>more_vert</mat-icon>
                      </button>

                      <mat-menu #menu="matMenu">
                        <ng-container [ngTemplateOutlet]="gridConditionActions"
                          [ngTemplateOutletContext]="{condition: condition,index:  order$ - 1}" />
                      </mat-menu>

                      <div class="actions-direct-buttons">
                        <ng-container [ngTemplateOutlet]="gridConditionActions"
                          [ngTemplateOutletContext]="{condition: condition,index:  order$ - 1}" />
                      </div>
                    </td>
                    } @else {
                    <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>
                    }
                    }
                    @else {
                    <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>
                    } 
                  </tr>
                  }
                </tbody>
              </table>
            </div>
            <div class="d-flex justify-content-center p-2 aegov-pagination">
              <ngb-pagination [maxSize]="10" [ellipses]="true" [(page)]="conditionPage" [pageSize]="conditionPageSize"
                [collectionSize]="boardConditions.length" (pageChange)="conditionPagination()"
                aria-label="Custom pagination">
                <ng-template ngbPaginationPrevious>
                  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                    <path
                      d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                  </svg>
                  <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                </ng-template>
                <ng-template ngbPaginationNext>
                  <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                    <path
                      d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                  </svg>
                </ng-template>
              </ngb-pagination>

            </div>
          </div>

          <div class="figma-card-container">
            @for(condition of conditionReprsentedDataTable.controls; track $index){
            <div class="figma-card">
              @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001)
              {
              @if(condition.get("canEdit")?.value != false){
              <button *ngIf="!isNotAllowedToEdit" mat-icon-button type="button" class="figma-actions-menu-trigger"
                [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu">
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu #menu="matMenu">
                <ng-container [ngTemplateOutlet]="gridConditionActions"
                  [ngTemplateOutletContext]="{condition: condition,index: $index}" />
              </mat-menu>
              }
              }
              <div class="figma-card-content">
                <div class="figma-card-field">
                  <span class="static-value">#</span>
                  <span class="dynamic-value">{{ $index + 1 }}</span>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{messageTranslationPrefix+"nominationConditionEnglish" |translate}}:</div>
                  <div class="dynamic-value" lang="en">{{condition?.get("nominationEn")?.value }}</div>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{messageTranslationPrefix+"nominationConditionArabic" | translate}}:</div>
                  <div class="dynamic-value" lang="ar">{{condition?.get("nominationAr")?.value }}</div>
                </div>
                <!-- <div class="figma-card-field">
                  <div class="static-value">{{ messageTranslationPrefix + 'status'| translate }}:</div>
                  <div class="dynamic-value">
                    {{getStatusByName(condition.get("SatusReason")?.value) }}
                  </div>
                </div> -->
              </div>
            </div>
            }
          </div>
        </div>
      <!-- } -->
    </div>
    <div class="row">
      <div class="col-12">
        <div class="col-12 d-flex justify-content-between flex-wrap">
          <h1 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsPositions')
            | translate}}
            <span class="pro_names">{{boardPositions.length}}
              {{messageTranslationPrefix+'boardOfDirectorsPositions' |
              translate}}</span>
          </h1>

        </div>
      </div>
    </div>

    <div class="row section-separator">
      <div style="height: 64px !important;"
        class="col-12 col-lg-5 col-xl-4 d-flex align-items-center justify-content-start" *ngIf="!isNotAllowedToEdit">
        <app-modal
          *ngIf="isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001"
          [form]="boardPositionsForm" [resetThenDismiss]="false"
          [title]="messageTranslationPrefix+'addPosition'| translate" [size]="'lg'" buttonIcon="fas fa-circle-plus"
          [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary btn-plus'"
          [buttonLabel]="messageTranslationPrefix+'addPosition'| translate"
          (submitAction)="managePositions($event,GRID_ACTION_TYPES.ADD)" [showCancelButton]="false">
          <div class="row">
            <app-input [label]="messageTranslationPrefix+'adminPositionTitleEnglish' | translate"
              [control]="bpControls.positionEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
              [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionAr">
            </app-input>
            <app-input [label]="messageTranslationPrefix+'adminPositionTitleArabic' | translate"
              [control]="bpControls.positionAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
              [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionEn"
              class="ar_lang"> </app-input>
          </div>
        </app-modal>
      </div>

      <!-- @if(boardPositions.length > 0) -->
      <!-- { -->
        <div formArrayName="Positions">
          <div style="margin-top: 24px !important;" class="d-block_mobile">
            <div class="table-responsive">
              <table class="my-table">
                <thead>
                  <tr>
                    <th class="align-middle text-center" scope="col">#</th>
                    <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"adminPositionTitleEnglish"
                      |translate }}</th>
                    <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"adminPositionTitleArabic"
                      |translate }}</th>
                    <!-- <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"status" | translate}}</th> -->
                    <th class="align-middle text-center text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                      {{messageTranslationPrefix+"Actions" | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  @for (position of positionReprsentedDataTable.controls; track
                  $index) {
                  @let order$=$index + positionTableIndex + 1;

                  <tr [formGroupName]="$index">
                    <td class="align-middle text-center">{{ order$ }}</td>
                    <td class="align-middle text-center" lang="en">{{position.get("positionEn")?.value}}</td>
                    <td class="align-middle text-center" lang="ar">{{position.get("positionAr")?.value}}</td>
                    <!-- <td class="align-middle text-center">{{getStatusByName(position.get("SatusReason")?.value) }}</td> -->
                    @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001)
                    {
                    @if(position.get("canEdit")?.value != false)
                    {
                    <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit">
                      <button mat-icon-button type="button" class="actions-menu-trigger" [matMenuTriggerFor]="menu"
                        aria-label="Example icon-button with a menu">
                        <mat-icon>more_vert</mat-icon>
                      </button>

                      <mat-menu #menu="matMenu">
                        <ng-container [ngTemplateOutlet]="gridPositionActions"
                          [ngTemplateOutletContext]="{position: position,index:  order$ - 1}" />
                      </mat-menu>

                      <div class="actions-direct-buttons">
                        <ng-container [ngTemplateOutlet]="gridPositionActions"
                          [ngTemplateOutletContext]="{position: position,index:  order$ - 1}" />
                      </div>
                    </td>
                    } @else {<td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>}

                    }@else {<td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>}
                  </tr>
                  }
                </tbody>
              </table>
            </div>
            <div class="d-flex justify-content-center p-2 aegov-pagination">
              <ngb-pagination [maxSize]="10" [ellipses]="true" [(page)]="positionPage" [pageSize]="positionPageSize"
                [collectionSize]="boardPositions.length" (pageChange)="positionPagination()" aria-label="Custom pagination">
                <ng-template ngbPaginationPrevious>
                  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                    <path
                      d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                  </svg>
                  <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
                </ng-template>
                <ng-template ngbPaginationNext>
                  <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                    <path
                      d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                  </svg>
                </ng-template>
              </ngb-pagination>

            </div>
          </div>

          <div class="figma-card-container">
            @for(position of positionReprsentedDataTable.controls; track $index){
            <div class="figma-card">
              @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001)
              {
              @if(position.get("canEdit")?.value != false){
              <button *ngIf="!isNotAllowedToEdit" mat-icon-button type="button" class="figma-actions-menu-trigger"
                [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu">
                <mat-icon>more_vert</mat-icon>
              </button>

              <mat-menu #menu="matMenu">
                <ng-container [ngTemplateOutlet]="gridPositionActions"
                  [ngTemplateOutletContext]="{position: position,index: $index}" />
              </mat-menu>
              }
              }
              <div class="figma-card-content">
                <div class="figma-card-field">
                  <span class="static-value">#</span>
                  <span class="dynamic-value">{{ $index + 1 }}</span>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{messageTranslationPrefix+"adminPositionTitleEnglish" |translate}}:</div>
                  <div class="dynamic-value" lang="en">{{position.get("positionEn")?.value }}</div>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{messageTranslationPrefix+"adminPositionTitleArabic" | translate}}:</div>
                  <div class="dynamic-value" lang="ar">{{position.get("positionAr")?.value }}</div>
                </div>
                <!-- <div class="figma-card-field">
                  <div class="static-value">{{ messageTranslationPrefix + 'status'| translate }}:</div>
                  <div class="dynamic-value">
                    {{getStatusByName(position.get("SatusReason")?.value) }}
                  </div>
                </div> -->
              </div>
            </div>
            }
          </div>
        </div>
      <!-- } -->

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" class="btn basic-filled-button" (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()">
        {{'Next' | translate}}
      </button>
    </div>
  </form>
</div>

<ng-template #gridConditionActions let-index="index" let-condition="condition">
  <ng-container *ngIf="showIconEdit(isReturnForUpdate,condition)">
    <app-modal [operation]="'edit'" [form]="boardConditionForm" [title]="messageTranslationPrefix+'editCondition'"
      [size]="'lg'" buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      [resetThenDismiss]="false" (submitAction)="manageConditionForm($event, GRID_ACTION_TYPES.EDIT)"
      (opened)="handleEditConditions(condition, index)" [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      [isIcon]="true">
      <div class="row">
        <app-textarea [label]="messageTranslationPrefix+'nominationConditionEnglish' | translate"
          [control]="bcControls?.nominationEn" [columns]="6" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bcControls?.nominationAr">
        </app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'nominationConditionArabic' | translate"
          [control]="bcControls?.nominationAr" [columns]="6" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bcControls?.nominationEn"
          class="ar_lang"> </app-textarea>
      </div>
    </app-modal>
    <app-modal *ngIf="isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001" [operation]="'clone'" [form]="boardConditionForm"
      [title]="messageTranslationPrefix+'cloneCondition'" [size]="'lg'" buttonIcon="content_copy"
      [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'" [resetThenDismiss]="false"
      (submitAction)="manageConditionForm($event,GRID_ACTION_TYPES.CLONE)" (opened)="handleCloneConditions(condition)"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false" [isIcon]="true">
      <div class="row">
        <app-textarea [label]="messageTranslationPrefix+'nominationConditionEnglish' | translate"
          [control]="bcControls?.nominationEn" [columns]="12" [showGoogleTranslate]="Page_View_Type === 2"
          [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="bcControls?.nominationAr">
        </app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'nominationConditionArabic' | translate"
          [control]="bcControls?.nominationAr" [columns]="12" [showGoogleTranslate]="Page_View_Type === 2"
          [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="bcControls?.nominationEn" class="ar_lang"> </app-textarea>
      </div>
    </app-modal>
  </ng-container>
  <button type="button" class="btn text-primary" (click)="removeConditions(condition,index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>

</ng-template>

<ng-template #gridPositionActions let-index="index" let-position="position">
  <ng-container *ngIf="showIconEdit((isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001),position)">
    <app-modal [operation]="'edit'" [form]="boardPositionsForm" [title]="messageTranslationPrefix+'editPosition'"
      [resetThenDismiss]="false" [size]="'lg'" buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true"
      [buttonClass]="'btn btn-primary'" (submitAction)="managePositions($event, GRID_ACTION_TYPES.EDIT)"
      (opened)="handleEditPositions(position, index)" [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      [isIcon]="true">
      <div class="row">
        <app-input [label]="messageTranslationPrefix+'adminPositionTitleEnglish' | translate"
          [control]="bpControls.positionEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionAr">
        </app-input>
        <app-input [label]="messageTranslationPrefix+'adminPositionTitleArabic' | translate"
          [control]="bpControls.positionAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionEn"
          class="ar_lang"> </app-input>
      </div>
    </app-modal>
    <app-modal *ngIf="isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001" [operation]="'clone'" [form]="boardPositionsForm" [resetThenDismiss]="false"
      [title]="messageTranslationPrefix+'clonePosition'" [size]="'lg'" buttonIcon="content_copy"
      [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      (submitAction)="managePositions($event, GRID_ACTION_TYPES.CLONE)" (opened)="handleClonePositions(position)"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false" [isIcon]="true">
      <div class="row">
        <app-input [label]="messageTranslationPrefix+'adminPositionTitleEnglish' | translate"
          [control]="bpControls.positionEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionAr">
        </app-input>
        <app-input [label]="messageTranslationPrefix+'adminPositionTitleArabic' | translate"
          [control]="bpControls.positionAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
          [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="bpControls.positionEn"
          class="ar_lang"> </app-input>
      </div>
    </app-modal>
  </ng-container>
  <button type="button" class="btn text-primary" (click)="removePositions(position,index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>
</ng-template>
