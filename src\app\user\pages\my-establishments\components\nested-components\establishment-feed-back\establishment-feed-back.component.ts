import { Component, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';


@Component({
  selector: 'app-establishment-feed-back',
  templateUrl: './establishment-feed-back.component.html',
  styleUrl: './establishment-feed-back.component.scss'
})

export class EstablishmentFeedBackComponent
  extends MyEstablishmentComponentBase
  implements OnInit {


  @Input() feedbackList: any[] | undefined;
  @Output() activeStepperIndex: EventEmitter<string> = new EventEmitter<string>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.feedBack.';

  }

  ngOnInit(): void { }

  openSection = (sectionName: string): void => {
    this.activeStepperIndex.emit(sectionName);
  }
}
