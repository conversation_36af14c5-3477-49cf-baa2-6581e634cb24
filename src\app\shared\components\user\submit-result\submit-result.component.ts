import { Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { DataService } from '../../../services/data.service';
import { CustomerpulseService } from '../../../services/customerpulse.service';
import { LanguageService } from '../../../services/language.service';
import { AuthService } from '../../../services/auth.service';
import { environment } from '../../../../../environments/environment';

@Component({
  selector: 'app-submit-result',
  templateUrl: './submit-result.component.html',
  styleUrl: './submit-result.component.scss'
})
export class SubmitResultComponent implements OnInit {
  @Input() isSuccess: boolean;
  @Input() applicationNumber: string;
  @Input() isCase: boolean = false;
  requestId: string;
  serviceCatalogId: string;
  public messageTranslationPrefix: string = 'submitResult.';
  userInfo: any;

  constructor(private router: Router, private customerPulseService: CustomerpulseService,
    protected lang: LanguageService, private auth: AuthService,
    private route: ActivatedRoute

  ) {


  }
  ngOnInit(): void {
    this.userInfo = this.auth.getUserInfo();

    this.route.queryParams.subscribe(params => {
      this.requestId = params['requestId'];
      this.serviceCatalogId = params['serviceCatalogId'];
      if (!this.isCase) {
        if (this.isSuccess) {
          this.customerPulseService.postCustomerPulse(this.requestId, this.serviceCatalogId, this.lang.IsArabic ? 'ar' : 'en');
        }
      }
      else
      {
        this.applicationNumber = params['caseNumber'];
        this.customerPulseService.postCustomerPulse(this.requestId, this.serviceCatalogId, this.lang.IsArabic ? 'ar' : 'en');

      }
    });

  }

  goToServices = (): void => { window.location.href = `${environment.websiteUrl}/${this.lang.IsArabic ? 'ar' : 'en'}/digital-services` };
  

  goToMyApps() {
    this.router.navigate(['/user-pages/my-applications']);
  }
  goToMyCases() {
    this.router.navigate(['/user-pages/my-cases']);
  }
}
