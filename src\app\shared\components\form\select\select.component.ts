import { LanguageService } from '../../../services/language.service';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Lookup } from '../../../models/lookup.model';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-select',
  templateUrl: './select.component.html',
  styleUrl: './select.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class SelectComponent {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() data: Lookup[] | undefined = [];
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Input() multiple: boolean = false;

  @Output() onValueChange: EventEmitter<any> = new EventEmitter<any>();


  selectedItem: Lookup;

  _placeholder: string = '';

  public get Placeholder() {
    if (this._placeholder) {
      return this._placeholder;
    }
    this._placeholder = this.placeholder ? `${this.translate.instant(this.placeholder)}` : `${this.translate.instant('Please select...')}${this.translate.instant(this.label)}`;
    return this._placeholder;
  }

  constructor(protected lang: LanguageService, public translate: TranslateService, private validationService: ValidationService) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder = '';
    });

  }

  onSelectionChange(item: any) {
    this.control.setValue(item?.value);
    this.onValueChange.emit(item);
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
