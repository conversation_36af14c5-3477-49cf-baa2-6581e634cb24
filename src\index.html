<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>MOCE Digital Services</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

</head>

<body class="mat-typography" id="app-body">

<app-root></app-root>
  <!-- Sign Language widget script -->
  <!-- <script src="https://cdn.mindrocketsapis.com/client/Latest/jquery.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/jquery.cookie.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/toolkit.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/mrmegapack.bundle.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/MRUAP/mocdnew/integrator-uap.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/signsplayer.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/tooltip_add.js"></script>
<script src="https://cdn.mindrocketsapis.com/client/Latest/mr_advwinr1.js"></script>
<script src=https://cdn.mindrocketsapis.com/client/Mocd/contactusex.js></script>
<script src="https://cdn.mindrocketsapis.com/client/mocdnew/integrator.js"></script> -->

  <script async src="https://www.googletagmanager.com/gtag/js?id=G-JBHFL2QHX1"></script>
  <script async src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIr2Tc3WMfUVoslY65Y6SLq67lmkQ1xPw&amp;libraries=places"></script>
<script type="text/javascript" charset="utf-8">
    /*
      *** gtag
    */
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-JBHFL2QHX1');

    /*
      *** genesys-bootstrap
    */
    (function (g, e, n, es, ys) {
      g['_genesysJs'] = e;
      g[e] = g[e] || function () {
        (g[e].q = g[e].q || []).push(arguments)
      };
      g[e].t = 1 * new Date();
      g[e].c = es;
      ys = document.createElement('script'); ys.async = 1; ys.src = n; ys.charset = 'utf-8'; document.head.appendChild(ys);
    })(window, 'Genesys', 'https://apps.mec1.pure.cloud/genesys-bootstrap/genesys.min.js', {
      environment: 'prod-mec1',
      deploymentId: 'a2492e16-1653-4abf-8b2f-dcea7fc02f88'
    });

    
  </script>
</body>

</html>
