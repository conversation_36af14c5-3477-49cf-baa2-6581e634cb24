import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ActionFormType } from '../../../../e-services/npo-license-declaration/enums/action-form-type';
import { DownloadFileDto } from '../../../../e-services/npo-license-declaration/models/download-file-dto';
import { FoundingMemberConfirmation, PostResponseForFounderMember } from '../../../../e-services/npo-license-declaration/models/founding-member-confirmation.model';
import { AIService } from '../../../../shared/services/AI.service';
import { DataService } from '../../../../shared/services/data.service';

@Injectable({
  providedIn: 'root'
})
export class MyEstablishmentService {

  public FORM_MODE = ActionFormType.CREATE;
  private _lookupData$ = new BehaviorSubject<any>(undefined);
  get lookupData$() {
    return this._lookupData$.asObservable();
  }

  constructor(private dataService: DataService, private aiService: AIService) {

  }
  resetLookupData = (): void => this._lookupData$.next(null);
  getLookupData = (): void => {
    this.dataService.get(`Configuration/NPOLookups`).subscribe((res) => {
      this._lookupData$.next(res);
    });
  }

  getApplicationDetails = (requestId: any) => this.dataService.get(`NPODeclaration/GetRequestByID?requestId=${requestId}`);
  saveDraft = (request: any): Observable<any> => this.dataService.post(`NPODeclaration/Request`, request);
  createEstablishmentObjective = (data: any): Observable<any> => this.dataService.post(`NPOEstablishmentObjective`, data);
  getAllEstablishmentObjectives = (requestId: string): Observable<any> => this.dataService.get(`NPOEstablishmentObjective/GetEstablishmentObjectivesByRequestId?requestId=${requestId}`);
  deleteEstablishmentObjective = (id: string): Observable<any> => this.dataService.delete(`NPOEstablishmentObjective?entityId=${id}`);
  getIcpData = (eid: string): Observable<any> => this.dataService.getIcpData(eid)
  saveMember = (request: any): Observable<any> => this.dataService.post(`NPOEstablishmentMember`, request);
  getFoundingMembers = (requestId: string): Observable<any> => this.dataService.get(`NPOEstablishmentMember/GetNPOMembersByRequestId?requestId=${requestId}`);
  deleteFoundingMember = (id: string): Observable<any> => this.dataService.delete(`NPOEstablishmentMember?entityId=${id}`);
  saveInterimCommitteeData = (request: any): Observable<any> => this.dataService.post('/NPOEstablishmentMeeting', request);
  getInterimCommitteeData = (requestId: string): Observable<any> => this.dataService.get(`NPOEstablishmentMeeting/GetNPOMeetingsByRequestId?requestId=${requestId}`);
  saveFoundingData = (request: any): Observable<any> => this.dataService.post('/NPOEstablishmentMeeting', request);
  getFoundingData = (requestId: any): Observable<any> => this.dataService.get(`NPOEstablishmentMeeting/GetNPOMeetingsByRequestId?requestId=${requestId}`);
  saveBoardOfDirectors = (request: any): Observable<any> => this.dataService.post('/NPODeclaration/UpateRequestBoardOfDirectorsData', request);
  getBoardOfDirectorsData = (requestId: string): Observable<any> => this.dataService.get(`/NPODeclaration/GetRequestBoardOfDirectorsData?requestId=${requestId}`)
  saveMembershipData = (request: any): Observable<any> => this.dataService.post('/NPODeclaration/UpsertRequestMembershipData', request);
  getMembershipData = (requestId: string): Observable<any> => this.dataService.get(`/NPODeclaration/GetRequestMembershipData?requestId=${requestId}`);
  saveAttachment = (request: any): Observable<any> => this.dataService.post(`Document`, request);
  deleteAttachment = (id: string): Observable<any> => this.dataService.delete(`Document?entityId=${id}`);
  fetchAttachments = (requestId: string): Observable<any> => this.dataService.get(`Document/GetDocumentsByRequestId?requestId=${requestId}`);
  submit = (requestId: string): Observable<any> => this.dataService.post(`NPODeclaration/Submit?requestId=${requestId}`, null);
  getLegalFormTypes = (): Observable<any> => this.dataService.get(`/Configuration/GetFormlist`);
  getPositions = (): Observable<any> => this.dataService.get(`Configuration/GetGenralPosition`);
  createUpdateNpoRequestForm = (request: any): Observable<any> => this.dataService.post(`/NPORequestDeclaration/CreateUpdateNpoRequestFormMigrated`, request);

  getEstablishmentDeclarationBYID = (requestId: any): Observable<any> => this.dataService.get(`/NPOEstablishment/GetEstablishmentDeclarationBYID?requestId=${requestId}`);
  getNpoRequestForm = (requestId: any): Observable<any> => this.dataService.get(`/NPORequestDeclaration/GetNPODeclarationBYID?requestId=${requestId}`);


  VerifyFounderMember = (emiratesId: string, dateOfBirth: string): Observable<any> => this.dataService.post(`/NPORequestDeclaration/VerifyFounderMember?EmiratesId=${emiratesId}&DateOfBirth=${dateOfBirth}`, {});
  GetNPoEstbalishmentbyUnifiedNumber = (unifiedNumber: string): Observable<any> => this.dataService.get(`/NPORequestDeclaration/GetNPoEstbalishmentbyUnifiedNumber?unifiedNumber=${unifiedNumber}`);
  delete = (entityId: string, entityName: string): Observable<any> => this.dataService.delete(`/NPORequestDeclaration?entityName=${entityName}&entityId=${entityId}`);

  getFieldInfoForFounderMember = (id: string): Observable<FoundingMemberConfirmation> => this.dataService.get(`NPORequestDeclaration/GetFieldInfoForFounderMember?RelationshipId=${id}`)
  postNpoRequestDeclaration = (payload: PostResponseForFounderMember, id: string) => this.dataService.post(`NPORequestDeclaration/PostResponseForFounderMember?RelationshipId=${id}`, payload);
  downloadNpoFiles = (payload: DownloadFileDto): Observable<any> => this.dataService.post(`Report/download`, payload);
  validateProposedNames = (payload: any): Observable<any> => this.dataService.post(`IaApi`, payload);


  getEstablishmentNMWPBYID = (requestId: any): Observable<any> => this.dataService.get(`/NPOEstablishment/GetNMWEstablishmentDetailsBYID?requestId=${requestId}`);
  getNMWPLookupData = (): void => {
    this.dataService.get(`Configuration/NMWPLookups`).subscribe((res) => {
      this._lookupData$.next(res);
    });
  }

  checkSubmittingFundraisingPermit = (id: any, ispreviousRequests: boolean = true): Observable<any> => this.dataService.get(`/FundRaising/IsAccountRequireException?accountId=${id}&ispreviousRequests=${ispreviousRequests}`);

}
