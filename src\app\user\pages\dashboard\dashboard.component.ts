import { AfterViewInit, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DataService } from '../../../shared/services/data.service';
import { AuthService } from '../../../shared/services/auth.service';
import Swiper, { SwiperOptions, Pagination, Autoplay, Mousewheel } from 'swiper';
import { LanguageService } from '../../../shared/services/language.service';
import { environment } from '../../../../environments/environment';
import { Lookup } from '../../../shared/models/lookup.model';
import { Router } from '@angular/router';

Swiper.use([Pagination, Mousewheel, Autoplay]);



@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit {
  modalForm: FormGroup;
  searchQuery: string = '';
  data: any;
  swiper1: any;
  userInfo: any;
  swiper1Config: SwiperOptions = {
    direction: 'horizontal',
    // pagination: {
    //   el: '.vertical-slider .swiper-pagination',
    //   clickable: true
    // },
    // mousewheel: false,
    slidesPerView: 'auto',
    spaceBetween: 24,
    speed: 750,
    // autoplay: {
    //   delay: 2500,
    //   disableOnInteraction: false
    // },
    // breakpoints:{
    //   576: {
    //     slidesPerView:  'auto',
    //   },
    //   768: {
    //     slidesPerView: 3

    //   },
    //   1200: {
    //     slidesPerView: 5

    //   }
    // }
  };
  swiper2: any;
  swiper2Config: SwiperOptions = {
    direction: 'horizontal',
    pagination: {
      el: '.service-suggestions-swiper .swiper-pagination',
      clickable: true
    },
    // navigation: true,
    navigation: {
      nextEl: '.service-suggestions-swiper .swiper-button-next',
      prevEl:'.service-suggestions-swiper .swiper-button-prev',
    },
    slidesPerView: 1,
    spaceBetween: 24,
    breakpoints:{
      576: {
        slidesPerView:  1,
      },
      768: {
        slidesPerView: 2
      },
      1200: {
        slidesPerView: 3
      }
    }
  };
  allowOnDevOnly: boolean;
  allowOnTestOnly: boolean;
  allowOnProdOnly: boolean;
  applicationsTypes: Lookup[] = [];


  constructor(
    private cdr: ChangeDetectorRef,
    protected translate: TranslateService,
    protected lang: LanguageService,
    private formBuilder: FormBuilder,
    protected auth: AuthService,
    protected router: Router,
    private dataService: DataService
  ) {
    this.modalForm = this.formBuilder.group({
      firstName: ['', Validators.required],
    });

      this.allowOnDevOnly = environment.allowOnDevOnly;
      this.allowOnTestOnly = environment.allowOnTestOnly;
      this.allowOnProdOnly = environment.allowOnProdOnly;

      this.applicationsTypes = [
          // new Lookup(1, 'Social Cases', 'الحالات الاجتماعية'),
          new Lookup(1, 'Applications', 'طلباتي'),
          new Lookup(2, 'Inquiries/Suggestions', 'الاستفسارات والملاحظات'),
          new Lookup(3, 'My Establishments', 'مؤسساتي')
        ];
  }

  //::TODO Replace static Id with CRM_USER_ID (Heaiba)
  ngOnInit(): void {
    this.userInfo = this.auth.getUserInfo();
    this.userInfo.userInfo.firstnameEN = this.userInfo.userInfo.firstnameEN.toLowerCase();
    this.userInfo.userInfo.fullnameEN = this.userInfo.userInfo.fullnameEN.toLowerCase();
    this.dataService
      .get(`Global/GetUserDashboard?UserId=${this.userInfo.crmUserId}`)
      // .get(`Global/GetUserDashboard?UserId=${'5c4f074f-df92-ef11-b111-005056010908'}`)
      .subscribe((res) => {
        this.data = res?.data;
        setTimeout(() => {
          this.swiper1 = new Swiper('.service-status-card-swiper', this.swiper1Config);
          this.swiper2 = new Swiper('.service-suggestions-swiper', this.swiper2Config);
        });
      });
  }
  get f(): any {
    return this.modalForm.controls;
  }

redirectToType(id:number){
    switch (id) {
      // case 1:
      //   this.router.navigate(['/user-pages/my-social-cases']);
      //   break;
      case 1:
        this.router.navigate(['/user-pages/my-applications']);
        break;
      case 2:
        this.router.navigate(['/user-pages/my-cases']);
        break;
        case 3:
        this.router.navigate(['/user-pages/my-establishments']);
        break;
      default:
        console.warn('Unknown item selected');
        break;
    }
    
  }

  onSpeakComplete(text:any){
    this.searchQuery = text;
    this.cdr.detectChanges();
  }

  onSubmit(form: any): void {
    if (form.valid) {
      const query = this.searchQuery.trim();
      var url = `${environment.searchUrl+encodeURIComponent(query)}`;
      if(this.lang.IsArabic)
      {
        url = url.replace('/en/', '/ar/')
      }
      window.location.href = url; // Redirect to the desired URL
      this.searchQuery = '';
    } else {
      alert('Please enter a search query.');
    }
  }

  cleanText(text: string) {
    var resText = text?.replaceAll(',', ' ');
    return resText;
  }

  submitModal(data: any) { }


  slideNext(){
    this.swiper2.slideNext(500);
  }
  slidePrev(){
    this.swiper2.slidePrev(500);
  }

}



