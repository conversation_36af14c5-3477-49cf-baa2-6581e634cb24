
.user-card{
  padding: 32px 24px;
  border-radius: 24px;
  background: $white;
  // display: grid;
  // gap: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  @media only screen and (min-width: 1024px) {
    grid-template-columns: 45% 45%;
    gap: 10%;
  }

  @media (max-width: 1024px){
    flex-wrap: wrap !important;
  }
  
  &__img{
    border-radius: 50%;
    overflow: hidden;
    width: 94px;
    height: 94px;
    min-width: 94px;
    img{
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
  &__title{
    // font-family: Inter !important;
    // &[lang="ar"] {
    //   font-family: "Noto Kufi Arabic" !important;;
    // }
    font-size: 26px !important;
    font-weight: 700 !important;
    line-height: 32px !important;
    color: $aegold-900;
    @media only screen and (min-width: 1024px) {
      font-size: 40px !important;
      line-height: 46px !important;
    }
  }
  &__desc{
    font-size: 16px;
    line-height: 22px; 
    color: $aegold-900;
  }
  .aegov-form-control {
    .form-control-input{
      border-radius: 60px;
      gap: 8px;
      box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.10);
      transition: all 0.3s;
      border: 1px solid transparent;
      padding: 2px 0px;
      &:has(input:focus) {
        border-color: $aegold-200;
      }
      
      span{
        &:first-of-type{
          margin-left: 12px;
          display: flex;
          align-items: center;
          // @media only screen and (min-width: 1024px) {
          //   margin-left: 26px;
          // }
          [ng-reflect-dir=rtl] &, [dir=rtl] &{
            margin-left: 0px;
            margin-right: 12px;
          }
        }
        a{
          &:hover{
            svg{
              fill: $aegold-500;
            }
          }
          svg{
            fill: $aegold-600;
          }
        }
        
      }
      input{
        padding: 4px 0px;
        height: 44px;
        box-shadow: none;
        border: none;
        @media only screen and (min-width: 1024px) {
          height: 52px;
        }
        &:focus , &:focus-visible{
          border: none;
          box-shadow: none;
          
        }
      }
      button.inputLarge {
        border-radius: 50px !important;
        width: 64px !important;
        max-height: 44px !important;
        @media (min-width: 1024px) {
          border-radius: 50px !important;
          width: 72px !important;
          max-height: 52px !important;
       }
       svg{
        [ng-reflect-dir=rtl] &, [dir=rtl] &{
          transform: rotate(180deg);
        }
       }
      }
    }
  }
    

}