import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function dateValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const dateValue = control.value;
    // Check if the date is in a valid format (YYYY-MM-DD)
    const isValidFormat = /^\d{2}\/\d{2}\/\d{4}$/.test(dateValue);
    if (!isValidFormat) {
      return { invalidDate: true };
    }

    const date = new Date(dateValue);
    // Optional: Check if the date is in the past or any other condition
    if (date < new Date()) {
      return { dateInPast: true };
    }

    return null; // Valid date
  };
}
