import { Injectable } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidationErrors } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  constructor(private translate: TranslateService) { }

  getValidatorErrorMessage(validatorName: string, validatorValue?: any, control?: FormControl): string {
    let messageKey = `validationMessage.${validatorName}`;
    let translatedMessage = this.translate.instant(messageKey);

    // const hasArabic = /[\u0600-\u06FF]/.test(control?.value); // Detects Arabic letters
    // const hasEnglish = /[A-Za-z]/.test(control?.value); // Detects English letters

    // if (hasArabic) {
    //   translatedMessage = this.translate.instant(
    //     'validationMessage.englishOnly'
    //   );
    //   return translatedMessage;
    // }

    // if (hasEnglish) {
    //   translatedMessage = this.translate.instant(
    //     'validationMessage.arabicOnly'
    //   );
    //   return translatedMessage;
    // }

    if (validatorName === 'minlength') {
      translatedMessage = this.translate.instant(messageKey, { requiredLength: validatorValue.requiredLength });
    } else if (validatorName === 'maxlength') {
      translatedMessage = this.translate.instant(messageKey, { requiredLength: validatorValue.requiredLength });
    }

    if (validatorName === 'min' || validatorName === 'max') {
      const placeholder = validatorName === 'min' ? 'MinimumValue' : 'MaximumValue';
      translatedMessage = translatedMessage.replace(placeholder, validatorValue[validatorName]);
    }

    if(validatorName === 'pattern') {
      translatedMessage = this.translate.instant(messageKey, { requiredPattern: validatorValue.requiredPattern });
    }

    return translatedMessage;
  }


  resetFromValidation = (form: FormGroup): void => {
    setTimeout(() => {
      Object.keys(form.controls).forEach((key) => {
        const control = form.controls[key];
        control.markAsPristine();
        control.markAsUntouched();
       // control.setErrors(null);
      });
    }, 100);
  }
}
