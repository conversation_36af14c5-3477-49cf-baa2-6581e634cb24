export enum LookupIds {
    DRAFT_STATUS_ID = 'aa414ce7-c46c-ed11-81ac-0022480da504',
    SOCIALAID_TEMPLATE_ID = 'abd2d86d-4a75-4c6d-8156-2ba0b523942f',
    INFLATION_TEMPLATE_ID = '94b2a9e5-d2ae-ee11-a568-000d3a6c23a9',
    INFLATION_TEMPLATE_ID_2 = 'c1b5dedd-62e8-40a6-9a3a-997355dda8ec',
    PENDING_STATUS_ID = '07ddf599-8278-ed11-81ad-0022480da237',

    WIFE_LOOKUP_ID = '4bfd9b8d-6a75-ed11-81ad-002248cbd873',
    DAUGHTER_LOOKUP_ID = '4ffd9b8d-6a75-ed11-81ad-002248cbd873',
    SISTER_LOOKIP_ID = '55fd9b8d-6a75-ed11-81ad-002248cbd873',
    BROTHER_LOOKUP_ID = '53fd9b8d-6a75-ed11-81ad-002248cbd873',
    SON_LOOKUP_ID = '4dfd9b8d-6a75-ed11-81ad-002248cbd873',
    UNDER_45_AGE = 'ff3db27d-9257-ee11-be6f-6045bd14ccdc',
    DIVORCED = '0b3eb27d-9257-ee11-be6f-6045bd14ccdc',
    BORN_UNKNOWN_PARENTS = '1c3eb27d-9257-ee11-be6f-6045bd14ccdc',
    CHILD_OF_PRISONER = '1a3eb27d-9257-ee11-be6f-6045bd14ccdc',
    ORPHANS_ONLY = '183eb27d-9257-ee11-be6f-6045bd14ccdc',
    CHILD_IN_DIFFICULT_SITUATION = '15a6a54f-9257-ee11-be6f-6045bd6aa1f5',
    SPOUSE_INCAPACITATED_FOREIGNER = '163eb27d-9257-ee11-be6f-6045bd14ccdc',
    ABANDONED = '143eb27d-9257-ee11-be6f-6045bd14ccdc',
    COMPLAINT_TYPE = '1',
    INQUIRY_TYPE = '2',
    EMPLOYEDLOWINCOME = '09a6a54f-9257-ee11-be6f-6045bd6aa1f5',
    DIVORCED_WOMAN_ABOVE_45 = 'cdbc97cd-2294-ee11-be37-6045bd6a5296',

    PROCESS_TEMPLATE_ID = 'abd2d86d-4a75-4c6d-8156-2ba0b523942f',
    TESTING_PROCESS_TEMPLATE_ID = 'abd2d86d-4a75-4c6d-8156-2ba0b523942f',
    ALLOWANCE_CATEGORY = '51211E5E-9D6C-ED11-81AC-0022480DA504',

    FARMER_PROCESS_TEMPLATE = '977cf93a-e2ef-ed11-8849-6045bd6a528f',
    // CHILD_LIST = [
    //     SON_LOOKUP_ID,
    //     BROTHER_LOOKUP_ID,
    //     SISTER_LOOKIP_ID,
    //     DAUGHTER_LOOKUP_ID
    // ],

}