import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from './language.service';

@Injectable({
  providedIn: 'root'
})
export class NotifyService {

  constructor(private toastr: ToastrService, private translate: TranslateService, private lang: LanguageService) { }

  showSuccess(titleKey: string, messageKey: string) {
    var title = this.translate.instant(titleKey);
    var message = this.translate.instant(messageKey);
    this.toastr.success(message, title, {
      timeOut: 3000,
      positionClass: 'toast-top-right',
      progressBar: true,
      closeButton: true,
      toastClass: this.lang.IsArabic ? 'ngx-toastr rtl' : 'ngx-toastr'
    });
  }

  showInfo(titleKey: string, messageKey: string) {
    var title = this.translate.instant(titleKey);
    var message = this.translate.instant(messageKey);
    this.toastr.info(message, title, {
      timeOut: 3000,
      positionClass: 'toast-top-right',
      progressBar: true,
      closeButton: true,
      toastClass: this.lang.IsArabic ? 'ngx-toastr rtl' : 'ngx-toastr'
    });
  }

  showError(titleKey: string, messageKey: string) {
    var title = this.translate.instant(titleKey);
    var message = this.translate.instant(messageKey);
    this.toastr.error(message, title, {
      timeOut: 3000,
      positionClass: 'toast-top-right',
      progressBar: true,
      closeButton: true,
      toastClass: this.lang.IsArabic ? 'ngx-toastr rtl' : 'ngx-toastr'
    });
  }

  showParsedError(titleKey: string, errorResponse: string) {
    let parsedErrors;

    try {
      parsedErrors = JSON.parse(errorResponse);
    } catch {
      parsedErrors = { MessageEn: errorResponse, MessageAr: errorResponse };
    }

    const errorMessage = this.lang.IsArabic ? parsedErrors.MessageAr : parsedErrors.MessageEn;
    this.showError(titleKey, errorMessage);
  }

}
