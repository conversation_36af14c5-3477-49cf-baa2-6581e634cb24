import { Component, EventEmitter, HostListener, Input, Output } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { DateAdapter } from '@angular/material/core';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-input-date',
  templateUrl: './input-date.component.html',
  styleUrl: './input-date.component.scss',
  host: {
    '[class]': "'col-md-' + columns"
  }
})
export class InputDateComponent {
  @Input() label: string;
  @Input() placeholder: string = 'DD/MM/YYYY';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;
  @Input() max: Date;
  @Input() min: Date;
  @Output() change: EventEmitter<any> = new EventEmitter<any>();

  isMobileView = window.innerWidth < 1024;

  constructor(private translate: TranslateService, private _adapter: DateAdapter<any>, private validationService: ValidationService) {

    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      _adapter.setLocale(event.lang);
    });

  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobileView = window.innerWidth <= 1024;
  }


  onDateChange(data: any) {
    if (data.value && data.value._isAMomentObject) {
      const formattedDate = data.value.format('MM/DD/YYYY');
      const dateOnlyRegex = /^\d{2}\/\d{2}\/\d{4}$/;
      const dateTimeRegex = /^\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}$/;
      const isValidFormat = dateOnlyRegex.test(formattedDate) || dateTimeRegex.test(formattedDate);
      const date = data.value.toDate();
      if (!isValidFormat) {
        data.targetElement.value = '';
        return;
      }
      if (isNaN(date.getTime())) {
        data.targetElement.value = '';
        return;
      }
      this.change.emit(data);
    } else {
      data.targetElement.value = '';
    }
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }
  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
