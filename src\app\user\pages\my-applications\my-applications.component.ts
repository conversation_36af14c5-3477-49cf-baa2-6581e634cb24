import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';


@Component({
  selector: 'app-my-applications',
  templateUrl: './my-applications.component.html',
  styleUrl: './my-applications.component.scss'
})
export class MyApplicationsComponent {
  status: string;
  disabled: boolean=true;

  constructor(private route: ActivatedRoute){
    var status = this.route.snapshot.paramMap.get('status');

    this.disabled = false;
    if(status)
      {
        this.status = status;
      }
      else
      {
        this.status = '-1';
      }
  }

}
