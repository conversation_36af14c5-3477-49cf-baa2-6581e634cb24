import { Form<PERSON><PERSON>er, FormGroup, ValidatorFn, Validators } from "@angular/forms";
import { MoveTableItem } from "../../../../../e-services/npo-license-declaration/enums/action-form-type";
import { GridActionTypesEnum } from "../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum";
import { LegalFormTypesEnum } from "../../../../../e-services/npo-license-declaration/enums/legal-form-types-enum";
import { InputType } from "../../../../../shared/enums/input-type.enum";
import { ServiceCatalogue } from "../../../../../shared/enums/service-catalogue.enum";
import { TranslateService } from "@ngx-translate/core";
import { ToastrService } from "ngx-toastr";
import { PaymentService } from "../../../../../shared/services/payment.service";
import { ActivatedRoute, Router } from "@angular/router";
import { MyEstablishmentStepperService } from "../../services/my-establishment-stepper.service";
import { MyEstablishmentService } from "../../services/my-establishment.service";
import { LanguageService } from "../../../../../shared/services/language.service";
import { NotifyService } from "../../../../../shared/services/notify.service";
import { ElementRef, Injector } from "@angular/core";
import { AuthService } from "../../../../../shared/services/auth.service";
import { FormService } from "../../../../../shared/services/form.service";
import { AlertService } from "../../../../../shared/services/alert.service";
import { ValidationService } from "../../../../../shared/services/validation.service";
import { NpoLicenseDeclaration } from "../../../../../e-services/npo-license-declaration/models/npo-license.model";
import { SubmitType } from "../../../../../e-services/npo-license-declaration/models/submit-type";
import { Feedback, FileType } from "../../../../../e-services/npo-license-declaration/models/feedback";
import { AlertType } from "../../../../../e-services/npo-license-declaration/models/alert-type";
import { DownloadFileDto } from "../../../../../e-services/npo-license-declaration/models/download-file-dto";
import { DataService } from "../../../../../shared/services/data.service";
import { letterPatternValidatorGeneral } from "../../../../../shared/validators/letter-pattern-validator-accept-general";

//#endregion
export abstract class MyEstablishmentComponentBase {

  //#region "PUBLIC_PROPS"
  public messageTranslationPrefix: string = 'services.npoLicenseDeclaration.';
  public validationTranslationPrefix: string = '';
  public LEGAL_FORM_TYPES = LegalFormTypesEnum;
  public GRID_ACTION_TYPES = GridActionTypesEnum;
  public SERVICE_CATALOGUE = ServiceCatalogue;
  public InputType = InputType;
  public currentApplicantId = 'CB64AD01-F79D-ED11-AAD1-0022480DA504';
  public EMPTY_GUID = "00000000-0000-0000-0000-000000000000";
  public MOVE_TYPE = MoveTableItem;


  public PROPOSED_NAME_GRID_IN_CRM: string = 'mocd_npoproposedname';
  public NPO_NUMBER_GRID_IN_CRM: string = 'mocd_npounionconnexion';
  public OBJECTIVE_GRID_IN_CRM: string = 'mocd_npoestablishmentiobjective';
  public RELATIONSHIP_GRID_IN_CRM: string = 'mocd_nporelationship';
  public MEMBERSHIP_CONDITION_GRID_IN_CRM: string = 'mocd_npomembershipcondition';
  public POSITION_GRID_IN_CRM: string = 'mocd_position';
  public DOCUMENTS_GRID_IN_CRM: string = 'mocd_document';
  public Fund_SERVICES_GRID_IN_CRM: string = 'mocd_nposervice';
  public TARGET_GROUP_GRID_IN_CRM: string = 'mocd_npotargetgroup';
  public ALLOCATED_FUND_GRID_IN_CRM: string = 'mocd_npoallocatedfund';
  public NPO_EXCEPTION_GRID_IN_CRM: string = 'mocd_npoexception';

  //#endregion

  //#region "PRIVATE_PORPS"
  private formBuilder: FormBuilder;
  private translate: TranslateService;
  private stepperService: MyEstablishmentStepperService;
  private toastr: ToastrService;
  private paymentService: PaymentService;
  private route: ActivatedRoute;
  private myEstablishmentService: MyEstablishmentService;
  private lang: LanguageService;
  private notify: NotifyService;
  private elementRef: ElementRef;
  private auth: AuthService;
  private router: Router;
  private formService: FormService;
  private alertService: AlertService;
  private validationService: ValidationService;
  private dataService: DataService;
  //#endregion

  //#region "GTTER"
  protected get FormBuilder(): FormBuilder {
    return this.formBuilder;
  }
  protected get Translation(): TranslateService {
    return this.translate;
  }
  protected get StepperService(): MyEstablishmentStepperService {
    return this.stepperService;
  }
  protected get Toastr(): ToastrService {
    return this.toastr;
  }
  protected get PaymentService(): PaymentService {
    return this.paymentService;
  }
  protected get ActivatedRoute(): ActivatedRoute {
    return this.route;
  }
  protected get MyEstablishmentService(): MyEstablishmentService {
    return this.myEstablishmentService;
  }
  protected get LanguageService(): LanguageService {
    return this.lang;
  }
  protected get NotifyService(): NotifyService {
    return this.notify;
  }
  protected get ElementRef(): ElementRef {
    return this.elementRef;
  }
  protected get AuthService(): AuthService {
    return this.auth;
  }
  protected get Router(): Router {
    return this.router;
  }
  protected get FormService(): FormService {
    return this.formService;
  }
  protected get AlertService(): AlertService {
    return this.alertService;
  }
  protected get ValidationService(): ValidationService {
    return this.validationService;
  }
  protected get DataService(): DataService {
    return this.dataService;
  }
  // view mode = 1 ::: edit mode = 2
  public get Page_View_Type(): number {
    const type = this.ActivatedRoute.snapshot.paramMap.get('type')
    if (type) {
      return Number(type);
    }
    return 0;
  }
  //#endregion

  //#region "CTOR"
  constructor(injector: Injector) {
    this.formBuilder = injector.get(FormBuilder);
    this.translate = injector.get(TranslateService);
    this.stepperService = injector.get(MyEstablishmentStepperService);
    this.toastr = injector.get(ToastrService);
    this.paymentService = injector.get(PaymentService);
    this.route = injector.get(ActivatedRoute);
    this.myEstablishmentService = injector.get(MyEstablishmentService);
    this.lang = injector.get(LanguageService);

    this.notify = injector.get(NotifyService);
    this.elementRef = injector.get(ElementRef);
    this.auth = injector.get(AuthService);
    this.router = injector.get(Router);
    this.formService = injector.get(FormService);
    this.alertService = injector.get(AlertService);
    this.validationService = injector.get(ValidationService);
    this.dataService = injector.get(DataService);
  }
  //#endregion

  //#region "REUSEABLE_FUNCTIONS"

  getRequestMappingObject = (key, value, code): NpoLicenseDeclaration => {
    const formData = this.StepperService.getRequestData();
    formData["Id"] = (this.StepperService.requestId == "" || this.StepperService.requestId == null || this.StepperService.requestId == undefined) ? this.EMPTY_GUID : this.StepperService.requestId;
    formData["StatusCode"] = code;
    if (key && value)
      formData[key] = value;

    /**
     * spical Case
     */
    if (formData?.BoardOfTrusteesForm?.Memberlist?.length) {
      formData.BoardOfTrusteesForm.Memberlist.forEach((member: any) => {
        if (member?.Position?.IdExt === null) {
          member.Position.IdExt = '';
        }
      });
    }

    return formData;
  }


  savingFormData = async (type: SubmitType): Promise<void> => {
    const parms = this.saveAsDraftAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      this.handleSaveRequest(type, true);
    }
  }

  savingLazyFormData = async (type: SubmitType): Promise<void> => {
    this.handleSaveRequest(type);
  }


  handleSaveRequest = (type: SubmitType, isNotLazy: boolean = false): void => {
    let requestCode = (this.StepperService.requestCode != ********* && this.StepperService.requestCode != *********) ? 1 : this.StepperService.requestCode;

    let request = this.getRequestMappingObject(type.key, type.callBack(), requestCode);

    this.MyEstablishmentService.createUpdateNpoRequestForm(request).subscribe((res) => {
      if (res.Status == 2000) {
        if (isNotLazy) {
          this.NotifyService.showSuccess('notify.success', 'notify.draftSaved');
        }

        this.StepperService.updateFormData(type.key, type.form.getRawValue());
        this.StepperService.updateRequestAllData(res?.Npodeclaration);

        this.StepperService.requestId = res?.Id;
        
        this.StepperService.requestCode = res?.Npodeclaration?.StatusCode;
        this.StepperService.establishmentId = res?.EstablishmentId;

        if (!this.StepperService.requestId) {
          this.StepperService.setAutoStep(true);
        } else {
          this.getRouterMode();
          if (!type.isDraft)
            type.next.emit();
        }

      } else {
        let errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
        this.NotifyService.showError('notify.error', errorMessage.trim());
      }
    }, error => {
      this.NotifyService.showError('notify.error', error);
    });
  };

  continueToNextStep = (type: SubmitType, goToNextStep: boolean = true): void => {
    this.StepperService.updateFormData(type.key, type.form.getRawValue());
    this.StepperService.updateRequestData(type.key, type.callBack());

    if (goToNextStep)
      type.next.emit();
  };

  getRouterMode = (): void => {

    let establishmentId = this.ActivatedRoute.snapshot.paramMap.get('establishmentId');
    let requestId = this.ActivatedRoute.snapshot.paramMap.get('id');
    let type = this.ActivatedRoute.snapshot.paramMap.get('type');

    if (!requestId) {
      window.history.replaceState({}, '', `/user-pages/my-establishments/details/${establishmentId}/${type}/${this.StepperService.requestId}`);
      //window.location.reload();
      // this.router.navigate([`/user-pages/my-establishments/details/${establishmentId}/${type}/${this.StepperService.requestId}`], { replaceUrl: false });
      this.StepperService.updateDetectedChangeSubject(1);
    }
  }


  /**
  * this function get updated fildes and table rows into case request edit
  * @param isReturnForUpdate flag for is request edit mode or not
  * @param feedbackList lits of updated fildes
  * @param sectionName section technical name
  * @param fb form controls
  * @returns the array of regarding
  * create by mohamed ali heaiba
  */
  getUpdatedFildes = (
    isReturnForUpdate: boolean,
    feedbackList: Feedback[] | undefined,
    sectionName: string,
    fb: any
  ): string[] => {
    if (isReturnForUpdate) {
      let fildes = feedbackList?.filter(
        (_) =>
          _.SectionTechnicalname?.toLowerCase().trim().replaceAll(' ', '') == sectionName.toLowerCase().trim().replaceAll(' ', '') && _.Type == FileType.Field
      );
      fildes?.forEach((_) => {
        if (_.FieldorGridTechnicalname) {
          let keyName = Object.keys(fb).find(
            (key) =>
              key.toLocaleLowerCase().trim().replaceAll(' ', '') ==
              _.FieldorGridTechnicalname!.toLocaleLowerCase().trim().replaceAll(' ', '')
          );
          if (keyName) fb[keyName].enable();
        }
      });
      let tableRows = feedbackList?.filter(
        (_) =>
          _.SectionTechnicalname?.toLowerCase().trim().replaceAll(' ', '') ==
          sectionName.toLowerCase().trim().replaceAll(' ', '') && _.Type == FileType.Grid
      );
      if (tableRows && tableRows.length > 0)
        return tableRows.map((_) => _.RegardingId ?? '');
    }
    return [];
  };

  checkIsEditSection = (feedbackList: Feedback[] | undefined, sectionName: string, name: string): boolean => {
    let tableRows = feedbackList?.filter(_ => _.SectionTechnicalname?.toLowerCase().trim() == sectionName.toLowerCase().trim() && _.Type == FileType.Grid);
    if (tableRows && tableRows.length > 0) {
      let propIndex$ = tableRows.findIndex(_ => _.FieldorGridTechnicalname?.toLowerCase().trim() == name.toLowerCase().trim());
      if (propIndex$ > -1)
        return true;

      if (tableRows.some(_ => _.Decision == 3))
        return true;
    }
    return false;
  }


  resetFromValidation = (form: FormGroup): void => this.ValidationService.resetFromValidation(form);

  removeFromCRM = (entityId: any, entityName: string): void => {
    if (entityId === undefined || entityId === this.EMPTY_GUID || entityId === "") return;

    this.MyEstablishmentService.delete(entityId, entityName).subscribe((data) => {
      console.log(data);
    });
  }

  deleteAlertParams(): AlertType {
    const translationTitle = this.Translation.instant('alert.deleteTitle');
    const translationMessage = this.Translation.instant('notify.deleteMessage');
    const translations = this.Translation.instant('alert');

    return {
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#b52520',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmDeleteButtonText,
      cancelButtonText: translations.cancelDeleteButtonText
    };
  }

  saveAsDraftAlertParams(): AlertType {
    const translationTitle = this.Translation.instant('alert.saveTheRequestAsDraftTitle');
    const translationMessage = this.Translation.instant('notify.saveTheRequestAsDraftMessage');
    const translations = this.Translation.instant('alert');

    return {
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#92722A',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmButtonText,
      cancelButtonText: translations.cancelButtonText
    };
  }

  moveTableItem<T>(index: number, array: T[], moveType: MoveTableItem, callBack: () => void, preDefinLastIndex: number = -1): void {
    switch (moveType) {
      case MoveTableItem.Down:
        if (index < array.length - 1) {
          if (preDefinLastIndex != -1) {
            if (index + 1 > preDefinLastIndex) {
              this.moveUpItem<T>(index, array, callBack);
            }
          } else {
            this.moveUpItem<T>(index, array, callBack);
          }
        }
        break;
      case MoveTableItem.Up:
        if (index > 0) {
          if (preDefinLastIndex != -1) {
            if (index - 1 >= preDefinLastIndex) {
              this.moveDownItem<T>(index, array, callBack);
            }
          } else {
            this.moveDownItem<T>(index, array, callBack);
          }
        }
        break;
    }
  }

  moveUpItem<T>(index: number, array: T[], callBack: () => void): void {
    const [object] = array.splice(index, 1);
    array.splice(index + 1, 0, object);
    callBack();
  }
  moveDownItem<T>(index: number, array: T[], callBack: () => void): void {
    const [object] = array.splice(index, 1);
    array.splice(index - 1, 0, object);
    callBack();
  }

  /**
   *  we need to check if the value inside the array existed before or no
   * if it didn't exist we return false
   * if it exists we return true
   */
  isDuplicated = (array: any[], value: any): [boolean, number] => {
    if (value === undefined || value === null) return [false, -1]; // Handle undefined/null values as non-duplicates

    let isExist = false;
    let index = -1;

    if (typeof value === 'string') {
      const val = value.trim().toLowerCase();
      isExist = array.some(el => typeof el === 'string' && el.trim().toLowerCase() === val);
    } else if (typeof value === 'number') {
      isExist = array.some(el => el === value);
    } else if (typeof value === 'object' && value !== null) {
      isExist = array.some(el => {
        if (typeof el !== 'object' || el === null) return false;

        // Ensure all keys and values in el match those in value
        return Object.keys(el).every(key => {
          const elValue = el[key];
          const valueToCheck = value[key];

          return (
            elValue === valueToCheck || // Direct equality
            (elValue === null && valueToCheck === "") || // Null equivalent to empty string
            (elValue === "" && valueToCheck === null) // Empty string equivalent to null
          );
        });
      });
    }

    if (isExist) {
      index = array.findIndex(_ => value);
    }

    return [isExist, index];
  }

  generateDistinctId(length: number = 3): string {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    return result;
  }

  changeValidations = (form: FormGroup, condition: boolean, nameAr: string, nameEn: string): void => {
    let baseValidatorsAr = [Validators.required, letterPatternValidatorGeneral('ar')];
    let baseValidatorsEn = [letterPatternValidatorGeneral('en', false)];
    // let baseValidatorsAr = [Validators.required,Validators.pattern(/^[\u0600-\u06FF\s\d\n!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{2,500}$/)];
    // let baseValidatorsEn = [Validators.pattern(/^[A-Za-z\s\d\n!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~]{2,500}$/)];

    // if (condition && this.LanguageService.IsArabic)
    //   baseValidatorsAr.push(Validators.required);
    if (condition && !this.LanguageService.IsArabic) {
      // baseValidatorsEn.push(Validators.required);
      baseValidatorsEn = [Validators.required, letterPatternValidatorGeneral('en', true)];
    }

    this.toggleValidators(form, condition, [nameAr], baseValidatorsAr);
    this.toggleValidators(form, condition, [nameEn], baseValidatorsEn);

    form?.updateValueAndValidity();
  };

  toggleValidators = (form: FormGroup, condition: boolean, fields: string[], validators: ValidatorFn[]) => {
    if (condition) {
      this.FormService.addValidators(form, fields, validators);
    }
    else {
      this.FormService.clearFields(form, fields, true);
    }
  };

  downloadNpoFiles = (contentType: string, name: string, requestId: string, logicalName: string, establishmentId: string): void => {

    const payload: DownloadFileDto = {
      Name: name,
      Parametre: [
        {
          Nom: logicalName,
          ListeValeur: [logicalName == "account" ? establishmentId : requestId]
        }
      ]
    };

    this.MyEstablishmentService.downloadNpoFiles(payload).subscribe(_ => {
      if (_?.success === true) {
        const linkSource = `data:${contentType};base64,${_?.file}`;
        const downloadLink = document.createElement("a");
        downloadLink.href = linkSource;
        downloadLink.download = name;
        downloadLink.click();
      }
    });
  }

  //#endregion


  isFlexLgRowActive(): boolean {
    // Check if the viewport matches the `lg` breakpoint (Bootstrap default is ≥ 992px)
    return window.matchMedia('(min-width: 992px)').matches;
  }

  getStatusByName = (status: number): string => {

    const statuses = {
      en: {
        1: 'Active',
        *********: 'Draft',
        2: 'Inactive',
        *********: 'Declared',
        *********: 'License In-Progress'
      },
      ar: {

        1: 'نشط',
        *********: 'مسودة',
        2: 'غير نشط',
        *********: 'معلن',
        *********: 'الترخيص قيد التنفيذ'
      }
    };

    const language = this.LanguageService?.IsArabic ? 'ar' : 'en';

    return statuses[language][status] || statuses[language][*********];
  };

    getNmwpStatusByName = (status: string): string => {
    const trimmedStatus = status?.trim()?.toLocaleLowerCase()?.replace(/\s+/g, '');

    const statuses = {
      en: {
        proposed: 'Proposed',
        pendingconfirmation: 'Pending Confirmation',
        draft: 'Draft',
        confirmed: 'Confirmed',
        refused: 'Refused',
        approved: 'Approved',
        rejected: 'Rejected',
        requestupdate: 'Request Update',
      },
      ar: {
        proposed: 'مقترح',
        pendingconfirmation: 'في انتظار التأكيد',
        draft: 'مسودة',
        confirmed: 'تم التأكيد',
        refused: 'مرفوض',
        approved: 'موافق',
        rejected: 'مرفوض',
        requestupdate: 'طلب تحديث',
      }
    };

    const language = this.LanguageService?.IsArabic ? 'ar' : 'en';

    return statuses[language][trimmedStatus] || statuses[language].draft;
  };


  canSeeFoundingMember = (status: string): boolean => {
    if (status?.toLowerCase()?.trim() === 'pending confirmation' ||
      status?.toLowerCase()?.trim() === 'draft' ||
      status?.toLowerCase()?.trim() === 'rejected' ||
      status?.toLowerCase()?.trim() === 'refused')
      return false;

    return true;
  };

  showIconEdit = (isReturnForUpdate: boolean | undefined, item: any, key: string = 'SatusReason'): boolean => {
    if (isReturnForUpdate && item) {
      const value = item?.get(key)?.value?.trim().toLocaleLowerCase();
      if (value === "rejected") {
        return false;
      }
    }
    return true;
  };


  isEmptyString = (value: string | undefined): boolean => value === undefined || value === '';
}
