import { Component, Injector, OnInit } from '@angular/core';
import { MyEstablishmentComponentBase } from '../../models/base/my-establishment-component-base';
import { SummaryStepsFullJourny } from '../../../../../shared/models/summry-steps-full-journey';
import { Lookup } from '../../../../../shared/models/lookup.model';
import {
  ALLOCATION_CYCLE,
  BOARD_ELECTION_CYCLE_LIST,
  ELECTION_METHOD_LIST,
  FREQUENCY_OF_BOARD_MEETINGS_LIST,
  FREQUENCY_OF_MEETING,
  NATURE_OF_FUNDS_ALLOCATED,
  NUMBER_OF_PERMISSIBLE_MEMBERS_LIST,
  RENOMINATION_LIST,
  STATUS_LIST,
} from '../../../../../e-services/non-muslims-worship-place-license/models/non-muslims-worship-place-lookup-data';
import { LangChangeEvent } from '@ngx-translate/core';
import { sumBy } from 'lodash';
import { TreeNode } from '../../models/tree-menu';

@Component({
  selector: 'app-nmwp-establishment-details',
  templateUrl: './nmwp-establishment-details.component.html',
  styleUrls: ['./nmwp-establishment-details.component.scss'],
})
export class NmwpEstablishmentDetailsComponent
  extends MyEstablishmentComponentBase
  implements OnInit
{
  viewMode: string = 'Details';

  treeMenu: TreeNode[] = [
    {
      key: 'establishmentDetails',
      allowed: true,
      nameEn: 'My Establishment Details',
      nameAr: 'تفاصيل مؤسستي',
      icon: 'establishmentDetails',
      disabled: false,
      isOrdered: false,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Manage Employees',
      nameAr: 'إدارة الموظفين',
      children: [],
      icon: 'manageEmployees',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'NPO Amendment',
      nameAr: 'تعديل مؤسسة النفع العام',
      children: [],
      icon: 'npoAmendment',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'License Management',
      nameAr: 'إدارة التراخيص',
      children: [],
      icon: 'licenseManagement',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'General Assembly Meetings',
      nameAr: 'اجتماعات الجمعية العامة',
      children: [],
      icon: 'generalAssemblyMeeting',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'fundraising',
      allowed: false,
      nameEn: 'Fundraising',
      nameAr: 'جمع التبرعات',
      children: [
        {
          icon: '',
          key: 'permit',
          allowed: true,
          nameEn: 'Request to Issue Fundraising Permit',
          nameAr: 'طلب تصريح جمع التبرعات',
          route: `fund-raising-service/type-npo/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          disabled: false,
          isOrdered: true,
        },
        // {
        //   icon: '',
        //   key: 'extend',
        //   allowed: true,
        //   nameEn: 'Extend Fundraising Permit',
        //   nameAr: 'تمديد تصريح جمع التبرعات',
        //   route: '',
        //   disabled: true,
        // },
        // {
        //   icon: '',
        //   key: 'establishmentDetails',
        //   allowed: true,
        //   nameEn: 'Appeal to Fundraising Permit Rejection',
        //   nameAr: 'الاستئناف على رفض تصريح جمع التبرعات',
        //   route: '',
        //   disabled: true,
        // },
        {
          icon: '',
          key: 'establishmentDetails',
          allowed: true,
          nameEn: 'Fundraising Permits List',
          nameAr: 'قائمة تصاريح جمع التبرعات',
          click: (): any => (this.viewMode = 'PermitList'),
          disabled: false,
          isOrdered: true,
        },
      ],
      icon: 'fundraising',
      disabled: false,
      isOrdered: true,
    },
    {
      key: 'events',
      allowed: true,
      nameEn: 'Events / Activities',
      nameAr: 'الفعاليات / الانشطة',
      children: [
        {
          key: 'organizingEventsAndActivities',
          allowed: false,
          nameEn: 'Organize Activities / Events (Inside UAE)',
          nameAr: 'تنظيم أنشطة/فعاليات (داخل دولة الإمارات العربية المتحدة)',
          icon: '',
          // we need to add the indication of NMWP or NPO comping from backend
          route: `organizing-events-and-activities/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          disabled: false,
          isOrdered: true,
        },
        {
          key: 'participateInActivitiesAndEventsInsideAndOutsideUAE',
          allowed: true,
          nameEn:
            'Participate in Activities and Events Request (Inside/Outside UAE)',
          nameAr:
            'مشاركة في الأنشطة والفعاليات (داخل/خارج الإمارات العربية المتحدة)',
          icon: '',
          route: `activities-and-events-participation/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          disabled: false,
          isOrdered: true,
        },
      ],
      icon: 'events',
      disabled: false,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Investment',
      nameAr: 'استثمار',
      children: [],
      icon: 'investment',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'General Requests',
      nameAr: 'الطلبات العامة',
      children: [],
      icon: 'generalRequests',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Voluntary Dissolution & Liquidation',
      nameAr: 'الحل والتصفية الطوعية',
      children: [],
      icon: 'voluntaryDissolutionAndLiquidation',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'establishmentDetails',
      allowed: false,
      nameEn: 'Decisions and Regulations',
      nameAr: 'القرارات واللوائح',
      children: [],
      icon: 'decisionsAndRegulations',
      disabled: true,
      isOrdered: true,
    },
    {
      key: 'joiningAndAffiliatingAssociations',
      allowed: false,
      nameEn:
        'Affiliate, Subscribe, or Join Associations or Regional and International Entities',
      nameAr:
        'طلب تصريح الانتساب أو الاشتراك أو الانضمام إلى الجمعيات والهيئات الإقليمية أو الدولية',
      icon: 'joiningAndAffiliatingAssociations',
      route: `joining-and-affiliating-associations/${this.ActivatedRoute.snapshot.paramMap.get(
        'establishmentId'
      )}`,
      disabled: false,
      isOrdered: true,
    },
    {
      key: 'branches',
      allowed: false,
      nameEn: 'Manage Branches',
      nameAr: 'إدارة الفروع',
      icon: 'manageBranches',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'openingBranchesRequest',
          allowed: true,
          nameEn: 'Request for Approval of Opening NPO Branch ',
          nameAr: ' طلب الموافقة على إنشاء فرع مؤسسة نفع عام ',
          route: `request-for-approval-of-opening-npo-branch/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
          icon: '',
          disabled: false,
          isOrdered: true,
        },
      ],
    },
    {
      key: 'nocRequest',
      allowed: false,
      nameEn: 'Request for Receive Donations NOC',
      nameAr: 'طلب الحصول على شهادة عدم ممانعة لتلقي التبرعات',
      icon: 'nocRequest',
      disabled: false,
      isOrdered: true,
      route: `noc-request/${this.ActivatedRoute.snapshot.paramMap.get(
        'establishmentId'
      )}`,
    },
    {
      key: 'banks',
      allowed: false,
      nameEn: 'Bank Account Management',
      nameAr: 'ادارة الحساب البنكي',
      icon: 'banks',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'newBankAccountCertificate',
          allowed: true,
          nameEn: 'Bank Accounts Management Procedures',
          nameAr: 'إجراءات إدارة الحسابات المصرفية',
          icon: '',
          disabled: false,
          isOrdered: true,
          route: `request-to-open-new-bank-account-certificate/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
        },
        {
          key: 'npoBankAccountsList',
          allowed: true,
          nameEn: 'NPO Bank Accounts List',
          nameAr: 'قائمة الحسابات البنكية للمؤسسة',
          icon: '',
          disabled: true,
          isOrdered: true,
          route: ``,
        },
      ],
    },
    {
      key: 'membersList',
      allowed: false,
      nameEn: 'Members List',
      nameAr: 'قائمة الاعضاء',
      icon: 'membersList',
      disabled: false,
      isOrdered: true,
      children: [
        {
          key: 'pendingMembershipRequests',
          allowed: true,
          nameEn: 'Pending Membership Requests',
          nameAr: 'طلبات العضوية المعلقة',
          icon: '',
          disabled: true,
          isOrdered: true,
          click: (): any => (this.viewMode = 'PendingMemrshipRequests'),
        },
        {
          key: 'registerNewMember',
          allowed: true,
          nameEn: 'Register New Member',
          nameAr: 'تسجيل عضو جديد',
          icon: '',
          disabled: false,
          isOrdered: true,
          route: `request-to-join-npo/${this.ActivatedRoute.snapshot.paramMap.get(
            'establishmentId'
          )}`,
        },
      ],
    },
  ];

  legalFormId: any;
  requestId: any;
  userInfo: any;
  formData: any;
  feedbackList: any;
  journeySteps: SummaryStepsFullJourny[] = [];
  panelOpenState: boolean = false;
  emirates: Lookup[] = [];
  EntitySectorTypes: Lookup[];
  positions: Lookup[] = [];
  worshipPlaceTypesCollection: Lookup[];
  religions: Lookup[];
  sectsCollection: Lookup[];
  countries: Lookup[];
  requestReasonData: Lookup[] = [
    {
      ID: '3EE61E21-43F8-EF11-B112-005056010908',
      NameArabic: 'المبنى لا زال قيد الإنشاء',
      NameEnglish: 'The building is still under construction',
      ParentID: '',
    },
    {
      ID: 'b61dea2f-43f8-ef11-b112-005056010908',
      NameArabic: 'تعذر استكمال الموافقات من الجهات ذات العلاقة',
      NameEnglish: 'Unable to complete approvals from the relevant authorities',
      ParentID: '',
    },
    {
      ID: '4aa3b56c-43f8-ef11-b112-005056010908',
      NameArabic: 'كلا السببين',
      NameEnglish: 'Both reasons',
      ParentID: '',
    },
    {
      ID: '2623ac75-43f8-ef11-b112-005056010908',
      NameArabic: 'أخرى',
      NameEnglish: 'Other',
      ParentID: '',
    },
  ];
  npoStatus = STATUS_LIST;
  public docTypes: any[];
  applicationNumber: string = '';
  NPO_SERVICES_CATALOGUE_NAME: string =
    'Issue License for Non-Muslim Worship Place';

  serviceCatalogueID: string = '';

  reportParameter: any[] = [];
  downloadButtons: any[] = [];
  NPOHistoryRequestList: any[] = [];
  requestNumber: string = '';
  rerender: boolean = false;
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix =
      'services.nonMuslimsWorshipPlaceLicense.forms.requestDetails.';
  }

  ngOnInit() {
    this.MyEstablishmentService.getNMWPLookupData();
    this.MyEstablishmentService.lookupData$.subscribe((data) => {
      if (data) {
        this.emirates = data?.Emirates;
        this.positions = data?.InteriimCommiteePosition;
        this.reportParameter = data?.ReportParametre;

        this.countries = data?.Countries;
        this.religions = data?.religion;
        this.sectsCollection = data?.religionSector;
        this.worshipPlaceTypesCollection = data?.worshipPlaceType;

        this.ActivatedRoute.data.subscribe(({ establishmentData }) => {
          this.formData = establishmentData?.data[0];

          if (
            this.formData?.BasicInformationForm
              ?.IsEligableForOrganizeActivitiesEvents
          ) {
            this.changeTreeMenuVisibilityByParent(
              'events',
              'organizingEventsAndActivities',
              true
            );
          }

          this.requestNumber = establishmentData?.data[1]?.UnifiedNumber;
          this.getData(establishmentData);
          this.StepperService.setRequestData(establishmentData?.data[0]);
          this.StepperService.requestId = establishmentData?.data[0]?.Id;
          this.StepperService.requestCode =
            establishmentData?.data[0]?.StatusCode;

          this.docTypes = data.DocumentType;
          const serviceCatalogues = data.CrmConfiguration;
          const legalFormType =
            establishmentData?.data[0]?.BasicInformationForm?.npoform;

          this.applicationNumber =
            establishmentData?.data[0]?.BasicInformationForm?.name;

          const service = serviceCatalogues.find(
            (_) =>
              _.Name.toLocaleLowerCase().trim() ==
              this.NPO_SERVICES_CATALOGUE_NAME.toLocaleLowerCase().trim()
          );
          if (service) {
            this.docTypes = this.docTypes.filter(
              (_) => _.ServiceCatalogue === service.Value
            );
          }

          this.NPOHistoryRequestList =
            establishmentData?.data[3]?.NPOHistoryRequestList ?? [];

          this.getDownloadButton(establishmentData?.data[0]?.StatusCode);
          this.checkTempleteReplace();
          this.rerender = true;
        });
      }
    });

    this.Translation.onLangChange.subscribe((event: LangChangeEvent) => {
      this.rerender = false;
      setTimeout(() => {
        this.rerender = true;
      }, 1000);
    });
  }

  changeTreeMenuVisibility(key: string, allowed: boolean): void {
    let menu = this.treeMenu.find((tree) => tree.key === key);
    if (menu) menu.allowed = allowed;
  }

  changeTreeMenuVisibilityByParent(
    parentKey: string,
    key: string,
    allowed: boolean
  ): void {
    let menu = this.treeMenu
      .find((tree) => tree.key === parentKey)
      ?.children?.find((_) => _.key === key);
    if (menu) menu.allowed = allowed;
  }

  getStatusByNameDataRequest = (status: string): string => {
    const trimmedStatus = status
      ?.trim()
      ?.toLocaleLowerCase()
      ?.replace(/\s+/g, '');

    const statuses = {
      en: {
        proposed: 'Proposed',
        pendingconfirmation: 'Pending Confirmation',
        draft: 'Draft',
        confirmed: 'Confirmed',
        refused: 'Refused',
        approved: 'Approved',
        rejected: 'Rejected',
        requestupdate: 'Request Update',
      },
      ar: {
        proposed: 'مقترح',
        pendingconfirmation: 'في انتظار التأكيد',
        draft: 'مسودة',
        confirmed: 'تم التأكيد',
        refused: 'مرفوض',
        approved: 'موافق',
        rejected: 'مرفوض',
        requestupdate: 'طلب تحديث',
      },
    };

    const language = this.LanguageService?.IsArabic ? 'ar' : 'en';

    return statuses[language][trimmedStatus] || statuses[language].draft;
  };

  getDocTypeName = (id: string): string => {
    let doctype = this.docTypes?.find((_) => _.ID == id);
    return (
      (this.LanguageService.IsArabic
        ? doctype?.NameArabic
        : doctype?.NameEnglish) ?? ''
    );
  };

  getDownloadButton = (statusCode: number): void => {
    if (!this.legalFormTypeTitleEn) {
      return;
    }
    const legalFormType = this.legalFormTypeTitleEn.toLocaleLowerCase().trim();
    const collection = this.reportParameter.filter(
      (param) => param.LegalForm?.toLocaleLowerCase().trim() === legalFormType
    );
    if (statusCode == 1 || statusCode == 100000001) {
      this.downloadButtons = collection.filter(
        (_) =>
          _.RequestStatus == 1 ||
          _.RequestStatus == null ||
          _.RequestStatus == 100000001
      );
    } else {
      this.downloadButtons = collection.filter(
        (_) => _.RequestStatus == statusCode || _.RequestStatus == null
      );
    }
  };

  getData(res: any): void {
    this.formData = res?.data[0];
    this.legalFormId = this.formData?.BasicInformationForm?.npoform;
    this.feedbackList = res?.data[1]?.FeedbackList;

    this.collectionSizeProposedName =
      this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
    this.filteredCountProposedName =
      this.formData?.BasicInformationForm?.ProposedNames?.length || 0;
    this.getPremiumDataProposedName();

    this.collectionSizeFoundingMember =
      this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
    this.filteredCountFoundingMember =
      this.formData?.FoundingMembersForm?.FounderMember?.length || 0;
    this.getPremiumDataFoundingMember();

    this.collectionSizePurposesAndActivities =
      this.formData?.PurposesAndActivitiesForm?.PurposesAndActivities?.length ||
      0;
    this.filteredCountPurposesAndActivities =
      this.formData?.PurposesAndActivitiesForm?.PurposesAndActivities?.length ||
      0;
    this.getPremiumDataPurposesAndActivities();

    this.collectionSizeInterimMembers =
      this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
    this.filteredCountInterimMembers =
      this.formData?.InterimCommitteeForm?.InterimCommitee?.length || 0;
    this.getPremiumDataInterimCommittee();

    this.collectionSizeMemberShip =
      this.formData?.MembershipForm?.MembershipConditions?.length || 0;
    this.filteredCountMemberShip =
      this.formData?.MembershipForm?.MembershipConditions?.length || 0;
    this.getPremiumDataMemberShip();

    this.collectionSizeBoardConditions =
      this.formData?.BoardOfInformationForm?.MembershipConditions?.length || 0;
    this.filteredCountBoardConditions =
      this.formData?.BoardOfInformationForm?.MembershipConditions?.length || 0;
    this.getPremiumDataBoardConditions();

    this.collectionSizeBoardPositions =
      this.formData?.BoardOfInformationForm?.Positions?.length || 0;
    this.filteredCountBoardPositions =
      this.formData?.BoardOfInformationForm?.Positions?.length || 0;
    this.getPremiumDataBoardPositions();

    this.collectionSizeExtensionRequests =
      this.formData?.ExtensionRequestForm?.ExtensionRequests?.length || 0;
    this.filteredCountExtensionRequests =
      this.formData?.ExtensionRequestForm?.ExtensionRequests?.length || 0;
    this.getPremiumDataExtensionRequests();
  }

  legalFromTypes: Lookup[] = [];
  getLegalFormTypes = (): void => {
    this.MyEstablishmentService.getLegalFormTypes().subscribe((_) => {
      this.legalFromTypes = _;
    });
  };

  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes.find((_) => _.ID == this.legalFormId);
    if (type) {
      return this.LanguageService.IsArabic ? type.NameArabic : type.NameEnglish;
    }
    return '';
  }

  get legalFormTypeTitleEn(): string {
    let type = this.legalFromTypes.find((_) => _.ID == this.legalFormId);
    if (type) {
      return type.NameEnglish;
    }
    return '';
  }

  get requestStatus(): string {
    let status = this.npoStatus.find(
      (status) => status.Code == this.formData?.StatusCode
    );
    return this.LanguageService?.IsArabic == true
      ? status?.StatusAR ?? ''
      : status?.Status ?? '';
  }

  getReligion(id: any): string {
    const emirate = this.religions?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getSect(id: any): string {
    const emirate = this.sectsCollection?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getWorshipPlaceType(id: any): string {
    const emirate = this.worshipPlaceTypesCollection?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getCountry(id: any): string {
    const emirate = this.countries?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getEmirate(emirateId: any): string {
    const emirate = this.emirates?.find((e) => e.ID == emirateId);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getEntitySectorType = (id): string => {
    const type = this.EntitySectorTypes?.find((e) => e.ID == id);
    return this.LanguageService.IsArabic
      ? type?.NameArabic ?? ''
      : type?.NameEnglish ?? '';
  };

  getPosition(positionId: any): string {
    const postion = this.positions?.find((_) => _.ID === positionId);
    return this.LanguageService.IsArabic
      ? postion?.NameArabic ?? ''
      : postion?.NameEnglish ?? '';
  }

  getRequestReason(id: any): string {
    const reason = this.requestReasonData?.find((_) => _.ID === id);
    return this.LanguageService.IsArabic
      ? reason?.NameArabic ?? ''
      : reason?.NameEnglish ?? '';
  }

  getFrequencyOfBoardMeetings(id: any): string {
    const frequency = FREQUENCY_OF_BOARD_MEETINGS_LIST?.find(
      (freq) => freq.ID == id
    );
    return this.LanguageService.IsArabic
      ? frequency?.NameArabic ?? ''
      : frequency?.NameEnglish ?? '';
  }

  getElectionMethod(id: any): string {
    const method = ELECTION_METHOD_LIST?.find((m) => m.ID == id);
    return this.LanguageService.IsArabic
      ? method?.NameArabic ?? ''
      : method?.NameEnglish ?? '';
  }

  getRenominationStatus(id: any): string {
    const renomination = RENOMINATION_LIST?.find((r) => r.ID == id);
    return this.LanguageService.IsArabic
      ? renomination?.NameArabic ?? ''
      : renomination?.NameEnglish ?? '';
  }

  getPermissibleTerms(id: any): string {
    const terms = NUMBER_OF_PERMISSIBLE_MEMBERS_LIST?.find((t) => t.ID == id);
    return this.LanguageService.IsArabic
      ? terms?.NameArabic ?? ''
      : terms?.NameEnglish ?? '';
  }

  getBoardElectionCycle(id: any): string {
    const cycle = BOARD_ELECTION_CYCLE_LIST?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? cycle?.NameArabic ?? ''
      : cycle?.NameEnglish ?? '';
  }

  getAnnualMembershipDueDateValue() {
    return this.LanguageService.IsArabic
      ? 'مع بداية السنة المالية (جزء من قيمة الاشتراك)'
      : 'Beginning of the fiscal year(Partial subscription Amount)';
  }

  getFrequencyOfMeetings(id: any): string {
    const frequencyOfMeetings = FREQUENCY_OF_MEETING?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? frequencyOfMeetings?.NameArabic ?? ''
      : frequencyOfMeetings?.NameEnglish ?? '';
  }

  getNatureOfAllocatedFunds(id: any): string {
    const obj = NATURE_OF_FUNDS_ALLOCATED?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? obj?.NameArabic ?? ''
      : obj?.NameEnglish ?? '';
  }

  getAllocationCycle(id: any): string {
    const obj = ALLOCATION_CYCLE?.find((c) => c.ID == id);
    return this.LanguageService.IsArabic
      ? obj?.NameArabic ?? ''
      : obj?.NameEnglish ?? '';
  }

  generateDownloadLink = (file: any): string =>
    `data:${file?.mimeType};base64,${file?.Base64}`;

  createDownloadLink = (file: File): string => {
    const blob = new Blob([file], { type: file.type });
    return URL.createObjectURL(blob);
  };

  pageProposedName = 1;
  pageSizeProposedName = 10;
  collectionSizeProposedName = 0;
  filteredCountProposedName = 0;
  paginateDataProposedName: any[] = [];
  getPremiumDataProposedName(): void {
    const start = (this.pageProposedName - 1) * this.pageSizeProposedName;
    const end = start + this.pageSizeProposedName;
    this.paginateDataProposedName =
      this.formData?.BasicInformationForm?.ProposedNames?.slice(start, end) ||
      [];
  }

  pageFoundingMember = 1;
  pageSizeFoundingMember = 10;
  collectionSizeFoundingMember = 0;
  filteredCountFoundingMember = 0;
  paginateDataFoundingMember: any[] = [];
  getPremiumDataFoundingMember(): void {
    const start = (this.pageFoundingMember - 1) * this.pageSizeFoundingMember;
    const end = start + this.pageSizeFoundingMember;
    this.paginateDataFoundingMember =
      this.formData?.FoundingMembersForm?.FounderMember?.slice(start, end) ||
      [];
  }

  pagePurposesAndActivities = 1;
  pageSizePurposesAndActivities = 10;
  collectionSizePurposesAndActivities = 0;
  filteredCountPurposesAndActivities = 0;
  paginateDataPurposesAndActivities: any[] = [];
  getPremiumDataPurposesAndActivities(): void {
    const start =
      (this.pagePurposesAndActivities - 1) * this.pageSizePurposesAndActivities;
    const end = start + this.pageSizePurposesAndActivities;
    this.paginateDataPurposesAndActivities =
      this.formData?.PurposesAndActivitiesForm?.PurposesAndActivities?.slice(
        start,
        end
      ) || [];
  }

  pageInterimMembers = 1;
  pageSizeInterimMembers = 10;
  collectionSizeInterimMembers = 0;
  filteredCountInterimMembers = 0;
  paginateDataInterimMembers: any[] = [];
  getPremiumDataInterimCommittee(): void {
    const start = (this.pageInterimMembers - 1) * this.pageSizeInterimMembers;
    const end = start + this.pageSizeInterimMembers;
    this.paginateDataInterimMembers =
      this.formData?.InterimCommitteeForm?.InterimCommitee?.slice(start, end) ||
      [];
  }

  pageMemberShip = 1;
  pageSizeMemberShip = 10;
  collectionSizeMemberShip = 0;
  filteredCountMemberShip = 0;
  paginateDataMemberShip: any[] = [];
  getPremiumDataMemberShip(): void {
    const start = (this.pageMemberShip - 1) * this.pageSizeMemberShip;
    const end = start + this.pageSizeMemberShip;
    this.paginateDataMemberShip =
      this.formData?.MembershipForm?.MembershipConditions?.slice(start, end) ||
      [];
  }

  pageBoardConditions = 1;
  pageSizeBoardConditions = 10;
  collectionSizeBoardConditions = 0;
  filteredCountBoardConditions = 0;
  paginateDataBoardConditions: any[] = [];
  getPremiumDataBoardConditions(): void {
    const start = (this.pageBoardConditions - 1) * this.pageSizeBoardConditions;
    const end = start + this.pageSizeBoardConditions;
    this.paginateDataBoardConditions =
      this.formData?.BoardOfInformationForm?.MembershipConditions?.slice(
        start,
        end
      ) || [];
  }

  pageBoardPositions = 1;
  pageSizeBoardPositions = 10;
  collectionSizeBoardPositions = 0;
  filteredCountBoardPositions = 0;
  paginateDataBoardPositions: any[] = [];
  getPremiumDataBoardPositions(): void {
    const start = (this.pageBoardPositions - 1) * this.pageSizeBoardPositions;
    const end = start + this.pageSizeBoardPositions;
    this.paginateDataBoardPositions =
      this.formData?.BoardOfInformationForm?.Positions?.slice(start, end) || [];
  }

  pageExtensionRequests = 1;
  pageSizeExtensionRequests = 10;
  collectionSizeExtensionRequests = 0;
  filteredCountExtensionRequests = 0;
  paginateDataExtensionRequests: any[] = [];
  getPremiumDataExtensionRequests(): void {
    const start =
      (this.pageExtensionRequests - 1) * this.pageSizeExtensionRequests;
    const end = start + this.pageSizeExtensionRequests;
    this.paginateDataExtensionRequests =
      this.formData?.ExtensionRequestForm?.ExtensionRequests?.slice(
        start,
        end
      ) || [];
  }

  gettotalFundsAmount = (): number => {
    return sumBy(
      this.formData?.AllocationFundForm?.AllocationFunds,
      (control: any) => Number(control.ValueOfTheFund)
    );
  };

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
      case 'approved':
        return 'green';
      case 'pending confirmation':
        return 'orange';
      case 'rejected':
      case 'refused':
        return 'red';
      default:
        return '';
    }
  }

  getStatusBgColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
        return '#E7F5FF';
      case 'approved':
        return '#F3FAF4';
      case 'pending confirmation':
        return '#FFFBEB';
      case 'rejected':
      case 'refused':
        return '#FEF2F2';
      default:
        return '';
    }
  }

  ngOnDestroy(): void {
    console.log('teminated details');
    this.StepperService.resetFormData();
    this.StepperService.resetRequestData();
    this.MyEstablishmentService.resetLookupData();
  }

  download = (file: any): void =>
    this.downloadNpoFiles(
      'application/pdf',
      file.ReportName,
      this.formData?.Id,
      file.LogicalName,
      this.formData?.BasicInformationForm?.EstablishmentId
    );
  canSeeAgendaSection = (): boolean => {
    return true;
  };

  getProposedNameEn = (): string => {
    return this.formData?.BasicInformationForm?.ProposedNames[0]?.Name ?? '';
  };

  getProposedNameAr = (): string => {
    return this.formData?.BasicInformationForm?.ProposedNames[0]?.NameAr ?? '';
  };

  get isValidToEdit(): boolean {
    const status = this.formData?.StatusCode;
    const isStatusInProgress =
      status === STATUS_LIST.find((item) => item.Code === 100000001)?.Code;
    const isStatusReturned =
      status === STATUS_LIST.find((item) => item.Code === 100000002)?.Code;
    const isStatusDraft =
      status === STATUS_LIST.find((item) => item.Code === 1)?.Code;

    const isEligableForExtensionRequest =
      this.formData?.IsEligableForExtensionRequest;
    const IsPendingDocumentUpload = this.formData?.IsPendingDocumentUpload;

    const isFoundingMemberNeedUpdate =
      this.formData?.FoundingMembersForm?.FounderMember?.some((item) =>
        ['Refused', 'Rejected', 'Request Update'].includes(item.SatusReason)
      );

    return (
      (isStatusInProgress && isFoundingMemberNeedUpdate) ||
      isStatusReturned ||
      isStatusDraft ||
      isEligableForExtensionRequest ||
      IsPendingDocumentUpload
    );
  }

  checkTempleteReplace = (): void => {
    this.getPremiumDataMemberShip();
  };
}
