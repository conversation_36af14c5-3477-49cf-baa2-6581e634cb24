<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.AssociationByDecree ||
    fb.LegalFormId?.value == LEGAL_FORM_TYPES.NationalSocietyByDecree)
    {
    <div class="row section-separator">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <h1 class="d-flex align-items-center">{{(messageTranslationPrefix+'npoLegalForm')
            | translate}}</h1>
        </div>
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'localDecreeLawNumber' | translate"
          [control]="fb?.LocalDecreeLawNumber"> </app-input>
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input-date [max]="maxDate" [label]="messageTranslationPrefix+'issuanceDate' | translate"
          [control]="fb?.IssuanceDate"></app-input-date>
      </div>
    </div>
    }

    <div class="col-12">
      <!-- <div class="d-flex justify-content-between align-items-start flex-wrap">
        <h1 class="d-flex align-items-center">
          {{(messageTranslationPrefix+'npoName') | translate}}
          @if(fb.LegalFormId?.value != LEGAL_FORM_TYPES.AssociationByDecree &&
          fb.LegalFormId?.value != LEGAL_FORM_TYPES.NationalSocietyByDecree)
          {
          <span
            [ngStyle]="{'margin-right': LanguageService.IsArabic ? '1rem' : '0', 'margin-left': !LanguageService.IsArabic ? '1rem' : '0'}"
            class="pro_names">{{proposedNames.length}} /
            {{(messageTranslationPrefix+'threeProposedNames') |
            translate}}</span>
          }
        </h1>

        @if(fb.LegalFormId?.value != LEGAL_FORM_TYPES.AssociationByDecree &&
        fb.LegalFormId?.value != LEGAL_FORM_TYPES.NationalSocietyByDecree){
        <div class="d-block to-conditions to-tooltip">
          {{messageTranslationPrefix+'help' | translate}}
          <span class="info-icon">
            <svg width="20" height="20" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
                fill="#92722A" />
            </svg>
          </span>
          <div class="tooltip">
            <h4 class="tooltip-title">{{messageTranslationPrefix+'npoName' |
              translate}}:</h4>
            <div>
              <p>{{messageTranslationPrefix+'helpMessage' | translate}}</p>
            </div>
          </div>
        </div>
        }
      </div> -->

      <!-- @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.AssociationByDecree
      ||fb.LegalFormId?.value==LEGAL_FORM_TYPES.NationalSocietyByDecree)
      { -->
      <div class="row section-separator">
        <div class="col-md-6 col-sm-12">
          <!-- must not allow google translate component works because input will always be disabled -->
          <app-input [allowMaxLength]="true" [label]="messageTranslationPrefix+'NameEn' | translate" dir="ltr"
            [control]="fb?.FirstProposedNameEn" [showGoogleTranslate]="false" [googleTranslateTarget]="'en'"
            [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fb?.FirstProposedNameAr">
          </app-input>
        </div>
        <div class="col-md-6 col-sm-12">
          <app-input [allowMaxLength]="true" class="ar_lang" [label]="messageTranslationPrefix+'NameAr' | translate"
            [control]="fb?.FirstProposedNameAr" [showGoogleTranslate]="false" [googleTranslateTarget]="'ar'" dir="rtl"
            [showGoogleTranslateToRelatedCompoent]="true"
            [googleTranslateToRelatedCompoent]="fb?.FirstProposedNameEn"></app-input>
        </div>
      </div>
      <!-- } -->

      @if(fb.LegalFormId?.value != LEGAL_FORM_TYPES.AssociationByDecree && fb.LegalFormId?.value !=LEGAL_FORM_TYPES.NationalSocietyByDecree)
      {
      <!-- <div *ngIf="!isNotAllowedToEdit" style="height: 64px !important;"
        class="col-12 d-flex align-items-lg-center section-separator">
        <div class="col-12 col-lg-5 col-xl-4 d-flex align-items-center justify-content-start">
          <app-modal
            *ngIf="(!isReturnForUpdate)||(isReturnForUpdate&&checkIsEditSection(feedbackList,'basicinformation','proposedNames'))||(isReturnForUpdate&&proposedNames.length<2)"
            [form]="proposedNameForm" [title]="messageTranslationPrefix+'addProposedName'| translate" [size]="'md'"
            buttonIcon="fas fa-circle-plus" [viewContentOnly]="false" [buttonEnabled]="true"
            [disableButton]="disableAddProposed" [buttonClass]="'btn btn-primary btn-plus'"
            [buttonLabel]="messageTranslationPrefix+'addProposedName'| translate"
            (submitAction)="checkProposedNameFromAI($event, GRID_ACTION_TYPES.ADD)" [resetThenDismiss]="false"
            [showCancelButton]="false" (opened)="resetAiParamter()">

            <div *ngIf="displayAiInfoMessage || displayAiErrorMessage" style="margin-bottom: 24px; padding: 0.5rem; overflow: hidden;" class="row align-items-center justify-content-start">
              @if(displayAiInfoMessage){
              <div class="alert alert-warning text-primary p-3" style="max-height: 150px; overflow-y: scroll;">
                <div *ngFor="let text of aiMessage.split(',')" style="overflow: hidden;">
                  {{text}}
                </div>
              </div>
              <div class="d-flex align-items-center justify-content-center">
                <button type="button" class="btn btn-primary btn-sm d-flex align-items-center justify-content-center"
                  (click)="manageProposedName(confirmedProposedNames, GRID_ACTION_TYPES.ADD)">
                  {{messageTranslationPrefix+'confirm' | translate}}
                </button>
              </div>
              }

              @if(displayAiErrorMessage){
              <div class="alert alert-danger text-danger p-3">
                <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M25.4137 7.02875L18.9713 0.58625C18.5951 0.212317 18.0867 0.00168507 17.5562 0H8.44375C7.91333 0.00168507 7.40494 0.212317 7.02875 0.58625L0.58625 7.02875C0.212317 7.40494 0.00168507 7.91333 0 8.44375V17.5562C0.00168507 18.0867 0.212317 18.5951 0.58625 18.9713L7.02875 25.4137C7.40494 25.7877 7.91333 25.9983 8.44375 26H17.5562C18.0867 25.9983 18.5951 25.7877 18.9713 25.4137L25.4137 18.9713C25.7877 18.5951 25.9983 18.0867 26 17.5562V8.44375C25.9983 7.91333 25.7877 7.40494 25.4137 7.02875ZM12 7C12 6.73478 12.1054 6.48043 12.2929 6.29289C12.4804 6.10536 12.7348 6 13 6C13.2652 6 13.5196 6.10536 13.7071 6.29289C13.8946 6.48043 14 6.73478 14 7V14C14 14.2652 13.8946 14.5196 13.7071 14.7071C13.5196 14.8946 13.2652 15 13 15C12.7348 15 12.4804 14.8946 12.2929 14.7071C12.1054 14.5196 12 14.2652 12 14V7ZM13 20C12.7033 20 12.4133 19.912 12.1666 19.7472C11.92 19.5824 11.7277 19.3481 11.6142 19.074C11.5006 18.7999 11.4709 18.4983 11.5288 18.2074C11.5867 17.9164 11.7296 17.6491 11.9393 17.4393C12.1491 17.2296 12.4164 17.0867 12.7074 17.0288C12.9983 16.9709 13.2999 17.0007 13.574 17.1142C13.8481 17.2277 14.0824 17.42 14.2472 17.6666C14.412 17.9133 14.5 18.2033 14.5 18.5C14.5 18.8978 14.342 19.2794 14.0607 19.5607C13.7794 19.842 13.3978 20 13 20Z"
                    fill="currentColor" />
                </svg>
                {{aiMessage}}
              </div>
              }
            </div>

            <div class="row">
              <app-input [allowMaxLength]="true" lang="en" [label]="messageTranslationPrefix+'proposedNameEn'" [columns]="12"
                [control]="pnControls.proposedNameEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
                [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="pnControls.proposedNameAr"></app-input>
              <app-input [allowMaxLength]="true" lang="ar" class="ar_lang" [label]="messageTranslationPrefix+'proposedNameAr'"
                [columns]="12" [control]="pnControls.proposedNameAr" [showGoogleTranslate]="Page_View_Type === 2"
                [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="pnControls.proposedNameEn"></app-input>
            </div>
          </app-modal>
        </div>
      </div> -->
      @if(proposedNames.length > 0)
      {
      <div class="section-separator">
        <div formArrayName="proposedNames" >
          <div class="table-responsive d-block_mobile">
            <table class="my-table" style="margin-bottom: 0 !important;">
              <thead>
                <tr>
                  <th class="align-middle text-center" scope="col">#</th>
                  <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"proposedNameEn" | translate
                    }}</th>
                  <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"proposedNameAr" | translate
                    }}</th>
                  <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"status" | translate }}</th>
                  <th class="align-middle text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                    {{messageTranslationPrefix+"actions" | translate }}</th>
                </tr>
              </thead>
              <tbody>
                @for (proposedName of proposedNames.controls; track $index) {
                <tr [formGroupName]="$index">
                  <td class="align-middle text-center text-wrap">{{ $index + 1}}</td>
                  <td class="align-middle text-center text-wrap" lang="en">{{proposedName.get("proposedNameEn")?.value }}
                  </td>
                  <td class="align-middle text-center text-wrap" lang="ar">{{proposedName.get("proposedNameAr")?.value }}
                  </td>
                  <td class="align-middle text-center text-wrap">{{getStatusByName(proposedName.get("SatusReason")?.value)
                    }}
                  </td>
                  @if(isReturnForUpdate|| StepperService.requestCode === 1){
                  <td class="align-middle text-center text-wrap" *ngIf="!isNotAllowedToEdit">
                    <button mat-icon-button type="button" [matMenuTriggerFor]="menu" class="actions-menu-trigger"
                      aria-label="Example icon-button with a menu">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                    <!-- Mat Menu with actions for mobile view -->
                    <mat-menu #menu="matMenu">
                      <ng-container [ngTemplateOutlet]="gridActions"
                        [ngTemplateOutletContext]="{proposedName: proposedName,index: $index}" />
                    </mat-menu>
                    <!-- Direct action buttons for larger screens -->
                    <div class="actions-direct-buttons">
                      <ng-container [ngTemplateOutlet]="gridActions"
                        [ngTemplateOutletContext]="{proposedName: proposedName,index: $index}" />
                    </div>
                  </td>
                  }
                  @else {
                  <td class="align-middle text-center text-wrap" *ngIf="!isNotAllowedToEdit"></td>
                  }
                </tr>
                }
              </tbody>
            </table>
          </div>

          <div class="figma-card-container">
            @for (proposedName of proposedNames.controls; track $index){
            <div class="figma-card" [formGroupName]="$index">
              @if(isReturnForUpdate|| StepperService.requestCode === 1){
              <button *ngIf="!isNotAllowedToEdit" type="button" mat-icon-button [matMenuTriggerFor]="menu"
                class="figma-actions-menu-trigger" aria-label="Actions menu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #menu="matMenu">
                <ng-container [ngTemplateOutlet]="gridActions"
                  [ngTemplateOutletContext]="{proposedName: proposedName,index: $index}" />
              </mat-menu>}
              <div class="figma-card-content">
                <div class="figma-card-field">
                  <span class="static-value">#</span>
                  <span class="dynamic-value">{{ $index + 1 }}</span>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{ messageTranslationPrefix +'proposedNameEn' | translate }}:</div>
                  <div class="dynamic-value" lang="en">{{proposedName.get('proposedNameEn')?.value }}</div>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{ messageTranslationPrefix +'proposedNameAr' | translate }}:</div>
                  <div class="dynamic-value" lang="ar">{{proposedName.get('proposedNameAr')?.value }}</div>
                </div>
                <div class="figma-card-field">
                  <div class="static-value">{{ messageTranslationPrefix + 'status'| translate }}:</div>
                  <div class="dynamic-value">
                    {{getStatusByName(proposedName.get("SatusReason")?.value) }}
                  </div>
                </div>
              </div>
            </div>
            }
          </div>
        </div>



      </div>
      }
      }

      <div class="row section-separator">
        <div class="col-12">
          <h1 class="d-flex align-items-center">{{(messageTranslationPrefix+'npoContactDetails')| translate}}</h1>
        </div>

        <div class="col-md-6">
          <app-select [label]="messageTranslationPrefix+'emirate' | translate" class="select-with-caret"
            [control]="fb?.Emirate" [data]="emirates"></app-select>
        </div>

        <div class="col-md-6">
          <app-input-location [label]="messageTranslationPrefix+'geographicLocation' | translate"
            [control]="fb?.GeographicLocation" [intialAddress]="intialAddress"></app-input-location>
        </div>

        <div class="col-md-6">
          <app-input [type]="InputType.LandLineWithoutMask"
            [label]="messageTranslationPrefix+'landlineNumber' | translate" [control]="fb?.LandlineNumber"></app-input>
        </div>

        <div class="col-md-6">
          <app-input [type]="InputType.PoBox" [allowMaxLength]="true" [label]="messageTranslationPrefix+'poBox' | translate" [control]="fb?.POBox"></app-input>
        </div>

        <div class="col-md-6">
          <app-input [type]="InputType.Email" [label]="messageTranslationPrefix+'email' | translate"
            [control]="fb?.Email"></app-input>
        </div>

        <div class="col-md-6">
          <app-input [type]="InputType.WebSite" [label]="messageTranslationPrefix+'website' | translate"
            [control]="fb?.Website"></app-input>
        </div>

        <div class="col-md-6">
          <app-input [label]="messageTranslationPrefix+'address' | translate" [control]="fb?.Address"></app-input>
        </div>

      </div>

      @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.SocialSolidarityFunds){
      <div class="row section-separator">
        <div class="col-12">
          <h1 class="d-flex align-items-center">{{(messageTranslationPrefix+'fundbelongsDetails')| translate}}</h1>
        </div>

        <div class="col-md-6">
          <app-input [label]="messageTranslationPrefix+'EntityNameEn' | translate" [control]="fb?.EntityName"  [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fb?.EntityNameAr"></app-input>
        </div>

        <div class="col-md-6">
          <app-input class="ar_lang" [label]="messageTranslationPrefix+'EntityNameAr' | translate" [control]="fb?.EntityNameAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fb?.EntityName"></app-input>
        </div>

        <div class="col-md-6">
          <app-select [label]="messageTranslationPrefix+'EntityType' | translate" class="select-with-caret"
            [control]="fb?.Entitysectortype" [data]="EntitySectorTypes"></app-select>
        </div>

        <div class="col-md-6" *ngIf="fb?.Entitysectortype?.value?.ID==3">
          <app-input [type]="InputType.Number" [label]="messageTranslationPrefix+'NumberOfEmployees' | translate"
            [control]="fb?.Numberofemployees"></app-input>
        </div>
      </div>
      }

      @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.Association ||
      fb.LegalFormId?.value == LEGAL_FORM_TYPES.NationalSociety)
      {
      <div class="d-flex justify-content-between align-items-start flex-wrap">
        <h1 class="d-flex align-items-center">
          @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.Association){
          {{ messageTranslationPrefix + 'targetGroupsAssociation' | translate }}
          }

          @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.NationalSociety){
          {{ messageTranslationPrefix + 'targetGroupsNationalSociety' |
          translate }}
          }
        </h1>
      </div>

      <div *ngIf="!isNotAllowedToEdit" style="height: 64px !important;" class="col-12 d-flex align-items-lg-center">
        <div class="col-12 col-lg-5 col-xl-4 d-flex align-items-center justify-content-start">
          <app-modal
            *ngIf="isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001"
            [form]="targetGroupForm" [title]="messageTranslationPrefix+'addTargetGroup'| translate" [size]="'md'"
            buttonIcon="fas fa-circle-plus" [viewContentOnly]="false" [buttonEnabled]="true"
            [buttonClass]="'btn btn-primary btn-plus'"
            [buttonLabel]="messageTranslationPrefix+'addTargetGroup'| translate"
            (submitAction)="manageTargetGroup($event, GRID_ACTION_TYPES.ADD)" [resetThenDismiss]="false"
            [showCancelButton]="false">
            <div class="row">
              <app-input [allowMaxLength]="true" lang="en" [label]="messageTranslationPrefix+'targetGroupNameEn'" [columns]="12"
                [control]="targetGroupControls.targetGroupNameEn" [showGoogleTranslate]="Page_View_Type === 2"
                [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="targetGroupControls.targetGroupNameAr"></app-input>
              <app-input [allowMaxLength]="true" lang="ar" class="ar_lang" [label]="messageTranslationPrefix+'targetGroupNameAr'"
                [columns]="12" [control]="targetGroupControls.targetGroupNameAr" [showGoogleTranslate]="Page_View_Type === 2"
                [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="targetGroupControls.targetGroupNameEn"></app-input>
            </div>
          </app-modal>
        </div>
      </div>

      <!-- @if(targetGroups.length > 0) -->
      <!-- { -->
      <div formArrayName="targetGroups">
        <div class="table-responsive d-block_mobile">
          <table class="my-table" style="margin-bottom: 0 !important;">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"targetGroupNameEn" |
                  translate}}</th>
                <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"targetGroupNameAr" |translate}}</th>
                <!-- <th class="align-middle text-center" scope="col">{{messageTranslationPrefix+"status" | translate }}</th> -->
                <th class="align-middle text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                  {{messageTranslationPrefix+"actions" | translate }}
                </th>
              </tr>
            </thead>
            <tbody>
              @for (targetGroup of targetGroups.controls; track $index) {
              <tr [formGroupName]="$index">
                <td class="align-middle text-center text-wrap">{{ $index + 1}}</td>
                <td class="align-middle text-center text-wrap" lang="en">{{targetGroup.get("targetGroupNameEn")?.value
                  }}</td>
                <td class="align-middle text-center text-wrap" lang="ar">{{targetGroup.get("targetGroupNameAr")?.value
                  }}</td>
                <!-- <td class="align-middle text-center text-wrap">{{getStatusByName(targetGroup.get("SatusReason")?.value)}}</td> -->
                @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001){
                @if(targetGroup.get('canEdit')?.value){
                  <td class="align-middle text-center text-wrap" *ngIf="!isNotAllowedToEdit">
                    <button mat-icon-button type="button" [matMenuTriggerFor]="menu" class="actions-menu-trigger">
                      <mat-icon>more_vert</mat-icon>
                    </button>
                    <!-- Mat Menu with actions for mobile view -->
                    <mat-menu #menu="matMenu">
                      <ng-container [ngTemplateOutlet]="targetGroupActions"
                        [ngTemplateOutletContext]="{targetGroup: targetGroup,index: $index}" />
                    </mat-menu>
                    <!-- Direct action buttons for larger screens -->
                    <div class="actions-direct-buttons">
                      <ng-container [ngTemplateOutlet]="targetGroupActions"
                        [ngTemplateOutletContext]="{targetGroup: targetGroup,index: $index}" />
                    </div>
                  </td>
                }
                }
                @else {
                <td class="align-middle text-center text-wrap" *ngIf="!isNotAllowedToEdit"></td>
                }
              </tr>
              }
            </tbody>
          </table>
        </div>

        <div class="figma-card-container">
          @for (targetGroup of targetGroups.controls; track $index){
          <div class="figma-card" [formGroupName]="$index">
            @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001)
            {
            @if(targetGroup.get('canEdit')?.value){
            <button *ngIf="!isNotAllowedToEdit" type="button" mat-icon-button [matMenuTriggerFor]="menu"
              class="figma-actions-menu-trigger" aria-label="Actions menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            <mat-menu #menu="matMenu">
              <ng-container [ngTemplateOutlet]="targetGroupActions"
                [ngTemplateOutletContext]="{targetGroup: targetGroup,index: $index}" />
            </mat-menu>
            }
            }
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ $index + 1 }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{ messageTranslationPrefix +'targetGroupNameEn' | translate }}:</div>
                <div class="dynamic-value" lang="en">{{targetGroup.get('targetGroupNameEn')?.value }}</div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{ messageTranslationPrefix +'targetGroupNameAr' | translate }}:</div>
                <div class="dynamic-value" lang="ar">{{targetGroup.get('targetGroupNameAr')?.value }}</div>
              </div>
              <!-- <div class="figma-card-field">
                <div class="static-value">{{ messageTranslationPrefix + 'status'| translate }}:</div>
                <div class="dynamic-value">
                  {{getStatusByName(targetGroup.get("SatusReason")?.value) }}
                </div>
              </div> -->
            </div>
          </div>
          }
        </div>

      </div>
      <!-- } -->

      }

      @if(fb.LegalFormId?.value == LEGAL_FORM_TYPES.Association ||
      fb.LegalFormId?.value == LEGAL_FORM_TYPES.NationalSociety){
      <div class="d-flex justify-content-between align-items-start flex-wrap">
        <h1 class="d-flex align-items-center">
          {{ messageTranslationPrefix + 'ExamplesOfActivitiesAndProgramsOfferedByTheNPO' | translate }}
        </h1>
      </div>

      <div style="height: 64px !important;" class="d-flex align-items-lg-center">
        <div class="col-md-12" *ngIf="!isNotAllowedToEdit">

          <app-modal
            *ngIf="(!isReturnForUpdate)||(!isReturnForUpdate&&checkIsEditSection(feedbackList,'basicinformation','NpoActivityPrograms'))"
            [form]="exampleOfActivitiesForm" [title]="'Add Example Of Activities'" [size]="'lg'"
            buttonIcon="fas fa-circle-plus" [viewContentOnly]="false" [buttonEnabled]="true"
            [buttonClass]="'btn btn-primary btn-plus'" buttonLabel="Add Example Of Activities"
            [showCancelButton]="false" [resetThenDismiss]="false"
            (submitAction)="manageExampleOfActivitiesForm($event,GRID_ACTION_TYPES.ADD)">
            <div class="row">
              @if(LanguageService.IsArabic){
              <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesAr'" [columns]="6"
                [control]="eControls.exampleOfActivitiesAr" [showGoogleTranslate]="true" [googleTranslateTarget]="'ar'"
                [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesEn" class="ar_lang" />
              <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesEn'" [columns]="6"
                [control]="eControls.exampleOfActivitiesEn" [showGoogleTranslate]="true" [googleTranslateTarget]="'en'"
                [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesAr" />
              } @else {
              <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesEn'" [columns]="6"
                [control]="eControls.exampleOfActivitiesEn" [showGoogleTranslate]="true" [googleTranslateTarget]="'en'"
                [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesAr" />
              <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesAr'" [columns]="6"
                [control]="eControls.exampleOfActivitiesAr" [showGoogleTranslate]="true" [googleTranslateTarget]="'ar'"
                [showGoogleTranslateToRelatedCompoent]="true"
                [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesEn" class="ar_lang" />
              }

            </div>
          </app-modal>

        </div>
      </div>

      @if(NpoActivityPrograms.length > 0)
      {
      <div formArrayName="NpoActivityPrograms">
        <div style="margin-top: 26px !important;" class="d-block_mobile">
          <div class="table-responsive">
            <table class="my-table">
              <thead>
                <tr>
                  <th class="align-middle text-center" scope="col">#</th>
                  <th class="align-middle text-center text-wrap" scope="col">
                    {{messageTranslationPrefix+"exampleOfActivitiesEn" |translate }}
                  </th>
                  <th class="align-middle text-center text-wrap" scope="col">
                    {{messageTranslationPrefix+"exampleOfActivitiesAr" |translate }}
                  </th>
                  <th class="align-middle text-center text-wrap text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                    {{messageTranslationPrefix+"actions" |translate }}</th>
                </tr>
              </thead>
              <tbody>
                @for (exampleOfActivity of reprsentedDataTableExampleOfActivities.controls; track $index) {

                @let order$=$index + tableIndexExampleOfActivities + 1;
                <tr [formGroupName]="$index">
                  <td class="align-middle text-center">{{ order$ }}</td>
                  <td class="align-middle text-center text-truncate" lang="en">
                    {{exampleOfActivity.get("exampleOfActivitiesEn")?.value}}
                  </td>
                  <td class="align-middle text-center text-truncate" lang="ar">
                    {{exampleOfActivity.get("exampleOfActivitiesAr")?.value }}
                  </td>
                  @if(!isReturnForUpdate||(isReturnForUpdate&&checkIsEditId(exampleOfActivity.get("id")?.value))||(isReturnForUpdate&&exampleOfActivity.get("SatusReason")?.value?.trim()?.toLocaleLowerCase()=='draft')){
                  <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit">
                    <button mat-icon-button type="button" class="actions-menu-trigger" [matMenuTriggerFor]="menu"
                      aria-label="Example icon-button with a menu">
                      <mat-icon>more_vert</mat-icon>
                    </button>

                    <mat-menu #menu="matMenu">
                      <ng-container [ngTemplateOutlet]="examplesOfActivitiesActions"
                        [ngTemplateOutletContext]="{exampleOfActivity: exampleOfActivity,index: order$-1}" />
                    </mat-menu>

                    <div class="actions-direct-buttons">
                      <ng-container [ngTemplateOutlet]="examplesOfActivitiesActions"
                        [ngTemplateOutletContext]="{exampleOfActivity: exampleOfActivity,index: order$-1}" />
                    </div>
                  </td>
                  }
                  @else {
                  <td class="align-middle text-center text-wrap" *ngIf="!isNotAllowedToEdit"></td>
                  }
                </tr>
                }
              </tbody>
            </table>
          </div>
        </div>

        <div class="figma-card-container">
          @for(exampleOfActivity of reprsentedDataTableExampleOfActivities.controls; track $index) {
          @let order$=$index + tableIndexExampleOfActivities + 1;
          <div class="figma-card">
            @if(!isReturnForUpdate||(isReturnForUpdate&&checkIsEditId(exampleOfActivity.get("id")?.value))||(isReturnForUpdate&&exampleOfActivity.get("SatusReason")?.value?.trim()?.toLocaleLowerCase()=='draft'))
            {
            <button *ngIf="!isNotAllowedToEdit" mat-icon-button type="button" class="figma-actions-menu-trigger"
              [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #menu="matMenu">
              <ng-container [ngTemplateOutlet]="examplesOfActivitiesActions"
                [ngTemplateOutletContext]="{exampleOfActivity: exampleOfActivity,index: $index}" />
            </mat-menu>
            }
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ order$ }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"exampleOfActivitiesEn" | translate }}:</div>
                <div class="dynamic-value" lang="en">{{exampleOfActivity.get("exampleOfActivitiesEn")?.value }}</div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"exampleOfActivitiesAr" | translate }}:</div>
                <div class="dynamic-value" lang="ar">{{exampleOfActivity.get("exampleOfActivitiesAr")?.value }}</div>
              </div>
            </div>
          </div>
          }
        </div>
        <div class="d-flex justify-content-center p-2 aegov-pagination">
          <ngb-pagination [maxSize]="10" [ellipses]="true" [(page)]="pageExampleOfActivities"
            [pageSize]="pageSizeExampleOfActivities" [collectionSize]="NpoActivityPrograms.length"
            (pageChange)="paginationExampleOfActivities()" aria-label="Custom pagination">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
        </div>
      </div>
      }
      }

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()"> {{ "Next" | translate }}</button>
    </div>
  </form>
</div>

<ng-template #gridActions let-index="index" let-proposedName="proposedName">
  <ng-container >
    <app-modal [operation]="'edit'" [form]="proposedNameForm" [title]="messageTranslationPrefix+'edit'" [size]="'md'"
      buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      (submitAction)="checkProposedNameFromAI($event, GRID_ACTION_TYPES.EDIT)"
      (opened)="handleEditProposedName(proposedName, index)" [resetThenDismiss]="false"
      [isIcon]="true" >

      <div *ngIf="displayAiInfoMessage || displayAiErrorMessage" style="margin-bottom: 24px; padding: 0.5rem; overflow: hidden;" class="row align-items-center justify-content-start">
        @if(displayAiInfoMessage){
        <div class="alert alert-warning text-primary p-3" style="max-height: 150px; overflow-y: scroll;">
          <div *ngFor="let text of aiMessage.split(',')" style="overflow: hidden;">
            {{text}}
          </div>
          <!-- <pre style="overflow: hidden;">
            {{aiMessage}}
          </pre> -->
        </div>
        <div class="d-flex align-items-center justify-content-center">
          <button type="button" class="btn btn-primary btn-sm d-flex align-items-center justify-content-center"
            (click)="manageProposedName(confirmedProposedNames, GRID_ACTION_TYPES.EDIT)">
            {{messageTranslationPrefix+'confirm' | translate}}
          </button>
        </div>
        }

        @if(displayAiErrorMessage){
        <div class="alert alert-danger text-danger p-3">
          <svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M25.4137 7.02875L18.9713 0.58625C18.5951 0.212317 18.0867 0.00168507 17.5562 0H8.44375C7.91333 0.00168507 7.40494 0.212317 7.02875 0.58625L0.58625 7.02875C0.212317 7.40494 0.00168507 7.91333 0 8.44375V17.5562C0.00168507 18.0867 0.212317 18.5951 0.58625 18.9713L7.02875 25.4137C7.40494 25.7877 7.91333 25.9983 8.44375 26H17.5562C18.0867 25.9983 18.5951 25.7877 18.9713 25.4137L25.4137 18.9713C25.7877 18.5951 25.9983 18.0867 26 17.5562V8.44375C25.9983 7.91333 25.7877 7.40494 25.4137 7.02875ZM12 7C12 6.73478 12.1054 6.48043 12.2929 6.29289C12.4804 6.10536 12.7348 6 13 6C13.2652 6 13.5196 6.10536 13.7071 6.29289C13.8946 6.48043 14 6.73478 14 7V14C14 14.2652 13.8946 14.5196 13.7071 14.7071C13.5196 14.8946 13.2652 15 13 15C12.7348 15 12.4804 14.8946 12.2929 14.7071C12.1054 14.5196 12 14.2652 12 14V7ZM13 20C12.7033 20 12.4133 19.912 12.1666 19.7472C11.92 19.5824 11.7277 19.3481 11.6142 19.074C11.5006 18.7999 11.4709 18.4983 11.5288 18.2074C11.5867 17.9164 11.7296 17.6491 11.9393 17.4393C12.1491 17.2296 12.4164 17.0867 12.7074 17.0288C12.9983 16.9709 13.2999 17.0007 13.574 17.1142C13.8481 17.2277 14.0824 17.42 14.2472 17.6666C14.412 17.9133 14.5 18.2033 14.5 18.5C14.5 18.8978 14.342 19.2794 14.0607 19.5607C13.7794 19.842 13.3978 20 13 20Z"
              fill="currentColor" />
          </svg>
          {{aiMessage}}
        </div>
        }
      </div>

      <app-input [allowMaxLength]="true" [label]="messageTranslationPrefix+'proposedNameEn'" [columns]="12"
        [control]="pnControls.proposedNameEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="pnControls.proposedNameAr">
      </app-input>
      <app-input [allowMaxLength]="true" class="ar_lang" [label]="messageTranslationPrefix+'proposedNameAr'"
        [columns]="12" [control]="pnControls.proposedNameAr" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="pnControls.proposedNameEn"></app-input>
    </app-modal>
  </ng-container>
  <button class="text-primary btn" type="button" (click)="removeProposedName(index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>

</ng-template>

<ng-template #targetGroupActions let-index="index" let-targetGroup="targetGroup">
  <ng-container >
    <app-modal [operation]="'edit'" [form]="targetGroupForm" [title]="messageTranslationPrefix+'edit'" [size]="'md'"
      buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      (submitAction)="manageTargetGroup($event, GRID_ACTION_TYPES.EDIT)"
      (opened)="handleEditTargetGroup(targetGroup, index)" [resetThenDismiss]="false" [isIcon]="true">
      <app-input [allowMaxLength]="true" [label]="messageTranslationPrefix+'targetGroupNameEn'" [columns]="12"
        [control]="targetGroupControls.targetGroupNameEn" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="targetGroupControls.targetGroupNameAr">
      </app-input>
      <app-input [allowMaxLength]="true" class="ar_lang" [label]="messageTranslationPrefix+'targetGroupNameAr'"
        [columns]="12" [control]="targetGroupControls.targetGroupNameAr" [showGoogleTranslate]="Page_View_Type === 2"
        [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="targetGroupControls.targetGroupNameEn"></app-input>
    </app-modal>
  </ng-container>

  <button class="text-primary btn" type="button" (click)="removeTargetGroup(index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>
</ng-template>

<ng-template #examplesOfActivitiesActions let-index="index" let-exampleOfActivity="exampleOfActivity">
  <ng-container *ngIf="showIconEdit(isReturnForUpdate,exampleOfActivity)">
    <app-modal [operation]="'edit'" [form]="exampleOfActivitiesForm" [title]="messageTranslationPrefix+'edit'"
      [size]="'md'" buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      (submitAction)="manageExampleOfActivitiesForm($event, GRID_ACTION_TYPES.EDIT)"
      (opened)="handleEditExampleOfActivities(exampleOfActivity, index)" [resetThenDismiss]="false" [isIcon]="true">
      @if(LanguageService.IsArabic){
      <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesAr'" [columns]="6"
        [control]="eControls.exampleOfActivitiesAr" [showGoogleTranslate]="true" [googleTranslateTarget]="'ar'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesEn" class="ar_lang"></app-textarea>
      <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesEn'" [columns]="6"
        [control]="eControls.exampleOfActivitiesEn" [showGoogleTranslate]="true" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesAr"></app-textarea>
      } @else {
      <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesAr'" [columns]="6"
        [control]="eControls.exampleOfActivitiesEn" [showGoogleTranslate]="true" [googleTranslateTarget]="'en'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesAr"></app-textarea>
      <app-textarea [label]="messageTranslationPrefix+'exampleOfActivitiesEn'" [columns]="6"
        [control]="eControls.exampleOfActivitiesAr" [showGoogleTranslate]="true" [googleTranslateTarget]="'ar'"
        [showGoogleTranslateToRelatedCompoent]="true"
        [googleTranslateToRelatedCompoent]="eControls.exampleOfActivitiesEn" class="ar_lang"></app-textarea>
      }
    </app-modal>
  </ng-container>

  <button class="text-primary btn" type="button" (click)="removeExampleOfActivity(index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>
</ng-template>
