import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormGroup } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';

@Component({
  selector: 'app-donation-purpose',
  templateUrl: './donation-purpose.component.html',
  styleUrls: ['./donation-purpose.component.scss'],
})
export class DonationPurposeComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  countries: Lookup[];
  minDate: Date;
  requestTypes: Lookup[];
  entityLegalForms: Lookup[];

  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.donationPurpose.';

    const today = new Date();
    today.setDate(today.getDate() + 30);
    this.minDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue && changes['feedbackList']?.currentValue?.length > 0 && changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.NocRequestService.lookupData$.subscribe((data) => {
      if (data) {
        this.countries = data?.Countries;
        this.requestTypes = data?.RequestType;
        this.entityLegalForms = data?.EntityLegalForm;

        this.StepperService.requestData$.subscribe((_) => {
          if (_ && _.isFullDetails == true) {
            this.mapData(_?.DonationPurpose);
          } else if (_ && _.isFullDetails == false) {
            this.mapData(_?.DonationPurpose);
          }
        });
      }
    });
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );
    return result;
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'DonationPurpose',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'DonationPurpose',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      donationPurpose: this.fb?.DonationPurpose?.value ?? '',
    };
  };

  mapData = (data: any): void => {
    if (!data) return;

    this.fb.DonationPurpose.setValue(data?.donationPurpose);
  };
  
  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "donationPurpose", this.fb);
  }
}
