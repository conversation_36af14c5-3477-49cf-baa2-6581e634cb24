// -- buttons

.cancel-button,
.cancel-button:not(:disabled){
  border: 2px solid $aered-600 !important;
  border-radius: 8px !important;
  color: $aered-600 !important;
  padding: 12px 16px !important;
  height: 46px !important;
  background: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover , &:focus{
    background-color: $aered-100 !important;
    border-color: $aered-100 !important;
  }
}
.cancel-filled-button{
  border: 2px solid $aered-600 !important;
  border-radius: 8px !important;
  background-color: $aered-600 !important;
  color: $white !important;
  padding: 10px 16px !important;
  height: 46px !important;
}
.basic-button{
  border: 2px solid $aegold-600 !important;
  border-radius: 8px !important;
  color: $aegold-600 !important;
  padding: 12px 16px !important;
  height: 46px !important;
  background: transparent !important;
  display: flex;
  align-items: center;
  justify-content: center;
  &:hover{
    background-color: $aegold-100 !important;
    // border-color: $aegold-100 !important;
    border: 5px solid #92722A !important;
    padding: 7.5px 13px !important;
  }
}
.basic-filled-button{
  border: 2px solid $aegold-600 !important;
  border-radius: 8px !important;
  color: $white !important;
  background-color: $aegold-600 !important;
  padding: 12px 16px !important;
  height: 46px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  &.btn:disabled{
    background-color: $aegold-50 !important;
    border-color: $aegold-50 !important;
    color: $aegold-300 !important;
  }
}
.basic-filled-button:hover:not(.disabled){
    border-color: $aegold-500 !important;
    background-color: $aegold-500 !important;
    color: $aegold-50 !important;
    box-shadow:0px 0px 0px 6px #F2ECCF;
}
.basic-filled-button.disabled{
  cursor:unset !important;
  border: 2px solid $aegold-200 !important;
  border-radius: 8px !important;
  color: $aegold-50 !important;
  background-color: $aegold-200 !important;
  padding: 10px 16px !important;
  height: 46px !important;
  pointer-events: none !important;
}
.download-button {
  color: $aegold-600 !important;
  background-color: $aegold-100 !important;
  border-radius: 8px !important;
  padding: 10px 16px !important;
  width: fit-content !important;
  height: 48px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: ease;
}

.download-button:hover {
  border: 5px solid #92722A;
  padding: 7.5px 12px !important;
}

.delete-button{
  color: $aered-600 !important;
  background-color: $aered-100 !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  margin-left: 8px;
  width: fit-content !important;
}

.help-button {
  border: none !important;
  transition: 300ms;
  color: $aegold-600 !important;
  padding: 16px !important;
  height: 46px !important;
  border-radius: 16px 0 0 16px !important;
  background: $aegold-50 !important;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.16) !important;
  position: fixed !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  top: 200px !important;
  right: 0; // Default position for LTR
  z-index: 1000;

  // RTL adjustments
  [dir='rtl'] & {
    left: 0;
    right: auto;
    border-radius: 0 16px 16px 0 !important;
  }

  &:hover {
    background-color: $aegold-100;
  }

  // Responsive positioning
  @media (max-width: 1024px) {
    right: 0;
    height: 56px;
    width: 56px;
    padding: 8px;

    [dir='rtl'] & {
      left: 0;
      right: auto;
      border-radius: 0 16px 16px 0 !important;
    }
  }
}

.btn.btn-sm{
  height: 38px !important;
}
.aegov-btn {
  display: inline-flex;
  flex-shrink: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  text-align: center;
  text-decoration-line: none;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-duration: .2s;
  transition-timing-function: cubic-bezier(.4,0,.2,1);
  height: 3rem;
  gap: .5rem;
  border-radius: .5rem;
  border-width: 2px;
  border-color: transparent;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  background-color: $aegold-600;
  --tw-text-opacity: 1;
  color: white;
  &:hover ,&:focus{
    background-color: $aegold-500;
    box-shadow: 0px 0px 0px 6px $aegold-100;
  }
  &.btn-lg {
    height: 3.25rem;
    gap: .75rem;
    padding-left: 1.75rem;
    padding-right: 1.75rem;
    font-size: 1.125rem;
  }
  &.btn-icon {
    width: 3rem;
    gap: 0px;
    padding-left: 0;
    padding-right: 0;
    svg{
      fill: white;
    }
  }
  &.btn-outline{
    border-color: $aegold-600;
    border-bottom-color: #f9c34a;
    border-right-color: #f9c34a;
    background-color: white;
    color: $aegold-600;
    &:hover ,&:focus{
      background-color: $aegold-50;
      box-shadow: none;
    }
  }
}

.action-button{
  border: none !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  background: transparent !important;
  &:hover{
    background-color: $aegold-100 !important;
  }
  &::after{
    display: none;
  }
  @media (max-width: 1024px){
    padding: 0 !important;
  }
}
////////////////
///////////
.btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #{$aegold-600}; // Converts to #b57e47
  --bs-btn-border-color: #{$aegold-600};
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #{$aegold-500};
  --bs-btn-hover-border-color: #{$aegold-500};
  --bs-btn-focus-shadow-rgb: 133, 108, 81;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #{$aegold-700};
  --bs-btn-active-border-color: #{$aegold-700};
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #{$aegold-200};
  --bs-btn-disabled-bg: #{$aegold-50};
  --bs-btn-disabled-border-color: #{$aegold-50};
}
.mat-mdc-unelevated-button.mat-primary {
  --mdc-filled-button-container-color: #{$aegold-600} !important;
  --mat-filled-button-ripple-color: #{$aegold-600} !important;
}
.btn {
  @media(min-width:1024px){
    height: 46px;
  }
}
.button_gp{
  margin-top: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  // padding-bottom: 40px !important;
  @media (max-width: 575px) {
    flex-wrap: wrap;
    justify-content: stretch;
    flex-direction: column-reverse;
    margin-top: 22px;
    padding: 12px;
    // padding-bottom: 32px !important;
    button{
      width: 100% !important;
    }
  }
}
//////////////////////////
// mat-form-field.mat-mdc-form-field.appFullWidth.ng-tns-c508571215-80.mat-mdc-form-field-type-mat-input.mat-form-field-appearance-outline.mat-primary.ng-touched.ng-dirty.ng-valid.ng-star-inserted{
// .mat-mdc-text-field-wrapper , 
// .mat-mdc-text-field-wrapper.mdc-text-field.ng-tns-c508571215-15.mdc-text-field--outlined.mdc-text-field--no-label,
// .mdc-text-field--outlined .mat-mdc-form-field-infix, .mdc-text-field--no-label .mat-mdc-form-field-infix{
//   padding: 0 !important;
// }
// }
mat-form-field.mat-mdc-form-field.appFullWidth.ng-tns-c508571215-38.mat-mdc-form-field-type-mat-input.mat-form-field-appearance-outline.mat-primary.ng-valid.ng-star-inserted.ng-dirty.ng-touched {
  padding: 0 !important;
  margin: 0 !important;
}
//  .col-md-12 mat-form-field.mat-mdc-form-field.appFullWidth.ng-tns-c508571215-19.mat-mdc-form-field-type-mat-input.mat-form-field-appearance-outline.mat-primary.ng-untouched.ng-pristine.ng-valid.ng-star-inserted > .mat-mdc-text-field-wrapper.mdc-text-field.mdc-text-field--outlined.mdc-text-field--outlined.mdc-text-field--no-label,
// .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading , .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mat-mdc-notch-piece{
//   border: 0 !important;
//   outline: 0 !important;
//   box-shadow: none !important;
// }
.mdc-notched-outline{
  padding: .5rem;
}
.mdc-switch:enabled .mdc-switch__shadow {
  box-shadow: none !important;
  background-color: #fff;
  border-color: #fff !important;
  outline: 0 !important;
}
.mdc-switch__track::before {
  background: #E1E3E5 !important;
}

.mdc-switch--selected .mdc-switch__icon{
  display: none !important;
}
.mdc-switch__track::after {
  background: $aegold-500 !important;
}
.mdc-switch__handle::before {
  background: #ffffff !important;
}
.mdc-switch--selected:enabled .mdc-switch__handle::after {
  background: #ffffff !important;
  border: 1px solid $aegold-600 ;
}
.mat-typography p:empty {
  display: none;
}