import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function bankAccountMaxLength(max: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) return null;
    return control.value.length > max
      ? {
        invalidBankAccountFormat: {
            requiredLength: max,
            actualLength: control.value.length,
            message: 'validationMessage.invalidBankAccountFormat',
          },
        }
      : null;
  };
}
