import { Component, EventEmitter, Injector, Input, OnInit, Output } from '@angular/core';
import { Feedback } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';

@Component({
  selector: 'app-noc-request-feed-back',
  templateUrl: './noc-request-feed-back.component.html',
  styleUrls: ['./noc-request-feed-back.component.scss']
})
export class NocRequestFeedBackComponent extends NocRequestComponentBase implements OnInit {


  @Input() feedbackList: Feedback[] | undefined;
  @Output() activeStepperIndex: EventEmitter<string> = new EventEmitter<string>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = this.messageTranslationPrefix + 'forms.feedBack.';

  }

  ngOnInit(): void { }

  openSection = (sectionName: string): void => {
    this.activeStepperIndex.emit(sectionName);
  }
}
