/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EstablishmentBoardOfDirectorsByDecreeComponent } from './establishment-board-of-directors-by-decree.component';

describe('EstablishmentBoardOfDirectorsByDecreeComponent', () => {
  let component: EstablishmentBoardOfDirectorsByDecreeComponent;
  let fixture: ComponentFixture<EstablishmentBoardOfDirectorsByDecreeComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EstablishmentBoardOfDirectorsByDecreeComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EstablishmentBoardOfDirectorsByDecreeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
