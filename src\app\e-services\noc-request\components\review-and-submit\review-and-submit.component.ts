import {
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitionType } from '../../enums/submition-type.enum';
import { Feedback } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { DONOR_TYPES } from '../../models/npo-lookup-data';
import { LegalFormTypesEnum } from '../../enums/legal-form-types-enum';
import { Donor_Types_Enum } from '../../enums/donor-types.enum';

@Component({
  selector: 'app-review-and-submit',
  templateUrl: './review-and-submit.component.html',
  styleUrls: ['./review-and-submit.component.scss'],
})
export class ReviewAndSubmitComponent
  extends NocRequestComponentBase
  implements OnInit, OnChanges {
  NPO_SERVICES_CATALOGUE_NAME: string = 'NPO-NPO DeclarationServiceCatalogue';
  NPO_DECREE_SERVICES_CATALOGUE_NAME: string =
    'NPO-NPO Declaration By DecreeServiceCatalogue';
  NPO_LINCEING: string = 'NPO Declaration';
  panelOpenState: boolean = false;
  formData: any;
  emirates: Lookup[] = [];
  countries: Lookup[] = [];
  requestTypes: Lookup[] = [];
  legalFromTypes: Lookup[] = [];
  entityLegalFormType: Lookup[] = [];
  filteredLicensingAuthorities: Lookup[] = [];
  nationalities: Lookup[] = [];
  passportTypes: Lookup[] = [];
  cashDonationTypeLookups: Lookup[] = [];
  inkindDonationTypeLookups: Lookup[] = [];
  reportParameter: any[] = [];
  downloadButtons: any[] = [];
  npoLicensedList: Lookup[] = [];
  banks: Lookup[] = [];
  currencies: Lookup[] = [];
  accountTypes: Lookup[] = [];
  donationDateAndDurationTypes: Lookup[] = [];
  categories: Lookup[] = [];
  entityTypes: Lookup[] = [];
  donorTypes: Lookup[] = [];
  requestType = LegalFormTypesEnum;
  donorTypesEnum = Donor_Types_Enum;

  private showMessage: boolean = false;
  private submitationType: SubmitionType = SubmitionType.SaveAsDraft;
  private referenceNumber: string = '';
  public docTypes: any[];
  public get ShowSuccessMessage(): boolean {
    return this.showMessage;
  }
  public get SubmitationType(): number {
    return this.submitationType;
  }
  public get ReferenceNumber(): string {
    return this.referenceNumber;
  }

  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes?.find(
      (_) => _.ID == this.selectedLegalFormType
    );
    if (type) {
      return type.NameEnglish;
    }
    return '';
  }


  @Input() selectedLegalFormType: LegalFormTypesEnum | undefined;
  @Input() form: FormGroup;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() isCashTypSelected: boolean;
  @Input() showAction: boolean = false;
  @Input() showGetFoundingMemberButton: boolean = false;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() currentEstablishment: any;
  @Input() npoEstablishmentDonor: any;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.reviewAndSubmit.';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['currentEstablishment'] &&
      changes['currentEstablishment']?.currentValue
    ) {
      this.getBankList();
    }
  }

  ngOnInit() {
    this.NocRequestService.lookupData$.subscribe((dataLookup$) => {
      if (dataLookup$) {
        this.emirates = dataLookup$?.Emirates ?? [];
        this.countries = dataLookup$?.Countries ?? [];
        this.currencies = dataLookup$?.Currencies ?? [];
        this.accountTypes = dataLookup$?.AccountType ?? [];
        this.donationDateAndDurationTypes = dataLookup$?.DonationDateAndDurationType ?? [];
        this.categories = dataLookup$?.Categories ?? [];
        this.requestTypes = dataLookup$?.RequestType ?? [];
        this.legalFromTypes = dataLookup$?.LegalForms ?? [];
        this.entityLegalFormType = dataLookup$?.EntityType ?? [];
        this.reportParameter = dataLookup$?.ReportParametre ?? [];
        this.filteredLicensingAuthorities = dataLookup$?.LicensingAuthority ?? [];
        this.nationalities = dataLookup$?.Nationalities ?? [];
        this.passportTypes = dataLookup$?.PassportType ?? [];
        this.npoLicensedList = dataLookup$?.NPOLicensedForFundRaising ?? [];
        this.docTypes = dataLookup$.DocumentType ?? [];
        this.cashDonationTypeLookups = dataLookup$?.CashDonationType;
        this.inkindDonationTypeLookups = dataLookup$?.InkindDonationType;
        this.donorTypes = dataLookup$?.DonorType ?? [];
        this.entityTypes = dataLookup$?.EntityType ?? [];


        this.StepperService.requestData$.subscribe((data: any) => {
          this.formData = data;

          this.collectionSizeCashDonationTypes = this.formData?.TypeOfDonations?.CashDonationTypes?.length || 0;
          this.filteredCountCashDonationTypes = this.formData?.TypeOfDonations?.CashDonationTypes?.length || 0;
          this.getPremiumDataCashDonationTypes();
          
          this.collectionSizeInkindDonationTypes = this.formData?.TypeOfDonations?.InkindDonationTypes?.length || 0;
          this.filteredCountInkindDonationTypes = this.formData?.TypeOfDonations?.InkindDonationTypes?.length || 0;
          this.getPremiumDataInkindDonationTypes();

          this.checkAttachmentRequest(this.StepperService.requestId);

          if (this.formData?.NPOBankAccountDetails?.bank && this.currentEstablishment?.Id && this.formData?.NPOBankAccountDetails?.BankAccount) {
            this.getBankAccountDetails();
          }
        });

        this.getDownloadButton(this.StepperService.requestCode);
      }
    });
  }

  pageCashDonationTypes = 1;
  pageSizeCashDonationTypes = 5;
  collectionSizeCashDonationTypes = 0;
  filteredCountCashDonationTypes = 0;
  paginateDataCashDonationTypes: any[] = [];

  getPremiumDataCashDonationTypes(): void {
    const start =
      (this.pageCashDonationTypes - 1) * this.pageSizeCashDonationTypes;
    const end = start + this.pageSizeCashDonationTypes;
    this.paginateDataCashDonationTypes =
      this.formData?.TypeOfDonations?.cashDonations?.slice(start, end) ||
      [];
  }

  pageInkindDonationTypes = 1;
  pageSizeInkindDonationTypes = 5;
  collectionSizeInkindDonationTypes = 0;
  filteredCountInkindDonationTypes = 0;
  paginateDataInkindDonationTypes: any[] = [];

  getPremiumDataInkindDonationTypes(): void {
    const start =
      (this.pageInkindDonationTypes - 1) * this.pageSizeInkindDonationTypes;
    const end = start + this.pageSizeInkindDonationTypes;
    this.paginateDataInkindDonationTypes =
      this.formData?.TypeOfDonations?.inkindDonations?.slice(start, end) ||
      [];
  }

  getDocTypeName = (id: string): string => {
    let doctype = (this.docTypes.filter((doc) => doc != null && doc.InsideOutsideUAE == this.selectedLegalFormType)[0]?.DocumentType ?? []).find((_) => _.ID == id);
    return (
      (this.LanguageService.IsArabic
        ? doctype?.NameArabic
        : doctype?.NameEnglish) ?? ''
    );
  };

  getDownloadButton = (statusCode: number): void => {
    if (!this.legalFormTypeTitle) {
      return;
    }
    const legalFormType = this.legalFormTypeTitle.toLocaleLowerCase().trim();
    const collection = this.reportParameter.filter(
      (param) =>
        param.LegalForm?.toLocaleLowerCase().trim() === legalFormType &&
        param.ServiceCatlogue?.toLocaleLowerCase().trim() ==
        this.NPO_LINCEING.toLocaleLowerCase().trim()
    );
    if (statusCode == 1 || statusCode == 100000001) {
      this.downloadButtons = collection.filter(
        (_) =>
          _.RequestStatus == 1 ||
          _.RequestStatus == null ||
          _.RequestStatus == 100000001
      );
    } else {
      this.downloadButtons = collection.filter(
        (_) => _.RequestStatus == statusCode
      );
    }
  };

  getFundingMembersConfirmation = async (): Promise<void> => {
    const translationMessage = this.Translation.instant(
      'notify.saveTheRequestAsConfirmMessage'
    );
    const confirmed = await this.AlertService.confirmSubmit(translationMessage);
    if (confirmed) {
      let request = this.getRequestMappingObject(
        undefined,
        undefined,
        100000001
      );
      this.getFundingMember(request);
    }
  };

  getFundingMember = (request): void => {
    this.NocRequestService.createUpdateRecieveDonationRequest(
      request
    ).subscribe(
      (res) => {
        if (res.Status == 2000) {
          this.NotifyService.showSuccess('notify.success', 'notify.submitted');
          this.StepperService.requestId = res?.Id;
          this.StepperService.establishmentId = res?.EstablishmentId;
          this.showMessage = true;
          this.referenceNumber = res?.RequestNo;
          this.submitationType = SubmitionType.GetFoundingMemberConfirmation;
        } else {
          let errorMessage = this.LanguageService.IsArabic
            ? res.MessageAR
            : res.MessageEN;
          this.NotifyService.showError('notify.error', errorMessage.trim());
        }
      },
      (error) => {
        this.NotifyService.showError('notify.error', error);
      }
    );
  };

  submit = async (saveDraft: boolean = true): Promise<void> => {
    const translationTitle = this.Translation.instant(
      saveDraft
        ? 'alert.saveTheRequestAsDraftTitle'
        : 'alert.saveTheRequestAsSubmittTitle'
    );
    const translationMessage = this.Translation.instant(
      saveDraft
        ? 'notify.saveTheRequestAsDraftMessage'
        : 'notify.saveTheRequestAsSubmitMessage'
    );
    const translations = this.Translation.instant('alert');
    let param = {
      title: translationTitle,
      message: translationMessage,
      showCancelButton: true,
      confirmButtonColor: '#92722A',
      cancelButtonColor: '#d33',
      confirmButtonText: translations.confirmButtonText,
      cancelButtonText: translations.cancelButtonText,
    };
    const confirmed = await this.AlertService.showAlert(param);
    if (confirmed) {
      let requestCode = this.StepperService.requestCode;
      let request = this.getRequestMappingObject(
        undefined,
        undefined,
        saveDraft === true ? requestCode : 100000000
      );
      if (!saveDraft) {
        request.IsSubmitted = true;
        if (requestCode == 100000002) {
          request.IsResubmitted = true;
        }
      }
      this.NocRequestService.createUpdateRecieveDonationRequest(
        request
      ).subscribe(
        (res) => {
          if (res.Status == 2000) {
            this.NotifyService.showSuccess(
              'notify.success',
              saveDraft ? 'notify.draftSaved' : 'notify.submitted'
            );

            this.StepperService.requestId = res?.Id;
            this.StepperService.establishmentId = res?.EstablishmentId;
            this.referenceNumber = res?.RequestNo;
            this.submitationType = SubmitionType.SaveAsDraft;
            if (!saveDraft) {
              this.submitationType = SubmitionType.Submit;
              this.showMessage = true;
            } else {
              this.showMessage = false;
            }
          } else {
            let errorMessage = this.LanguageService.IsArabic
              ? res.MessageAR
              : res.MessageEN;
            this.NotifyService.showError('notify.error', errorMessage.trim());
          }
        },
        (error) => {
          this.NotifyService.showError('notify.error', error);
        }
      );
    }
  };


  getBankAccountDetails = (): void => {
    this.NocRequestService.getListOfBankAccounts(
      this.currentEstablishment?.Id,
      this.formData?.NPOBankAccountDetails?.bank
    ).subscribe({
      next: (_) => {

        const bankAccount = (_.BankAccounts ?? []).find(_ => _.recordId == this.formData?.NPOBankAccountDetails?.BankAccount);
        if (bankAccount) {
          this.formData.NPOBankAccountDetails.bankAccountDetails = bankAccount;
        }

      },
    });
  };

  getEmirate(emirateId: any): string {
    const emirate = this.emirates?.find((e) => e.ID == emirateId);
    return this.LanguageService.IsArabic
      ? emirate?.NameArabic ?? ''
      : emirate?.NameEnglish ?? '';
  }

  getCountry(countryId: any): string {
    const country = this.countries?.find((country) => country.ID == countryId);
    return this.LanguageService.IsArabic
      ? country?.NameArabic ?? ''
      : country?.NameEnglish ?? '';
  }

  generateDownloadLink = (file: any): string =>
    `data:${file?.mimeType};base64,${file?.Base64}`;

  createDownloadLink = (file: File): string => {
    const blob = new Blob([file], { type: file.type });
    return URL.createObjectURL(blob);
  };

  getLegalFormType = (legalFormId: string): string => {
    if (!legalFormId) return '';
    const legalFormFound = this.legalFromTypes.find(
      (legalForm) =>
        legalForm.ID?.toLocaleLowerCase()?.trim() ==
        legalFormId?.toLocaleLowerCase()?.trim()
    );
    if (!legalFormFound) return '';
    return this.LanguageService.IsArabic
      ? legalFormFound?.NameArabic
      : legalFormFound?.NameEnglish;
  };

  getRequestType = (requestTypeId: string): string => {
    if (!requestTypeId) return '';
    const requestTypeFound = this.requestTypes.find(
      (requestType) => requestType.ID == requestTypeId
    );
    if (!requestTypeFound) return '';
    return this.LanguageService.IsArabic
      ? requestTypeFound?.NameArabic
      : requestTypeFound?.NameEnglish;
  };

  getDonorType = (donorTypeId: number): Lookup | undefined => {
    const donorType = DONOR_TYPES.find(type => type.ID == donorTypeId);
    return donorType;
  }

  getEntityLegalFormType = (entityLegalFormTypeId: number): string => {
    const entityLegalFormFound = this.entityLegalFormType.find(
      (entityLegalFormType) => entityLegalFormType.ID == entityLegalFormTypeId
    );
    if (!entityLegalFormFound) return '';
    return this.LanguageService.IsArabic
      ? entityLegalFormFound?.NameArabic
      : entityLegalFormFound?.NameEnglish;
  };

  getBank = (bankId: string): string => {
    const bank = this.banks.find(b => b.ID == bankId);
    return this.LanguageService.IsArabic
      ? (bank?.NameArabic ?? '')
      : (bank?.NameEnglish ?? '');
  }

  getCurrency = (currencyId: string): string => {
    const currency = this.currencies.find(b => b.ID == currencyId);
    return this.LanguageService.IsArabic
      ? (currency?.NameArabic ?? '')
      : (currency?.NameEnglish ?? '');
  }

  getBankAccountType = (bankAccountType: number): string => {
    const accountType = this.accountTypes.find(b => b.ID == String(bankAccountType));
    return this.LanguageService.IsArabic
      ? (accountType?.NameArabic ?? '')
      : (accountType?.NameEnglish ?? '');
  }

  getDonationDateAndDurationType = (type: number): Lookup | undefined => {
    const donationDateAndDurationType = this.donationDateAndDurationTypes.find(b => b.ID == String(type));
    return donationDateAndDurationType!;
  }

  getBankList = (): void => {
    this.NocRequestService.getBankList(this.currentEstablishment.Id).subscribe(_ => {
      this.banks = [];
      (_.Banks as any[]).forEach(_ => {
        this.banks.push({
          ID: _.recordId,
          NameArabic: _.BankArabicName,
          NameEnglish: _.BankEnglishName,
          ParentID: ''
        })
      })
    })
  }

  getLicensingEntity = (licensingAuthority: any): string => {
    if (!licensingAuthority) return '';
    const licensingEntityFound = this.filteredLicensingAuthorities.find(
      (auth) => auth.ID == licensingAuthority
    );
    if (!licensingEntityFound) return '';
    return this.LanguageService.IsArabic
      ? licensingEntityFound?.NameArabic
      : licensingEntityFound?.NameEnglish;
  };

  getNationality = (nationality: any): string => {
    if (!nationality) return '';
    const nationalityFound = this.nationalities.find(
      (nation) => nation.ID == nationality
    );
    if (!nationalityFound) return '';
    return this.LanguageService.IsArabic
      ? nationalityFound?.NameArabic
      : nationalityFound?.NameEnglish;
  };

  getPassportType = (passportTypeId: any): string => {
    if (!passportTypeId) return '';
    const passportTypeFound = this.passportTypes.find(
      (passportType) => passportType.ID == passportTypeId
    );
    if (!passportTypeFound) return '';
    return this.LanguageService.IsArabic
      ? passportTypeFound?.NameArabic
      : passportTypeFound?.NameEnglish;
  };

  getEntityType = (id: any): string => {
    if (!id) return '';
    const typeFound = this.entityTypes.find(
      (type) => type.ID == id
    );
    if (!typeFound) return '';
    return this.LanguageService.IsArabic
      ? typeFound?.NameArabic
      : typeFound?.NameEnglish;
  };



  getMainCategory = (mainCategory: any): string => {
    if (!mainCategory) return '';
    const mainCategoryFound = this.categories.find(
      (category) => category.ID == mainCategory
    );
    if (!mainCategoryFound) return '';
    return this.LanguageService.IsArabic
      ? mainCategoryFound?.NameArabic
      : mainCategoryFound?.NameEnglish;
  };

  getCashDonationType = (cashDonationTypeId: number): Lookup | undefined => {
    const typeFound = this.cashDonationTypeLookups.find(
      (type) => type.ID == String(cashDonationTypeId)
    );
    return typeFound!;
  }

  getInkindDonationType = (inkindDonationTypeId: number): Lookup | undefined => {
    const typeFound = this.inkindDonationTypeLookups.find(
      (type) => type.ID == String(inkindDonationTypeId)
    );
    return typeFound!;
  }

  download = (file: any): void => {
    if (this.formData?.Id) {
      this.downloadNpoFiles(
        'application/pdf',
        file.ReportName,
        this.formData?.Id,
        file.LogicalName,
        this.formData?.BasicInformationForm?.EstablishmentId
      );
    } else {
      let request = this.getRequestMappingObject(undefined, undefined, 1);
      this.NocRequestService.createUpdateRecieveDonationRequest(
        request
      ).subscribe(
        (res) => {
          if (res.Status == 2000) {
            this.StepperService.requestId = res?.Id;
            this.StepperService.establishmentId = res?.EstablishmentId;
            this.referenceNumber = res?.RequestNo;
            this.getRouterMode();
            this.downloadNpoFiles(
              'application/pdf',
              file.ReportName,
              res?.Id,
              file.LogicalName,
              res?.BasicInformationForm?.EstablishmentId
            );
          }
        },
        (error) => {
          this.NotifyService.showError('notify.error', error);
        }
      );
    }
  };

  checkSubmitButtonIncaseRequestForUpdate = (): boolean => {
    return true;
  };

  compareJsonObjects(json1: any, json2: any, key: string): boolean {
    if (json1.hasOwnProperty(key) && json2.hasOwnProperty(key)) {
      const value1 = json1[key];
      const value2 = json2[key];

      if (this.isObject(value1) && this.isObject(value2)) {
        return this.deepCompareObjects(value1, value2);
      }

      return value1 === value2;
    } else {
      return false;
    }
  }

  isObject(value: any): boolean {
    return value !== null && typeof value === 'object';
  }

  deepCompareObjects(obj1: any, obj2: any): boolean {
    if (typeof obj1 !== 'object' || typeof obj2 !== 'object') {
      return false;
    }

    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (let key of keys1) {
      if (!obj2.hasOwnProperty(key)) {
        return false;
      }

      if (!this.compareJsonObjects(obj1, obj2, key)) {
        return false;
      }
    }

    return true;
  }

  documents: any[] = [];
  checkAttachmentRequest = (requestId: string): void => {
    if (requestId && requestId != '' && requestId != undefined) {
      this.NocRequestService.fetchAttachments(requestId)
        .subscribe(res => {
          this.documents = res?.data ?? [];
        });
    }
  }
}
