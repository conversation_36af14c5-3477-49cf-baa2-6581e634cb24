import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function editEstablishmentNumberValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value = control.value;

    if (value === null || value === undefined || (typeof value === 'string' && value.trim() === '')) {
      return null;
    }

    const normalized = typeof value === 'string' ? value.trim() : String(value);

    if (normalized === '0') {
      return { invalidNumber: true };
    }

    const isValid = /^[1-9][0-9]{0,9}$/.test(normalized);
    return isValid ? null : { invalidNumber: true };
  };
}
