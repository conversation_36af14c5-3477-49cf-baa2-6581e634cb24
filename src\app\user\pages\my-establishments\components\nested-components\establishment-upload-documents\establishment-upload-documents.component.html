<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="row align-items-center justify-content-between" style="row-gap: 1rem;">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-start flex-wrap">
          <h1>{{messageTranslationPrefix+'title' | translate}}</h1>
        </div>
      </div>

      @if(Page_View_Type === 2){
        <ng-container *ngFor="let document of documentList">
          <div class="col-md-12">
            <app-file-upload [label]="LanguageService.IsArabic ? document.NameArabic : document.NameEnglish"
              [docTypeId]="document.ID"
              [description]="LanguageService.IsArabic ? document.descriptionArabic : document.descriptionEnglish"
              [control]="getFormControl(document.ID)" [columns]="12" (fileUploaded)="onFileUploaded($event)"
              (fileDeleted)="onFileDeleted($event)" [required]="document.Requirement === 'Required'" [isNpoServices]="true"
              [useCustomExtensions]="true" [allowedExtensions]="document?.AllowedExtensions??'PNG,JPG,JPEG,PDF'"
              [maxFileSize]="document?.AllowedSize??5" [lang]="LanguageService.IsArabic ? 'ar':'en'"
              [externalInjectFile]="getexternalInjectFile(document.ID)">
            </app-file-upload>
          </div>
        </ng-container>
      }

      @if(Page_View_Type === 1){
        <div class="col-md-12">

          <div class="table-container d-block_mobile">
            <table class="my-table">
              <thead>
                <th translate class="align-middle">{{'Name' | translate}}</th>
                <th translate class="align-middle">{{'Type' | translate}}</th>
                <th translate class="align-middle">{{'Action' | translate}}</th>
              </thead>
              <ng-container *ngFor="let file of documents">
                <tr>
                  <td class="align-middle text-center">{{file?.FileName}}</td>
                  <td class="align-middle text-center"> {{getDocTypeName(file?.DocumentType)}}</td>
                  <td class="align-middle text-center">
                    <a type="button" *ngIf="file" class="btn text-danger" [href]="generateDownloadLink(file)"
                      [download]="file?.FileName">
                      <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                          fill="#92722A" />
                      </svg>
                    </a>
                  </td>
                </tr>
              </ng-container>

              <ng-container *ngFor="let button of downloadButtons">
                <tr>
                  <td class="align-middle text-center">
                    {{LanguageService.IsArabic?button?.ButtonLabelAr?.split('تحميل')[1]:
                    button?.ButtonLabelEn?.split('Download')[1]}}.pdf</td>
                  <td class="align-middle text-center">
                    {{LanguageService.IsArabic?button?.ButtonLabelAr?.split('تحميل')[1]:
                    button?.ButtonLabelEn?.split('Download')[1]}}</td>
                  <td class="align-middle text-center">
                    <a type="button" class="btn text-danger" (click)="download(button)">
                      <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                          fill="#92722A" />
                      </svg>
                    </a>
                  </td>
                </tr>
              </ng-container>

            </table>
          </div>

          <ng-container *ngFor="let file of documents">
            <div class="figma-card-container">
              <div class="figma-card">

                <button mat-icon-button type="button" [matMenuTriggerFor]="menu" class="figma-actions-menu-trigger">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #menu="matMenu">
                  <a type="button" *ngIf="file" class="btn text-danger" [href]="generateDownloadLink(file)"
                    [download]="file?.FileName">
                    <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                        fill="#92722A" />
                    </svg>
                  </a>
                </mat-menu>

                <div class="figma-card-content">
                  <div class="figma-card-field">
                    <span class="static-value">{{ "Name" | translate}}:</span>
                    <span class="dynamic-value">{{ file.FileName }}</span>
                  </div>
                  <div class="figma-card-field">
                    <span class="static-value">{{ 'Type' | translate }}:</span>
                    <span class="dynamic-value">{{ file.mimeType ?? file.MimeType }}</span>
                  </div>

                </div>
              </div>
            </div>
          </ng-container>
          <ng-container *ngFor="let button of downloadButtons">
            <div class="figma-card-container">
              <div class="figma-card">

                <button mat-icon-button type="button" [matMenuTriggerFor]="menu" class="figma-actions-menu-trigger">
                  <mat-icon>more_vert</mat-icon>
                </button>
                <mat-menu #menu="matMenu">
                  <a type="button" class="btn text-danger" (click)="download(button)">
                    <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
                        fill="#92722A" />
                    </svg>
                  </a>
                </mat-menu>

                <div class="figma-card-content">
                  <div class="figma-card-field">
                    <span class="static-value">{{ "Name" | translate}}:</span>
                    <span class="dynamic-value"> {{LanguageService.IsArabic?button?.ButtonLabelAr?.split('تحميل')[1]:
                      button?.ButtonLabelEn?.split('Download')[1]}}.pdf</span>
                  </div>
                  <div class="figma-card-field">
                    <span class="static-value">{{ 'Type' | translate }}:</span>
                    <span class="dynamic-value"> {{LanguageService.IsArabic?button?.ButtonLabelAr?.split('تحميل')[1]:
                      button?.ButtonLabelEn?.split('Download')[1]}}</span>
                  </div>

                </div>
              </div>
            </div>
          </ng-container>
        </div>
      }

    </div>
    <!-- <div class="button_gp">
      <ng-container *ngFor="let button of downloadButtons">
        <button type="button" class="btn download-button" (click)="download(button)">
          <svg width="20" height="20" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M18 11.25V16.5C18 16.8978 17.842 17.2794 17.5607 17.5607C17.2794 17.842 16.8978 18 16.5 18H1.5C1.10218 18 0.720644 17.842 0.43934 17.5607C0.158035 17.2794 0 16.8978 0 16.5V11.25C0 11.0511 0.0790178 10.8603 0.21967 10.7197C0.360322 10.579 0.551088 10.5 0.75 10.5C0.948912 10.5 1.13968 10.579 1.28033 10.7197C1.42098 10.8603 1.5 11.0511 1.5 11.25V16.5H16.5V11.25C16.5 11.0511 16.579 10.8603 16.7197 10.7197C16.8603 10.579 17.0511 10.5 17.25 10.5C17.4489 10.5 17.6397 10.579 17.7803 10.7197C17.921 10.8603 18 11.0511 18 11.25ZM8.46937 11.7806C8.53903 11.8504 8.62175 11.9057 8.7128 11.9434C8.80384 11.9812 8.90144 12.0006 9 12.0006C9.09856 12.0006 9.19616 11.9812 9.2872 11.9434C9.37825 11.9057 9.46097 11.8504 9.53063 11.7806L13.2806 8.03063C13.3503 7.96094 13.4056 7.87822 13.4433 7.78717C13.481 7.69613 13.5004 7.59855 13.5004 7.5C13.5004 7.40145 13.481 7.30387 13.4433 7.21283C13.4056 7.12178 13.3503 7.03906 13.2806 6.96937C13.2109 6.89969 13.1282 6.84442 13.0372 6.8067C12.9461 6.76899 12.8485 6.74958 12.75 6.74958C12.6515 6.74958 12.5539 6.76899 12.4628 6.8067C12.3718 6.84442 12.2891 6.89969 12.2194 6.96937L9.75 9.43969V0.75C9.75 0.551088 9.67098 0.360322 9.53033 0.21967C9.38968 0.0790178 9.19891 0 9 0C8.80109 0 8.61032 0.0790178 8.46967 0.21967C8.32902 0.360322 8.25 0.551088 8.25 0.75V9.43969L5.78063 6.96937C5.63989 6.82864 5.44902 6.74958 5.25 6.74958C5.05098 6.74958 4.86011 6.82864 4.71937 6.96937C4.57864 7.11011 4.49958 7.30098 4.49958 7.5C4.49958 7.69902 4.57864 7.88989 4.71937 8.03063L8.46937 11.7806Z"
              fill="#92722A" />
          </svg>
          {{LanguageService.IsArabic?button?.ButtonLabelAr: button?.ButtonLabelEn}}
        </button>
      </ng-container>
    </div> -->

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" class="btn basic-filled-button" (click)="previous.emit()">{{'Previous' |translate}}</button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" > {{ "Next" | translate }}</button>
    </div>

  </form>
</div>
