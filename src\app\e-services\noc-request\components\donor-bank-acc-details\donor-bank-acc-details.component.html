<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="col-md-12 d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center">
        {{(messageTranslationPrefix+'title') | translate}}
      </h1>
    </div>


    <div class="row section-separator">
      <div class="col-md-6 col-sm-12">
        <app-select-search [label]="messageTranslationPrefix+'country' | translate" [control]="fb?.Country"
          [data]="countries" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'bankName' | translate" [control]="fb?.BankName" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'accountNumber' | translate" [control]="fb?.AccountNumber" />
      </div>
      <div class="col-md-6 col-sm-12">
        <app-input [label]="messageTranslationPrefix+'ibanNumber' | translate" [control]="fb?.IBANNumber" />
      </div>

    </div>

    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
        (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{"saveAsDraft" | translate }}</button>
      <button type="submit" class="btn basic-filled-button" [disabled]="!isValidForm()"> {{ "Next" | translate }}
      </button>
    </div>
  </form>
</div>
