<div class="container-fluid">
  <form class="appform" [formGroup]="form" (ngSubmit)="submit()" novalidate>
    <div class="d-flex justify-content-between align-items-start flex-wrap">
      <h1 class="d-flex align-items-center mb-0">{{(messageTranslationPrefix+'Title')
        | translate}}
        <span class="pro_names">{{objectives.length}}
          {{messageTranslationPrefix+'objectives' |
          translate}}</span>
      </h1>
      @if(legalFormType !== LEGAL_FORM_TYPES.Union){
       <!--  <div class="d-block to-conditions to-tooltip">
          {{messageTranslationPrefix+'help' | translate}}
          <span class="info-icon">
            <svg width="20" height="20" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
                fill="#92722A" />
            </svg>
          </span>
          <div class="tooltip">
            <h4 class="tooltip-title">{{messageTranslationPrefix+'npoObjectives' |
              translate}}:</h4>
            <div>
              <ul>
                @if(legalFormType === LEGAL_FORM_TYPES.AssociationByDecree ||
                legalFormType === LEGAL_FORM_TYPES.NationalSocietyByDecree){
                <li>{{messageTranslationPrefix+'helpMessageByDecree' |
                  translate}}</li>
                }
                @else{
                <li>{{messageTranslationPrefix+'helpMessage' | translate}}</li>
                }
              </ul>
            </div>
          </div>
        </div>-->
      }
    </div>
    <div *ngIf="!isNotAllowedToEdit" style="height: 64px !important;" class="d-flex align-items-lg-center">
      <div class="col-12 col-lg-5 col-xl-4" >
        @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001){
        <app-modal [form]="internalObjectiveForm" [title]="'Add Objective'" [size]="'lg'"
          buttonIcon="fas fa-circle-plus" [viewContentOnly]="false" [buttonEnabled]="true"
          [buttonClass]="'btn btn-primary btn-plus'" buttonLabel="Add Objective" [showCancelButton]="false"
          [resetThenDismiss]="false" (submitAction)="manageObjective($event,GRID_ACTION_TYPES.ADD)">
          <div class="row">
            <app-textarea [label]="messageTranslationPrefix+'ObjectiveEn'" [columns]="6"
              [control]="fControls.Objectives" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
              [showGoogleTranslateToRelatedCompoent]="true"
              [googleTranslateToRelatedCompoent]="fControls.ObjectivesAR"></app-textarea>
            <app-textarea [label]="messageTranslationPrefix+'ObjectiveAr'" [columns]="6"
              [control]="fControls.ObjectivesAR" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
              [showGoogleTranslateToRelatedCompoent]="true" [googleTranslateToRelatedCompoent]="fControls.Objectives"
              class="ar_lang"></app-textarea>
            <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveEn'" [columns]="6"
              [control]="fControls.MeansOfAchievingObjective" [showGoogleTranslate]="Page_View_Type === 2"
              [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
              [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjectiveAR"></app-textarea>
            <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveAr'" [columns]="6"
              [control]="fControls.MeansOfAchievingObjectiveAR" [showGoogleTranslate]="Page_View_Type === 2"
              [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
              [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjective" class="ar_lang"></app-textarea>

          </div>
        </app-modal>
        }
      </div>

    </div>

    <!-- @if(objectives.length > 0) -->
    <!-- { -->
      <div formArrayName="objectives">
        <div style="margin-top: 26px !important;" class="d-block_mobile">
          <div class="table-responsive">
            <table class="my-table">
              <thead>
                <tr>
                  <th *ngIf="!isNotAllowedToEdit" class="align-middle text-center" scope="col"></th>
                  <th class="align-middle text-center" scope="col">#</th>
                  <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"ObjectiveEn"
                    |translate }}</th>
                  <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"ObjectiveAr"
                    |translate }}</th>
                  <th class="align-middle text-center text-wrap" scope="col">
                    {{messageTranslationPrefix+"MeansOfAchievingObjectiveEn" |translate}}</th>
                  <th class="align-middle text-center text-wrap" scope="col">
                    {{messageTranslationPrefix+"MeansOfAchievingObjectiveAr" |translate}}</th>
                  <!-- <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"status" | translate
                    }}</th> -->
                  <th class="align-middle text-center text-wrap text-center" scope="col" *ngIf="!isNotAllowedToEdit">
                    {{messageTranslationPrefix+"Actions" |translate }}</th>
                </tr>
              </thead>
              <tbody>
                @for (objective of reprsentedDataTable.controls; track $index) {

                @let order$=$index + tableIndex + 1;
                <tr [formGroupName]="$index">
                  <td *ngIf="!isNotAllowedToEdit" class="align-middle text-center">
                    <!-- <div class="d-flex gap-1 align-items-center justify-content-center w-100 h-100"
                      *ngIf="objective.get('canEdit')?.value != false">
                      <i class="fas fa-arrow-down-long cursor-pointer" *ngIf="objectives.controls.length!==(order$)"
                        (click)="moveTableItem(order$-1,objectives.controls,MOVE_TYPE.Down,pagination,countOfPredefindConditions)"></i>
                      <i class="fas fa-arrow-up-long cursor-pointer" *ngIf="(order$)!==(1)"
                        (click)="moveTableItem(order$-1,objectives.controls,MOVE_TYPE.Up,pagination,countOfPredefindConditions)"></i>
                    </div> -->
                  </td>
                  <td class="align-middle text-center">{{ order$ }}</td>
                  <td class="align-middle text-center text-truncate" lang="en"
                    [matTooltip]="objective.get('Objectives')?.value" style="max-width: 150px;">{{
                    objective.get("Objectives")?.value}}</td>
                  <td class="align-middle text-center text-truncate" lang="ar"
                    [matTooltip]="objective.get('ObjectivesAR')?.value" style="max-width: 150px;">
                    {{objective.get("ObjectivesAR")?.value }}</td>
                  <td class="align-middle text-center text-truncate" lang="en"
                    [matTooltip]="objective.get('MeansOfAchievingObjective')?.value" style="max-width: 150px;">
                    {{objective.get("MeansOfAchievingObjective")?.value}}</td>
                  <td class="align-middle text-center text-truncate" lang="ar"
                    [matTooltip]="objective.get('MeansOfAchievingObjectiveAR')?.value" style="max-width: 150px;">
                    {{objective.get("MeansOfAchievingObjectiveAR")?.value}}</td>
                  <!-- <td class="align-middle text-center text-wrap">{{getStatusByName(objective.get("SatusReason")?.value) }}
                  </td> -->
                  @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001){
                  @if(objective.get('canEdit')?.value != false){
                  <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit">
                    <button mat-icon-button type="button" class="actions-menu-trigger" [matMenuTriggerFor]="menu"
                      aria-label="Example icon-button with a menu">
                      <mat-icon>more_vert</mat-icon>
                    </button>

                    <mat-menu #menu="matMenu">
                      <ng-container [ngTemplateOutlet]="gridActions"
                        [ngTemplateOutletContext]="{objective: objective,index: order$-1}" />
                    </mat-menu>

                    <div class="actions-direct-buttons">
                      <ng-container [ngTemplateOutlet]="gridActions"
                        [ngTemplateOutletContext]="{objective: objective,index: order$-1}" />
                    </div>
                  </td>
                  }@else{<td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>}
                  }
                  @else { <td class="align-middle text-center" *ngIf="!isNotAllowedToEdit"></td>}
                </tr>
                }
              </tbody>
            </table>
          </div>
          <div class="d-flex justify-content-center p-2 aegov-pagination">
            <ngb-pagination [maxSize]="10" [ellipses]="true" [(page)]="page" [pageSize]="pageSize"
              [collectionSize]="objectives.length" (pageChange)="pagination()" aria-label="Custom pagination">
              <ng-template ngbPaginationPrevious>
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                  <path
                    d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
                </svg>
                <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
              </ng-template>
              <ng-template ngbPaginationNext>
                <span class="d-none d-lg-block">{{'Next' | translate}}</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                  <path
                    d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
                </svg>
              </ng-template>
            </ngb-pagination>

          </div>
        </div>

        <div class="figma-card-container">
          @for(objective of reprsentedDataTable.controls; track $index) {

          <div class="figma-card">
            @if(isReturnForUpdate || StepperService.requestCode === 1 || StepperService.requestCode ===100000001){
            @if(objective.get('canEdit')?.value != false){
            <button *ngIf="!isNotAllowedToEdit" mat-icon-button type="button" class="figma-actions-menu-trigger"
              [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu">
              <mat-icon>more_vert</mat-icon>
            </button>
            }
            <mat-menu #menu="matMenu">
              <ng-container [ngTemplateOutlet]="gridActions"
                [ngTemplateOutletContext]="{objective: objective,index: $index}" />
            </mat-menu>
            }
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ $index + 1 }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"ObjectiveEn" | translate }}:</div>
                <div class="dynamic-value" lang="en">{{objective.get("Objectives")?.value }}</div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"ObjectiveAr" | translate }}:</div>
                <div class="dynamic-value" lang="ar">{{objective.get("ObjectivesAR")?.value }}</div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"MeansOfAchievingObjectiveEn" | translate}}:</div>
                <div class="dynamic-value" lang="en">{{objective.get("MeansOfAchievingObjective")?.value}}</div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">{{messageTranslationPrefix+"MeansOfAchievingObjectiveAr" | translate}}:</div>
                <div class="dynamic-value" lang="ar">{{objective.get("MeansOfAchievingObjectiveAR")?.value}}</div>
              </div>
              <!-- <div class="figma-card-field">
                <div class="static-value">{{ messageTranslationPrefix + 'status'| translate }}:</div>
                <div class="dynamic-value">
                  {{getStatusByName(objective.get("SatusReason")?.value) }}
                </div>
              </div> -->
            </div>
          </div>
          }
        </div>
      </div>
    <!-- } -->
    <div *ngIf="!isNotAllowedToEdit" class="button_gp">
      <button type="button" class="btn basic-filled-button" (click)="previous.emit()">
        {{'Previous' | translate}}
      </button>
      <button type="button" (click)="saveAsDraft()" class="btn basic-button"> {{
        "saveAsDraft" | translate }}</button>
      <button [disabled]="!(isValidForm())" class="btn basic-filled-button">{{'Next' | translate}}

      </button>
    </div>
  </form>
</div>

<ng-template #gridActions let-index="index" let-objective="objective">
  <ng-container >
    <app-modal [operation]="'edit'" [form]="internalObjectiveForm" [title]="messageTranslationPrefix+'editObjective'"
      [size]="'lg'" buttonIcon="edit" [viewContentOnly]="false" [buttonEnabled]="true" [buttonClass]="'btn btn-primary'"
      [buttonLabel]="'Save'| translate" [showCancelButton]="false" [resetThenDismiss]="false"
      (submitAction)="manageObjective($event,GRID_ACTION_TYPES.EDIT)" (opened)="handleEdit(objective,index)"
      [isIcon]="true">
      <div class="row">
        <app-textarea [label]="messageTranslationPrefix+'ObjectiveEn'" [control]="fControls.Objectives"
          [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.ObjectivesAR"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'ObjectiveAr'" [control]="fControls.ObjectivesAR"
          [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.Objectives" class="ar_lang"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveEn'" [columns]="6"
          [control]="fControls.MeansOfAchievingObjective" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
          [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjectiveAR"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveAr'" [columns]="6"
          [control]="fControls.MeansOfAchievingObjectiveAR" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
          [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjective" class="ar_lang"></app-textarea>

      </div>
    </app-modal>
    <app-modal [operation]="'clone'" [form]="internalObjectiveForm" [title]="messageTranslationPrefix+'cloneObjective'"
      [size]="'lg'" buttonIcon="content_copy" [viewContentOnly]="false" [buttonEnabled]="true"
      [buttonClass]="'btn btn-primary'" [buttonLabel]="'Save'| translate" [showCancelButton]="false"
      [resetThenDismiss]="false" (submitAction)="manageObjective($event,GRID_ACTION_TYPES.CLONE)"
      (opened)="handleClone(objective)" [isIcon]="true">
      <div class="row">
        <app-textarea [label]="messageTranslationPrefix+'ObjectiveEn'" [control]="fControls.Objectives"
          [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.ObjectivesAR"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'ObjectiveAr'" [control]="fControls.ObjectivesAR"
          [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'" [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.Objectives" class="ar_lang"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveEn'" [columns]="6"
          [control]="fControls.MeansOfAchievingObjective" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'en'"
          [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjectiveAR"></app-textarea>
        <app-textarea [label]="messageTranslationPrefix+'MeansOfAchievingObjectiveAr'" [columns]="6"
          [control]="fControls.MeansOfAchievingObjectiveAR" [showGoogleTranslate]="Page_View_Type === 2" [googleTranslateTarget]="'ar'"
          [showGoogleTranslateToRelatedCompoent]="true"
          [googleTranslateToRelatedCompoent]="fControls.MeansOfAchievingObjective" class="ar_lang"></app-textarea>
      </div>
    </app-modal>
  </ng-container>
  <button class="btn text-primary" type="button" (click)="removeObjective(objective,index)">
    <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
        fill="#92722A" />
    </svg>
  </button>
</ng-template>
