import { Attachment } from './../models/attachment.model';
import { ElementRef, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Lookup } from '../models/lookup.model';

@Injectable({
  providedIn: 'root',
})
export class StepperService {
  private formDataSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  formData$: Observable<any> = this.formDataSubject.asObservable();

  private applicationDataSubject: BehaviorSubject<any> = new BehaviorSubject<any>({});
  applicationData$: Observable<any> = this.applicationDataSubject.asObservable();

  private enablePay: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  enablePay$: Observable<any> = this.enablePay.asObservable();

  constructor() {}

  getAttachments() {
    return this.formDataSubject.value?.attachments;
  }
  getFormData() {
    return this.formDataSubject.value;
  }

  updateFormData(formData: any, isAttachments:boolean=false) {
    if (!isAttachments) {
      const updatedData = { ...this.formDataSubject.getValue(), ...formData };
      this.formDataSubject.next(updatedData);
    }
    else
    {
      const updatedData = { ...this.formDataSubject.getValue(), attachments: formData };
      this.formDataSubject.next(updatedData);
    }
  }
  updateApplicationData(applicationData: any) {
    const updatedData = { ...this.applicationDataSubject.getValue(), ...applicationData };
    this.applicationDataSubject.next(updatedData);
  }

  resetFormData() {
    this.formDataSubject.next({});
  }

  scrollToError(elementRef: ElementRef) {
    const firstDiv = elementRef.nativeElement.querySelector('.ng-invalid');
    if (firstDiv) {
      firstDiv.scrollIntoView({ behavior: 'smooth' });
    }
  }

  enableDisablePay(enable:boolean)
  {
    this.enablePay.next(enable);
  }

  getLookup(list: Lookup[], id: string): Lookup {
    return list?.filter((x) => String(x.ID).toLowerCase() == String(id)?.toLowerCase())[0];
  }

  setAutoStep(isEnabled:boolean){
    localStorage.setItem('auto.step', isEnabled?'on':'off');
  }

  public get IsAutoStep() {
    const autoStep = localStorage.getItem('auto.step');
    return autoStep==='on'?true:false;
  }

}
