app-footer {
    width: 100%;
    position: absolute;
    background-color: $aeblack-50 !important;

    &::before {
        position: absolute;
        top: 0;
        height: .5rem;
        width: 100%;
        content: "";
        background-image: linear-gradient(to bottom, $aegold-400, $aegold-600);
    }
}

#footer {
    position: relative;
    padding-top: .5rem;

    ul {
        list-style: none;
        padding: 0;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }
    }

    .footer-top {
        padding-top: 24px;
        padding-bottom: 24px;

        @media (min-width: 768px) {
            padding-top: 48px;
            padding-bottom: 48px;
        }
    }

    .footer-top-left {
        gap: 12px;
        display: block;

        @media (min-width: 768px) {
            display: flex;
        }
    }

    .footer-siteInfo {
        padding: 24px;

        @media (min-width: 768px) {
            display: none;
        }

        p {
            margin-bottom: 12px;
            color: $aeblack-700;
            font-size: 14px;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
            //     font-family: "Noto <PERSON> Arabic" !important;
            // }

            &:last-of-type {
                margin-bottom: 0;
            }
        }
    }

    .accordion-title button {
        font-size: 20px !important;
        line-height: 28px;
        // font-family: 'Inter' !important;
        font-weight: 700 !important;
        justify-content: space-between !important;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }
    }

    .footer-contact li svg {
        width: 20px;
        height: 20px;
        fill: $aegreen-600;
    }

    .divide-y {
        li {
            padding: 16px 0 !important;
        }

        .custom-divide {
            border-top: 1px $aeblack-100 solid;
            border-bottom: 1px $aeblack-100 solid;
            display: flex;

            a:first-of-type {
                border-right: 1px $aeblack-100 solid;
                margin-right: 16px;
                padding-right: 16px;
            }

            [ng-reflect-dir=rtl] &,
            [dir=rtl] & {
                a:first-of-type {
                    border-left: 1px $aeblack-100 solid;
                    margin-left: 16px;
                    padding-left: 16px;
                    border-right: none;
                    margin-right: 0;
                    padding-right: 0;
                }
            }
        }
    }

    a {
        font-size: .875rem;
        font-weight: 400;
        color: rgb(62 64 70 / var(--tw-text-opacity));
        text-decoration-line: none;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }
    }

    @media (min-width: 768px) {
        .accordion-title button {
            color: $aegold-500 !important;
        }

        .accordion-item {
            border-bottom: none !important;
        }
    }

    .accordion-item {
        border-bottom: 1px solid $aeblack-100;
        padding: 16px 0;
    }

    .aegov-footer .accordion-title button {
        // color: $aegold-700 !important;
        color: $aegold-500 !important;
    }

    .aegov-footer .accordion-title button.accordion-active {
        color: $aegold-700;
    }

    .aegov-footer .accordion-title button.accordion-active svg {
        color: $aegold-700;
    }

    .aegov-footer .accordion-content-body>:not([hidden])~:not([hidden]) {
        margin-top: 12px;
    }

    .footer-contact li {
        width: 100%;
        justify-content: center;
        padding-top: .75rem;
        padding-bottom: .75rem;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }
    }

    @media (min-width: 1280px) {
        .footer-contact li {
            padding-left: 2rem;
            padding-right: 2rem
        }
    }

    .inline-with-gap {
        display: inline-flex;
        align-items: center;
        gap: .75rem;
        text-wrap: nowrap;
    }

    .footer-bottom {
        border-top: 1px solid $aeblack-100 !important;
        padding: 48px 0;
        text-align: center;
        color: $aeblack-700;

        @media (min-width: 1024px) {
            text-align: left !important;
            padding: 54px 0;
        }
    }

    .social-sharing {
        display: flex;
        align-items: center;
        gap: 1.5rem;

        a {
            color: $aeblack-600;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
            //     font-family: "Noto Kufi Arabic" !important;
            // }
        }

        a:hover {
            color: $aegold-600;
        }

        svg {
            display: inline-block;
            height: 2rem;
            width: 2rem;
        }

        ul {
            margin: 0 !important;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
            //     font-family: "Noto Kufi Arabic" !important;
            // }
        }

        span {
            display: none;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
            //     font-family: "Noto Kufi Arabic" !important;
            // }

            @media (min-width: 768px) {
                display: inline-block;
            }
        }
    }

    .grid {
        display: grid;
    }

    .sm\:gap-x-2\.5 {
        @media (min-width: 768px) {
            gap: 36px;
        }
    }

    .sm\:grid-cols-2 {
        @media (min-width: 768px) {
            grid-template-columns: repeat(2, minmax(0, 1fr));
        }
    }

    .lg\:grid-cols-4 {
        @media (min-width: 1024px) {
            grid-template-columns: repeat(4, minmax(0, 1fr))
        }
    }

    .accordion-title button {
        width: 100%;
        flex-wrap: nowrap;
        justify-content: flex-start;
        -moz-column-gap: 1rem;
        column-gap: 1rem;
        text-align: start;
        font-weight: 600;
        transition-property: all;
        transition-timing-function: cubic-bezier(.4, 0, .2, 1);
        transition-duration: .15s;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }
    }

    .accordion-title button svg {
        height: 1.5rem;
        width: 1.5rem;
        flex-shrink: 0;
        fill: currentColor;
        color: $aeblack-600;
    }

    .accordion-content {
        color: $aeblack-800;
        display: none;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
        //     font-family: "Noto Kufi Arabic" !important;
        // }

        @media (min-width: 768px) {
            display: block;
        }
    }

    .accordion-content-body {
        a {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: $aeblack-700 !important;
            text-decoration: none !important;
            // font-family: "Roboto" !important;

            @media (max-width: 1024px){
                font-weight: 700;
            }
            // &:dir(rtl) {
            //     font-family: "Noto Kufi Arabic" !important;
            // }
        }
    }

    .accordion-title {
        span {
            // font-family: 'Inter' !important;
            // font-weight: 700 !important;
            font-size: 20px !important;
            line-height: 28px;
            color: $aegold-500 !important;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
                // font-family: "Noto Kufi Arabic" !important;
            // }
        }

        button {
            padding: 1rem 0 !important;
            // margin-bottom: 16px;
            border: none !important;
            background-color: $aeblack-50 !important;
            // font-family: "Roboto" !important;
            font-weight: 700;

            // &:dir(rtl) {
                // font-family: "Noto Kufi Arabic" !important;
            // }
        }
    }

    .sm\:hidden {
        display: inline-block;

        @media (min-width: 768px) {
            display: none;
        }
    }

    .footer-siteInfo-DT {
        display: none;
        color: $aeblack-300;
        // font-family: "Roboto" !important;
        font-weight: 700;

        // &:dir(rtl) {
            // font-family: "Noto Kufi Arabic" !important;
        // }

        @media (min-width: 768px) {
            display: inline;
        }
    }

    .flex {
        display: flex
    }

    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0
    }

    .lg\:w-7\/12 {
        @media (min-width: 1024px) {
            width: 58.333333%;
        }
    }

    .lg\:justify-end {
        @media (min-width: 1024px) {
            justify-content: flex-end;
        }
    }

    .lg\:w-5\/12 {
        @media (min-width: 1024px) {
            width: 41.666667%;
        }
    }

    .aegov-accordion .accordion-item {
        border-bottom-width: 1px;
        border-color: $aeblack-100;
        border-radius: 0;
        background-color: $aeblack-50 !important;
    }

    .gap-y-6 {
        row-gap: 1.5rem;
    }

    .aegov-accordion .accordion-title button {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        font-size: 1rem;
        font-weight: 600;
        color: $aeblack-800;
    }

    .justify-center {
        justify-content: center
    }

    .mobile-only {
        display: block;

        @media (min-width: 768px) {
            display: none;
        }
    }

    .desktop-only {
        display: none;

        @media (min-width: 768px) {
            display: block;
        }
    }

    .accordion-content-body {
        @media (max-width: 1024px) {
            margin: 0 !important;
        }
    }

    .accordion-body {
        @media (max-width: 1024px) {
            padding: 1rem 0 !important;
        }
    }

    .accordion-header,
    .accordion-title {
        @media (max-width:1024px) {
            margin: 0 !important;
        }
    }

    .accordion-item {
        @media (max-width:1024px) {
            padding: 0 !important;
        }
    }

    .toll-free {
        // font-family: "Roboto" !important;
        direction: ltr !important;
    }

    .last-updates {
        // font-family: "Roboto" !important;
        direction: ltr !important;
        font-weight: 500;
    }
}