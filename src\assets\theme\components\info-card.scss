
.info-card{
  padding: 16px;
  background-color: $mocdyellow;
  border-radius: 16px;
  @media only screen and (min-width: 1024px) {
    padding: 24px;
  }
  .info-card__title{
    // font-family: Inter;
    font-size: 20px !important;
    font-weight: 700 !important;
    line-height: 26px !important; 
    margin-top: 0px !important;
    margin-bottom: 8px !important;
    color: $aeblack-800;
    // [ng-reflect-dir=rtl] & , [dir=rtl] & {
    //   font-family: 'Alexandria' !important;
    // }
    @media only screen and (min-width: 1024px) {
      font-size: 26px !important;
      line-height: normal !important;
    }
  }
  .info-card__desc{
    font-size: 16px !important;
    font-weight: 400 !important;
    line-height: 22px !important; 
    margin-bottom: 0px !important;
    @media only screen and (min-width: 1024px) {
      font-size: 18px !important;
      line-height: 28px !important;
    }
  }
}
.info-card__wrapper{
  display: grid;
  gap: 16px;
  @media only screen and (min-width: 1024px) {
    gap: 24px;
    grid-template-columns: 50% 50%;
  }
}