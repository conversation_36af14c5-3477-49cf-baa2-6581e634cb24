// app-header{
//     position: fixed;
//     top: 0;
//     width: 100%;
//     background-color: $white;
//     z-index: 10000;
// }
// #content-wrapper{
//     padding-top: 68px;
//     @media (min-width: 992px){
//         padding-top: 195px;
//     }
// }
#header {

    &[ng-reflect-dir=rtl],
    &[dir=rtl] {

        // .control-prefix{
        //     margin-left: 0 !important;
        //     margin-right: 16px;
        // }
        .stars-logo-size {
            margin-left: 0;
            margin-right: 16px;
        }

        .nav-link svg {
            margin-left: 8px;
            margin-right: 0;
        }

        .header-search-input {
            direction: rtl !important;
            // font-family: 'Noto Kufi Arabic' !important;
        }
    }

    .lang-primary {
        font-size: 1.875rem;
        font-weight: 700;

        a {
            color: #000;

            &:hover {
                color: #9c7f5f !important;
            }
        }

        .selected-lang {
            color: #000 !important;
            pointer-events: none !important;
        }

        @media (max-width:1024px) {
            font-size: 1.5rem;
        }
    }

    .lang-primary-divider {
        height: 1.5rem;
        display: inline-block;
        width: 1px;
        --tw-bg-opacity: 1;
        background-color: rgb(158 162 169 / var(--tw-bg-opacity));

        @media (max-width:1024px) {
            height: 1rem;
        }
    }

    // .font-notokufi {
    //     font-family: 'Noto Kufi Arabic' !important;
    // }

    .lang-header {
        margin-bottom: 3rem;
        gap: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;

        @media (max-width:1024px) {
            margin-bottom: 2rem;
            gap: 2rem;
        }
    }

    .other-lang {
        padding: 0 1.25rem;
        text-align: center;

        &-title {
            margin-bottom: 0 !important;
            border-bottom-width: 1px !important;
            --border-opacity: 1 !important;
            border-color: rgb(225 227 229 / var(--border-opacity)) !important;
            padding-bottom: 1.5rem !important;
            font-size: .875rem !important;
            font-weight: 400 !important;
            --text-opacity: 1 !important;
            color: rgb(158 162 169 / var(--text-opacity)) !important;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        &-content {
            li {
                a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 1rem;
                    font-size: 1rem;
                    font-weight: 400;
                    --text-opacity: 1;
                    color: rgb(62 64 70 / var(--text-opacity));
                    text-decoration-line: none;
                }
            }
        }
    }



    // &:before {
    //     position: absolute;
    //     top: 0;
    //     height: .5rem;
    //     width: 100%;
    //     content: "";
    //     background-image: linear-gradient(to bottom, $aegold-400, $aegold-600);
    // }
    // #mobileMenue:before {
    //     position: absolute;
    //     top: 0;
    //     height: .5rem;
    //     width: 100%;
    //     content: "";
    //     background-image: linear-gradient(to bottom, $aegold-400, $aegold-600);
    //     z-index: 100;
    // }
    .form-control-input {
        position: relative;
        display: flex;
        border-radius: .375rem;
        background-color: $white;
        border: 2px solid $aegold-400;
        border-radius: 4px;
        width: 420px;
        max-width: 100%;

        // &:has(:focus-visible){
        //     border: 2px solid $aegold-600;
        // }
        .control-prefix {
            margin: 0 16px;
            display: flex;
            user-select: none;
            align-items: center;
            color: $aeblack-300;

            svg {
                height: 1.5rem;
                width: 1.5rem;
                fill: $aegold-600;
                display: block;
            }
        }

        input {
            display: block;
            width: 100%;
            flex: 1 1 0%;
            border-width: 0px;
            background-color: transparent;
            padding: .75rem 1rem;
            font-size: 1rem;
            line-height: 1.5rem;
            color: $aegold-900;

            &:focus-visible {
                outline: none;
                box-shadow: none;
            }
        }
    }

    .desktop-header {
        display: none !important;

        @media (min-width: 1024px) {
            display: block !important;
        }
    }

    .mobile-header {
        display: block !important;

        @media (min-width: 1024px) {
            display: none !important;
        }
    }

    button {
        padding: 12px 16px !important;
    }

    #navbarNav {
        .nav-link {
            border-bottom: 3px solid $white;

            &:hover {
                border-bottom: 3px solid $aegold-500;
            }
        }
    }

    .nav-item {
        &:hover {
            background-color: $white ;
        }

        .nav-link {
            font-size: 14px;
            font-weight: 700;
            line-height: 24px;
            color: $aeblack-900;
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: 0.3rem;

            @media (min-width: 1280px) {
                font-size: 16px;
            }

            @media (min-width: 1536px) {
                font-size: 18px;
            }

            &:hover {
                color: $aegold-500;
            }

            &.active {
                background-color: $white ;
                border-radius: 0;
                // color: $aegold-800;
                color: $aegold-500;
                border-bottom: 3px solid $aegold-500 !important;
                // border-bottom: 3px solid $aegold-800;
            }

            // svg{
            //     margin-right: 8px;
            // }
        }

        .dropdown-menu .nav-link {
            &:hover {
                color: #1B1D21; // Prevent hover color change
            }
        }
    }

    .mobile-header {
        .header-top {
            // padding: 20px 0 12px 0;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        .logo-item {
            img {
                width: 165.767px;
                // height: 36px;
                height: 44px;
            }
        }

        #mobileSearch {
            .modal-header {
                padding: 0;
            }
        }

        #mobileMenue,
        #mobileSearch {
            height: 100% !important;
            width: 100% !important;

            .modal-dialog {
                margin: 0 !important;
                padding: 0 !important;

                .modal-content {
                    border-radius: 0 !important;
                    border: none !important;
                    width: 100vw;

                    .modal-header {
                        border: none !important;
                        padding-top: 20px;
                    }
                }
            }

            ul {
                list-style: none;
                padding: 0;

                li {
                    padding: 0 12px;
                }

                :first-child {
                    padding-left: 0;
                }
            }

            .navbar-nav {
                float: none !important;

                li {
                    margin: 0;
                    padding: 17px 0;
                    border-bottom: 1px solid $aeblack-200;
                }
            }

            .btn-close {
                margin: 0 !important;
                padding: 0 !important;
                color: $aeblack-900 !important;
            }
        }

        #mobileLanguageModal {
            height: 100% !important;
            width: 100% !important;

            .modal-dialog {
                .modal-content {
                    border: none !important;
                    width: 100%;
                }
            }

            ul {
                list-style: none;
                padding: 0;

                li {
                    padding: 0 12px;
                }
            }

            // .btn-close {
            //     margin: 0 !important;
            //     padding: 0 !important;
            //     color: $aeblack-900 !important;
            // }
        }
    }

    #modal-gold-star {
        .modal-dialog {
            max-width: 384px;
            width: 100%;
        }

        .modal-content {
            border: none;
            padding: 4px;
        }

        .modal-header {
            border-bottom: none;
        }

        .modal-body {
            img {
                display: inline-block;
                margin-bottom: 16px;
                width: 112px;

                @media (min-width: 768px) {
                    width: 128px;
                }

                @media (min-width: 1024px) {
                    width: 144px;
                    margin-bottom: 24px;
                }

                @media (min-width: 1280px) {
                    width: 160px;
                }

                @media (min-width: 1536px) {
                    width: 176px;
                }
            }

            p {
                margin-bottom: 16px;
                color: $aeblack-800;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;

                a {
                    text-decoration: none;
                    color: $aegold-600;
                    font-weight: 700;
                }

                @media (min-width: 1024px) {
                    margin-bottom: 24px;
                }

            }

            .link {
                margin-top: 16px;
                font-weight: 300;
                font-size: 12px;
                line-height: 16px;
                color: $aegold-500;

                @media (min-width: 1024px) {
                    margin-top: 24px;
                }

                a {
                    text-decoration: none;
                }
            }
        }
    }
}

// new dropdown in header
.nav-item.dropdown>.nav-link::after {
    display: inline-block;
    margin-left: 8px;
    transition: transform 0.2s ease;
}

.nav-item.dropdown:hover>.nav-link::after {
    transform: rotate(180deg); // Rotates the arrow upward
}

.figma-dropdown-menu {
    position: absolute;
    // width: 362px;
    width: fit-content !important;
    height: auto; // Adjusts for content
    top: 100%; // Align dropdown below the toggle
    left: 0;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 4px 6px -4px rgba(27, 29, 33, 0.1), 0px 10px 15px -3px rgba(27, 29, 33, 0.1);
    border-radius: 0 0 16px 16px !important;
    opacity: 0;
    padding: 24px !important;
    visibility: hidden;
    transform: translateY(-10px); // Add a subtle animation effect
    transition: opacity 0.2s ease, transform 0.2s ease;
    z-index: 1000; // Ensure it appears above other content
}

.nav-item.dropdown {
    position: relative; // Ensure dropdown aligns with its parent
}

.nav-item.dropdown:hover .figma-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.figma-dropdown-menu li {
    // width: 314px;
    height: 54px;
    // background: rgba(255, 255, 255, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    color: rgba(27, 29, 33, 1);
    transition: all 0.2s ease;
}

.figma-dropdown-menu li:hover {
    background: rgba(255, 255, 255, 1);
    color: rgba(95, 100, 109, 1);
}

.dropdown-item {
    color: #000;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.dropdown-item:hover {
    color: #007bff; // Add hover color effect for links
}