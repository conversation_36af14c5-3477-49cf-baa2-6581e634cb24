import { Injectable } from '@angular/core';

import { environment } from "../../../environments/environment";
import { LanguageService } from './language.service';
import { NotifyService } from './notify.service';
import { DataService } from './data.service';

declare global {
    interface Window {
        CustomerPulse: any;
    }
}
@Injectable({
    providedIn: 'root'
})

export class CustomerpulseService {

    constructor(
        private notify: NotifyService,
        private lang: LanguageService,
        private dataService: DataService
    ) {
    }

    postCustomerPulse(requestId: string, serviceId: string, language: string) {

        this.dataService.get(`Global/CustomerPulse?serviceId=${serviceId}&requestId=${requestId}`).subscribe(
            (res) => {

                if (res.Status == "2000") {
                    this.renderSurveyModal(res.data, language);

                }
            },
            (error) => {
                console.error(error);
                // this.notify.showError(
                //     'notify.error',
                //     'notify.error'
                // );
            }
        )
    }

    renderSurveyModal(surveyLink: string, language: string): void {
        window.CustomerPulse.render(
            document.body,
            {
                modal: true,
                token: surveyLink,// Replace with your link or token,
                allow_close: false,
                lang: this.lang.IsArabic ? 'ar' : 'en'
            }
        );
        this.openModal();

    }
    openModal(): void {
        window.CustomerPulse.openModal();
    }

    renderWebisteSurveyModal(language: string): void {

        window.CustomerPulse.render(
            document.body,
            {
                modal: true,
                link: environment.customerPulseWebsite,// Replace with your link or token,
                allow_close: true,
                lang: language
            }
        );
        this.openModal();

    }
}
