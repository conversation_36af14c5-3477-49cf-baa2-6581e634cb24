import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, ViewEncapsulation } from '@angular/core';
import { MatStepper } from '@angular/material/stepper';
import { SummaryStepsFullJourny } from '../../../models/summry-steps-full-journey';
import { LanguageService } from '../../../services/language.service';

@Component({
  selector: 'app-shared-stepper-request-timeline',
  templateUrl: './shared-stepper-request-timeline.component.html',
  styleUrl: './shared-stepper-request-timeline.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class SharedStepperRequestTimelineComponent implements OnInit {
  @Input() steps: any[];

  constructor(protected lang: LanguageService) {}

  ngOnInit() {
    console.log('called')
  }
}
