import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

function validateChecksum(value: string): boolean {
  let sum = 0;
  let shouldDouble = false;
  for (let i = value.length - 1; i >= 0; i--) {
    let digit = parseInt(value.charAt(i), 10);
    if (shouldDouble) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    sum += digit;
    shouldDouble = !shouldDouble;
  }
  return (sum % 10) === 0;
}

export function uaeEmiratesIdValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const emiratesIdPattern = /^\d{15}$/;
    const value = control.value;
    if (!emiratesIdPattern.test(value)) {
      return { invalidEmiratesId: true };
    }
    const numericId = value.replace(/-/g, '');
    var isValidChecksum = validateChecksum(numericId);
    if(isValidChecksum && numericId === '000000000000000')
      isValidChecksum = false;

    return isValidChecksum ? null : { invalidEmiratesId: true };
  };
}
