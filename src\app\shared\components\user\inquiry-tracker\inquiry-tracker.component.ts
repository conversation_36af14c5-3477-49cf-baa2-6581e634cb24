import { Component, Input, QueryList, ViewChildren } from '@angular/core';
import { NgbdSortableHeader, SortEvent } from '../../../directives/sortable.diractive';
import { Form, FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { DataService } from '../../../services/data.service';
import { AlertService } from '../../../services/alert.service';
import { Router } from '@angular/router';
import { LanguageService } from '../../../services/language.service';
import { AuthService } from '../../../services/auth.service';
import { getValueFromName } from '../../../enums/application-status.enum';
import { HttpClient } from '@angular/common/http';
import { FormService } from '../../../services/form.service';
import { NotifyService } from '../../../services/notify.service';

@Component({
  selector: 'app-inquiry-tracker',
  templateUrl: './inquiry-tracker.component.html',
  styleUrl: './inquiry-tracker.component.scss'
})
export class InquiryTrackerComponent {
  @Input() status: string = '-1';
  @Input() disabled: boolean = true;


  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;

  submitForm: FormGroup;
  modalRequest: FormGroup;
  apps: any;
  userInfo: any;
  statusId = "-1";
  page = 1;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 100];
  filteredData: any = [];
  filteredCount: number;
  sortBy = 'TicketNumber';
  sortOrder = 'desc';
  searchTerm = '';
  sortOption = 'relevant';

  constructor(
    private dataService: DataService,
    public fb: FormBuilder,
    private alert: AlertService,
    private router: Router,
    protected lang: LanguageService,
    protected auth: AuthService,
    private http: HttpClient,
    private formService: FormService,
    private notify: NotifyService,
  ) {

    this.modalRequest = this.fb.group({
      Id: new FormControl('', Validators.required),
      Reason: new FormControl('', [Validators.required, Validators.maxLength(200)]),
    });

  }

  ngOnInit(): void {
    this.statusId = getValueFromName(this.status)?.toString() ?? '-1';
    this.userInfo = this.auth.getUserInfo();

    if (this.sortBy === 'CreatedOn') {
      this.sortOption = this.sortOrder === 'asc' ? 'oldest' : 'newest';
    } else {
      this.sortOption = 'relevant';
    }

    this.getDataCases();
  }


  onSort({ column, direction }: SortEvent) {
    this.sortBy = column;
    this.sortOrder = direction;

    if (column === 'CreatedOn') {
      this.sortOption = direction === 'asc' ? 'oldest' : 'newest';
    } else if (column === 'TicketNumber' && direction === 'desc') {
      this.sortOption = 'relevant';
    } else {
      this.sortOption = 'relevant';
    }

    this.applyFiltersCases();
  }

  searchCases() {
    this.applyFiltersCases();
  }

  applyFiltersCases() {
    this.getDataCases();
  }

  getDataCases() {
    var query = `id=${this.userInfo?.userInfo?.idn}`;
    query += `&statusId=${this.statusId}`;
    query += `&page=${this.page}`;
    query += `&pageSize=${this.pageSize}`;
    query += `&sortColumn=${this.sortBy}`;
    query += `&sortDirection=${this.sortOrder}`;
    query += `&searchTerm=${this.searchTerm}`;
 
    this.dataService
      .get(`swpproxy/Complaint/GetBeneficiaryComplaints?${query}`)
      .subscribe((res: any) => {
        this.filteredData = res?.Data;
        this.filteredCount = res?.data?.dataCount;
      });
  }

  async deleteApplication(id: string, entityName: string) {
    const confirmed = await this.alert.confirmSubmit('');
    if (confirmed) {
      this.dataService
        .delete(`Global/DeleteDraftRequest?requestId=${id}&entityName=${entityName}`)
        .subscribe((res) => {
          this.applyFiltersCases();
        });
    }
  }


  editMember($event: any, arg1: any) {
    this.dataService.post(`swpproxy/Complaint/Appeal?ComplaintId=${$event.Id
      }&reOpenReason=${$event.Reason}`, {}).subscribe((res) => {
        if (res) {
          this.getDataCases();
          this.notify.showSuccess('notify.success', res.Message);
        }
      })
  }
  handleMemberEdit(arg0: any) {
    this.formService.enableFields(this.modalRequest, ['Id', 'Reason'])
    this.modalRequest.patchValue({
      Id: arg0?.Id,
      Reason: arg0?.Reason
    });

  }

  getStatusLabel(status: number): string {
    switch (status) {
      case 5: return 'problemSolved';
      case 1: return 'inProgress';
      case 662410002: return 'Reopened';
      case 1000: return 'InformationProvided';
      case 662410004: return 'Closed';
      default: return 'inProgress';
    }
  }

  getStatusStyles(status: number): { [key: string]: string } {
    if (status === 5) {
      return { 'background-color': '#F7FFFB', 'color': '#1A804C' };
    } else {
      return { 'background-color': '#FFF9F0', 'color': '#996516' };
    }
  }


  daysPassed(modifiedOn: string): boolean {
    const modifiedDate = new Date(modifiedOn);
    const currentDate = new Date();
    const timeDifference = currentDate.getTime() - modifiedDate.getTime();
    const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
    return daysDifference > 3;
  }

  reopenComplaint(complaintId: string) {
    this.alert.confirmSubmit('Are you sure you want to reopen this complaint?').then((confirmed) => {
      if (confirmed) {
        this.dataService.post(`Complaints/Reopen`, { complaintId }).subscribe((res) => {
          this.applyFiltersCases();
        });
      }
    });
  }

  onSortChangeCases(sortBy: string) {
    if (sortBy === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'asc';
    }
    this.applyFiltersCases();
  }

  onSortOptionChangeCases() {
    switch (this.sortOption) {
      case 'relevant':
        this.sortBy = 'TicketNumber';
        this.sortOrder = 'desc';
        break;
      case 'oldest':
        this.sortBy = 'CreatedOn';
        this.sortOrder = 'asc';
        break;
      case 'newest':
        this.sortBy = 'CreatedOn';
        this.sortOrder = 'desc';
        break;
    }
    this.applyFiltersCases();
  }
  onFilterChange() {
    this.applyFiltersCases();
  }
  onPageChange() {
    this.applyFiltersCases();
  }
  onSelectChange() {
    this.applyFiltersCases();
  }
  onStatusChangeCases() {
    this.applyFiltersCases();
  }

  get d(): any {
    return this.modalRequest.controls;
  }
  getPageSymbol(current: number) {
    return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'][current - 1];
  }
}
