import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function customInternationalIbanValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    let iban = control.value;
    if (!iban) return null;

    iban = iban.replace(/\s+/g, '').toUpperCase(); // normalize

    const internationalIbanPattern = /^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/;

    const isCorrectFormat = internationalIbanPattern.test(iban);
    const isCorrectLength = iban.length >= 15 && iban.length <= 34;

    const isValid = isCorrectFormat && isCorrectLength;

    return isValid ? null : { invalidIban: true };
  };
}
