import { Component, EventEmitter, Injector, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup, FormArray, Validators, FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { filter, tap } from 'rxjs';
import { ActionFormType } from '../../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { GridActionTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/grid-action-types-enum';
import { NationalTypesEnum } from '../../../../../../e-services/npo-license-declaration/enums/NationalTypesEnum';
import { Feedback } from '../../../../../../e-services/npo-license-declaration/models/feedback';
import { FREQUENCY_OF_MEETING, FREQUENCY_OF_MEETING_APPOINTMENT } from '../../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { SubmitType } from '../../../../../../e-services/npo-license-declaration/models/submit-type';
import { Lookup } from '../../../../../../shared/models/lookup.model';
import { uaeEmiratesIdValidator } from '../../../../../../shared/validators/uae.emiratesId.validator';
import { MyEstablishmentComponentBase } from '../../../models/base/my-establishment-component-base';
import { letterPatternValidatorGeneral } from '../../../../../../shared/validators/letter-pattern-validator-accept-general';

@Component({
  selector: 'app-establishment-board-of-trustees',
  templateUrl: './establishment-board-of-trustees.component.html',
  styleUrls: ['./establishment-board-of-trustees.component.scss']
})
export class EstablishmentBoardOfTrusteesComponent extends MyEstablishmentComponentBase
  implements OnInit, OnChanges {
  membersOptions: Lookup[] = [];
  memberPosition: Lookup[] = [];
  frequencyOfMeetings = FREQUENCY_OF_MEETING;
  frequencyOfAppointment = FREQUENCY_OF_MEETING_APPOINTMENT;

  positions: any[];
  fixedPositions: Lookup[];
  conditions: any[];

  conditionFormEditIndex: number;
  memberPositionsEditIndex: number;
  includeConditionIds: boolean = false;
  includePositionIds: boolean = false;
  maxDate: Date;
  nationalityTypes: Lookup[];
  NationalTypesEnum = NationalTypesEnum;
  personalInfo: any;
  memberEditIndex: number;

  get fbControls(): any {
    return this.form.controls;
  }
  boardConditionForm: FormGroup;
  get bcControls(): any {
    return this.boardConditionForm.controls;
  }
  get boardConditions(): FormArray<any> {
    return this.form.get("Conditions") as FormArray;
  }

  boardPositionsForm: FormGroup;
  get bpControls(): any {
    return this.boardPositionsForm.controls;
  }
  get boardPositions(): FormArray {
    return this.form.get("Positions") as FormArray;
  }

  membersEditIndex: number;
  membersForm: FormGroup;
  get members(): FormArray {
    return this.form.get("members") as FormArray;
  }
  get mControls(): any {
    return this.membersForm.controls;
  }

  @Input() form: FormGroup;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.nationalityTypes = [
      new Lookup('1', 'UAE Local', 'مواطن إماراتي'),
      new Lookup('2', 'Resident', 'مقيم'),
    ];
    this.messageTranslationPrefix = this.messageTranslationPrefix + "forms.boardOfTrustees.";
    this.intiBoardConditionForm();
    this.intiBoardPositionsForm();
    this.intiBoardMembersForm();



    this.StepperService.formData$
      .subscribe((data: any) => {
        this.membersOptions = [];
        data?.FoundingMembersForm?.members?.forEach(member => {
          if (!this.checkMemberOptions(member?.emiratesId)) {
            this.membersOptions.push(new Lookup(member?.emiratesId, member?.emiratesId, member?.emiratesId, member?.dateOfBirth));
          }
        });

        // if (data?.FoundingMembersForm?.members?.length > 0)
        //   this.removeDeletedMemberFromFormData(data?.FoundingMembersForm?.members);
      });



    const today = new Date();
    today.setHours(23, 59, 59, 999);
    this.maxDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true) {
      this.checkEditableFildes();
    }
  }


  ngOnInit() {
    this.MyEstablishmentService.lookupData$
      .pipe(
        filter(data => !!data),
        tap(data => {
          this.positions = data.BoardtrusteesPosition.filter(_ => _.LegalForm === 'National Society') ?? [];
          this.positions = this.positions.sort((a, b) => a.Priority - b.Priority);

          this.conditions = data.GeneralConditionBoardoftrustees.filter(_ => _.LegalForm === 'National Society') ?? [];
          this.conditions = this.conditions.sort((a, b) => a.Priority - b.Priority);

          this.memberPosition = data.InteriimCommiteePosition ?? [];
          this.fixedPositions = data.InteriimCommiteePosition ?? [];

          if (this.MyEstablishmentService.FORM_MODE == ActionFormType.CREATE) {
            this.fillPredefinePositions();
            this.fillPredefineConditions();
          }
          this.StepperService.requestData$.subscribe(_ => {
            if (_ && (_.isFullDetails == true)) {
              this.mapData(_.BoardOfTrusteesForm);
              this.updateMembers(_.FoundingMembersForm);
              // Object.keys(this.form.controls).forEach((control) => {
              //   if (
              //     (this.form.get(control)?.value === null ||
              //       this.form.get(control)?.value === '' ||
              //       !this.form.get(control)?.value) &&
              //     this.isNotAllowedToEdit === false
              //   ) {
              //     this.form.get(control)?.enable();
              //   } else {
              //     this.form.get(control)?.disable();
              //   }
              // });
            }
            else if (_ && (_.isFullDetails == false) && _.BoardOfTrusteesForm) {
              this.boardPositions.clear();
              this.boardConditions.clear();
              this.members.clear();
              this.mapData(_?.BoardOfTrusteesForm);
            }
            if (_.FoundingMembersForm && _.FoundingMembersForm?.FounderMember?.length > 0) {
              this.updateMembers(_.FoundingMembersForm);
            }
          });
        })
      )
      .subscribe();

    this.resetFromValidation(this.form);

    this.positionPagination();
    this.conditionPagination();
    this.memberPaginationMember();
    this.boardPositions.valueChanges.subscribe(_ => this.positionPagination());
    this.boardConditions.valueChanges.subscribe(_ => this.conditionPagination());
    this.members.valueChanges.subscribe(_ => this.memberPaginationMember());
  }

  get countOfPredefindConditions(): number {
    return this.conditions?.length ?? -1;
  }


  private fillPredefineConditions(): void {
    this.conditions.forEach(_ => {
      if (!this.checkConditionsExist(_?.ID)) {
        this.boardConditions.push(this.FormBuilder.group({
          id: [_.Id],
          nominationEn: [_.Name],
          nominationAr: [_.NameAr],
          canEdit: [false],
          SatusReason: _?.SatusReason?.trim() || 'draft'
        }));
      }
    });
  }

  private fillPredefinePositions(): void {
    if (this.positions.length > 0) {
      this.positions.forEach((item) => {
        if (!this.checkPositionsExist(item?.ID)) {
          this.boardPositions.push(this.FormBuilder.group({
            id: [item?.ID],
            positionEn: [item?.NameEnglish],
            positionAr: [item?.NameArabic],
            canEdit: [false],
            SatusReason: [item?.SatusReason?.trim() || 'draft']
          }));
        }
      });
    }
  }


  intiBoardConditionForm = (): void => {
    this.boardConditionForm = this.FormBuilder.group({
      id: [''],
      nominationEn: new FormControl("", [Validators.required, letterPatternValidatorGeneral('en', true)]),
      nominationAr: new FormControl("", [Validators.required, letterPatternValidatorGeneral('ar', true)]),
      canEdit: new FormControl(''),
      SatusReason: ['']
    });
  }

  intiBoardPositionsForm = (): void => {
    this.boardPositionsForm = this.FormBuilder.group({
      id: [''],
      positionEn: new FormControl("", [Validators.required, letterPatternValidatorGeneral('en', true)]),
      positionAr: new FormControl("", [Validators.required, letterPatternValidatorGeneral('ar', true)]),
      canEdit: new FormControl(''),
      SatusReason: ['']
    });
  }

  intiBoardMembersForm = (): void => {

    this.membersForm = this.FormBuilder.group({
      id: [''],
      nationalityType: new FormControl(''),
      foundingMemberAgeIsLessThan21YearsOldLocal: new FormControl(''),
      foundingMemberAgeIsLessThan21YearsOldNonLocal: new FormControl(''),
      foundingMemberHasDiplomaticStatus: new FormControl(''),
      foundingMemberResidencyIsLessThan3Years: new FormControl(''),

      exceptionReasonEnForAgeIsLessThan21Local: new FormControl(""),
      exceptionReasonArForAgeIsLessThan21Local: new FormControl(""),

      exceptionReasonEnForAgeIsLessThan21NonLocal: new FormControl(""),
      exceptionReasonArForAgeIsLessThan21NonLocal: new FormControl(""),

      exceptionReasonEnForHasDiplomaticStatus: new FormControl(""),
      exceptionReasonArForHasDiplomaticStatus: new FormControl(""),

      exceptionReasonEnForResidencyIsLessThan3Years: new FormControl(""),
      exceptionReasonArForResidencyIsLessThan3Years: new FormControl(""),

      emiratesId: new FormControl('', []),
      dateOfBirth: new FormControl(''),
      contactId: new FormControl(''),

      NameEn: new FormControl(''),
      NameAr: new FormControl(''),
      NationalityEn: new FormControl(''),
      NationalityAr: new FormControl(''),
      ResponseDate: new FormControl(''),
      SatusReason: [''],
      memberPosition: new FormControl("", []),
    });
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(controlName => {
      const control = this.form.get(controlName);
      return control?.valid;
    });

    return result;
    // return result && this.checkEqualPositionMember();
    // return true;
  }


  checkEqualPositionMember = (): boolean => {
    const tablePositions = this.members.controls.map(_ => _.get("memberPosition")?.value);
    if (!tablePositions || tablePositions.length === 0) {
      return false;
    }
    return this.boardPositions.controls.every((control) => {
      const nameEnglish = control.get('positionEn')?.value?.toLocaleLowerCase().trim();
      return tablePositions.some(position => position?.NameEnglish?.toLocaleLowerCase().trim() === nameEnglish);
    });
  };

  isItemDuplicated = (item: any): boolean => {
    if (!this.boardConditions?.controls) return false;

    return this.boardConditions.controls.some(control => {
      const { conditionEn, conditionAr, id } = {
        conditionEn: control.get('nominationEn')?.value,
        conditionAr: control.get('nominationAr')?.value,
        id: control.get('id')?.value
      };

      const isSameContent = conditionEn === item.nominationEn || conditionAr === item.nominationAr;
      const isDifferentOrEmptyId = id !== item.id;

      return isSameContent && isDifferentOrEmptyId;
    });
  };

  manageConditionForm(item: any, type: GridActionTypesEnum) {
    if (this.isItemDuplicated(item)) {
      this.NotifyService.showError('notify.error', 'notify.conditionExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.boardConditions.push(this.FormBuilder.group({
        id: [this.generateDistinctId()],
        nominationEn: [item.nominationEn],
        nominationAr: [item.nominationAr],
        SatusReason: [item?.SatusReason?.trim() || 'draft'],
        canEdit: [true],
      }));
    else
      this.boardConditions
        .at(this.conditionFormEditIndex)
        .patchValue({
          id: item.id,
          nominationEn: item.nominationEn,
          nominationAr: item.nominationAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
          canEdit: true,
        });

    this.boardConditionForm.reset();
    this.modalService.dismissAll();
  }

  handleEditConditions = (data: any, idx: number): void => {
    this.conditionFormEditIndex = idx;
    this.boardConditionForm.patchValue({
      id: data?.value?.id,
      nominationEn: data?.value?.nominationEn,
      nominationAr: data?.value?.nominationAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.boardConditionForm, ['nominationEn', 'nominationAr']);
  }

  handleCloneConditions = (data: any): void => {
    this.boardConditionForm.patchValue({
      nominationEn: data?.value?.nominationEn,
      nominationAr: data?.value?.nominationAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.boardConditionForm, ['nominationEn', 'nominationAr'])
  }
  removeConditions = async (data: any, idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let condition = this.boardConditions?.value[idx];
      if (condition)
        this.removeFromCRM(condition?.id, this.MEMBERSHIP_CONDITION_GRID_IN_CRM);

      this.boardConditions.removeAt(idx);
    }
  }

  managePositions = (item: any, type: GridActionTypesEnum): void => {
    if (this.boardPositions.value.some((_: any) => (_.positionEn === item.positionEn || _.positionAr === item.positionAr) && _.id !== item.id)) {
      this.NotifyService.showError('notify.error', 'notify.positionExistsError');
      return;
    }

    if (type != GridActionTypesEnum.EDIT)
      this.boardPositions.push(this.FormBuilder.group({
        id: [this.generateDistinctId()],
        positionEn: [item.positionEn],
        positionAr: [item.positionAr],
        SatusReason: [item?.SatusReason?.trim() || 'draft'],
        canEdit: [true],
      }));
    else
      this.boardPositions
        .at(this.memberPositionsEditIndex)
        .patchValue({
          id: item.id,
          positionEn: item.positionEn,
          positionAr: item.positionAr,
          SatusReason: item.SatusReason?.trim() || 'draft',
          canEdit: true,
        });

    this.boardPositionsForm.reset();
    this.modalService.dismissAll();
    this.manageMemberPositionOption();
  }


  handleEditPositions = (data: any, idx: number): void => {
    this.memberPositionsEditIndex = idx;
    this.boardPositionsForm.patchValue({
      id: data?.value?.id,
      positionEn: data?.value?.positionEn,
      positionAr: data?.value?.positionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.boardPositionsForm, ['positionEn', 'positionAr'])
  }

  handleClonePositions = (data: any): void => {
    this.boardPositionsForm.patchValue({
      positionEn: data?.value?.positionEn,
      positionAr: data?.value?.positionAr,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      canEdit: true,
    });
    this.FormService.enableFields(this.boardPositionsForm, ['positionEn', 'positionAr'])
  }
  removePositions = async (data: any, idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.boardPositions?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.POSITION_GRID_IN_CRM);

      this.boardPositions.removeAt(idx);
    }
  }

  // saveAsDraft = (): void => {
  //   const submitParams: SubmitType = this.createSubmitParams("BoardOfTrusteesForm", true);
  //   this.savingFormData(submitParams);
  // }

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams("BoardOfTrusteesForm", true);
    if (isLazy)
      this.savingLazyFormData(submitParams);
    else
      this.savingFormData(submitParams);
  }

  submit = (): void => {
    const submitParams: SubmitType = this.createSubmitParams("BoardOfTrusteesForm", false);
    this.handleSaveRequest(submitParams);
  }

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
    this.NotifyService.showError('notify.error', 'notify.invalidForm');
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    return {
      form: this.form,
      callBack: this.getMappingObject,
      next: this.next,
      key: key,
      isDraft: isDraft
    };
  }

  tempPostionIdList: any[] = [];
  getnerateTempPostionIds = (): void => {
    this.tempPostionIdList = [];
    this.boardPositions.controls.forEach(_ => {
      this.tempPostionIdList.push({
        NameArabic: _.get('positionAr')?.value,
        NameEnglish: _.get('positionEn')?.value,
        Id: this.generateDistinctId(5)
      });
    });
  }
  getTempPostionId = (nameArabic: string, nameEnglish: string): string => {
    const index$ = this.tempPostionIdList?.findIndex(_ => _.NameEnglish.toLocaleLowerCase().trim() == nameEnglish.toLocaleLowerCase().trim() && _.NameArabic.toLocaleLowerCase().trim() == nameArabic.toLocaleLowerCase().trim());
    if (index$ > -1) {
      return this.tempPostionIdList[index$].Id;
    }
    return '';
  }


  getMappingObject = (): any => {
    this.getnerateTempPostionIds();
    return {
      frequencyOfMeetings: this.fbControls?.frequencyOfMeetings?.value?.ID ?? '',
      frequencyOfAppointments: this.fbControls?.frequencyOfAppointments?.value?.ID ?? '',

      Conditions: (this.boardConditions.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: (this.includeConditionIds && _$.id) ? _$.id : (_$.canEdit == false ? this.EMPTY_GUID : _$.id ?? this.EMPTY_GUID),
          Name: _$.nominationEn,
          NameAr: _$.nominationAr,
          Position: "string",
          PositionId: this.EMPTY_GUID,
          Priority: index + 1,
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$?.SatusReason?.trim() || 'draft',
        }
      }),
      Positions: (this.boardPositions.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: (this.includePositionIds && _$.id) ? _$.id : (_$.canEdit == false ? this.EMPTY_GUID : _$.id ?? this.EMPTY_GUID),
          Name: _$.positionEn,
          NameAr: _$.positionAr,
          GeneralPosition: "",
          GeneralPositionCode: 0,
          Priority: index + 1,
          IdExt: this.getTempPostionId(_$.positionAr, _$.positionEn) ?? '',
          IsGeneral: _$.canEdit == false ? 2 : null,
          SatusReason: _$?.SatusReason?.trim() || 'draft'
        }
      }),

      Memberlist: (this.members.controls as Array<any>).map((_, index) => {
        let _$ = _.value;
        return {
          Id: _$.id ?? this.EMPTY_GUID,
          Name: "",
          Position: {
            NpoRequestId: this.StepperService.requestId ?? this.EMPTY_GUID,
            Name: _$.memberPosition?.NameEnglish,
            NameAr: _$.memberPosition?.NameArabic,
            IdExt: this.getTempPostionId(_$.memberPosition?.NameArabic, _$.memberPosition?.NameEnglish) ?? ''
          },
          Priority: index + 1,
          Description: "",
          Email: "",
          localmember: _$?.nationalityType?.ID === NationalTypesEnum.Local ? 1 : 0,
          Contact: {
            Id: _$.contactId ?? this.EMPTY_GUID,
            EmirateId: _$.emiratesId,
            Dob: _$.dateOfBirth
          },
          SatusReason: _$?.SatusReason?.trim() || 'draft'
        }
      })

    }
  }

  getDecimal = (value: any): number => {
    if (value !== '') {
      const parsedValue = parseFloat(value);
      return isNaN(parsedValue) ? 0 : parsedValue;
    }

    return 0;
  }

  mapData = (data: any): void => {
    if (!data) return;

    this.fbControls.frequencyOfMeetings.setValue(this.frequencyOfMeetings?.find(_ => _.ID === data?.frequencyOfMeetings) ?? '');
    this.fbControls.frequencyOfAppointments.setValue(this.frequencyOfAppointment?.find(_ => _.ID === data?.frequencyOfAppointments) ?? '');

    data.Conditions = data.Conditions.sort((a, b) => a.Priority - b.Priority);
    data.Conditions?.forEach((item: any) => {
      this.boardConditions.push(this.FormBuilder.group({
        id: item.Id ?? '',
        nominationEn: item.Name ?? '',
        nominationAr: item.NameAr ?? '',
        canEdit: true,
        SatusReason: item?.SatusReason?.trim() || 'draft'
      }));
    });

    data.Positions = data.Positions.sort((a, b) => a.Priority - b.Priority);
    data.Positions?.forEach((item: any) => {
      this.boardPositions.push(this.FormBuilder.group({
        id: item.Id ?? '',
        positionEn: item.Name ?? '',
        positionAr: item.NameAr ?? '',
        canEdit: true,
        SatusReason: item?.SatusReason?.trim() || 'draft'
      }));
    });

    data.Memberlist = data.Memberlist.sort((a, b) => a.Priority - b.Priority);
    data.Memberlist.forEach((item: any) => {
      this.members.push(this.FormBuilder.group({
        id: [item.Id],
        emiratesId: item?.Contact?.EmirateId,
        dateOfBirth: item?.Contact?.Dob,
        contactId: item?.Contact?.Id,
        nationalityType: item?.localmember === 1 ? this.nationalityTypes?.at(0) : this.nationalityTypes?.at(1),
        exceptionReasonEnForAgeIsLessThan21Local: item?.Exceptionageislessthan21?.Description,
        exceptionReasonArForAgeIsLessThan21Local: item?.Exceptionageislessthan21?.DescriptionAr,
        exceptionReasonEnForHasDiplomaticStatus: item?.Exceptionshasadisplomaticstatus?.Description,
        exceptionReasonArForHasDiplomaticStatus: item?.Exceptionshasadisplomaticstatus?.DescriptionAr,
        exceptionReasonEnForResidencyIsLessThan3Years: item?.Exceptionsresidencyislessthan3years?.Description,
        exceptionReasonArForResidencyIsLessThan3Years: item?.Exceptionsresidencyislessthan3years?.DescriptionAr,
        exceptionReasonEnForAgeIsLessThan21NonLocal: item?.Exceptionageislessthan21?.Description,
        exceptionReasonArForAgeIsLessThan21NonLocal: item?.Exceptionageislessthan21?.DescriptionAr,
        foundingMemberAgeIsLessThan21YearsOldNonLocal: (item?.ageislessthan21 === 1) ? true : false,
        foundingMemberAgeIsLessThan21YearsOldLocal: (item?.ageislessthan21 === 1) ? true : false,
        foundingMemberHasDiplomaticStatus: (item?.hasadisplomaticstatus === 1) ? true : false,
        foundingMemberResidencyIsLessThan3Years: (item?.residencyislessthan3years === 1) ? true : false,

        canEdit: true,
        SatusReason: item?.SatusReason?.trim() || 'draft',
        NationalityEn: item?.Nationality ?? '',
        NameEn: (this.LanguageService.IsArabic ? (item?.Contact?.FullnameAr) : (item?.Contact?.FullName)) ?? '',
        ResponseDate: '',

        memberPosition: item?.Position ? {
          NpoRequestId: item?.Position?.NpoRequestId,
          ID: item?.Position?.NpoRequestId,
          NameEnglish: item?.Position?.Name,
          NameArabic: item?.Position?.NameAr,
        } : null,
      }));
    });

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.boardConditions?.value || this.boardConditions?.value?.length == 0)) {
      this.fillPredefineConditions();
    } else {
      this.includeConditionIds = true;
    }

    if (this.MyEstablishmentService.FORM_MODE == ActionFormType.UPDATE && (!this.boardPositions?.value || this.boardPositions?.value?.length == 0)) {
      this.fillPredefinePositions();
    } else {
      this.includePositionIds = true;
    }

    this.manageMemberPositionOption();
  };

  editRows: string[] = [];
  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(this.isReturnForUpdate ?? false, this.feedbackList, "boardoftrustees", this.fbControls);
  }
  checkIsEditId = (id: | undefined): boolean => (id && id != null && id != undefined && id != '' && id != ' ') ? this.editRows.findIndex(_ => _ == id) > -1 : false;
  checkPositionsExist = (id: string): boolean => this.boardPositions.controls.findIndex(_ => _.get('id')?.value == id) > -1;
  checkConditionsExist = (id: string): boolean => this.boardConditions.controls.findIndex(_ => _.get('id')?.value == id) > -1;
  checkMemberExist = (id: string): boolean => this.members.controls.findIndex(_ => _.get('id')?.value == id) > -1;

  conditionPage = 1;
  conditionPageSize = 10;
  conditionDataTable: FormArray = this.FormBuilder.array([]);
  get conditionReprsentedDataTable(): FormArray { return this.conditionDataTable; }
  get conditionTableIndex(): number { return (this.conditionPage - 1) * (this.conditionPageSize) }
  conditionPagination = (): void => {
    this.conditionDataTable = this.FormBuilder.array([]);
    let data$ = this.boardConditions.controls.slice((this.conditionPage - 1) * this.conditionPageSize, (this.conditionPage - 1) * this.conditionPageSize + this.conditionPageSize);
    data$.forEach(_ => this.conditionDataTable.push(_));
  }

  positionPage = 1;
  positionPageSize = 10;
  positionDataTable: FormArray = this.FormBuilder.array([]);
  get positionReprsentedDataTable(): FormArray { return this.positionDataTable; }
  get positionTableIndex(): number { return (this.positionPage - 1) * (this.positionPageSize) }
  positionPagination = (): void => {
    this.positionDataTable = this.FormBuilder.array([]);
    let data$ = this.boardPositions.controls.slice((this.positionPage - 1) * this.positionPageSize, (this.positionPage - 1) * this.positionPageSize + this.positionPageSize);
    data$.forEach(_ => this.positionDataTable.push(_));

    this.manageMemberPositionOption();
  }

  manageMemberPositionOption = (postion: any | undefined | null = undefined): void => {
    this.memberPosition = [];
    this.boardPositions.controls.forEach(_ => {
      this.memberPosition.push({
        ID: _.get('id')?.value,
        NameArabic: _.get('positionAr')?.value,
        NameEnglish: _.get('positionEn')?.value,
        ParentID: ""
      });
    });
    const tablePostions = this.members?.controls?.map(_ => _.get("memberPosition")?.value) ?? [];
    if (tablePostions && tablePostions.length >= 0) {
      tablePostions.map(_ => {
        this.memberPosition = this.memberPosition.filter(ele => ele?.NameEnglish?.toLocaleLowerCase().trim() != _?.NameEnglish?.toLocaleLowerCase().trim());
      });
    }

    const index$ = this.memberPosition?.findIndex(_ => _?.NameEnglish?.toLocaleLowerCase().trim() == "A member of the Board of Trustees".toLocaleLowerCase().trim());
    if (index$ == -1) {
      const boardMember = this.positions?.find(_ => _?.NameEnglish?.toLocaleLowerCase().trim() == "A member of the Board of Trustees".toLocaleLowerCase().trim());
      this.memberPosition.push({
        ID: boardMember?.ID,
        NameArabic: "عضو مجلس الأمناء",
        NameEnglish: "A member of the Board of Trustees",
        ParentID: ""
      });
    }

    if (postion) {
      this.memberPosition.push(postion);
    }

    this.memberPosition.sort((a, b) => {
      if (this.LanguageService.IsArabic) {
        return a.NameArabic.toLowerCase().trim().localeCompare(b.NameArabic.toLowerCase().trim());
      } else {
        return a.NameEnglish.toLowerCase().trim().localeCompare(b.NameEnglish.toLowerCase().trim());
      }
    });
  }




  addChairman = (item: any): void => {
    if (item?.value?.memberPosition?.NameEnglish === 'Committee Chairman') {
      const chairmanPosition = this.fixedPositions?.find(position => position.NameEnglish === 'Committee Chairman');
      this.memberPosition.push(new Lookup(chairmanPosition?.ID, 'Committee Chairman', 'رئيس اللجنة', ''));
    }
  }

  addEID = (data: any): void => {
    this.membersOptions.push(new Lookup(data?.value?.foundingMember?.NameEnglish, data?.value?.foundingMember?.NameEnglish, data?.value?.foundingMember?.NameEnglish, ''));
  }

  onModelOpening = (event: any): void => {
    this.removeDublication();
    this.clearValidationsAndValues();
  };
  checkMemberOptions = (id: string): boolean => this.membersOptions.findIndex(_ => _.ID == id) > -1;

  updateMembers = (data: any): void => {
    this.membersOptions = [];
    data?.FounderMember?.forEach(member => {
      const eid = member?.Contact?.EmirateId;
      if (!this.checkMemberOptions(eid)) {
        this.membersOptions.push(new Lookup(eid, eid, eid, eid));
      }
    });
  }

  removeSelectedDataFromList() {
    this.members?.value?.forEach(element => {
      if (element?.memberPosition?.NameEnglish === 'Committee Chairman')
        this.memberPosition = this.memberPosition.filter(_ => _.NameEnglish !== 'Committee Chairman');

      if (this?.membersOptions?.find(_ => _.NameEnglish === element?.foundingMember?.NameEnglish))
        this.membersOptions = this.membersOptions.filter(_ => _.NameEnglish !== element?.foundingMember?.NameEnglish);
    });
  }

  addChairmanIfNotExist = (): void => {
    const isChairmanExist = this.form?.value?.members.find(member => member?.memberPosition?.NameEnglish === 'Committee Chairman');
    const isChairmanExistInPosition = this.memberPosition.find(_ => _?.NameEnglish === 'Committee Chairman');
    const chairmanPosition = this.fixedPositions?.find(position => position.NameEnglish === 'Committee Chairman');

    if (!isChairmanExist && !isChairmanExistInPosition)
      this.memberPosition.push(new Lookup(chairmanPosition?.ID, 'Committee Chairman', 'رئيس اللجنة', ''));
  }

  removeDublication() {
    this.memberPosition = this.memberPosition.filter((value, index, self) =>
      index === self.findIndex(_ => _.NameEnglish === value.NameEnglish)
    );

    this.membersOptions = this.membersOptions.filter((value, index, self) =>
      index === self.findIndex(_ => _.NameEnglish === value.NameEnglish)
    );
  }


  pageMember = 1;
  pageMemberSize = 10;
  dataTableMember: FormArray = this.FormBuilder.array([]);
  get reprsentedeMemberDataTable(): FormArray { return this.dataTableMember; }
  get tableIndex(): number { return (this.pageMember - 1) * (this.pageMemberSize) }
  memberPaginationMember = (): void => {
    this.dataTableMember = this.FormBuilder.array([]);
    let data$ = this.members.controls.slice((this.pageMember - 1) * this.pageMemberSize, (this.pageMember - 1) * this.pageMemberSize + this.pageMemberSize);
    data$.forEach(_ => this.dataTableMember.push(_));
  }

  percentageOfLocalMembers = (): number => {
    const totalNumber = this.members.value.length;
    const localMembersCount = this.members.value.filter(member => member.nationalityType?.ID === NationalTypesEnum.Local).length;
    const localPercentage = (localMembersCount / totalNumber) * 100;
    return Math.floor(localPercentage);
  };

  clearValidationsAndValues = (): void => {
    this.FormService.clearFields(this.membersForm, [
      'exceptionReasonArForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21NonLocal',
      'exceptionReasonArForAgeIsLessThan21NonLocal',
      'exceptionReasonEnForHasDiplomaticStatus',
      'exceptionReasonArForHasDiplomaticStatus',
      'exceptionReasonEnForResidencyIsLessThan3Years',
      'exceptionReasonArForResidencyIsLessThan3Years',
      'foundingMemberAgeIsLessThan21YearsOldLocal',
      'foundingMemberAgeIsLessThan21YearsOldNonLocal',
      'foundingMemberHasDiplomaticStatus',
      'foundingMemberResidencyIsLessThan3Years'
    ], true);

    this.membersForm.updateValueAndValidity();
  };

  clearValidations = (): void => {
    this.FormService.clearValidators(this.membersForm, [
      'exceptionReasonArForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21Local',
      'exceptionReasonEnForAgeIsLessThan21NonLocal',
      'exceptionReasonArForAgeIsLessThan21NonLocal',
      'exceptionReasonEnForHasDiplomaticStatus',
      'exceptionReasonArForHasDiplomaticStatus',
      'exceptionReasonEnForResidencyIsLessThan3Years',
      'exceptionReasonArForResidencyIsLessThan3Years',
      'foundingMemberAgeIsLessThan21YearsOldLocal',
      'foundingMemberAgeIsLessThan21YearsOldNonLocal',
      'foundingMemberHasDiplomaticStatus',
      'foundingMemberResidencyIsLessThan3Years'
    ]);

    this.membersForm.updateValueAndValidity();
  };
  formatDate = (date: string | Date): string => {
    const d = new Date(date);
    return d.getFullYear() + '-' + (d.getMonth() + 1).toString().padStart(2, '0') + '-' + d.getDate().toString().padStart(2, '0');
  }
  validate = (item: any): boolean => {
    if (!this.isDataNotChanged(item)) {
      this.NotifyService.showError('notify.error', 'notify.DataNotCorrect');
      return false;
    }

    if (!this.isEidNotRepeated(item)) {
      this.NotifyService.showError('notify.error', 'notify.ThisMemberIsAlreadyAdded');
      return false;
    }

    return true;
  };
  isDataNotChanged = (item: any): boolean => {
    return (
      item?.emiratesId === this.personalInfo?.identityCardnumber &&
      this.formatDate(item?.dateOfBirth) === this.formatDate(this.personalInfo?.dateOfBirth)
    );
  };
  isEidNotRepeated = (item: any): boolean => {
    if (!this.members?.controls) return true;

    const matchingMember = this.members.controls.find(control => {
      const emiratesId = control.get('emiratesId')?.value;
      const id = control.get('id')?.value;
      return emiratesId === item.emiratesId && id !== item.id;
    });

    return !matchingMember;
  };
  manageMember = (item: any, type: GridActionTypesEnum): void => {
    const formValueWithDisabled = this.membersForm.getRawValue();
    if (this.validate(formValueWithDisabled)) {
      const memberFormGroup = this.FormBuilder.group({
        id: [formValueWithDisabled.id ?? this.generateDistinctId()],
        emiratesId: formValueWithDisabled?.emiratesId,
        dateOfBirth: formValueWithDisabled?.dateOfBirth,
        nationalityType: formValueWithDisabled?.nationalityType,
        foundingMemberAgeIsLessThan21YearsOldLocal: formValueWithDisabled?.foundingMemberAgeIsLessThan21YearsOldLocal,
        foundingMemberAgeIsLessThan21YearsOldNonLocal: formValueWithDisabled?.foundingMemberAgeIsLessThan21YearsOldNonLocal,
        foundingMemberResidencyIsLessThan3Years: formValueWithDisabled?.foundingMemberResidencyIsLessThan3Years,
        foundingMemberHasDiplomaticStatus: formValueWithDisabled?.foundingMemberHasDiplomaticStatus,
        exceptionReasonEnForAgeIsLessThan21Local: formValueWithDisabled?.exceptionReasonEnForAgeIsLessThan21Local,
        exceptionReasonArForAgeIsLessThan21Local: formValueWithDisabled?.exceptionReasonArForAgeIsLessThan21Local,
        exceptionReasonEnForResidencyIsLessThan3Years: formValueWithDisabled?.exceptionReasonEnForResidencyIsLessThan3Years,
        exceptionReasonArForResidencyIsLessThan3Years: formValueWithDisabled?.exceptionReasonArForResidencyIsLessThan3Years,
        exceptionReasonEnForHasDiplomaticStatus: formValueWithDisabled?.exceptionReasonEnForHasDiplomaticStatus,
        exceptionReasonArForHasDiplomaticStatus: formValueWithDisabled?.exceptionReasonArForHasDiplomaticStatus,
        exceptionReasonEnForAgeIsLessThan21NonLocal: formValueWithDisabled?.exceptionReasonEnForAgeIsLessThan21NonLocal,
        exceptionReasonArForAgeIsLessThan21NonLocal: formValueWithDisabled?.exceptionReasonArForAgeIsLessThan21NonLocal,
        contactId: formValueWithDisabled?.contactId,
        SatusReason: formValueWithDisabled?.SatusReason?.trim() || 'draft',
        memberPosition: formValueWithDisabled?.memberPosition,
      });

      if (type === GridActionTypesEnum.EDIT) {
        this.members.at(this.memberEditIndex)?.patchValue(memberFormGroup.value);
      } else {
        this.members.push(memberFormGroup);
      }


      this.membersForm.reset();
      this.modalService.dismissAll();
      // this.showAddFoundBtn = false;

      this.manageMemberPositionOption();
    }

  };
  handleEditMember = (data: any, idx: number): void => {
    this.memberEditIndex = idx;
    this.membersForm.patchValue({
      id: data?.value?.id,
      emiratesId: data?.value?.emiratesId,
      dateOfBirth: data?.value?.dateOfBirth,
      nationalityType: data?.value?.nationalityType,
      foundingMemberAgeIsLessThan21YearsOldLocal: data?.value?.foundingMemberAgeIsLessThan21YearsOldLocal,
      foundingMemberAgeIsLessThan21YearsOldNonLocal: data?.value?.foundingMemberAgeIsLessThan21YearsOldNonLocal,
      foundingMemberHasDiplomaticStatus: data?.value?.foundingMemberHasDiplomaticStatus,
      foundingMemberResidencyIsLessThan3Years: data?.value?.foundingMemberResidencyIsLessThan3Years,
      exceptionReasonEnForAgeIsLessThan21Local: data?.value?.exceptionReasonEnForAgeIsLessThan21Local,
      exceptionReasonArForAgeIsLessThan21Local: data?.value?.exceptionReasonArForAgeIsLessThan21Local,
      exceptionReasonEnForResidencyIsLessThan3Years: data?.value?.exceptionReasonEnForResidencyIsLessThan3Years,
      exceptionReasonArForResidencyIsLessThan3Years: data?.value?.exceptionReasonArForResidencyIsLessThan3Years,
      exceptionReasonEnForHasDiplomaticStatus: data?.value?.exceptionReasonEnForHasDiplomaticStatus,
      exceptionReasonArForHasDiplomaticStatus: data?.value?.exceptionReasonArForHasDiplomaticStatus,
      exceptionReasonEnForAgeIsLessThan21NonLocal: data?.value?.exceptionReasonEnForAgeIsLessThan21NonLocal,
      exceptionReasonArForAgeIsLessThan21NonLocal: data?.value?.exceptionReasonArForAgeIsLessThan21NonLocal,
      contactId: data?.value?.contactId,
      SatusReason: data?.value?.SatusReason?.trim() || 'draft',
      memberPosition: data?.value?.memberPosition
    });

    this.clearValidations();
    this.updateMemberValidations(data?.value?.nationalityType?.ID);

    this.personalInfo = {
      identityCardnumber: data?.value?.emiratesId,
      dateOfBirth: this.formatDate(data?.value?.dateOfBirth)
    }
    this.manageMemberPositionOption(data?.value?.memberPosition);
  }
  removeMember = async (idx: number): Promise<void> => {
    const parms = this.deleteAlertParams();
    const confirmed = await this.AlertService.showAlert(parms);
    if (confirmed) {
      let obj = this.members?.value[idx];
      if (obj)
        this.removeFromCRM(obj?.id, this.RELATIONSHIP_GRID_IN_CRM);

      this.members.removeAt(idx);
      this.manageMemberPositionOption();
    }
  }
  getInfo = (emiratesId: string, dateOfBirth: string): void => {
    const formattedDate = this.FormService.formatDate(dateOfBirth, 'yyyy-MM-dd');
    if (!formattedDate) {
      this.NotifyService.showError('notify.error', 'notify.invalidDateFormat');
      return;
    }
    this.MyEstablishmentService.VerifyFounderMember(emiratesId, formattedDate).subscribe(
      (response) => {
        if (response && response.data) {
          this.setData(response.data);
          // this.showAddFoundBtn = true;
        } else {
          this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'لم يتم العثور على البيانات.' : 'Data not found.');
        }
      },
      (error) => {
        this.NotifyService.showError('notify.error', this.LanguageService.IsArabic ? 'خطأ في جلب البيانات.' : 'Error fetching data.');
      }
    );
  };
  setData = (data: any): void => {
    const isLocal = data?.IsUAENational === 1;
    const isNonLocal = data?.IsUAENational === 0;

    if (!isLocal && !isNonLocal) {
      this.NotifyService.showError('notify.error', 'Invalid nationality status.');
    }

    const nationalType = isLocal ? NationalTypesEnum.Local : NationalTypesEnum.NonLocal;
    this.updatePersonalInfo(data, nationalType);
  };
  updatePersonalInfo = (data: any, nationalType: string): void => {
    this.setValues(nationalType, data?.dateOfBirth);
    //this.updateMemberValidations(nationalType);
    this.disableFields(nationalType);
    this.personalInfo = data;
    this.membersForm.updateValueAndValidity();
  };
  private getNationalityType = (typeId: string) => {
    return this.nationalityTypes.find((type) => type.ID === typeId) || null;
  };
  calculateAge(dateOfBirth: string): number {
    if (!dateOfBirth) return 0;

    const birthDate = new Date(dateOfBirth);
    if (isNaN(birthDate.getTime())) {
      this.NotifyService.showError('notify.error', 'Invalid date of birth.');
      return 0;
    }

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }
  setValues = (localTypeId: string, dateOfBirth: string): void => {
    const age = this.calculateAge(dateOfBirth);

    if (localTypeId === NationalTypesEnum.Local) {
      this.mControls.nationalityType.setValue(this.getNationalityType(NationalTypesEnum.Local));
      this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.setValue(age < 21);
    } else {
      this.mControls.nationalityType.setValue(this.getNationalityType(NationalTypesEnum.NonLocal));
      this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.setValue(age < 21);
    }
  }
  updateMemberValidations = (localId: string): void => {
    if (localId === NationalTypesEnum.Local)
      this.changeValidations(this.membersForm, this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.value, 'exceptionReasonArForAgeIsLessThan21Local', 'exceptionReasonEnForAgeIsLessThan21Local');
    else {
      this.changeValidations(this.membersForm, this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.value, 'exceptionReasonArForAgeIsLessThan21NonLocal', 'exceptionReasonEnForAgeIsLessThan21NonLocal');
      this.changeValidations(this.membersForm, this.mControls.foundingMemberResidencyIsLessThan3Years.value, 'exceptionReasonArForResidencyIsLessThan3Years', 'exceptionReasonEnForResidencyIsLessThan3Years');
      this.changeValidations(this.membersForm, this.mControls.foundingMemberHasDiplomaticStatus.value, 'exceptionReasonArForHasDiplomaticStatus', 'exceptionReasonEnForHasDiplomaticStatus');
    }
  }
  disableFields = (localTypeId: string): void => {
    if (localTypeId === NationalTypesEnum.Local) {
      this.mControls.foundingMemberAgeIsLessThan21YearsOldLocal.disable();
    } else {
      this.mControls.foundingMemberAgeIsLessThan21YearsOldNonLocal.disable();
      this.mControls.foundingMemberResidencyIsLessThan3Years.disable();
    }
  }
  getStatusColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
      case 'approved':
        return 'green';
      case 'pending confirmation':
        return 'orange';
      case 'rejected':
      case 'refused':
        return 'red';
      default:
        return '';
    }
  }
  getStatusBgColor(status: string): string {
    switch (status?.toLowerCase()?.trim()) {
      case 'confirmed':
        return '#E7F5FF'
      case 'approved':
        return '#F3FAF4';
      case 'pending confirmation':
        return '#FFFBEB';
      case 'rejected':
      case 'refused':
        return '#FEF2F2';
      default:
        return '';
    }
  }
}

