import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { jwtDecode } from 'jwt-decode';
import { environment } from '../../../environments/environment';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { Router } from '@angular/router';
import { LanguageService } from './language.service';
import { TranslateService } from '@ngx-translate/core';

const JWT_TOKEN_KEY = 'jwtToken';
const USER_INFO_KEY = 'userInfo';
const REDIRECT_URL_KEY = 'redirectUrl';



@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private _userInfo$ = new BehaviorSubject<any>({});

  constructor(private http: HttpClient, public router: Router, private languageService: LanguageService, private translate: TranslateService) { }

  get userInfo$() {
    var userInfo = localStorage.getItem(USER_INFO_KEY);
    if (userInfo) this._userInfo$.next(JSON.parse(userInfo));
    return this._userInfo$.asObservable();
  }

  setJwtToken(token: string) {
    if (this.isTokenValid(token)) localStorage.setItem(JWT_TOKEN_KEY, token);
    return true;
  }

  setUserInfo(userInfo: any) {
    this._userInfo$.next(userInfo);
    localStorage.setItem(USER_INFO_KEY, JSON.stringify(userInfo));
  }

  //::TODO Remove static data (Heaiba)
  getUserInfo() {
    var userInfoString = localStorage.getItem(USER_INFO_KEY);
    if (userInfoString && userInfoString.length > 0) {
      return JSON.parse(userInfoString);
    }
    return null;

    //  return JSON.parse('{ "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1laWQiOiI3ODQxOTU5MjgwODc0ODUiLCJlbWFpbCI6Ik5PVVJBMjEwMTRASE9UTUFJTC5DT00iLCJuYW1lIjoiTk9VUkEgRkFIRUQgQUJEVUxIQURJIEFMIEFIQkFCSSIsIm5iZiI6MTcyODkyMDYzOCwiZXhwIjoxNzI4OTI0MjM4LCJpYXQiOjE3Mjg5MjA2MzgsImlzcyI6Ik1PQ0QiLCJhdWQiOiJNT0NETlBPIn0.bVgrfox-_6FWqI1wNTN_cySdSaURFB_VVny8bX635Tk", "userIdentifier": "000144be-027c-430c-9e82-07fe7df459e9", "crmUserId": "58e0a6bc-4210-ef11-b109-0050560108b0", "fullnameEN": "NOURA FAHED ABDULHADI AL AHBABI", "email": "<EMAIL>", "message": null, "userInfo": { "sub": "122558", "fullnameAR": "نوره فهد عبدالهادى الاحبابى", "gender": "FEMALE", "mobile": "************", "lastnameEN": "FAHED ABDULHADI AL AHBABI", "fullnameEN": "NOURA FAHED ABDULHADI AL AHBABI", "uuid": "784195928087485", "lastnameAR": "فهد عبدالهادى الاحبابى", "idn": "784195928087485", "nationalityEN": "UNITED ARAB EMIRATES", "firstnameEN": "NOURA", "userType": "SOP3", "nationalityAR": "الإمارات العربية المتحدة", "firstnameAR": "نوره", "email": "<EMAIL>" } }');
  }

  getJwtToken(): string | null {
    return localStorage.getItem(JWT_TOKEN_KEY);
  }

  getUaePassToken(accessCode: string) {
    return this.http.get<any>(
      environment.authBaseURL + environment.getUaePassTokenCustom + accessCode
    );
  }

  isTokenValid(token: string | null): boolean {
    const authToken = token;
    if (!authToken) {
      return false;
    }

    const tokenPayload = jwtDecode(authToken) as any;
    const now = Date.now() / 1000;
    return tokenPayload.exp && tokenPayload.exp >= now;
  }

  //::TODO Remove static data (Heaiba)
  isAuthenticated() {
    // this.languageService.setDefaultLang();
    //return true;

    var token = this.getJwtToken();
    return this.isTokenValid(token);
  }

  login() {
    this.router.navigate(['/login']);
  }
  logout(currentUrl: string = '/') {
    localStorage.clear();
    if (currentUrl.indexOf('e-services') >= 0) {
      localStorage.setItem(REDIRECT_URL_KEY, currentUrl);
    }
    window.location.href = environment.authBaseURL + environment.logout + this.translate.currentLang;
  }

  getRedirectUrl(): string | null {
    return localStorage.getItem(REDIRECT_URL_KEY);
  }

  clearRedirectUrl() {
    localStorage.removeItem(REDIRECT_URL_KEY);
  }

  loginUDotAe(token: string, currentUrl: string = '/') {
    localStorage.clear();
    if (currentUrl.indexOf('e-services') >= 0) {
      localStorage.setItem(REDIRECT_URL_KEY, currentUrl);
    }
    window.location.href = environment.authBaseURL + environment.loginUDotAe + token;
  }
}

