#modal-map{
    .modal-content{
        padding: 24px;
        @media (min-width: 768px) {
            padding: 40px;
        }
        gap: 24px;
        iframe{
            width: 100%;
        }
        .modal-header, .modal-body, .modal-footer{
            padding: 0;
            border: none !important;
            h3{
                margin: 0 !important;
            }
        }
        #map {
            height: 500px;
        }
        .custom-map-control-button {
            background-color: #fff;
            border: 0;
            border-radius: 2px;
            box-shadow: 0 1px 4px -1px rgba(0, 0, 0, 0.3);
            margin: 10px;
            padding: 0 0.5em;
            font: 400 18px Roboto, Arial, sans-serif;
            overflow: hidden;
            height: 40px;
            cursor: pointer;
        }
        .custom-map-control-button:hover {
            background: rgb(235, 235, 235);
        }
    }
    .modal-header, .modal-content, .modal-footer{
        margin: 0;
    }
}
.npoForm{
    .table-listing-objective, .table-listing-member{
        margin: 32px 0;
        border-radius: 8px;
        background: $aegold-50;
        padding: 16px;
        @media (max-width: 992px) {
            padding: 24px;
        }
        table{
            margin-bottom: 0;
        }
        td{
            background-color: $aegold-50;
            padding: 16px;
            align-content: center;
            &:has(> button){
                text-align: end;
            }
        }
        tr:last-child{
            td{
                padding-bottom: 0;
                border: none;
            }
        }
        tr:first-child{
            td{
                padding-top: 0;
            }
        }
        .mobile-only{
            display: none;
            @media (max-width: 992px) {
                display: table-cell;
                strong{
                    display: block;
                }
                svg{
                    margin-right: 12px;
                }
            }
        }
        .desktop-only{
            @media (max-width: 992px) {
                display: none;
            }
        }
        .action-popup{
            box-shadow: 0px 4px 8px 0px #0000001F;
            border-radius: 8px;
            border: none !important;
            padding: 0 !important;
            width: 250px;
            max-width: 90%;
            font-size: 14px;
            text-align: start !important;
            ul{
                list-style: none;
                cursor: pointer;
                padding: 0;
                margin: 0;
                li, .dropdown-item{
                    border-radius: 8px;
                    padding: 15px;
                    color: $aeblack-900;
                    svg{
                        margin-right: 10px;
                        [ng-reflect-dir=rtl] &, [dir=rtl] & {
                            margin-right: 0;
                            margin-left: 10px;
                        }
                    }
                    &.delete{
                        color: $aered-600;
                    }
                    span{
                        display: inline;
                    }
                    &:hover{
                        background-color: $aegold-100 !important;
                    }
                }
            }
        }
        .table-listing__status{
            svg{
                margin-right: 12px;
            }
            text-align: end;
        }
    }
    .aegov-pagination{
        margin: auto !important;
        width: fit-content !important;
        margin-top: 32px !important;
    }
    button{
        i{
            margin-right: 12px;
        }
    }
    .count{
        width: fit-content;
        // margin: 12px;
        padding: 4px 8px;
        border-radius: 40px;
        background: $aegold-50;
        color: $aegold-500;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
    }
    .form-control-input{
        position: relative;
        display: flex;
        background-color: $white;
        border: 2px solid $aegold-400;
        border-radius: 4px;
        width: min-content;
        max-width: 100%;
        &:has(:focus-visible){
            border: 2px solid $aegold-600;
            width: 376px;
        }
        .control-prefix{
            margin: 16px;
            display: flex;
            user-select: none;
            align-items: center;
            color: $aeblack-300;
            svg{
                height: 1.5rem;
                width: 1.5rem;
                fill: $aegold-600;
                display: block;
            }
        }
        input{
            display: block;
            width: 0;
            flex: 1 1 0%;
            position: absolute;
            left: 0;
            border-width: 0px !important;
            background-color: transparent !important;
            padding: 16px 45px !important;
            font-size: 1rem;
            line-height: 1.5rem;
            color: $aegold-900;
            box-shadow: none !important;
            &:has(:focus-visible){
                outline: none !important;
                border-width: 0px !important;
                // padding: 1.5rem !important;
                width: 100%;
                // left: 1.5rem !important;
            }
        }
    }
    .aegov-accordion{
        .accordion-item{
            color: $techblue-900 !important;
            background: $techblue-50 !important;
            .accordion-button{
                p{
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 22px;
                    color: $techblue-900 !important;
                }
                svg{
                    fill: $techblue-900;
                    width: 20px;
                    height: 20px;
                }
            }
            .accordion-body{
                font-size: 16px;
                font-weight: 400;
                line-height: 22px;
            }
        }
    }
}
.modal-content{
    padding: 24px;
    @media (min-width: 768px) {
        padding: 40px;
    }
    gap: 24px;
    .modal-header, .modal-body, .modal-footer{
        padding: 0;
        border: none;
    }
    .modal-header{
        // margin-bottom: 12px;
        .modal-title{
            margin: 0 0 24px 0;
            font-size: 24px;
            font-weight: 700;
            line-height: 32px;
            letter-spacing: normal !important;

            @media (max-width:1024px){
                margin:  0 0 16px 0 !important;
            }
        }
        .row{
            flex-direction: row-reverse !important;
            justify-content: center !important;
            .col-1{
                text-align: end;
                padding-right: 0;
                justify-content: center;
            }
        }
        align-items: start !important;
    }
    .modal-footer{
        // margin-top: 24px;
        flex-direction: row-reverse !important;
        gap: 10px;
        button{
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

    }
    .count{
        width: fit-content;
        margin: 12px;
        padding: 4px 8px;
        border-radius: 40px;
        background: $aegold-50;
        color: $aegold-500;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
    }
}
.pro_names{
    font-size: 14px;
    font-weight: $font-medium;
    color: $aegold-500;
    background-color: $aegold-50;
    border-radius: 12px;
    padding: 12px;
    width: max-content;
    // display: inline-flex;
    max-height: 44px;
    // align-items: center;
    margin-left: 10px;
    margin-right: 0 ;
    line-height: 20px;
    text-align: center;

    @media (max-width: 1024px){
        width: 100% !important;
        max-height: fit-content !important;
    }
}
body [ng-reflect-dir=rtl],
body [dir=rtl] {
    .pro_names{
        margin-right: 10px ;
        margin-left: 0 ;
    }

}
.to-conditions{
    font-size: 16px;
    color: $aeblack-800;
    text-decoration: none;
    .info-icon{
        color: $aegold-500;
        width: 32px;
        height: 32px;
        text-align: center;
    }
    @media (max-width: 767px) {
        font-size: 14px;
    }
}
.upload_details{
    p{
        font-size: 16px;
        &:not(:last-child){
            margin-bottom: .5rem;
        }
        strong{

        }
    }
}
