/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EstablishmentBoardOfTrusteesComponent } from './establishment-board-of-trustees.component';

describe('EstablishmentBoardOfTrusteesComponent', () => {
  let component: EstablishmentBoardOfTrusteesComponent;
  let fixture: ComponentFixture<EstablishmentBoardOfTrusteesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EstablishmentBoardOfTrusteesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EstablishmentBoardOfTrusteesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
