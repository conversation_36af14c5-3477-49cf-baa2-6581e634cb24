/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EstablishmentFundActivitiesAndObjectivesComponent } from './establishment-fund-activities-and-objectives.component';

describe('EstablishmentFundActivitiesAndObjectivesComponent', () => {
  let component: EstablishmentFundActivitiesAndObjectivesComponent;
  let fixture: ComponentFixture<EstablishmentFundActivitiesAndObjectivesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EstablishmentFundActivitiesAndObjectivesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EstablishmentFundActivitiesAndObjectivesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
