<div class="container-fluid">
  <mat-accordion>
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'BasicInformation') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>
      @if(formData?.BasicInformationForm?.npoform
      ==LEGAL_FORM_TYPES.AssociationByDecree
      ||formData?.BasicInformationForm?.npoform
      ==LEGAL_FORM_TYPES.NationalSocietyByDecree)
      {
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'npoLegalForm') |
          translate}}</h4>
      </div>
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'localDecreeLawNumber'"
          [isPlain]="true" [value]="formData?.BasicInformationForm?.LocalDecreeLawNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'issuanceDate'" [isDate]="true"
          [value]="formData?.BasicInformationForm?.IssuanceDate"></div>

      </div>
      }
      <div class="not_card">
        <h4 class="title_wrapper">
          {{(messageTranslationPrefix+'npoName') | translate}}</h4>
      </div>

      <div class="figma-review-card-container">
        <div *ngIf="requestNumber" lang="en" summary-row class="figma-review-card" [label]="'services.npoLicenseDeclaration.appReferenceNumber'" [isPlain]="true" [value]="requestNumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NameEn'" [isPlain]="true" [value]="formData?.BasicInformationForm?.approvedNameEn??formData?.BasicInformationForm.name"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NameAr'" [isPlain]="true" [value]="formData?.BasicInformationForm?.approvedNameAr??formData?.BasicInformationForm.namear"></div>
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'npoContactDetails')
          | translate}}</h4>
      </div>
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Emirate'" [isPlain]="true"
          [value]="getEmirate(formData?.BasicInformationForm?.emirate)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'GeographicLocation'"
          [isPlain]="true" [value]="formData?.BasicInformationForm?.geographiclocation"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Landline'" [isPlain]="true"
          [value]="formData?.BasicInformationForm?.landlinenumber"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'POBox'" [isPlain]="true"
          [value]="formData?.BasicInformationForm?.pobox"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Email'" [isPlain]="true"
          [value]="formData?.BasicInformationForm?.email"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Website'" [isPlain]="true"
          [value]="formData?.BasicInformationForm?.website"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'Address'" [isPlain]="true"
          [value]="formData?.BasicInformationForm?.address"></div>
      </div>

      @if(formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.SocialSolidarityFunds){

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'fundbelongsDetails')| translate}}</h4>
      </div>
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityNameEn'" [isPlain]="true" [value]="formData?.BasicInformationForm?.EntityName"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityNameAr'" [isPlain]="true" [value]="formData?.BasicInformationForm?.EntityNameAr"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EntityType'" [isPlain]="true" [value]="getEntitySectorType(formData?.BasicInformationForm?.Entitysectortype)"></div>
        <div *ngIf="formData?.BasicInformationForm?.Entitysectortype==3" summary-row class="figma-review-card"   [label]="messageTranslationPrefix+'NumberOfEmployees'" [isPlain]="true" [value]="formData?.BasicInformationForm?.Numberofemployees"></div>
      </div>
      }

      @if(formData?.BasicInformationForm?.npoform ==
      LEGAL_FORM_TYPES.Association ||
      formData?.BasicInformationForm?.npoform ==
      LEGAL_FORM_TYPES.NationalSociety){
      <div class="not_card">
        @if(formData?.BasicInformationForm?.npoform ==
        LEGAL_FORM_TYPES.Association){
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'targetGroupsAssociation')
          | translate}}</h4>
        }

        @if(formData?.BasicInformationForm?.npoform ==
        LEGAL_FORM_TYPES.NationalSociety){
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'targetGroupsNationalSociety')
          | translate}}</h4>
        }
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"targetGroupNameEn" |
                  translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"targetGroupNameAr" |
                  translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of paginateDataTargetGroup; let i = index">
                <td class="align-middle text-center">{{ (pageTargetGroup - 1) *
                  pageSizeTargetGroup + i + 1 }}</td>
                <td class="align-middle text-center " lang="en">{{ item?.Name
                  }}</td>
                <td class="align-middle text-center " lang="ar">{{ item?.NameAr
                  }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2 d-l">{{'Total count' | translate}}:
            {{filteredCountTargetGroup}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageTargetGroup" [pageSize]="pageSizeTargetGroup"
            [collectionSize]="filteredCountTargetGroup" (pageChange)="getPremiumDataTargetGroup()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeTargetGroup"
            (change)="getPremiumDataTargetGroup()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (targetGroup of paginateDataTargetGroup; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'targetGroupNameEn' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ targetGroup?.Name }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'targetGroupNameAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{ targetGroup?.NameAr
                }}</div>
            </div>
          </div>
        </div>
        }
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">
          {{(messageTranslationPrefix+'activitiesAndPrograms') | translate}}
        </h4>
      </div>

      <div class="col-12 table-container not_card">
        <div class="table-responsive d-block_mobile">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"exampleOfActivitiesEn" | translate}}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"exampleOfActivitiesAr" | translate}}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let exampleOfActivity of paginateDataExamplesOfActivities; let i = index">
                <td class="align-middle text-center">
                  {{ (pageExamplesOfActivities - 1) * pageSizeExamplesOfActivities + i + 1 }}
                </td>
                <td class="align-middle text-center " lang="en">
                  {{ exampleOfActivity?.Name }}
                </td>
                <td class="align-middle text-center " lang="ar">
                  {{ exampleOfActivity?.NameAr }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="figma-card-container">
          @for (exampleOfActivity of paginateDataExamplesOfActivities; track $index){
          <div class="figma-card">
            <div class="figma-card-content">
              <div class="figma-card-field">
                <span class="static-value">#</span>
                <span class="dynamic-value">{{ $index + 1 }}</span>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'exampleOfActivitiesEn' | translate }}:
                </div>
                <div class="dynamic-value" lang="en">
                  {{ exampleOfActivity?.exampleOfActivitiesEn }}
                </div>
              </div>
              <div class="figma-card-field">
                <div class="static-value">
                  {{ messageTranslationPrefix + 'exampleOfActivitiesAr' | translate }}:
                </div>
                <div class="dynamic-value" lang="ar">
                  {{ exampleOfActivity?.exampleOfActivitiesAr }}
                </div>
              </div>
            </div>
          </div>
          }
        </div>
      </div>

      <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
        <span class="col-2 d-l">{{'Total count' | translate}}:
          {{filteredCountExamplesOfActivities}}</span>
        <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageExamplesOfActivities"
          [pageSize]="pageSizeExamplesOfActivities" [collectionSize]="filteredCountExamplesOfActivities"
          (pageChange)="getPremiumDataExamplesOfActivities()">
          <ng-template ngbPaginationPrevious>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
            </svg>
            <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
          </ng-template>
          <ng-template ngbPaginationNext>
            <span class="d-none d-lg-block">{{'Next' | translate}}</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
              <path
                d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
            </svg>
          </ng-template>
        </ngb-pagination>
        <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeExamplesOfActivities"
          (change)="getPremiumDataExamplesOfActivities()">
          <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
          <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
        </select>
      </div>
      }
    </mat-expansion-panel>

    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>
          @if(formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.SocialSolidarityFunds){
          {{ messageTranslationPrefix+'fundServiceObjectives' | translate }}
          } @else {
          {{ messageTranslationPrefix+'Objectives' | translate }}
          }
        </mat-panel-title>
      </mat-expansion-panel-header>
      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                @if(formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.SocialSolidarityFunds){
                <th class="align-middle text-center text-wrap" scope="col">
                  {{ messageTranslationPrefix+"FundServiceObjectiveEn" | translate }}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{ messageTranslationPrefix+"FundServiceObjectiveAr" | translate }}
                </th>
                } @else {
                <th class="align-middle text-center text-wrap" scope="col">
                  {{ messageTranslationPrefix+"ObjectiveEn" | translate }}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{ messageTranslationPrefix+"ObjectiveAr" | translate }}
                </th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{ messageTranslationPrefix+"MeansOfAchievingObjectiveEn" | translate }}</th>
                <th class="align-middle text-center text-wrap text-center" scope="col">
                  {{ messageTranslationPrefix+"MeansOfAchievingObjectiveAr" | translate }}
                </th>
                }
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let objective of paginateDataObjectives; let i = index">
                <td class="align-middle text-center">{{ (pageObjectives - 1) *
                  pageSizeObjectives + i + 1 }}</td>
                <td class="align-middle text-center text-truncate" lang="en" [matTooltip]="objective?.Objectives"
                  style="max-width:150px;">{{ objective?.Objectives }}</td>
                <td class="align-middle text-center text-truncate" lang="ar" [matTooltip]="objective?.ObjectivesAR"
                  style="max-width:150px;">{{ objective?.ObjectivesAR }}</td>
                @if(formData?.BasicInformationForm?.npoform !== LEGAL_FORM_TYPES.SocialSolidarityFunds){
                <td class="align-middle text-center text-truncate" lang="en"
                  [matTooltip]="objective?.MeansOfAchievingObjective" style="max-width:150px;">{{
                  objective?.MeansOfAchievingObjective }}</td>
                <td class="align-middle text-center text-truncate" lang="ar"
                  [matTooltip]="objective?.MeansOfAchievingObjectiveAR" style="max-width:150px;">{{
                  objective?.MeansOfAchievingObjectiveAR }}</td>
                }
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2 d-l">{{'Total count' | translate}}:
            {{filteredCountObjectives}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageObjectives" [pageSize]="pageSizeObjectives"
            [collectionSize]="filteredCountObjectives" (pageChange)="getPremiumDataObjectives()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeObjectives"
            (change)="getPremiumDataObjectives()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (objective of paginateDataObjectives; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            @if(formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.SocialSolidarityFunds){
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'ObjectiveEn' | translate }}:
              </div>
              <div class="dynamic-value" lang="en">
                {{ objective?.Objectives }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'ObjectiveAr' | translate }}:
              </div>
              <div class="dynamic-value" lang="ar">
                {{ objective?.ObjectivesAR }}
              </div>
            </div>
            } @else {
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'ObjectiveEn' | translate }}:
              </div>
              <div class="dynamic-value" lang="en">
                {{ objective?.Objectives }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'ObjectiveAr' | translate }}:
              </div>
              <div class="dynamic-value" lang="ar">
                {{ objective?.ObjectivesAR }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'MeansOfAchievingObjectiveEn' | translate }}:
              </div>
              <div class="dynamic-value" lang="en">
                {{ objective?.MeansOfAchievingObjective }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">
                {{ messageTranslationPrefix + 'MeansOfAchievingObjectiveAr' | translate }}:
              </div>
              <div class="dynamic-value" lang="ar">
                {{ objective?.MeansOfAchievingObjectiveAR }}
              </div>
            </div>
            }
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>

    @if(formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.SocialSolidarityFunds){
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'FundServices') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>
      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"ServiceTitle" |
                  translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"ServiceTitleAR" |
                  translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let fundService of paginateDataFundServices; let i = index">
                <td class="align-middle text-center">{{ (pageFundServices - 1) *
                  pageSizeFundServices + i + 1 }}</td>
                <td class="align-middle text-center text-truncate" lang="en" [matTooltip]="fundService?.ServiceTitle"
                  style="max-width:150px;">{{ fundService?.Description }}</td>
                <td class="align-middle text-center text-truncate" lang="ar" [matTooltip]="fundService?.ServiceTitleAR"
                  style="max-width:150px;">{{ fundService?.DescriptionAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2 d-l">{{'Total count' | translate}}:
            {{filteredCountFundServices}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageFundServices" [pageSize]="pageSizeFundServices"
            [collectionSize]="filteredCountFundServices" (pageChange)="getPremiumDataFundServices()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeFundServices"
            (change)="getPremiumDataFundServices()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (objective of paginateDataFundServices; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'ServiceTitle' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ objective?.ServiceTitle }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'ServiceTitleAR' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{ objective?.ServiceTitleAR
                }}
              </div>
            </div>
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>
    }

    @if(formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.AssociationByDecree ||
    formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.NationalSocietyByDecree ||
    formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Association ||
    formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.SocialSolidarityFunds ||
    formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union ||
    formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.NationalSociety){
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'foundingMembers') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>
      @if(formData?.BasicInformationForm?.npoform ===
      LEGAL_FORM_TYPES.Association || formData?.BasicInformationForm?.npoform
      === LEGAL_FORM_TYPES.NationalSociety ||
      formData?.BasicInformationForm?.npoform === LEGAL_FORM_TYPES.Union){
      @if(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ==
      true || formData?.FoundingMembersForm?.nbrfmlessthan7code == true){
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'exceptionCases')
          | translate}}</h4>
      </div>
      }
      @if(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ==
      true){

      <div class="row not_card">
        <div summary-row class="figma-review-card"
          [label]="messageTranslationPrefix+'IsFoundingMembersHoldingTheNationalityIsLessThan70'" [isPlain]="true"
          [value]="(formData?.FoundingMembersForm?.uaenationalitylessthan70percentcode ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
        </div>
      </div>
      <div class="figma-review-card-container">
        <div lang="en" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70En'"
          [isPlain]="true" [value]="formData?.FoundingMembersForm?.ExceptionsRequestNationalityless70?.Description">
        </div>
        <div lang="ar" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'"
          [isPlain]="true" [value]="formData?.FoundingMembersForm?.ExceptionsRequestNationalityless70?.DescriptionAr">
        </div>
      </div>
      }
      @if(formData?.FoundingMembersForm?.nbrfmlessthan7code == true){

      <div class="row not_card">
        <div summary-row class="figma-review-card"
          [label]="messageTranslationPrefix+'IsNumberOfFoundingMembersIsLessThan7Members'" [isPlain]="true"
          [value]="(formData?.FoundingMembersForm?.nbrfmlessthan7code ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
        </div>
      </div>
      <div class="figma-review-card-container">
        <div lang="en" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70En'"
          [isPlain]="true" [value]="formData?.FoundingMembersForm?.ExceptionsRequestNumberless7?.Description"></div>
        <div lang="ar" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'"
          [isPlain]="true" [value]="formData?.FoundingMembersForm?.ExceptionsRequestNumberless7?.DescriptionAr"></div>
      </div>
      }
      }

      @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union){

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'foundingMembers')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"founderEmiratesID" |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"dateOfBirth" |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"founderNameEnglish" |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"founderNationality" |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"responseDate" |translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of paginateDataFoundingMember; let i = index">
                <td class="align-middle text-center">{{ (pageFoundingMember - 1)* pageSizeFoundingMember + i + 1 }}</td>
                <td class="align-middle text-center">{{ item?.Contact?.EmirateId}}</td>
                <td class="align-middle text-center">{{ item?.Contact?.Dob |date: 'dd/MM/yyyy' }}</td>
                <td class="align-middle text-center" lang="en">{{this.canSeeFoundingMember(item?.SatusReason) ?(item?.Name ??'') : '' }}</td>
                <td class="align-middle text-center" lang="en">{{this.canSeeFoundingMember(item?.SatusReason) ?(item?.Nationality ?? '') : '' }}</td>
                <td class="align-middle text-center">{{this.canSeeFoundingMember(item?.SatusReason) ?(item?.Responsedate| date: 'dd/MM/yyyy') : '' }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountFoundingMember}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageFoundingMember"
            [pageSize]="pageSizeFoundingMember" [collectionSize]="filteredCountFoundingMember"
            (pageChange)="getPremiumDataFoundingMember()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeFoundingMember"
            (change)="getPremiumDataFoundingMember()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (item of paginateDataFoundingMember; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderEmiratesID' | translate }}:</div>
              <div class="dynamic-value">{{ item?.Contact?.EmirateId }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'dateOfBirth' | translate }}:</div>
              <div class="dynamic-value">{{ item?.Contact?.Dob | date:
                'dd/MM/yyyy' }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderNameEnglish' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ item?.Name }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderNationality' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ item?.Nationality }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'responseDate' | translate }}:</div>
              <div class="dynamic-value">{{ item?.ResponseDate }}
              </div>
            </div>
          </div>
        </div>
        }
      </div>
      }

      @if(formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union)
      {
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'associationsNationalSocieties')
          | translate}}</h4>
      </div>
      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoUnifiedNumber"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoEstablishmentNameEN"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoEstablishmentNameAr"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoEstablishmentDate"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoEstablishmentLegalFormEN"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"npoEstablishmentLegalFormAr"
                  | translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of paginateDataNpoNumber; let i = index">
                <td class="align-middle text-center">{{ (pageNpoNumber - 1) *
                  pageSizeNpoNumber + i + 1 }}</td>
                <td class="align-middle text-center text-nowrap" langdir="ltr">{{
                  item?.npoEstablishmentNameEN }}</td>
                <td class="align-middle text-center" lang="en">{{
                  item?.UnifiedNumber }}</td>
                <td class="align-middle text-center" lang="ar">{{
                  item?.npoEstablishmentNameAr }}</td>
                <td class="align-middle text-center">{{
                  item?.npoEstablishmentDate }}</td>
                <td class="align-middle text-center" lang="en">{{
                  item?.npoEstablishmentLegalFormEN }}</td>
                <td class="align-middle text-center" lang="ar">{{
                  item?.npoEstablishmentLegalFormAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountNpoNumber}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageNpoNumber" [pageSize]="pageSizeNpoNumber"
            [collectionSize]="filteredCountNpoNumber" (pageChange)="getPremiumDataNpoNumber()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeNpoNumber"
            (change)="getPremiumDataNpoNumber()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (item of paginateDataNpoNumber; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>

            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +'npoUnifiedNumber' | translate }}:</div>
              <div class="dynamic-value" dir="ltr">{{item?.UnifiedNumber }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentNameEN' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{item?.npoEstablishmentNameEN }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentNameAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{item?.npoEstablishmentNameAr }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +'npoEstablishmentDate' | translate }}:</div>
              <div class="dynamic-value">{{ item?.npoEstablishmentDate }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'npoEstablishmentLegalFormEN' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{
                item?.npoEstablishmentLegalFormEN }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'npoEstablishmentLegalFormAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{
                item?.npoEstablishmentLegalFormAr }}
              </div>
            </div>
          </div>
        </div>
        }
      </div>
      }
    </mat-expansion-panel>
    }

    @if(formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.AssociationByDecree ||
    formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Association ||
    formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.SocialSolidarityFunds ||
    formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.Union)
    {
    @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union &&
    formData?.BasicInformationForm?.npoform !=
    LEGAL_FORM_TYPES.AssociationByDecree){
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'interimCommittee') |translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'interimCommitteeMembers')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"founderEmiratesID" |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"temporaryCommitteeMemberName"| translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"memberPosition"
                  |translate}}</th>
              </tr>
            </thead>
            <tbody>
              @for(member of paginateDataInterimMembers; track member; let i =
              $index){
              <tr>
                <td class="align-middle text-center">{{ (pageInterimMembers - 1)* pageSizeInterimMembers + i + 1 }}</td>
                <td class="align-middle text-center">{{member?.Contact?.EmirateId }}</td>
                <td class="align-middle text-center">{{ this.canSeeFoundingMember(member?.SatusReason) ? (LanguageService.IsArabic ? member?.Contact?.FullnameAr : member?.Contact?.FullName) : '' }}</td>
                <td [lang]="LanguageService.IsArabic ? 'ar':'en'" class="align-middle text-center"> {{getPosition(member.PositionId) }}</td>
              </tr>
              }

            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountInterimMembers}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageInterimMembers"
            [pageSize]="pageSizeInterimMembers" [collectionSize]="filteredCountInterimMembers"
            (pageChange)="getPremiumDataInterimCommittee()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeInterimMembers"
            (change)="getPremiumDataInterimCommittee()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (member of paginateDataInterimMembers; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderEmiratesID' | translate }}:</div>
              <div class="dynamic-value">{{ member?.Contact?.EmirateId }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'temporaryCommitteeMemberName' | translate }}:</div>
              <div class="dynamic-value">{{this.canSeeFoundingMember(member?.SatusReason) ? (member?.Name ??'') : '' }}
              </div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'memberPosition' | translate }}:</div>
              <div [lang]="LanguageService.IsArabic ? 'ar':'en'" class="dynamic-value">{{ getPosition(member.PositionId)
                }}</div>
            </div>
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>
    }

    @if(formData?.BasicInformationForm?.npoform !=
    LEGAL_FORM_TYPES.AssociationByDecree){
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'membership' |
          translate)}}</mat-panel-title>
      </mat-expansion-panel-header>

      <!-- @if(formData?.BasicInformationForm?.npoform != LEGAL_FORM_TYPES.Union) -->
      <!-- { -->
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'membershipInformation')
          | translate}}</h4>
      </div>
      <div class="figma-review-card-container">
        @if(formData?.BasicInformationForm?.npoform ==LEGAL_FORM_TYPES.Association){
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MembershipFees'" [isPlain]="true"
          [value]="formData?.MembershipForm?.Membershipfees|currency:'AED'"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EnrollmentFees'" [isPlain]="true"
          [value]="formData?.MembershipForm?.Enrollmentfees|currency:'AED'"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'beneficiaryMembershipFees'"
          [isPlain]="true" [value]="formData?.MembershipForm?.BeneficiaryOrMembershipFees|currency:'AED'"></div>
        }@else {
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'normalMembershipFees'"
          [isPlain]="true" [value]="formData?.MembershipForm?.Membershipfees|currency:'AED'"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'EnrollmentFees'" [isPlain]="true"
          [value]="formData?.MembershipForm?.Enrollmentfees|currency:'AED'"></div>
        }
      </div>
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'membershipConditions')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"membershipConditionEn"| translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"membershipConditionAr"| translate}}</th>
              </tr>
            </thead>
            <tbody>
              @for(memberShip of paginateDataMemberShip; track memberShip; let i= $index){
              <tr>
                <td class="align-middle text-center">{{ (pageMemberShip - 1) * pageSizeMemberShip + i + 1 }}</td>
                <td class="align-middle text-center" lang="en">{{memberShip?.Name }}</td>
                <td class="align-middle text-center" lang="ar">{{memberShip?.NameAr }}</td>
              </tr>
              }

            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountMemberShip}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageMemberShip" [pageSize]="pageSizeMemberShip"
            [collectionSize]="filteredCountMemberShip" (pageChange)="getPremiumDataMemberShip()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeMemberShip"
            (change)="getPremiumDataMemberShip()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (memberShip of paginateDataMemberShip; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'membershipConditionEn' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ memberShip?.Name }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'membershipConditionAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{ memberShip?.NameAr
                }}</div>
            </div>
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>
    }
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'boardOfDirectors') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      @if(formData?.BoardOfDirectorsForm?.nbrboardmemberexceed11code === 1){
      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'exceptionRequests')
          | translate}}</h4>
      </div>

      <div class="roe not_card">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'MemberIsExceeds11'"
          [isPlain]="true"
          [value]="(formData?.BoardOfDirectorsForm?.nbrboardmemberexceed11code ? (messageTranslationPrefix+'yes' | translate) : (messageTranslationPrefix+'no' | translate))">
        </div>
      </div>
      <div class="figma-review-card-container">
        <div lang="en" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70En'"
          [isPlain]="true" [value]="formData?.BoardOfDirectorsForm?.ExceptionsRequest?.Description"></div>
        <div lang="ar" summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ExceptionReasonFor70Ar'"
          [isPlain]="true" [value]="formData?.BoardOfDirectorsForm?.ExceptionsRequest?.DescriptionAr"></div>
      </div>
      }

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsInformation')
          | translate}}</h4>
      </div>

      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'numberOfBoardMembers'"
          [isPlain]="true" [value]="formData?.BoardOfDirectorsForm?.nbrofboardmembers"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'FrequencyOfMonthlyBoardMeetings'"
          [isPlain]="true"
          [value]="getFrequencyOfBoardMeetings(formData?.BoardOfDirectorsForm?.Frequencyofmonthlyboardmeetings)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'localBoardMembersPercentage'"
          [isPlain]="true" [value]="formData?.BoardOfDirectorsForm?.percentageofuaelocalboardmembers"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'ElectionMethod'" [isPlain]="true"
          [value]="getElectionMethod(formData?.BoardOfDirectorsForm?.Electionmethod)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'CanBeRenominated'"
          [isPlain]="true"
          [value]="getRenominationStatus(formData?.BoardOfDirectorsForm?.canboardmemberberenomatedforotherterm)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'NumberOfPermissibleTerms'"
          [isPlain]="true"
          *ngIf="getRenominationStatus(formData?.BoardOfDirectorsForm?.canboardmemberberenomatedforotherterm) != ''"
          [value]="getPermissibleTerms(formData?.BoardOfDirectorsForm?.NbrofpermissibleTerms)"></div>
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'BoardElectionCycle'"
          [isPlain]="true" [value]="getBoardElectionCycle(formData?.BoardOfDirectorsForm?.Boardelectioncycle)"></div>
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsConditions')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"nominationConditionEnglish"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"nominationConditionArabic"
                  | translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let membershipCondition of paginateDataBoardOfDirectorsMembershipConditions; let i = index">
                <td class="align-middle text-center">{{
                  (pageBoardOfDirectorsMembershipConditions - 1) *
                  pageSizeBoardOfDirectorsMembershipConditions + i + 1 }}</td>
                <td class="align-middle text-center" lang="en">{{
                  membershipCondition?.Name }}</td>
                <td class="align-middle text-center" lang="ar">{{
                  membershipCondition?.NameAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountBoardOfDirectorsMembershipConditions}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfDirectorsMembershipConditions"
            [pageSize]="pageSizeBoardOfDirectorsMembershipConditions"
            [collectionSize]="filteredCountBoardOfDirectorsMembershipConditions"
            (pageChange)="getPremiumDataBoardOfDirectorsMembershipConditions()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize"
            [(ngModel)]="pageSizeBoardOfDirectorsMembershipConditions"
            (change)="getPremiumDataBoardOfDirectorsMembershipConditions()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (membershipCondition of
        paginateDataBoardOfDirectorsMembershipConditions; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'nominationConditionEnglish' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'nominationConditionArabic' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{
                membershipCondition?.NameAr }}</div>
            </div>
          </div>
        </div>
        }
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfDirectorsPositions')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"adminPositionTitleEnglish"
                  | translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"adminPositionTitleArabic"
                  | translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let bOfDPosition of paginateDataBoardOfDirectorsPositions; let i = index">
                <td class="align-middle text-center">{{
                  (pageBoardOfDirectorsPositions - 1) *
                  pageSizeBoardOfDirectorsPositions + i + 1 }}</td>
                <td class="align-middle text-center" lang="en">{{
                  bOfDPosition?.Name }}</td>
                <td class="align-middle text-center" lang="ar">{{
                  bOfDPosition?.NameAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountBoardOfDirectorsPositions}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfDirectorsPositions"
            [pageSize]="pageSizeBoardOfDirectorsPositions" [collectionSize]="filteredCountBoardOfDirectorsPositions"
            (pageChange)="getPremiumDataBoardOfDirectorsPositions()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize"
            [(ngModel)]="pageSizeBoardOfDirectorsPositions" (change)="getPremiumDataBoardOfDirectorsPositions()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (bOfDPosition of paginateDataBoardOfDirectorsPositions; track
        $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'adminPositionTitleEnglish' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ bOfDPosition?.Name
                }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'adminPositionTitleArabic' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{ bOfDPosition?.NameAr
                }}</div>
            </div>
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>
    }
    @if(formData?.BasicInformationForm?.npoform ==
    LEGAL_FORM_TYPES.NationalSocietyByDecree ||
    formData?.BasicInformationForm?.npoform == LEGAL_FORM_TYPES.NationalSociety)
    {
    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{messageTranslationPrefix+'allocationOfFoundationFunds'
          | translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'allocationOfFoundationFunds')
          | translate}}</h4>
      </div>

      <p> {{ messageTranslationPrefix+"totalFundsAmountIs" | translate }} {{
        gettotalFundsAmount() | number: '1.0-2' }} {{ 'AED' | translate}}</p>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"foundingMemberEID" | translate
                  }}</th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"NatureOfFundsAllocated" |
                  translate }}</th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"DescriptionOfInKindFundsEn" |
                  translate }}</th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"DescriptionOfInKindFundsAr" |
                  translate }}</th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"FundsAmount" | translate }}
                </th>
                <th class="align-middle text-center" scope="col">{{
                  messageTranslationPrefix+"AllocationCycle" | translate
                  }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let fund of paginateDataAllocationOfFoundationFunds; let i = index">
                <td class="align-middle">{{ (pageAllocationOfFoundationFunds -
                  1) * pageSizeAllocationOfFoundationFunds + i
                  + 1 }}</td>
                <td class="align-middle">{{ fund?.Contact?.EmirateId }}</td>
                <td class="align-middle">{{
                  getNatureOfAllocatedFunds(fund?.NatureOfAllocatedFunds)
                  }}</td>
                <td class="align-middle" lang="en">{{ fund?.Description }}</td>
                <td class="align-middle" lang="ar">{{ fund?.DescriptionAr}}</td>
                <td class="align-middle">
                  {{ fund?.ValueOfTheFund | number: '1.0-2' }} {{ 'AED' | translate}}
                </td>
                <td class="align-middle">{{
                  getAllocationCycle(fund?.AllocationCycle) }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountAllocationOfFoundationFunds}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageAllocationOfFoundationFunds"
            [pageSize]="pageSizeAllocationOfFoundationFunds" [collectionSize]="filteredCountAllocationOfFoundationFunds"
            (pageChange)="getPremiumDataAllocationOfFoundationFunds()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize"
            [(ngModel)]="pageSizeAllocationOfFoundationFunds" (change)="getPremiumDataAllocationOfFoundationFunds()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (fund of paginateDataAllocationOfFoundationFunds; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'foundingMemberEID' | translate }}:</div>
              <div class="dynamic-value">{{ fund?.Contact?.EmirateId }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'NatureOfFundsAllocated' | translate }}:</div>
              <div class="dynamic-value">{{
                getNatureOfAllocatedFunds(fund?.NatureOfAllocatedFunds) }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'DescriptionOfInKindFundsEn' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ fund?.Description }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'DescriptionOfInKindFundsAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{ fund?.DescriptionAr
                }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'FundsAmount' | translate }}:</div>
              <div class="dynamic-value">{{ fund?.ValueOfTheFund |
                currency:'AED' }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'AllocationCycle' | translate }}:</div>
              <div class="dynamic-value">{{
                getAllocationCycle(fund?.AllocationCycle) }}</div>
            </div>
          </div>
        </div>
        }
      </div>
    </mat-expansion-panel>

    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{messageTranslationPrefix+'boardOfTrustees' |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesInformation')
          | translate}}</h4>
      </div>
      <div class="figma-review-card-container">
        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'frequencyOfMeetings'"
          [isPlain]="true" [value]="getFrequencyOfMeetings(formData?.BoardOfTrusteesForm?.frequencyOfMeetings)"></div>

        <div summary-row class="figma-review-card" [label]="messageTranslationPrefix+'frequencyOfAppointments'"
          [isPlain]="true" [value]="getFrequencyOfAppointments(formData?.BoardOfTrusteesForm?.frequencyOfAppointments)">
        </div>
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesConditions')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"Id" | translate}}</th>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"conditionForNominationEn"
                  | translate}}
                </th>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"conditionForNominationAr"
                  | translate}}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let membershipCondition of paginateDataBoardOfTrusteeMembershipConditions; let i = index">
                <td class="align-middle">{{
                  (pageBoardOfTrusteeMembershipConditions - 1) *
                  pageSizeBoardOfTrusteeMembershipConditions + i + 1 }}</td>
                <td class="align-middle" lang="en">{{ membershipCondition?.Name
                  }}</td>
                <td class="align-middle" lang="ar">{{
                  membershipCondition?.NameAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountBoardOfTrusteeMembershipConditions}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfTrusteeMembershipConditions"
            [pageSize]="pageSizeBoardOfTrusteeMembershipConditions"
            [collectionSize]="filteredCountBoardOfTrusteeMembershipConditions"
            (pageChange)="getPremiumDataBoardOfTrusteeMembershipConditions()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize"
            [(ngModel)]="pageSizeBoardOfTrusteeMembershipConditions"
            (change)="getPremiumDataBoardOfTrusteeMembershipConditions()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (membershipCondition of
        paginateDataBoardOfTrusteeMembershipConditions; track $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'conditionForNominationEn' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'conditionForNominationAr' | translate }}:</div>
              <div class="dynamic-value" lang="ar">{{
                membershipCondition?.NameAr }}</div>
            </div>
          </div>
        </div>
        }
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesPositions')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"Id" | translate}}</th>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"administrativePositionTitleEn"
                  |
                  translate}}</th>
                <th class="align-middle" scope="col">{{messageTranslationPrefix+"administrativePositionTitleAr"
                  |
                  translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let membershipCondition of paginateDataBoardOfTrusteePositions; let i = index">
                <td class="align-middle">{{ (pageBoardOfTrusteePositions - 1) *
                  pageSizeBoardOfTrusteePositions + i + 1 }}
                </td>
                <td class="align-middle" lang="en">{{ membershipCondition?.Name
                  }}</td>
                <td class="align-middle" lang="ar">{{
                  membershipCondition?.NameAr }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountBoardOfTrusteePositions}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfTrusteePositions"
            [pageSize]="pageSizeBoardOfTrusteePositions" [collectionSize]="filteredCountBoardOfTrusteePositions"
            (pageChange)="getPremiumDataBoardOfTrusteePositions()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize" [(ngModel)]="pageSizeBoardOfTrusteePositions"
            (change)="getPremiumDataBoardOfTrusteePositions()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (membershipCondition of paginateDataBoardOfTrusteePositions; track
        $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'administrativePositionTitleEn' | translate }}</div>
              <div class="dynamic-value" lang="en">{{ membershipCondition?.Name
                }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'administrativePositionTitleAr' | translate }}</div>
              <div class="dynamic-value" lang="ar">{{
                membershipCondition?.NameAr }}</div>
            </div>
          </div>
        </div>
        }
      </div>

      <div class="not_card">
        <h4 class="title_wrapper">{{(messageTranslationPrefix+'boardOfTrusteesMembers')
          | translate}}</h4>
      </div>

      <div class="col-12 table-container not_card d-block_mobile">
        <div class="table-responsive">
          <table class="my-table">
            <thead>
              <tr>
                <th class="align-middle text-center" scope="col">#</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"founderEmiratesID"
                  |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"dateOfBirth"
                  |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"founderNameEnglish"
                  |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">
                  {{messageTranslationPrefix+"founderNationality"
                  |translate}}</th>
                <th class="align-middle text-center text-wrap" scope="col">{{messageTranslationPrefix+"Position"
                  |translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let item of paginateDataBoardOfTrusteeMembershipMembers; let i = index">
                <td class="align-middle text-center">{{
                  (pageBoardOfTrusteeMembershipMembers - 1) *
                  pageSizeBoardOfTrusteeMembershipMembers + i + 1 }}</td>
                <td class="align-middle text-center">{{ item?.Contact?.EmirateId
                  }}</td>
                <td class="align-middle text-center">{{ item?.Contact?.Dob |date: 'dd/MM/yyyy' }}</td>
                <td class="align-middle text-center" lang="en">{{this.canSeeFoundingMember(item?.SatusReason) ? item?.Name:''}}</td>
                <td class="align-middle text-center" lang="en">{{this.canSeeFoundingMember(item?.SatusReason) ? item?.Nationality:'' }}</td>
                <td class="align-middle text-center">{{ LanguageService.IsArabic? item?.Position?.NameAr : item?.Position?.Name}}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="d-flex justify-content-between align-items-center p-2 aegov-pagination">
          <span class="col-2">{{'Total count' | translate}}:
            {{filteredCountBoardOfTrusteeMembershipMembers}}</span>
          <ngb-pagination [maxSize]="5" [ellipses]="true" [(page)]="pageBoardOfTrusteeMembershipMembers"
            [pageSize]="pageSizeBoardOfTrusteeMembershipMembers"
            [collectionSize]="filteredCountBoardOfTrusteeMembershipMembers"
            (pageChange)="getPremiumDataBoardOfTrusteeMembershipMembers()">
            <ng-template ngbPaginationPrevious>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M16.0306 18.9694C16.1003 19.039 16.1556 19.1218 16.1933 19.2128C16.231 19.3039 16.2504 19.4014 16.2504 19.5C16.2504 19.5985 16.231 19.6961 16.1933 19.7872C16.1556 19.8782 16.1003 19.9609 16.0306 20.0306C15.9609 20.1003 15.8782 20.1556 15.7872 20.1933C15.6961 20.231 15.5985 20.2504 15.5 20.2504C15.4015 20.2504 15.3039 20.231 15.2128 20.1933C15.1218 20.1556 15.0391 20.1003 14.9694 20.0306L7.46938 12.5306C7.39964 12.461 7.34433 12.3782 7.30658 12.2872C7.26884 12.1961 7.24941 12.0986 7.24941 12C7.24941 11.9014 7.26884 11.8038 7.30658 11.7128C7.34433 11.6217 7.39964 11.539 7.46938 11.4694L14.9694 3.96936C15.1101 3.82863 15.301 3.74957 15.5 3.74957C15.699 3.74957 15.8899 3.82863 16.0306 3.96936C16.1714 4.1101 16.2504 4.30097 16.2504 4.49999C16.2504 4.69901 16.1714 4.88988 16.0306 5.03061L9.06031 12L16.0306 18.9694Z" />
              </svg>
              <span class="d-none d-lg-block">{{'Previous' | translate}}</span>
            </ng-template>
            <ng-template ngbPaginationNext>
              <span class="d-none d-lg-block">{{'Next' | translate}}</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24">
                <path
                  d="M17.5306 12.5306L10.0306 20.0306C9.96093 20.1003 9.87821 20.1556 9.78716 20.1933C9.69612 20.231 9.59854 20.2504 9.49999 20.2504C9.40144 20.2504 9.30386 20.231 9.21282 20.1933C9.12177 20.1556 9.03905 20.1003 8.96936 20.0306C8.89968 19.9609 8.84441 19.8782 8.80669 19.7872C8.76898 19.6961 8.74957 19.5985 8.74957 19.5C8.74957 19.4014 8.76898 19.3039 8.80669 19.2128C8.84441 19.1218 8.89968 19.039 8.96936 18.9694L15.9397 12L8.96936 5.03061C8.82863 4.88988 8.74957 4.69901 8.74957 4.49999C8.74957 4.30097 8.82863 4.1101 8.96936 3.96936C9.11009 3.82863 9.30097 3.74957 9.49999 3.74957C9.69901 3.74957 9.88988 3.82863 10.0306 3.96936L17.5306 11.4694C17.6003 11.539 17.6557 11.6217 17.6934 11.7128C17.7312 11.8038 17.7506 11.9014 17.7506 12C17.7506 12.0986 17.7312 12.1961 17.6934 12.2872C17.6557 12.3782 17.6003 12.461 17.5306 12.5306Z" />
              </svg>
            </ng-template>
          </ngb-pagination>
          <select class="form-select " style="width: auto" name="pageSize"
            [(ngModel)]="pageSizeBoardOfTrusteeMembershipMembers"
            (change)="getPremiumDataBoardOfTrusteeMembershipMembers()">
            <option [ngValue]="5">5 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="10">10 {{'itemsPerPage' | translate}}</option>
            <option [ngValue]="50">50 {{'itemsPerPage' | translate}}</option>
          </select>
        </div>
      </div>

      <div class="figma-card-container">
        @for (item of paginateDataBoardOfTrusteeMembershipMembers; track
        $index){
        <div class="figma-card">
          <div class="figma-card-content">
            <div class="figma-card-field">
              <span class="static-value">#</span>
              <span class="dynamic-value">{{ $index + 1 }}</span>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderEmiratesID' | translate }}:</div>
              <div class="dynamic-value">{{ item?.Contact?.EmirateId }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'dateOfBirth' | translate }}:</div>
              <div class="dynamic-value">{{ item?.Contact?.Dob | date:
                'dd/MM/yyyy' }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderNameEnglish' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ item?.Name }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix +
                'founderNationality' | translate }}:</div>
              <div class="dynamic-value" lang="en">{{ item?.Nationality }}</div>
            </div>
            <div class="figma-card-field">
              <div class="static-value">{{ messageTranslationPrefix + 'Position'
                | translate }}:</div>
              <div class="dynamic-value">{{ getPosition(item.PositionId)}}</div>
            </div>
          </div>
        </div>
        }
      </div>

    </mat-expansion-panel>
    }

    <mat-expansion-panel (opened)="panelOpenState = true" (closed)="panelOpenState = false">
      <mat-expansion-panel-header>
        <mat-panel-title>{{(messageTranslationPrefix+'uploadDocuments') |
          translate}}</mat-panel-title>
      </mat-expansion-panel-header>

      @for (file of documents; track $index) {
      <div class="figma-review-card-container">
        <span class="figma-review-card">
          <h4>
            <strong [lang]="LanguageService.IsArabic ? 'ar':'en'">{{getDocTypeName(file?.DocumentType)}} : </strong>
          </h4>
          <a [href]="generateDownloadLink(file)" [download]="file?.FileName">{{file?.FileName}}</a>
        </span>
      </div>
      }
    </mat-expansion-panel>
  </mat-accordion>
</div>

<div class="button_gp">
  <ng-container *ngIf="!isNotAllowedToEdit">
    <button [lang]="LanguageService.IsArabic ? 'ar' : 'en'" type="button" class="btn basic-filled-button"
      (click)="previous.emit()">
      {{'Previous' | translate}}
    </button>
    <button type="button" class="btn basic-button" (click)="submit(true)">
      {{ messageTranslationPrefix+"saveAsDraft" | translate }}
    </button>
  </ng-container>
  <button *ngIf="requestId" type="button" class="btn btn-primary basic-filled-button" (click)="submit(false)">  {{"submitRequest" |translate}}</button>
</div>
