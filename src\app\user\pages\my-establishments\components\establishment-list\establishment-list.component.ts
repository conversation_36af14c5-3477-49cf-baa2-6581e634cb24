import {
  Component,
  Injector,
  OnInit,
  Query<PERSON>ist,
  ViewChildren,
} from '@angular/core';
import {
  NgbdSortableHeader,
  SortEvent,
} from '../../../../../shared/directives/sortable.diractive';
import { MyEstablishmentComponentBase } from '../../models/base/my-establishment-component-base';
import { FormGroup } from '@angular/forms';
import { ServiceCatalogue } from '../../../../../shared/enums/service-catalogue.enum';
import { LegalFormTypesEnum } from '../../enums/legal-form-types.enum';

@Component({
  selector: 'app-establishment-list',
  templateUrl: './establishment-list.component.html',
  styleUrls: ['./establishment-list.component.scss'],
})
export class EstablishmentListComponent
  extends MyEstablishmentComponentBase
  implements OnInit {
  submitForm: FormGroup;
  apps: any;
  userInfo: any;
  statusId = '-1';
  page = 1;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 100];
  filteredData: any = [];
  filteredCount: number;
  sortBy = 'ApplicationNumber';
  sortOrder = 'desc';
  searchTerm = '';
  NMWP_SERVICES_LEGAL_TYPE_NAME = "issue license for non-muslims worship place";
  legalFormTypeEnums = LegalFormTypesEnum;
  get ServiceCatalogue() {
    return ServiceCatalogue;
  }

  @ViewChildren(NgbdSortableHeader) headers: QueryList<NgbdSortableHeader>;
  constructor(injector: Injector) {
    super(injector);
    this.messageTranslationPrefix = 'userPages.myEstablishments.';
  }

  ngOnInit(): void {
    this.statusId = '-1';
    this.userInfo = this.AuthService.getUserInfo();
    this.getData();
  }

  onSort({ column, direction }: SortEvent) {
    this.sortBy = column;
    this.sortOrder = direction;
    this.applyFilters();
  }

  search() {
    this.applyFilters();
  }

  applyFilters() {
    this.getData();
  }

  getData() {
    const ApplicantId = this.userInfo.crmUserId;
    this.DataService.get(
      `NPOEstablishment/GetEstablishmentByApplicant?ApplicantId=${ApplicantId}&pageSize=${this.pageSize}&page=${this.page}`
    ).subscribe((res) => {
      this.filteredData = res.data;
      this.filteredCount = res?.dataCount;

      // this.filteredData = [];
      // this.filteredCount = 0;
    });
  }

  viewApplication(app: any) { }

  onSortChange(sortBy: string) {
    if (sortBy === this.sortBy) {
      this.sortOrder = this.sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = sortBy;
      this.sortOrder = 'asc';
    }
    this.applyFilters();
  }
  onFilterChange() {
    this.applyFilters();
  }
  onPageChange() {
    this.applyFilters();
  }
  onSelectChange() {
    this.applyFilters();
  }
  onStatusChange() {
    this.applyFilters();
  }

  getPageSymbol(current: number) {
    return ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11'][
      current - 1
    ];
  }

  goToMyEstablishment(accountId: any) {
    this.Router.navigate([
      `/user-pages/my-establishments/details/${accountId}/1`,
    ]);
  }

  goToNMWPEstablishment(accountId: any) {
    this.Router.navigate([
      `/user-pages/my-establishments/nmwp-management-details/${accountId}`,
    ]);
  }



  goToManageEstablishment(accountId: any) {
    this.Router.navigate([
      `/user-pages/my-establishments/management-details/${accountId}`,
    ]);
  }

  editMyEstablishment(accountId: any) {
    this.Router.navigate([
      `/user-pages/my-establishments/details/${accountId}/2`,
    ]);
  }
}
