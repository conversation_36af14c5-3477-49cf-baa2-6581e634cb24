import { Injectable } from '@angular/core';
import { ChannelType } from '../enums/channel-type.enum';

@Injectable({
    providedIn: 'root'
})
export class DeviceService {

    constructor() { }

    getDeviceType(): ChannelType {
        const userAgent = navigator.userAgent || navigator.vendor;

        if (/android|iPad|iPhone|iPod/.test(userAgent)) {
            return ChannelType.Mobile;
        } else {
            return ChannelType.Web;
        }
    }
}
