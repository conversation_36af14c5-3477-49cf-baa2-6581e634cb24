import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '../../../environments/environment';
import { Observable, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AIService {

  constructor(private http: HttpClient) { }

  // POST request
  post = (path: string, data: any): Observable<any> => this.http.post<any>(`${environment.apiBaseUrlAI}/${path}`, data);

}
