import { CanActivateFn } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

export const authGuard: CanActivateFn = (route, state) => {
  const auth = inject(AuthService);
  const translate = inject(TranslateService);
  if (auth.isAuthenticated()) {
    const redirectUrl = auth.getRedirectUrl();
    if (redirectUrl) {
      auth.clearRedirectUrl();
      return auth.router.navigate([redirectUrl]);
    }
    return true;
  } else {
    //old code
    // const currentUrl = state.url.split('?')[0];
    // const langParam = state.url.split('?')[1];
    // if (langParam && langParam.includes('lang=')) {
    //   translate.currentLang = langParam.split('=')[1];
    // }
    // auth.logout(currentUrl);
    // return false;
    //end old code

    const url = new URL(state.url, window.location.origin);
    const params = new URLSearchParams(url.search);
    const lang = params.get('lang');
    const token = params.get('token'); 
    if (lang) {
      translate.use(lang);
    }
    if(token && token.length>0)
    {
      auth.loginUDotAe(token, url.pathname);
    }
    else
    {
      auth.logout(url.pathname);
    }
    return false;
  }
};
