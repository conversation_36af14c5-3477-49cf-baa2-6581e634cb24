.container--dashboard, .application-overview__content{
    .table-listing-card{
        border-radius: 24px;
        border: 1px solid $mocdyellow;
        background: $white;
        padding: 24px;
        &__title{
            // font-family: Inter;
            font-size: 20px;
            font-weight: 700;
            line-height: 26px;
            margin-top: 0 !important;
            @media (max-width: 767px) {
                margin-bottom: 8px !important;
            }
        }
        th{
            color: $aeblack-300;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            padding: 12px 24px;
        }
        th:last-child, th:first-child{
            padding: 12px 0;
        }
        td{
            padding: 24px;
            align-content: center;
            span{
                display: block;
                margin-bottom: 6px;
            }
            img{
                border-radius: 6px;
                border: 1px solid $aeblack-100;
                width: 68px;
                height: 68px;
                @media (max-width: 767px) {
                    width: 40px;
                    height: 40px;
                }
            }
            @media (max-width: 767px) {
                &:has(> img){
                    align-content: start;
                }
                &:has(> button){
                    text-align: end;
                }
            }
        }
        td:last-child, td:first-child, .desktop-first{
            padding: 24px 0;
        }
        &__inner-title{
            color: #1E1E1E;
            font-size: 16px;
            font-weight: 700;
            line-height: 24px;
        }
        .mobile-only{
            display: none;
            @media (max-width: 767px) {
                display: table-cell;
            }
        }
        .desktop-only, thead{
            @media (max-width: 767px) {
                display: none;
            }
        }
        &__empty-state{
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            gap: 8px;
            min-height: 368px;
            h4{
                // font-family: Inter;
                font-size: 20px;
                font-style: normal;
                font-weight: 700;
                line-height: 26px;
                color: $aeblack-900;
                margin-bottom: 0px;
                @media only screen and (min-width: 1024px) {
                    font-size: 26px;
                    line-height: 32px;
                }
            }
            p{
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;
                color: #676767;
                margin-bottom: 8px;
                @media only screen and (min-width: 1024px) {
                    font-size: 18px;
                    line-height: 24px;
                    margin-bottom: 16px;
                }
            }
        }
    }
    .sm-margin{
        th{
            padding: 12px 16px;
        }
        th:last-child, th:first-child{
            padding: 12px 0;
        }
        td{
            padding: 16px;
        }
        td:last-child, td:first-child{
            padding: 16px 0;
        }
    }
    .table-listing-full-width{
        tbody{
            border-top: 1px solid $aeblack-100;
        }
        th{
            color: $aeblack-300;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            padding: 12px 10px;
        }
        tr:hover{
            td{
                background-color: $aeblack-50 !important;
            }
        }
        td{
            padding: 20px 10px;
            align-content: center;
            // align-content: start;
            p{
                margin-bottom: 8px;
            }
            @media (max-width: 767px) {
                padding: 16px 0;
                &:has(> button){
                    text-align: end;
                }
            }
        }
        &__inner-title{
            color: #1E1E1E;
            font-size: 14px;
            font-weight: 500;
            line-height: 20px;
            @media (max-width:1204px){
                text-wrap: nowrap !important;
            }
        }
        &__status{
            svg{
                margin-right: 12px;
                [ng-reflect-dir=rtl] &, [dir=rtl] & {
                    margin-right: 0;
                    margin-left: 12px;
                }
            }
            font-size: 12px;
            font-weight: 400;
            line-height: 16px; 
        }
        &__inner-title-container{
            margin-bottom: 10px;
        }
        &__new{
            width: fit-content;
            padding: 4px 10px;
            background-color: $techblue-100;
            color:  $techblue-500;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            display: inline;
            margin-left: 12px;
            [ng-reflect-dir=rtl] &, [dir=rtl] & {
                margin-right: 12px;
                margin-left: 0;
            }
        }
        .mobile-only{
            display: none;
            @media (max-width: 767px) {
                display: table-cell;
            }
        }
        .desktop-only, thead{
            @media (max-width: 767px) {
                display: none;
            }
        }
    }
    .action-popup{
        box-shadow: 0px 4px 8px 0px #0000001F;
        border-radius: 8px;
        border: none !important;
        padding: 0 !important;
        width: 250px;
        max-width: 90%;
        font-size: 14px;
        text-align: start !important;
        ul{
            list-style: none;
            cursor: pointer;
            padding: 0;
            margin: 0;
            li, .dropdown-item{
                border-radius: 8px;
                padding: 15px;
                color: $aeblack-900;
                svg{
                    margin-right: 10px;
                    [ng-reflect-dir=rtl] &, [dir=rtl] & {
                        margin-right: 0;
                        margin-left: 10px;
                    }
                }
                &.delete{
                    color: $aered-600;
                }
                span{
                    display: inline;
                }
                &:hover{
                    background-color: $aegold-100 !important;
                }
            }

            @media (max-width: 1024px){
                padding: 10px 0 !important;
            }
        }
    }
}
.page-title-wrapper{
    padding: 0 24px;
    .page-title{
        // font-family: Inter;
        font-size: 48px;
        font-weight: 700;
        line-height: 56px;
        margin-bottom: 24px;
        @media (max-width: 767px) {
            font-size: 32px;
            font-weight: 700;
            line-height: 38px;
        }
    }
    .page-subtitle{
        color: $aeblack-950;
        font-size: 30px;
        font-weight: 400;
        line-height: 36px;
        @media (max-width: 767px) {
            color: #676767;
            font-size: 24px;
            font-weight: 400;
            line-height: 30px;
        }
    }
}
/* Hide scrollbar for Chrome, Safari and Opera */
.application-overview__filters-tabs-parent::-webkit-scrollbar {
    display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.application-overview__filters-tabs-parent {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.container--dashboard {
    display: flex;
    flex-direction: column;
    row-gap: 32px;
    
    @media (max-width:1024px){
        row-gap: 1rem !important;
    }
}