import { <PERSON><PERSON>iewInit, ChangeDetector<PERSON>ef, Component, Injector, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { MyEstablishmentComponentBase } from '../../models/base/my-establishment-component-base';
import { StepsFullJournyEnum, SummaryStepsFullJourny } from '../../../../../shared/models/summry-steps-full-journey';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { JOURNEY_STEPS } from '../../../../../e-services/npo-license-declaration/models/npo-lookup-data';
import { letterPatternValidator } from '../../../../../shared/validators/letter-pattern-validator';
import { customEmailValidator } from '../../../../../shared/validators/custom.email.validator';
import { flexibleletterPatternValidator } from '../../../../../shared/validators/flexible-letter-pattern-validator';
import { numberValidator } from '../../../../../shared/validators/number-validator';
import { optionalNumberValidator } from '../../../../../shared/validators/optional-number-validator';
import { letterPatternValidatorAcceptNumbers } from '../../../../../shared/validators/letter-pattern-validator-accept-numbers';
import { ActionFormType } from '../../../../../e-services/npo-license-declaration/enums/action-form-type';
import { Feedback } from '../../../../../e-services/npo-license-declaration/models/feedback';
import { Lookup } from '../../../../../shared/models/lookup.model';
import { EstablishmentFoundingMembersComponent } from '../nested-components/establishment-founding-members/establishment-founding-members.component';
import { EstablishmentAllocationOfFoundationFundsComponent } from '../nested-components/establishment-allocation-of-foundation-funds/establishment-allocation-of-foundation-funds.component';
import { EstablishmentBasicInformationComponent } from '../nested-components/establishment-basic-information/establishment-basic-information.component';
import { EstablishmentBoardOfDirectorsComponent } from '../nested-components/establishment-board-of-directors/establishment-board-of-directors.component';
import { EstablishmentBoardOfTrusteesComponent } from '../nested-components/establishment-board-of-trustees/establishment-board-of-trustees.component';
import { EstablishmentFundServicesComponent } from '../nested-components/establishment-fund-services/establishment-fund-services.component';
import { EstablishmentInterimCommitteeComponent } from '../nested-components/establishment-interim-committee/establishment-interim-committee.component';
import { EstablishmentMembershipEnrollmentComponent } from '../nested-components/establishment-membership-enrollment/establishment-membership-enrollment.component';
import { EstablishmentObjectiveComponent } from '../nested-components/establishment-objective/establishment-objective.component';
import { EstablishmentUploadDocumentsComponent } from '../nested-components/establishment-upload-documents/establishment-upload-documents.component';
import { EstablishmentAllocationOfFoundationFundsByDecreeComponent } from '../nested-components/nested-by-decree-components/e-allocation-of-foundation-funds-by-decree/e-allocation-of-foundation-funds-by-decree.component';
import { EstablishmentBoardOfDirectorsByDecreeComponent } from '../nested-components/nested-by-decree-components/establishment-board-of-directors-by-decree/establishment-board-of-directors-by-decree.component';
import { EstablishmentBoardOfTrusteesByDecreeComponent } from '../nested-components/nested-by-decree-components/establishment-board-of-trustees-by-decree/establishment-board-of-trustees-by-decree.component';
import { EstablishmentFoundingMembersByDecreeComponent } from '../nested-components/nested-by-decree-components/establishment-founding-members-by-decree/establishment-founding-members-by-decree.component';
import { EstablishmentReviewAndSubmitComponent } from '../nested-components/establishment-review-and-submit/establishment-review-and-submit.component';
import { editEstablishmentNumberValidator } from '../../../../../shared/validators/edit-establishment-number-validator';

@Component({
  selector: 'app-establishment-request-stepper',
  templateUrl: './establishment-request-stepper.component.html',
  styleUrls: ['./establishment-request-stepper.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class EstablishmentRequestStepperComponent extends MyEstablishmentComponentBase
  implements OnInit, OnDestroy, AfterViewInit {
  journeySteps: SummaryStepsFullJourny[] = [];

  stepperForm: FormGroup;
  requestId: string | null;
  isReturnForUpdate: boolean = false;
  isNotAllowedToEdit: boolean = false;
  requestStatusCode: number = 1;

  //#region "getter"
  get basicInformation(): FormGroup {
    return this.stepperForm.get('basicInformation') as FormGroup;
  }

  get objectives(): FormGroup {
    return this.stepperForm.get('objectives') as FormGroup;
  }

  get fundServices(): FormGroup {
    return this.stepperForm.get('fundServices') as FormGroup;
  }

  get interimCommittee(): FormGroup {
    return this.stepperForm.get('interimCommittee') as FormGroup;
  }

  get foundingMembers(): FormGroup {
    return this.stepperForm.get('foundingMembers') as FormGroup;
  }

  get membership(): FormGroup {
    return this.stepperForm.get('membership') as FormGroup;
  }

  get boardOfDirectors(): FormGroup {
    return this.stepperForm.get('boardOfDirectors') as FormGroup;
  }

  get boardOfTrustees(): FormGroup {
    return this.stepperForm.get('boardOfTrustees') as FormGroup;
  }

  get documents(): FormGroup {
    return this.stepperForm.get('documents') as FormGroup;
  }

  get summary() {
    return this.stepperForm.get('summary') as FormGroup;
  }

  get allocationOfFoundationFunds(): FormGroup {
    return this.stepperForm.get('allocationOfFoundationFunds') as FormGroup;
  }



  userInfo: any;

  selectedLegalFormType;
  //#endregion

  //#region "ctor"
  @ViewChild('stepper') stepper: MatStepper;


  @ViewChild('reviewAndSubmitRef') reviewAndSubmitRef: EstablishmentReviewAndSubmitComponent | undefined;

  @ViewChild('foundingMemberTemplate') foundingMemberTemplate: EstablishmentFoundingMembersComponent;
  @ViewChild('objectiveTemplate') objectiveTemplate: EstablishmentObjectiveComponent;
  @ViewChild('basicInformationTemplate') basicInformationTemplate: EstablishmentBasicInformationComponent;
  @ViewChild('interimCommitteeTemplate') interimCommitteeTemplate: EstablishmentInterimCommitteeComponent;
  @ViewChild('boardOfDirectorsTemplate') boardOfDirectorsTemplate: EstablishmentBoardOfDirectorsComponent;
  @ViewChild('memberShipEnrollmentTemplate') memberShipEnrollmentTemplate: EstablishmentMembershipEnrollmentComponent;
  @ViewChild('boardOfTrusteesTemplate') boardOfTrusteesTemplate: EstablishmentBoardOfTrusteesComponent;
  @ViewChild('allocationOfFoundationFundsTemplate') allocationOfFoundationFundsTemplate: EstablishmentAllocationOfFoundationFundsComponent;
  @ViewChild('fundServicesTemplate') fundServicesTemplate: EstablishmentFundServicesComponent;
  @ViewChild('uploadDocumentsTemplate') uploadDocumentsTemplate: EstablishmentUploadDocumentsComponent;



  @ViewChild('foundingMemberByDecreeTemplate') foundingMemberByDecreeTemplate: EstablishmentFoundingMembersByDecreeComponent;
  @ViewChild('boardOfDirectorsByDecreeTemplate') boardOfDirectorsByDecreeTemplate: EstablishmentBoardOfDirectorsByDecreeComponent;
  @ViewChild('boardOfTrusteesByDecreeTemplate') boardOfTrusteesByDecreeTemplate: EstablishmentBoardOfTrusteesByDecreeComponent;
  @ViewChild('allocationOfFoundationFundsByDecreeTemplate') allocationOfFoundationFundsByDecreeTemplate: EstablishmentAllocationOfFoundationFundsByDecreeComponent;
  constructor(injector: Injector, private cdr: ChangeDetectorRef) {
    super(injector);
    this.userInfo = this.AuthService.getUserInfo();
    this.MyEstablishmentService.getLookupData();
    this.intiStepperForm();
    this.getLegalFormTypes();

  }

  //#endregion


  ngOnInit(): void {
    window.scrollTo(0, 0);
    this.checkNpoRequest();
    setTimeout(() => {
      this.stepper.linear = false;
    }, 500);
    this.intiJourneySteps(JOURNEY_STEPS);
    this.cdr.detectChanges();

    this.StepperService.detectedChangeSubject$.subscribe(val$ => {
      if (val$ == 1) {
        this.checkNpoRequest(this.StepperService.requestId, true);
      }
    })
  }

  ngAfterViewInit() {
    this.stepper.selectionChange.subscribe(_ => {
      let type = this.ActivatedRoute.snapshot.paramMap.get('type');
      if (Number(type) === 2) {
        this.saveIntoBackround();
      }
    });
  }

  intiStepperForm = (): void => {
    this.stepperForm = this.FormBuilder.group({
      basicInformation: this.intiBasicInformationForm(),
      objectives: this.intiObjectivesForm(),
      fundServices: this.intiFundServicesForm(),
      interimCommittee: this.intiInterimCommitteeForm(),
      foundingMembers: this.initFoundingMembersForm(),
      membership: this.initMembershipForm(),
      boardOfDirectors: this.intiBoardofDirectorsForm(),
      boardOfTrustees: this.intiboardOfTrusteesForm(),
      documents: this.intiDocumentsForm(),
      summary: this.initSummaryForm(),
      allocationOfFoundationFunds: this.initAllocationOfFoundationFundsForm()
    });
  };

  intiBasicInformationForm = (): FormGroup =>
    this.FormBuilder.group({
      LegalFormId: new FormControl('', []),

      LocalDecreeLawNumber: new FormControl(''),
      IssuanceDate: new FormControl('', []),
      // AssociationClassification: new FormControl('', []),

      FirstProposedNameEn: ['', []],
      FirstProposedNameAr: ['', []],

      LandlineNumber: new FormControl(''),
      POBox: new FormControl('', [Validators.maxLength(5)]),
      Email: new FormControl('', [customEmailValidator()]),
      Website: new FormControl('', [Validators.pattern(/^(https?:\/\/|www\.)[^\s\/:?#]+\.[a-zA-Z]{2,}$/)]),
      Address: new FormControl(''),

      GeographicLocation: new FormControl(''),

      Emirate: new FormControl('', []),

      PermanentAdvance: new FormControl(0, []),

      proposedNames: this.FormBuilder.array([],),
      targetGroups: this.FormBuilder.array([]),


      FundsAllocationAmount: new FormControl(''),
      Numberofemployees: new FormControl(''),
      EntityName: new FormControl(''),
      EntityNameAr: new FormControl(''),
      Entitysectortype: new FormControl(''),

      EstablishmentId: new FormControl(''),


      approvedNameAr: new FormControl(''),
      approvedNameEn: new FormControl(''),
      name: new FormControl(''),
      namear: new FormControl(''),

      NpoActivityPrograms: this.FormBuilder.array([]),
    });

  intiObjectivesForm = (): FormGroup =>
    this.FormBuilder.group({
      objectives: this.FormBuilder.array([]),
    });


  initAllocationOfFoundationFundsForm = (): FormGroup =>
    this.FormBuilder.group({
      funds: this.FormBuilder.array([]),
    });

  intiFundServicesForm = (): FormGroup =>
    this.FormBuilder.group({
      fundServices: this.FormBuilder.array([]),
    });

  intiInterimCommitteeForm = (): FormGroup =>
    this.FormBuilder.group({
      MeetingPlace: ["", []],
      Emirate: ["",],
      Date: ["",],
      Agenda: [""],
      Definetheadministrativepositions: new FormControl("", []),
      Appointthecommissioner: new FormControl("", []),
      MeetingId: [""],
      members: this.FormBuilder.array([]),
    });

  initFoundingMembersForm = (): FormGroup =>
    this.FormBuilder.group({
      LegalFormId: [""],
      IsFoundingMembersHoldingTheNationalityIsLessThan70: [""],
      ExceptionReasonFor70Ar: [""],
      ExceptionReasonFor70En: [""],
      IsNumberOfFoundingMembersIsLessThan7Members: [""],
      ExceptionReasonFor7Ar: [""],
      ExceptionReasonFor7En: [""],
      ageIsLessThan70Id: [""],
      ageIsLessThan7Id: [""],
      MeetingId: new FormControl(""),
      MeetingPlace: new FormControl(""),
      Emirate: new FormControl(""),
      Date: new FormControl("", []),
      Agenda: new FormControl(""),
      Discussingtheestablishment: new FormControl(""),
      Preparingthedraftstatute: new FormControl(""),
      Electionofmembersofthetemporarycommittee: new FormControl(""),
      members: this.FormBuilder.array([]),
      NPOUnifiedNumbersList: this.FormBuilder.array([]),
    });

  initMembershipForm = (): FormGroup =>
    this.FormBuilder.group({
      MembershipFees: new FormControl("", [editEstablishmentNumberValidator()]),
      BeneficiaryMembershipFees: new FormControl(""),
      AnnualMembershipDueDate: (''),
      EnrollmentFees: ["", [editEstablishmentNumberValidator()]],
      LegalFormId: (''),

      MembershipConditions: this.FormBuilder.array([]),
    });

  intiBoardofDirectorsForm = (): FormGroup =>
    this.FormBuilder.group({
      LegalFormId: new FormControl(""),
      MemberIsExceeds: new FormControl("", []),
      ExceptionReasonId: new FormControl(""),
      ExceptionReasonEn: new FormControl("", [letterPatternValidatorAcceptNumbers('en')]),
      ExceptionReasonAr: new FormControl("", [letterPatternValidatorAcceptNumbers('ar')]),
      FrequencyOfMonthlyBoardMeetings: new FormControl(""),
      // BoardNumbers: new FormControl("", [, Validators.min(5), Validators.max(11)]),
      BoardNumbers: new FormControl(""),
      BoardElectionCycle: new FormControl(""),
      CanBeRenominated: new FormControl(""),
      NumberOfPermissibleTerms: new FormControl(""),
      ElectionMethod: new FormControl(""),
      localBoardMembersPercentage: new FormControl("", [Validators.min(70), Validators.max(100), Validators.pattern('^[0-9]{1,10}$')]),
      Conditions: this.FormBuilder.array([]),
      Positions: this.FormBuilder.array([]),
    });

  intiboardOfTrusteesForm = (): FormGroup =>
    this.FormBuilder.group({
      frequencyOfMeetings: (''),
      // numberOfMembers: ['', [, Validators.pattern('^[0-9]+$')]],
      frequencyOfAppointments: (''),

      Conditions: this.FormBuilder.array([]),
      Positions: this.FormBuilder.array([]),
      members: this.FormBuilder.array([])
    });

  intiDocumentsForm = (): FormGroup => this.FormBuilder.group({});
  initSummaryForm = (): FormGroup => this.FormBuilder.group({});

  goNext = (): void => this.stepper.next();
  goPrevious = (): void => this.stepper.previous();

  submit = (): void => {
    if (this.stepperForm.invalid) {
      this.stepperForm.markAllAsTouched();
      this.Toastr.error('Invalid Form!', 'Failure');
    } else {
      this.Toastr.success('Valid Form!', 'Success');
    }
  }

  approvedEsablishmentName: any;
  esablishmentStatusCode: number | undefined;
  establishmentRequestId: string | undefined;
  npoRequestId: string | undefined;
  migrationID: string | undefined;

  proposedNameEn: string | undefined;
  proposedNameAr: string | undefined;
  requestNumber: string | undefined;

  checkNpoRequest = (requestId$: string | null = null, isRerender: boolean = false): void => {
    let establishmentId = this.ActivatedRoute.snapshot.paramMap.get('establishmentId');
    let requestId = requestId$ ?? this.ActivatedRoute.snapshot.paramMap.get('id');
    let type = this.ActivatedRoute.snapshot.paramMap.get('type');
    if (Number(type) === 2) { // edit mode
      this.isNotAllowedToEdit = false;
    } else { // view mode
      this.isNotAllowedToEdit = true;
      this.disableAllForms();
    }
    // to add condition to enable or disable forms based on query param (isMigrated)
    // to add condition to enable or disable forms based on query param (isMigrated)
    this.StepperService.requestId = requestId ?? "";
    let consumeningCall = this.MyEstablishmentService.getEstablishmentDeclarationBYID(establishmentId);

    if (requestId !== null) {
      consumeningCall = this.MyEstablishmentService.getNpoRequestForm(requestId);
    }
    this.establishmentRequestId = establishmentId ?? '';

    if (establishmentId !== null || requestId !== null) {
      this.MyEstablishmentService.FORM_MODE = ActionFormType.UPDATE;

      consumeningCall.subscribe((res) => {
        if (res.Status == 2000) {
          if (isRerender) {
            this.StepperService.setRerenderRequestData(res?.data[0]);
          } else {
            this.StepperService.setRequestData(res?.data[0]);
          }

          this.StepperService.requestId = requestId ?? "";
          this.StepperService.requestCode = res?.data[0]?.StatusCode;

          this.selectedLegalFormType = res?.data[0]?.BasicInformationForm?.npoform;

          this.npoRequestId = res?.data[0]?.NpoRequest;

          this.approvedEsablishmentName = {
            name: res?.data[0]?.BasicInformationForm?.name,
            namear: res?.data[0]?.BasicInformationForm?.namear
          }

          this.basicInformation.get("FirstProposedNameEn")?.setValue(res?.data[0]?.BasicInformationForm?.name);
          this.basicInformation.get("FirstProposedNameAr")?.setValue(res?.data[0]?.BasicInformationForm?.namear);

          this.proposedNameEn = res?.data[0]?.BasicInformationForm?.name;
          this.proposedNameAr = res?.data[0]?.BasicInformationForm?.namear;

          this.basicInformation.get('EstablishmentId')?.setValue(establishmentId);
          this.basicInformation.get('LegalFormId')?.setValue(this.selectedLegalFormType);

          this.foundingMembers.get('LegalFormId')?.setValue(this.selectedLegalFormType);
          this.membership.get('LegalFormId')?.setValue(this.selectedLegalFormType);
          this.boardOfDirectors.get('LegalFormId')?.setValue(this.selectedLegalFormType);

          this.requestStatusCode = res?.data[0]?.StatusCode;
          this.migrationID = res?.data[0]?.BasicInformationForm?.MigrationID;

          if (requestId !== null) {
            this.getApprovalNames();
            this.requestNumber = res?.data[0]?.BasicInformationForm?.name;
          }


          if (res?.data[0]?.StatusCode == 100000002) {
            this.isReturnForUpdate = true;
            this.isNotAllowedToEdit = false;
            this.feedbackList = res?.data[1]?.FeedbackList;
          }

        } else {
          let errorMessage = '';
          if (res.Status !== 2000) {
            errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
          }
          this.NotifyService.showError('notify.error', errorMessage.trim());
        }
      }, error => {
        this.NotifyService.showError('notify.error', error);
      });
    }
  }

  // get progress(): number {
  //   let progressTotal = 0;
  //   switch (this.selectedLegalFormType) {
  //     case this.LEGAL_FORM_TYPES.Association:
  //       this.isBasicInformationValidForm() ? progressTotal += 25 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 20 : 0;
  //       this.isFoundingMembersValidForm() ? progressTotal += 25 : 0;
  //       this.isInterimCommitteeValidForm() ? progressTotal += 10 : 0;
  //       this.isMemberShipEnrollmentValidForm() ? progressTotal += 10 : 0;
  //       this.isBoardOfDirectorsValidForm() ? progressTotal += 10 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //     case this.LEGAL_FORM_TYPES.NationalSociety:
  //       this.isBasicInformationValidForm() ? progressTotal += 30 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 20 : 0;
  //       this.isFoundingMembersValidForm() ? progressTotal += 25 : 0;
  //       this.isBoardOfTrusteesValidForm() ? progressTotal += 25 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //     case this.LEGAL_FORM_TYPES.Union:
  //       this.isBasicInformationValidForm() ? progressTotal += 20 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 15 : 0;
  //       this.isFoundingMembersValidForm() ? progressTotal += 15 : 0;
  //       this.isInterimCommitteeValidForm() ? progressTotal += 15 : 0;
  //       this.isMemberShipEnrollmentValidForm() ? progressTotal += 15 : 0;
  //       this.isBoardOfDirectorsValidForm() ? progressTotal += 20 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //     case this.LEGAL_FORM_TYPES.SocialSolidarityFunds:
  //       this.isBasicInformationValidForm() ? progressTotal += 30 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 20 : 0;
  //       this.isFoundingMembersValidForm() ? progressTotal += 10 : 0;
  //       this.isInterimCommitteeValidForm() ? progressTotal += 15 : 0;
  //       this.isMemberShipEnrollmentValidForm() ? progressTotal += 10 : 0;
  //       this.isBoardOfDirectorsValidForm() ? progressTotal += 15 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //     case (LEGAL_FORM_TYPES.AssociationByDecree):
  //       this.isBasicInformationValidForm() ? progressTotal += 55 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 35 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //     case (this.LEGAL_FORM_TYPES.NationalSocietyByDecree):
  //       this.isBasicInformationValidForm() ? progressTotal += 60 : 0;
  //       this.isObjectivesValidForm() ? progressTotal += 40 : 0;
  //       this.documents.valid ? progressTotal += 0 : 0;
  //       break;
  //   }

  //   return progressTotal;
  // }


  roundTo = (value: number, decimals: number): number => {
    const factor = Math.pow(10, decimals);
    return Math.round(value * factor) / factor;
  }

  get progress(): number {
    let precentage = 100 / this.journeySteps.length;
    return this.roundTo(
      (this.journeySteps.filter((_) => _.status == StepsFullJournyEnum.COMPLETE)
        .length ?? 0) * precentage,
      2
    );
  }

  isBasicInformationValidForm = (): boolean => this.basicInformationTemplate?.isValidForm();
  isObjectivesValidForm = (): boolean => this.objectiveTemplate?.isValidForm();
  isFoundingMembersValidForm = (): boolean => this.foundingMemberTemplate?.isValidForm();
  isInterimCommitteeValidForm = (): boolean => this.interimCommitteeTemplate?.isValidForm();
  isBoardOfDirectorsValidForm = (): boolean => this.boardOfDirectorsTemplate?.isValidForm();
  isMemberShipEnrollmentValidForm = (): boolean => this.memberShipEnrollmentTemplate?.isValidForm();
  isBoardOfTrusteesValidForm = (): boolean => this.boardOfTrusteesTemplate?.isValidForm();
  isAllocationOfFoundationFundsValidForm = (): boolean => this.allocationOfFoundationFundsTemplate?.isValidForm();
  isFundServicesValidForm = (): boolean => this.fundServicesTemplate?.isValidForm();
  isDocumentValidForm = (): boolean => this.uploadDocumentsTemplate?.isValidForm();

  isFoundingMembersByDecreeValidForm = (): boolean => this.foundingMemberByDecreeTemplate?.isValidForm();
  isAllocationOfFoundationFundsByDecreeValidForm = (): boolean => this.allocationOfFoundationFundsByDecreeTemplate?.isValidForm();
  isBoardOfTrusteesByDecreeValidForm = (): boolean => this.boardOfTrusteesByDecreeTemplate?.isValidForm();
  isBoardOfDirectorsByDecreeValidForm = (): boolean => this.boardOfDirectorsByDecreeTemplate?.isValidForm();


  lastselectedTabIndex: number;
  updateCurrentPortalStep = (index: number): void => {
    window.scrollTo(0, 0);
  }

  journeyIndexActiveStep: number;
  intiJourneySteps = (data: any): void => {
    this.journeySteps = [];
    (data as Array<any>).map(_ => {
      this.journeySteps.push({
        nameEn: _?.nameEn,
        nameAr: _?.nameAr,
        status: _?.IsCompleted == 'No' ? StepsFullJournyEnum.INPROGRESS : StepsFullJournyEnum.COMPLETE
      })
    });
    const lastIndex = [...this.journeySteps].reverse().findIndex(step => step.status === StepsFullJournyEnum.COMPLETE);
    const originalIndex = lastIndex !== -1 ? this.journeySteps.length - 1 - lastIndex : -1;
    this.journeyIndexActiveStep = originalIndex < 0 ? 0 : originalIndex + 1;
  }

  get legalFormTypeTitle(): string {
    let type = this.legalFromTypes.find(_ => _.ID == this.selectedLegalFormType);
    if (type) {
      return this.LanguageService.IsArabic ? type.NameArabic : type.NameEnglish;
    }
    return "";
  }

  feedbackList: Feedback[] | undefined;
  disableAllForms = (): void => {
    this.stepperForm.enable();
    const isValid = this.stepperForm.valid;
    this.stepperForm.disable();
    this.stepperForm.updateValueAndValidity();
  }

  changeStepperIndex = (sectionName: string): void => {
    switch (sectionName.toLocaleLowerCase().trim()) {
      case 'basicinformation':
        this.stepper.selectedIndex = (this.isReturnForUpdate) ? 1 : 0;
        break;
      case 'objectives':
        this.stepper.selectedIndex = (this.isReturnForUpdate) ? 2 : 1;
        break;
      case 'fundservices':
        this.stepper.selectedIndex = (this.isReturnForUpdate) ? 3 : 2;
        break;
      case 'allocationoffoundationfunds':
        this.stepper.selectedIndex = (this.isReturnForUpdate ? 4 : 3);
        break;
      case 'foundingmembers':
        this.stepper.selectedIndex = (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 4 : 3) : (this.isReturnForUpdate ? 3 : 2));
        break;
      case 'interimcommittee':
        this.stepper.selectedIndex = (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 5 : 4) : (this.isReturnForUpdate ? 4 : 3));
        break;
      case 'membership':
        this.stepper.selectedIndex = (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 4 : 3) : ((this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 6 : 5) : (this.isReturnForUpdate ? 5 : 4))));
        break;
      case 'boardofdirectors':
        this.stepper.selectedIndex = (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree) ? (this.isReturnForUpdate ? 4 : 3) : (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 5 : 4) : ((this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 7 : 6) : (this.isReturnForUpdate ? 6 : 5))));
        break;
      case 'boardoftrustees':
        this.stepper.selectedIndex = (this.isReturnForUpdate ? 5 : 4);
        break;
      case 'documents':
        this.stepper.selectedIndex = (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union
          ? (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 6 : 5) : (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 8 : 7) : (this.isReturnForUpdate ? 7 : 6)))
          : (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree ? (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree ? (this.isReturnForUpdate ? 5 : 4) : (this.isReturnForUpdate ? 6 : 5)) : this.isReturnForUpdate ? 6 : 5)
        )
        break;
      default:
        break;
    }

  }


  ngOnDestroy(): void {
    console.log("teminated stepper");
    this.StepperService.resetFormData();
    this.StepperService.resetRequestData();
    this.MyEstablishmentService.resetLookupData();
    this.StepperService.resetDetectedChangeSubject();
  }


  isSelectedAndCompleted(stepIndex: number): boolean {
    const step = this.stepper?.steps.toArray()[stepIndex];
    return !!step?.completed && this.stepper?.selectedIndex === stepIndex;
  }

  legalFromTypes: Lookup[] = [];
  getLegalFormTypes = (): void => {
    this.MyEstablishmentService.getLegalFormTypes().subscribe(_ => {
      this.legalFromTypes = _;
    }, error => {
      this.legalFromTypes
    });
  }

  get allowShowAction(): boolean {
    if ((this.isBasicInformationValidForm() && this.isObjectivesValidForm()) == false) {
      return false;
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSociety) {
      if (this.isFoundingMembersValidForm() == false) {
        return false;
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds) {
      if ((this.isInterimCommitteeValidForm() && this.isBoardOfDirectorsValidForm() && this.isMemberShipEnrollmentValidForm()) == false) {
        return false;
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union) {
      if ((this.isBoardOfDirectorsValidForm() && this.isMemberShipEnrollmentValidForm()) == false) {
        return false;
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSociety) {
      if ((this.isBoardOfTrusteesValidForm() && this.isAllocationOfFoundationFundsValidForm()) == false) {
        return false;
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree) {
      if (this.isFoundingMembersByDecreeValidForm() == false || this.isBoardOfDirectorsByDecreeValidForm() == false || this.isDocumentValidForm() == false) {
        return false;
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree) {
      if (this.isFoundingMembersByDecreeValidForm() == false || this.isAllocationOfFoundationFundsByDecreeValidForm() == false || this.isBoardOfTrusteesByDecreeValidForm() == false || this.isDocumentValidForm() == false) {
        return false;
      }
    }

    return true;
  }

  get canShowGetFoundingMemberButton(): boolean {
    return this.foundingMemberTemplate?.isAnyMemberStillDraft();
  }

  get getMeetingDate(): Date {
    return this.foundingMemberTemplate?.getMeetingDate();
  }

  saveIntoBackround = (): void => {
    if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 1 : 0)) {
      this.basicInformationTemplate.saveAsDraft(true);
    }

    if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 2 : 1)) {
      this.objectiveTemplate.saveAsDraft(true);
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree) {
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 3 : 2)) {
        this.foundingMemberByDecreeTemplate.saveAsDraft(true);
      }
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 4 : 3)) {
        this.boardOfDirectorsByDecreeTemplate.saveAsDraft(true);
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree) {
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 3 : 2)) {
        this.foundingMemberByDecreeTemplate.saveAsDraft(true);
      }
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 4 : 3)) {
        this.allocationOfFoundationFundsByDecreeTemplate.saveAsDraft(true);
      }
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 5 : 4)) {
        this.boardOfTrusteesByDecreeTemplate.saveAsDraft(true);
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds) {
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 3 : 2)) {
        this.fundServicesTemplate.saveAsDraft(true);
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSociety) {
      if (this.stepper.selectedIndex === (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 4 : 3) : (this.isReturnForUpdate ? 3 : 2))) {
        this.foundingMemberTemplate.saveAsDraft(true);
      }
    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union) {

      if (this.selectedLegalFormType != this.LEGAL_FORM_TYPES.Union) {
        if (this.stepper.selectedIndex === (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 5 : 4) : (this.isReturnForUpdate ? 4 : 3))) {
          this.interimCommitteeTemplate.saveAsDraft(true);
        }
      }

      if (this.stepper.selectedIndex === (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 4 : 3) : ((this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 6 : 5) : (this.isReturnForUpdate ? 5 : 4))))) {
        this.memberShipEnrollmentTemplate.saveAsDraft(true);
      }

      if (this.stepper.selectedIndex === (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 5 : 4) : ((this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 7 : 6) : (this.isReturnForUpdate ? 6 : 5))))) {
        this.boardOfDirectorsTemplate.saveAsDraft(true);
      }

    }

    if (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSociety) {
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 4 : 3)) {
        this.allocationOfFoundationFundsTemplate.saveAsDraft(true);
      }
      if (this.stepper.selectedIndex === (this.isReturnForUpdate ? 5 : 4)) {
        this.boardOfTrusteesTemplate.saveAsDraft(true);
      }
    }

    if (this.stepper.selectedIndex === (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Association || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union
      ? (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.Union ? (this.isReturnForUpdate ? 6 : 5) : (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.SocialSolidarityFunds ? (this.isReturnForUpdate ? 8 : 7) : (this.isReturnForUpdate ? 7 : 6)))
      : (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree || this.selectedLegalFormType == this.LEGAL_FORM_TYPES.NationalSocietyByDecree ? (this.selectedLegalFormType == this.LEGAL_FORM_TYPES.AssociationByDecree ? (this.isReturnForUpdate ? 5 : 4) : (this.isReturnForUpdate ? 6 : 5)) : this.isReturnForUpdate ? 6 : 5)
    )) {
      this.uploadDocumentsTemplate.saveAsDraft(true);
    }

  }

  getApprovalNames = (): void => {
    let establishmentId = this.ActivatedRoute.snapshot.paramMap.get('establishmentId');
    this.MyEstablishmentService.getEstablishmentDeclarationBYID(establishmentId).subscribe((res) => {
      if (res.Status == 2000) {
        this.approvedEsablishmentName = {
          name: res?.data[0]?.BasicInformationForm?.name,
          namear: res?.data[0]?.BasicInformationForm?.namear
        }
        this.esablishmentStatusCode = res?.data[0]?.StatusCode;
        this.basicInformation.get("FirstProposedNameEn")?.setValue(res?.data[0]?.BasicInformationForm?.name);
        this.basicInformation.get("FirstProposedNameAr")?.setValue(res?.data[0]?.BasicInformationForm?.namear);

        this.proposedNameEn = res?.data[0]?.BasicInformationForm?.name;
        this.proposedNameAr = res?.data[0]?.BasicInformationForm?.namear;

      } else {
        let errorMessage = '';
        if (res.Status !== 2000) {
          errorMessage = this.LanguageService.IsArabic ? res.MessageAR : res.MessageEN;
        }
        this.NotifyService.showError('notify.error', errorMessage.trim());
      }
    }, error => {
      this.NotifyService.showError('notify.error', error);
    });
  }
}
