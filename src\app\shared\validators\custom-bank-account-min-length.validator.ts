import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function bankAccountMinLength(min: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (!control.value) return null;
    return control.value.length < min
      ? {
        invalidBankAccountFormat: {
            requiredLength: min,
            actualLength: control.value.length,
            message: 'validationMessage.invalidBankAccountFormat',
          },
        }
      : null;
  };
}
