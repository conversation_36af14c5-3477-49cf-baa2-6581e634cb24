import { PaymentFailedComponent } from './pages/payment/payment-failed/payment-failed.component';
import { MyDraftsComponent } from './pages/my-drafts/my-drafts.component';
import { MyDocumentsComponent } from './pages/my-documents/my-documents.component';
import { MyFinancialTransactionComponent } from './pages/my-financial-transaction/my-financial-transaction.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MyApplicationsComponent } from './pages/my-applications/my-applications.component';
import { ApplicationsReadyToPayComponent } from './pages/applications-ready-to-pay/applications-ready-to-pay.component';
import { MyCasesComponent } from './pages/my-cases/my-cases.component';
import { DashboardComponent } from './pages/dashboard/dashboard.component';
import { LoginComponent } from './pages/login/login.component';
import { authGuard } from '../shared/guards/auth.guard';
import { PaymentSuccessComponent } from './pages/payment/payment-success/payment-success.component';
import { SubmitSummaryComponent } from './pages/application/submit-summary/submit-summary.component';
import { LoginWebsiteComponent } from './pages/login-website/login-website.component';
import { MySocialCasesComponent } from './pages/my-social-cases/my-social-cases.component';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  { path: 'login-website', component: LoginWebsiteComponent },
  { path: 'dashboard', canActivate: [authGuard], component: DashboardComponent },
  { path: 'my-applications', canActivate: [authGuard], component: MyApplicationsComponent },
  { path: 'my-applications/:status', canActivate: [authGuard], component: MyApplicationsComponent },
  { path: 'my-drafts', canActivate: [authGuard], component: MyDraftsComponent },
  { path: 'my-social-cases', canActivate: [authGuard], component: MySocialCasesComponent },
  { path: 'applications-ready-to-pay', canActivate: [authGuard], component: ApplicationsReadyToPayComponent },
  { path: 'my-financial-transaction', canActivate: [authGuard], component: MyFinancialTransactionComponent },
  { path: 'my-cases', canActivate: [authGuard], component: MyCasesComponent },
  { path: 'my-documents', canActivate: [authGuard], component: MyDocumentsComponent },
  { path: 'payment-success/:id', canActivate: [authGuard], component: PaymentSuccessComponent },
  { path: 'payment-failed/:id', canActivate: [authGuard], component: PaymentFailedComponent },
  { path: 'application/:result/:id', canActivate: [authGuard], component: SubmitSummaryComponent },
  { path: 'my-establishments', loadChildren: () => import('./pages/my-establishments/module/my-establishment.module').then(m => m.MyEstablishmentModule) },
  { path: '', redirectTo: 'user-pages/dashboard', pathMatch: 'full' },
  { path: '**', redirectTo: 'user-pages' }
];


@NgModule({
  imports: [
    RouterModule.forChild(routes)
  ],
  exports:[RouterModule]
})
export class UserRoutingModule { }
