import { Component, ElementRef, EventEmitter, Input, NgZone, OnChanges, OnInit, Output, SimpleChanges, TemplateRef, viewChild, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { InputType } from '../../../enums/input-type.enum';
import { ValidationService } from '../../../services/validation.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NotifyService } from '../../../services/notify.service';
import { LanguageService } from '../../../services/language.service';
import { GoogleMap, MapInfoWindow, MapMarker } from '@angular/google-maps';
import { Observable } from 'rxjs';
import { SUGGESTION_LOCATION } from './enums/suggestion-location';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';

export interface PlaceSearchResult {
  address: string;
  location?: google.maps.LatLng;
  imageUrl?: string;
  iconUrl?: string;
  name?: string;
}

@Component({
  selector: 'app-input-location',
  templateUrl: './input-location.component.html',
  styleUrls: ['./input-location.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class InputLocationComponent implements OnInit, OnChanges {
  mapOptions: google.maps.MapOptions = {
    mapTypeId: google.maps.MapTypeId.ROADMAP,
    zoomControl: true,
    scrollwheel: false,
    disableDoubleClickZoom: true,
    maxZoom: 20,
    minZoom: 4,
  };
  markerInfoContent = '';
  markerOptions: google.maps.MarkerOptions = {
    draggable: true,
    animation: google.maps.Animation.DROP,

  };
  loading = false;

  lat;
  lng;
  mapZoom = 12;
  mapCenter: google.maps.LatLng;
  markerPositions: google.maps.LatLngLiteral[] = [];
  autocomplete: google.maps.places.Autocomplete | undefined;
  _placeholder: string = '';

  get InputType() { return InputType; }
  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(key, this.control.errors[key]);
        }
      }
    }
    return null;
  }
  public get Placeholder() {
    if (this._placeholder) {
      return this._placeholder;
    }
    this._placeholder = this.placeholder ? this.placeholder : `${this.translate.instant('Please enter')}${this.translate.instant(this.label)}`;
    return this._placeholder;
  }

  @Input() intialAddress: string;
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() hint: string = '';
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() type: InputType = InputType.Default;
  @Input() columns: number = 6;

  @ViewChild("inputField") inputField: ElementRef;
  @ViewChild(GoogleMap, { static: false }) map: GoogleMap;
  @ViewChild(MapInfoWindow, { static: false }) infoWindow: MapInfoWindow;
  @Output() placeChanged = new EventEmitter<PlaceSearchResult>();
  constructor(
    private validationService: ValidationService,
    private ngZone: NgZone,
    private modalService: NgbModal,
    private notify: NotifyService,
    protected lang: LanguageService,
    private translate: TranslateService) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder = '';
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['intialAddress']?.currentValue) {
      this.control.setValue('');
      this.getSavedLocation();
    }
  }

  ngOnInit() {
    this.getCurrentLocation();
  }

  hasError = (errorType: string): boolean => this.control.touched && this.control.hasError(errorType);

  addMarker(event: google.maps.MapMouseEvent) {
    if (event.latLng != null)
      this.markerPositions.push(event.latLng.toJSON());
  }

  public onDragend(m, event) {
    const point: google.maps.LatLngLiteral = { lat: event?.latLng?.lat() ?? 0, lng: event?.latLng?.lng() ?? 0 };
    this.mapCenter = new google.maps.LatLng(point);
    this.markerPositions = [point]
    this.map.googleMap?.setCenter(point);
  }

  intiLocation = (): void => {
    this.autocomplete = new google.maps.places.Autocomplete(
      this.inputField.nativeElement
    );
    this.autocomplete.addListener('place_changed', () => {
      this.ngZone.run(() => {
        const place: google.maps.places.PlaceResult | undefined = this.autocomplete?.getPlace();
        const result: PlaceSearchResult = {
          address: this.inputField.nativeElement.value,
          name: place?.name,
          location: place?.geometry?.location,
          imageUrl: this.getPhotoUrl(place),
          iconUrl: place?.icon,
        };
        this.placeChanged.emit(result);
        const point: google.maps.LatLngLiteral = { lat: place?.geometry?.location?.lat() ?? 0, lng: place?.geometry?.location?.lng() ?? 0 };
        this.mapCenter = new google.maps.LatLng(point);
        this.markerPositions = [point]
        this.map.googleMap?.setCenter(point);
      });
    });
  }


  getPhotoUrl(place: google.maps.places.PlaceResult | undefined): string | undefined {
    return place?.photos && place?.photos.length > 0 ? place?.photos[0].getUrl({ maxWidth: 500 }) : undefined;
  }



  @Input() size: string = '';
  open = (content: TemplateRef<any>): void => {
    const modalRef = this.modalService.open(content, {
      backdrop: 'static',
      windowClass: 'location_modal' // Adds the custom class
    });
    modalRef.shown.subscribe(() => { })
    modalRef.result.then(
      (result) => { },
      (reason) => { }
    );
    modalRef.shown.subscribe(() => {
      this.intiLocation();
      if (this.control.value) {
        const location$ = (this.control.value as string).replaceAll(' ', '').split(',');
        const point: google.maps.LatLngLiteral = { lat: (Number.parseFloat(location$[0])) ?? 0, lng: (Number.parseFloat(location$[1])) ?? 0 };
        this.lat = point.lat;
        this.lng = point.lng;
        this.mapCenter = new google.maps.LatLng(point);
        this.map?.panTo(point);
        this.markerOptions = {
          draggable: true,
          animation: google.maps.Animation.DROP,
        };
      } else {
        this.getSavedLocation();
      }
    });
  }


  getSavedLocation = (): void => {
    if (this.intialAddress != null ||
      this.intialAddress != undefined ||
      this.intialAddress != '' ||
      this.intialAddress != ' ') {

      setTimeout(() => {
        this.inputField?.nativeElement?.focus();
        this.inputField?.nativeElement?.click();
        let location = SUGGESTION_LOCATION.find(_ => _.name.trim().toLocaleLowerCase() == this.intialAddress.trim().toLocaleLowerCase());
        if (location) {

          if (this.inputField && this.inputField?.nativeElement)
            this.inputField.nativeElement.value = location.description;

          const point: google.maps.LatLngLiteral = { lat: (Number.parseFloat(location!.location.lat)) ?? 0, lng: (Number.parseFloat(location!.location.log)) ?? 0 };
          this.lat = point.lat;
          this.lng = point.lng;
          this.mapCenter = new google.maps.LatLng(point);
          this.map?.panTo(point);
          this.markerOptions = {
            draggable: true,
            animation: google.maps.Animation.DROP,
          };
        }
      }, 500);

    }
  }



  openInfoWindow(marker: MapMarker) {
    this.infoWindow.open(marker);
  }

  getCurrentLocation() {
    this.loading = true;
    navigator.geolocation.getCurrentPosition(
      (position: GeolocationPosition) => {
        this.loading = false;
        const point: google.maps.LatLngLiteral = { lat: position.coords.latitude, lng: position.coords.longitude };
        this.lat = position.coords.latitude;
        this.lng = position.coords.longitude;
        this.mapCenter = new google.maps.LatLng(point);
        this.map?.panTo(point);
        this.markerInfoContent = "Pan Can Be Draggable .";
        this.markerOptions = {
          draggable: true,
          animation: google.maps.Animation.DROP,
        };
      },
      (error) => {
        this.loading = false;
        if (error.PERMISSION_DENIED) {
          console.error("Couldn't get your location", 'Permission denied');
        } else if (error.POSITION_UNAVAILABLE) {
          console.error("Couldn't get your location", 'Position unavailable');
        } else if (error.TIMEOUT) {
          console.error("Couldn't get your location", 'Timed out');
        } else {
          console.error(error.message, `Error: ${error.code}`);
        }
      },
      { enableHighAccuracy: true, }
    );
  }

  saveLocation = (modal: any): void => {
    this.control.setValue(`${this.mapCenter.lat().toFixed(6)} , ${this.mapCenter.lng().toFixed(6)}`);
    this.lat = this.mapCenter.lat().toFixed(6);
    this.lng = this.mapCenter.lng().toFixed(6);
    modal.close();
  }

  cancel = (modal: any): void => {
    this.control.setValue('');
    modal.close()
  }

  ngOnDestroy() {
    if (this.autocomplete) {
      google.maps.event.clearInstanceListeners(this.autocomplete);
    }
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true
      }
    }
    return false;
  }
}
