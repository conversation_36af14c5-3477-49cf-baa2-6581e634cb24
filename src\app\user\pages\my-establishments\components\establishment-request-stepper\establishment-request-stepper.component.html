<div class="container dashboard-padding" [ngClass]="{ showSuccessMessage: reviewAndSubmitRef?.ShowSuccessMessage }">
  <div class="row m-sm-auto">
    <h1 class="eService_title">
      {{this.LanguageService.IsArabic?this.approvedEsablishmentName?.namear: this.approvedEsablishmentName?.name}}
    </h1>
  </div>

  <!-- <div class="row m-sm-auto">
    <app-establishment-stepper-full-journey [steps]="journeySteps"
      [lastIndexActiveStep]="journeyIndexActiveStep"></app-establishment-stepper-full-journey>
  </div> -->

  <!-- <div class="row progress_wrapper">
    <mat-progress-bar mode="determinate" class="p-md-0" style="border-radius: 8px !important;"
      [value]="progress"></mat-progress-bar>
    <span class="progress_txt">{{ "Progress" | translate }} ({{ progress
      }}%)</span>
  </div> -->

  <div class="row gap-service-32 flex-md-nowrap m-sm-auto">
    <div class="col-12 col-md-4 col-lg-4 col-xl-3 d-flex flex-column align-items-center scrollSteps">
      <section class="horizontal-scroll w-100">
        <mat-stepper linear #stepper="matVerticalStepper" [selectedIndex]="lastselectedTabIndex"
          (selectedIndexChange)="updateCurrentPortalStep($event)" orientation="vertical" class="w-100">
          @if(isReturnForUpdate){
          <mat-step [completed]="false">
            <ng-template matStepLabel> {{messageTranslationPrefix +
              "forms.feedBack.Title"| translate}}</ng-template>
          </mat-step>
          }
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.basicInformation.title" | translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>
              @if(selectedLegalFormType === LEGAL_FORM_TYPES.SocialSolidarityFunds){
              {{ messageTranslationPrefix + "forms.objectives.FundServiceTitle" | translate }}
              } @else {
              {{ messageTranslationPrefix + "forms.objectives.Title" | translate }}
              }
            </ng-template>
          </mat-step>

          @if(selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds){
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.FundServices.title" | translate}}
            </ng-template>
          </mat-step>
          }

          @if(selectedLegalFormType == LEGAL_FORM_TYPES.Association ||
          selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds ||
          selectedLegalFormType == LEGAL_FORM_TYPES.Union ||
          selectedLegalFormType == LEGAL_FORM_TYPES.NationalSociety) {
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.foundingMembers.title"| translate}}
            </ng-template>
          </mat-step>
          }

          @if(selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree) {
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.foundingMembers.title"| translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.boardOfDirectors.Title"| translate}}
            </ng-template>
          </mat-step>
          }

          @if(selectedLegalFormType == LEGAL_FORM_TYPES.NationalSocietyByDecree)
          {
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.foundingMembers.title"| translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.allocationOfFoundationFunds.title"| translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.boardOfTrustees.title"| translate}}
            </ng-template>
          </mat-step>
          }

          @if(selectedLegalFormType== LEGAL_FORM_TYPES.Association ||
          selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds
          ||selectedLegalFormType == LEGAL_FORM_TYPES.Union) {
          <ng-container *ngIf="selectedLegalFormType != LEGAL_FORM_TYPES.Union">
            <mat-step [completed]="false">
              <ng-template matStepLabel>{{messageTranslationPrefix +
                "forms.interimCommittee.Title"| translate}}
              </ng-template>
            </mat-step>
          </ng-container>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.membership.title" | translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.boardOfDirectors.Title"| translate}}
            </ng-template>
          </mat-step>
          }

          @if(selectedLegalFormType == LEGAL_FORM_TYPES.NationalSociety) {
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.allocationOfFoundationFunds.title"| translate}}
            </ng-template>
          </mat-step>

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.boardOfTrustees.title"| translate}}
            </ng-template>
          </mat-step>
          }

          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix +
              "forms.uploadDocuments.title" | translate}}
            </ng-template>
          </mat-step>
          @if(Page_View_Type === 2){
          <mat-step [completed]="false">
            <ng-template matStepLabel>{{messageTranslationPrefix + "forms.reviewAndSubmit.title"| translate}}</ng-template>
          </mat-step>
        }
        </mat-stepper>
      </section>
    </div>

    <div class="col-12 col-md-8 col-lg-8 col-xl-9 p-0">
      @if(isReturnForUpdate){
      <div [hidden]="stepper.selectedIndex !== 0">
        <app-establishment-feed-back [feedbackList]="feedbackList" (activeStepperIndex)="changeStepperIndex($event)"></app-establishment-feed-back>
      </div>
      }

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 1 : 0)">
        <app-establishment-basic-information #basicInformationTemplate [form]="basicInformation" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-basic-information>
      </div>

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 2 : 1)">
        @if(selectedLegalFormType === LEGAL_FORM_TYPES.SocialSolidarityFunds){
        <app-establishment-fund-activities-and-objectives #objectiveTemplate [legalFormType]="selectedLegalFormType"
          [form]="objectives" (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate" [isNotAllowedToEdit]="isNotAllowedToEdit" />
        } @else if(selectedLegalFormType === LEGAL_FORM_TYPES.Union){
        <app-establishment-union-objectives #objectiveTemplate [legalFormType]="selectedLegalFormType"
          [form]="objectives" (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate" [isNotAllowedToEdit]="isNotAllowedToEdit" />
        } @else {
        <app-establishment-objective #objectiveTemplate [legalFormType]="selectedLegalFormType" [form]="objectives"
          (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-objective>
        }
      </div>

      @if(selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree){
      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 3 : 2)">
        <app-establishment-founding-members-by-decree #foundingMemberByDecreeTemplate [form]="foundingMembers"
          (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate" [isNotAllowedToEdit]="isNotAllowedToEdit"
          [formStatusCode]="requestStatusCode"></app-establishment-founding-members-by-decree>
      </div>

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 4 : 3)">
        <app-establishment-board-of-directors-by-decree #boardOfDirectorsByDecreeTemplate [form]="boardOfDirectors"
          (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-board-of-directors-by-decree>
      </div>
      }

      @if(selectedLegalFormType == LEGAL_FORM_TYPES.NationalSocietyByDecree){
      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 3 : 2)">
        <app-establishment-founding-members-by-decree #foundingMemberByDecreeTemplate [form]="foundingMembers"
          (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate" [isNotAllowedToEdit]="isNotAllowedToEdit"
          [formStatusCode]="requestStatusCode"></app-establishment-founding-members-by-decree>
      </div>

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 4 : 3)">
        <app-establishment-allocation-of-foundation-funds-by-decree #allocationOfFoundationFundsByDecreeTemplate
          [formStatusCode]="requestStatusCode" [form]="allocationOfFoundationFunds" (next)="goNext()"
          (previous)="goPrevious()"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-allocation-of-foundation-funds-by-decree>
      </div>

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 5 : 4)">
        <app-establishment-board-of-trustees-by-decree #boardOfTrusteesByDecreeTemplate [form]="boardOfTrustees"
          (next)="goNext()" (previous)="goPrevious()" [feedbackList]="feedbackList"
          [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-board-of-trustees-by-decree>
      </div>
      }

      @if(selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds){
      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 3 : 2)">
        <app-establishment-fund-services #fundServicesTemplate [form]="fundServices" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-fund-services>
      </div>
      }

      @if(selectedLegalFormType == LEGAL_FORM_TYPES.Association || selectedLegalFormType ==
      LEGAL_FORM_TYPES.SocialSolidarityFunds || selectedLegalFormType == LEGAL_FORM_TYPES.Union ||
      selectedLegalFormType== LEGAL_FORM_TYPES.NationalSociety) {
      <div
        [hidden]="stepper.selectedIndex !== (selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 4 : 3):(isReturnForUpdate ? 3 : 2))">
        <app-establishment-founding-members #foundingMemberTemplate [form]="foundingMembers" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"
          [formStatusCode]="requestStatusCode"></app-establishment-founding-members>
      </div>
      }

      @if(selectedLegalFormType== LEGAL_FORM_TYPES.Association || selectedLegalFormType ==
      LEGAL_FORM_TYPES.SocialSolidarityFunds || selectedLegalFormType == LEGAL_FORM_TYPES.Union) {

      <ng-container *ngIf="selectedLegalFormType != LEGAL_FORM_TYPES.Union">
        <div
          [hidden]="stepper.selectedIndex !== (selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 5 : 4):(isReturnForUpdate ? 4 : 3))">
          <app-establishment-interim-committee #interimCommitteeTemplate [form]="interimCommittee"
            [formStatusCode]="requestStatusCode" [meetingDate]="getMeetingDate" (next)="goNext()"
            (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
            [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-interim-committee>
        </div>
      </ng-container>

      <div
        [hidden]="stepper.selectedIndex !== (selectedLegalFormType==LEGAL_FORM_TYPES.Union?(isReturnForUpdate ? 4 : 3):((selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 6 : 5):(isReturnForUpdate ? 5 : 4))))">
        <app-establishment-membership-enrollment #memberShipEnrollmentTemplate [form]="membership" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-membership-enrollment>
      </div>
      <div
        [hidden]="stepper.selectedIndex !== (selectedLegalFormType==LEGAL_FORM_TYPES.Union?(isReturnForUpdate ? 5 : 4):((selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 7 : 6):(isReturnForUpdate ? 6 : 5))))">
        <app-establishment-board-of-directors #boardOfDirectorsTemplate [form]="boardOfDirectors" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-board-of-directors>
      </div>
      }

      @if(selectedLegalFormType == LEGAL_FORM_TYPES.NationalSociety) {
      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 4 : 3)">
        <app-establishment-allocation-of-foundation-funds #allocationOfFoundationFundsTemplate
          [formStatusCode]="requestStatusCode" [form]="allocationOfFoundationFunds" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-allocation-of-foundation-funds>
      </div>

      <div [hidden]="stepper.selectedIndex !== (isReturnForUpdate ? 5 : 4)">
        <app-establishment-board-of-trustees #boardOfTrusteesTemplate [form]="boardOfTrustees" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [isNotAllowedToEdit]="isNotAllowedToEdit"></app-establishment-board-of-trustees>
      </div>
      }

      <div [hidden]="stepper.selectedIndex !==(selectedLegalFormType == LEGAL_FORM_TYPES.Association ||selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds ||selectedLegalFormType == LEGAL_FORM_TYPES.Union
              ? (selectedLegalFormType==LEGAL_FORM_TYPES.Union?(isReturnForUpdate ? 6 : 5):(selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 8 : 7):(isReturnForUpdate ? 7 : 6)))
              : (selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree || selectedLegalFormType == LEGAL_FORM_TYPES.NationalSocietyByDecree ? (selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree ? (isReturnForUpdate ? 5: 4) : (isReturnForUpdate ? 6: 5)) : isReturnForUpdate? 6: 5)
            )
          ">
        <app-establishment-upload-documents #uploadDocumentsTemplate [form]="documents" (next)="goNext()"
          (previous)="goPrevious()" [feedbackList]="feedbackList" [isReturnForUpdate]="isReturnForUpdate"
          [legalFormType]="selectedLegalFormType" [isNotAllowedToEdit]="isNotAllowedToEdit"
          [legalFromTypes]="legalFromTypes" [selectedLegalFormType]="selectedLegalFormType"
          [npoRequestId]="npoRequestId" [establishmentRequestId]="establishmentRequestId"
          [esablishmentStatusCode]="esablishmentStatusCode"
          [migrationID]="migrationID"></app-establishment-upload-documents>
      </div>
      @if(Page_View_Type === 2){
      <div [hidden]="
            stepper.selectedIndex !== (selectedLegalFormType == LEGAL_FORM_TYPES.Association || selectedLegalFormType == LEGAL_FORM_TYPES.SocialSolidarityFunds || selectedLegalFormType == LEGAL_FORM_TYPES.Union
              ? (selectedLegalFormType==LEGAL_FORM_TYPES.Union?(isReturnForUpdate ? 7 : 6):(selectedLegalFormType==LEGAL_FORM_TYPES.SocialSolidarityFunds?(isReturnForUpdate ? 9 : 8):(isReturnForUpdate ? 8 : 7)))
              : (selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree || selectedLegalFormType == LEGAL_FORM_TYPES.NationalSocietyByDecree ? (selectedLegalFormType == LEGAL_FORM_TYPES.AssociationByDecree ? (isReturnForUpdate ? 6: 5) : (isReturnForUpdate ? 7: 6)) : (isReturnForUpdate? 7: 6))
            )
          ">
        <app-establishment-review-and-submit #reviewAndSubmitRef [form]="summary" (next)="goNext()" (previous)="goPrevious()"
          [showGetFoundingMemberButton]="canShowGetFoundingMemberButton" [showAction]="allowShowAction"
           [isNotAllowedToEdit]="isNotAllowedToEdit" [legalFromTypes]="legalFromTypes"
          [selectedLegalFormType]="selectedLegalFormType" [isReturnForUpdate]="isReturnForUpdate"
          [feedbackList]="feedbackList" [migrationID]="migrationID" [proposedNameEn]="proposedNameEn" [proposedNameAr]="proposedNameAr" [requestNumber]="requestNumber"></app-establishment-review-and-submit>
      </div>
    }
    </div>
  </div>
</div>


<div class="container dashboard-padding" [ngClass]="{ showSuccessMessage: !reviewAndSubmitRef?.ShowSuccessMessage }">
  <div class="result-message">
    @if(reviewAndSubmitRef?.SubmitationType !== 2)
    {
    <div class="col-md-12 congratulation-message">
      <h1 class="text-success d-flex align-items-center justify-content-start">
        <svg class="result-icon" width="66" height="66" viewBox="0 0 66 66" fill="none"
          xmlns="http://www.w3.org/2000/svg">
          <path
            d="M47.2688 23.7312C47.5012 23.9634 47.6856 24.2392 47.8114 24.5426C47.9372 24.8461 48.002 25.1715 48.002 25.5C48.002 25.8285 47.9372 26.1539 47.8114 26.4574C47.6856 26.7608 47.5012 27.0366 47.2688 27.2688L29.7688 44.7688C29.5366 45.0012 29.2609 45.1856 28.9574 45.3114C28.6539 45.4372 28.3286 45.502 28 45.502C27.6715 45.502 27.3462 45.4372 27.0427 45.3114C26.7392 45.1856 26.4634 45.0012 26.2313 44.7688L18.7313 37.2688C18.2622 36.7996 17.9986 36.1634 17.9986 35.5C17.9986 34.8366 18.2622 34.2004 18.7313 33.7312C19.2004 33.2621 19.8366 32.9986 20.5 32.9986C21.1634 32.9986 21.7997 33.2621 22.2688 33.7312L28 39.4656L43.7313 23.7312C43.9634 23.4988 44.2392 23.3144 44.5427 23.1886C44.8462 23.0628 45.1715 22.998 45.5 22.998C45.8286 22.998 46.1539 23.0628 46.4574 23.1886C46.7609 23.3144 47.0366 23.4988 47.2688 23.7312ZM65.5 33C65.5 39.4279 63.5939 45.7114 60.0228 51.056C56.4516 56.4006 51.3758 60.5662 45.4372 63.0261C39.4986 65.4859 32.964 66.1295 26.6596 64.8755C20.3552 63.6215 14.5643 60.5262 10.019 55.981C5.47384 51.4358 2.37851 45.6448 1.12449 39.3404C-0.129527 33.0361 0.514082 26.5014 2.97393 20.5628C5.43378 14.6242 9.59938 9.54838 14.944 5.97724C20.2886 2.40609 26.5721 0.5 33 0.5C41.6168 0.509099 49.878 3.93612 55.9709 10.0291C62.0639 16.122 65.4909 24.3833 65.5 33ZM60.5 33C60.5 27.561 58.8872 22.2442 55.8654 17.7218C52.8437 13.1995 48.5488 9.67472 43.5238 7.59331C38.4988 5.5119 32.9695 4.96731 27.635 6.0284C22.3006 7.0895 17.4005 9.70862 13.5546 13.5546C9.70863 17.4005 7.08951 22.3005 6.02842 27.635C4.96733 32.9695 5.51192 38.4988 7.59333 43.5238C9.67474 48.5488 13.1995 52.8437 17.7218 55.8654C22.2442 58.8872 27.561 60.5 33 60.5C40.2909 60.4917 47.2809 57.5918 52.4363 52.4363C57.5918 47.2808 60.4917 40.2909 60.5 33Z"
            fill="#3F8E50" />
        </svg>
        {{ messageTranslationPrefix + "congratulations" | translate }}
      </h1>
    </div>
    }

    <div class="w-100 application-reference-number">
      <p>{{messageTranslationPrefix+'appReferenceNumber' | translate}}</p>
      <span class="eService_sub-title">
        {{ reviewAndSubmitRef?.ReferenceNumber }}
      </span>
    </div>

    <div class="w-100">
      <div class="notification-headsup">
        <p class="part1">
          {{messageTranslationPrefix+'emailNotification' | translate}}:
          <strong>{{ userInfo?.userInfo?.email }}</strong>
          {{messageTranslationPrefix+'smsNotification' | translate}}:
          <strong>{{ userInfo?.userInfo?.mobile }}</strong>
        </p>
        <p class="part2">
          {{messageTranslationPrefix+'applicationStatus' | translate}}
          <strong>{{messageTranslationPrefix+'myApplications' |
            translate}}</strong>.
        </p>
      </div>
    </div>

    <div class="w-100 actions-container">
      <button type="button" class="btn btn-lg btn-outline-primary" [routerLink]="['/e-services']">
        {{messageTranslationPrefix+'backToServices' | translate}}
      </button>
      <button type="button" class="btn btn-lg btn-primary" [routerLink]="['/user-pages/dashboard']">
        {{messageTranslationPrefix+'myApplications' | translate}}
      </button>
    </div>
  </div>
</div>
