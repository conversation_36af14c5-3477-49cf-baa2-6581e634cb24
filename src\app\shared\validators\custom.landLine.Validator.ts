import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function landLineValidator(startWith6: boolean = false, legnth: number): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const phoneNumber: string = control.value?.trim();

    if (!phoneNumber) {
      return null;
    }

    const isMaxLength = length < phoneNumber.length;

    const isValid = startWith6
      ? /^9716\d{3}\d{4}$/.test(phoneNumber)
      : /^9719\d{3}\d{4}$/.test(phoneNumber);

      if(isValid) {
        return null;
      }
      
      if(startWith6 && !isMaxLength) {
        return {invalidLandlineNumberStartWith6: true}
      } else if(isMaxLength){
        return {invalidLandlineNumberLength: true}
      } else {
        return {invalidLandlineNumberStartWith9: true}
      }

    // return isValid
    //   ? null
    //   : startWith6
    //     ? { invalidLandlineNumberStartWith6: true }
    //     : { invalidLandlineNumberStartWith9: true }
  };
}
