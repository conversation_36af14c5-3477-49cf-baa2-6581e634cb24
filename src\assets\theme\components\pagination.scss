.aegov-pagination {
  ul.pagination {
    margin: 0 !important;
    padding: 0 !important;
    li.page-item {
      &.disabled {
        .page-link {
          background: none;
        }
        &:first-of-type,
        &:last-of-type {
          .page-link {
            color: $aegold-200;
            svg {
              fill: $aegold-200;
            }
          }
        }
      }
      &.active {
        .page-link {
          background-color: $aegold-500;
          color: white;
          &:hover {
            background-color: $aegold-500;
            box-shadow: 0px 0px 0px 6px $aegold-100;
          }
        }
      }
      &:first-of-type,
      &:last-of-type {
        .page-link {
          display: flex;
          gap: 12px;
          color: $aegold-600;
          background: none;
          width: auto;
          svg {
            fill: $aegold-600;
          }
          &:focus {
            box-shadow: none;
          }
          &:hover {
            background: none;
            color: $aegold-500;
            svg {
              fill: $aegold-500;
            }
          }
        }
      }

      .page-link {
        display: flex;
        align-items: center;
        justify-content: center;

        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: $aeblack-800;

        height: 24px;
        width: 24px;
        border: none;
        border-radius: 50% !important;
        margin: 0px 4px;
        // transition: none;
        &:focus {
          box-shadow: 0px 0px 0px 6px $aegold-100;
        }
        &:hover {
          background-color: $aegold-50;
        }
        @media only screen and (min-width: 1024px) {
          font-size: 16px;
          line-height: 24px;
          margin: 0px 6px;
          height: 40px;
          width: 40px;
        }

        svg {
          transition: all 0.3s;
          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
.pagination {
  .active > .page-link {
    color: #fff;
    background-color: $aegold-600 !important;
    border-color: $aegold-600 !important;
  }
}
