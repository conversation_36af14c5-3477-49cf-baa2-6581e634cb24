<mat-stepper orientation="vertical" class="custom-stepper desktop-stepper">
    @for (request of steps; track $index) {
    <mat-step>
        <ng-template matStepLabel>
            <div class="step-content">
                <div class="left-side">
                    <span class="step-date">
                        {{request?.CreatedOn | date:'dd-MM-yyyy'}}
                    </span>
                    <span class="step-clock">
                        {{request?.CreatedOn | date:'h:mm a'}}
                    </span>
                </div>
                <span class="step-status status "
                    [ngClass]="{'status-warning':request?.NewStatusreason=='Returned','status-draft':request?.NewStatusreason=='Draft','status-primary':(request?.NewStatusreason!='Draft'||request?.NewStatusreason!='Returned')}">
                    {{lang.IsArabic ? request?.NewStatusreasonAr :
                    request?.NewStatusreason }}
                </span>
            </div>
        </ng-template>
    </mat-step>
    }
</mat-stepper>