@use '@angular/material' as mat;
@import './app-variables';
@import './app-theme.scss';
@import 'bootstrap/scss/bootstrap';
@import "@fortawesome/fontawesome-free/css/all.min.css";
@import 'ngx-toastr/toastr-bs5-alert';
@import 'assets/theme/index.scss';


$app-theme-primary: mat.m2-define-palette(mat.$m2-brown-palette, 500);
$app-theme-accent: mat.m2-define-palette(mat.$m2-purple-palette, A200, A100, A400);
$app-theme-warn: mat.m2-define-palette(mat.$m2-red-palette);

$app-theme: mat.m2-define-light-theme((
  color: (
    primary: $app-theme-primary,
    accent: $app-theme-accent,
    warn: $app-theme-warn,
  )
));

@include mat.core-theme($app-theme);
@include mat.all-component-themes($app-theme);


html, body {
  height: 100%;
}
// body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }


@font-face {
  font-family: "DroidArabicKufi";
  font-style: normal;
  font-weight: 400;
  src: url(assets/fonts/DroidArabicKufi.ttf) format("truetype");
}

@font-face {
  font-family: "DroidArabicKufiBold";
  font-style: normal;
  font-weight: 700;
  src: url(assets/fonts/DroidArabicKufiBold.ttf) format("truetype");
}

// body * {
//   font-family: "DroidArabicKufi", "Roboto", sans-serif, "Material Icons" !important,  "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
// }
body * {
  // font-family: "DroidArabicKufi", "DroidArabicKufiBold","Roboto", sans-serif, "Material Icons";

  // mat-option, h2{
  //   font-family: 'Noto Kufi Arabic', sans-serif !important;
  // }
  // h1, h2, h3, h4, h5, h6{
  //   font-family: 'Noto Kufi Arabic', sans-serif !important;
  // }

}
// .en_lang{
//     font-family: "DroidArabicKufi" !important;
// }
.center{text-align: center;}

#content-wrapper {
  // margin-top: 40px;
  // padding-top: 10px;
  // background-color: #fff;
  // background-color: #F7F7F7;
  // overflow-x: hidden;
  // min-height: 400px !important;
}
.nav-item{
  margin:5px;
}
@media (min-width: 992px){
.navbar-expand-lg .navbar-nav {
    padding-right: 0;
}
}

td {
  padding-right: 8px;
}

.pagination .page-item .page-link {
  border-radius: 5px !important;
  margin: 2px;
}


.justify-content-center {
  display: flex;
  justify-content: center;
}

.appform {
  // min-width: 150px;
  // max-width: 500px;
  width: 100%;
  // padding-top: 1em;
}


.margin10-left {
  margin-left: 10px; /* Add margin-right between buttons */
}
.margin10-right {
  margin-right: 10px; /* Add margin-right between buttons */
}


.mat-mdc-form-field {
  width: 100%;
  // padding-top: 20px;

}
.mat-mdc-select,
.mat-mdc-form-field {
  @media (max-width: 1024px) {
    font-size: 14px !important;
  }

}


.rtl {
  direction: rtl;
}


mat-hint{
  color: gray;
  font-size: 11px;
  line-height: 1.1;
  
  @media(max-width:1024px){
    line-height: 1.7 !important;
    margin-top: 4px !important;
    width: 100%;
    height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: break-spaces;
  }

  >i {
    color: #B68A35;
  }
}


th[sortable].desc:before, th[sortable].asc:before {
  content: "";
  display: block;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAmxJREFUeAHtmksrRVEUx72fH8CIGQNJkpGUUmakDEiZSJRIZsRQmCkTJRmZmJgQE0kpX0D5DJKJgff7v+ru2u3O3vvc67TOvsdatdrnnP1Y///v7HvvubdbUiIhBISAEBACQkAICAEhIAQ4CXSh2DnyDfmCPEG2Iv9F9MPlM/LHyAecdyMzHYNwR3fdNK/OH9HXl1UCozD24TCvILxizEDWIEzA0FcM8woCgRrJCoS5PIwrANQSMAJX1LEI9bqpQo4JYNFFKRSvIgsxHDVnqZgIkPnNBM0rIGtYk9YOOsqgbgepRCfdbmFtqhFkVEDVPjJp0+Z6e6hRHhqBKgg6ZDCvYBygVmUoEGoh5JTRvIJwhJo1aUOoh4CLPMyvxxi7EWOMgnCGsXXI1GIXlZUYX7ucU+kbR8NW8lh3O7cue0Pk32MKndfUxQFAwxdirk3fHappAnc0oqDPzDfGTBrCfHP04dM4oTV8cxr0SVzH9FF07xD3ib6xCDE+M+aUcVygtWzzbtGX2rPBrEUYfecfQkaFzYi6HjVnGBdtL7epqAlc1+jRdAap74RrnPc4BCijttY2tRcdN0g17w7HqZrXhdJTYAuS3hd8z+vKgK3V1zWPae0mZDMykadBn1hTQBLnZNwVrJpSe/NwEeDsEwCctEOsJTsgxLvCqUl2ACftEGvJDgjxrnBqkh3ASTvEWrIDQrwrnJpkB3DSDrGW7IAQ7wqnJtkBnLRztejXXVu4+mxz/nQ9jR1w5VB86ejLTFcnnDwhzV+F6T+CHZlx6THSjn76eyyBIOPHyDakhBAQAkJACAgBISAEhIAQYCLwC8JxpAmsEGt6AAAAAElFTkSuQmCC) no-repeat;
  background-size: 22px;
  width: 22px;
  height: 22px;
  float: left;
  // margin-left: -22px;
}

th[sortable].desc:before {
  transform: rotate(180deg);
  -ms-transform: rotate(180deg);
}


.appFullWidth {
  width: 100%;
}


.service-card{
  .card{
    min-height: 200px;
    margin: 10px;
  }
}


.col-md-6, section{
  // padding-top: 1em;
  h3{
    color: gray;
    margin: 16px 0 0 !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
}
.mat-typography{
  .col-md-6, section{
    @media (max-width: 767px) {
      padding-top: 0;
      // padding-bottom: .8em;

    }
  }
}


.mat-mdc-form-field-infix {
  padding-bottom: 2px !important;
}


.labelSummary{
  max-width: 100px;
}


// .mdc-floating-label[ng-reflect-floating="true"]{
//   margin-top: -10px;
//   mat-label{
//     font-size: 22px;
//     }
// }


 .table-responsive {
  overflow-x: auto;
}

@media (max-width: 767px) {
  .table-responsive {
    overflow-y: auto;
  }
}


.alert {
  padding-left: 1em;
  padding-right: 1em;
  padding-top: 0;
  padding-bottom: 0;
}

.mat-step-header {
  pointer-events: none !important;
}

.cdk-global-overlay-wrapper, .cdk-overlay-container {
  z-index: 99999 !important;
}


.mat-mdc-option .mdc-list-item__primary-text {
  font-size: smaller !important;
  padding: .5em !important;
}
.w-max-content{
  width: max-content;
}

input::placeholder {
  @media(max-width: 1023px) {
    font-size: 14px !important;
  }
}

.modal-backdrop {
  @media(max-width:1023px) {
    width: 100% !important;
    height: 100% !important;
  }
}


@media (max-width: 1024px){.container--dashboard .action-popup ul li,
  .container--dashboard .action-popup ul .dropdown-item,
  .application-overview__content .action-popup ul li,
  .application-overview__content .action-popup ul .dropdown-item {
    padding: 10px 15px !important;
  }
}

.modal-header {
  .btn-close {
    margin-left: auto !important;

    [ng-reflect-dir="rtl"] &,
    [dir="rtl"] & {
      margin-right: auto !important;
      margin-left: 0 !important;
    }
  }
}

.time-container {
  direction: ltr !important;
}

.special-display {
  @media (min-width: 1024px) {
    grid-column: 1 /3 !important;
  }
}