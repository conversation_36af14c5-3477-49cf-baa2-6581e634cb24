
.service-status-card{
  padding: 12px 24px;
  border-radius: 16px;
  background: $white;
  border: 1px solid $mocdyellow ;
  width: 45%;
  @media only screen and (min-width: 990px) {
    width: 240px;
  }
  &__status-wrapper{
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  &__lbl{
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: black;
    @media only screen and (min-width: 1024px) {
      font-size: 14px;
      line-height: 20px; 
    }
  }
  &__status{
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    color: $aeblack-800;
    @media only screen and (min-width: 1024px) {
      font-size: 24px;
      line-height: 30px;
    }
  }
  &__icon{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    @media only screen and (min-width: 1024px) {
      width: 50px;
      height: 50px;
    }
    &.success-progress{
      background-color: $camelyellow-50;
      color: $camelyellow-500;
    }
    &.success-completed{
      background-color: $aegreen-50;
      color: $aegreen-500;
    }
    &.success-returned{
      background-color: $aered-50;
      color: $aered-600;
    }
    &.success-ready{
      background-color: $techblue-50;
      color: $techblue-500;
    }
    &.success-draft{
      background-color: $aeblack-100;
      color: $aeblack-700;
    }
  }
  &__action{
    font-size: 14px;
    font-weight: 500;
    // line-height: 20px; 
    @media only screen and (min-width: 1024px) {
      font-size: 16px;
      // line-height: 22px;
    }
  }
}
.service-status-card-swiper .swiper-slide{
  width: 200px;
  @media only screen and (min-width: 1024px) {
    width: 240px;
  }
  // [ng-reflect-dir=rtl] &, [dir=rtl] &{
  //   direction: rtl;
  // }
}