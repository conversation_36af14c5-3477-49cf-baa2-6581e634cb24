import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { authGuard } from '../../../../shared/guards/auth.guard';
import { EstablishmentMainPageComponent } from '../pages/establishment-main-page/establishment-main-page.component';
import { EstablishmentDetailsPageComponent } from '../pages/establishment-details-page/establishment-details-page.component';
import { EstablishmentDetailsManagementComponent } from '../pages/establishment-details-management/establishment-details-management.component';
import { MyEstablishmentResolver } from '../resolvers/manage-establishments.resolver';
import { NMWPEstablishmentResolver } from '../resolvers/nmwp-establishment.resolver';
import { NmwpEstablishmentDetailsComponent } from '../pages/nmwp-establishment-details/nmwp-establishment-details.component';

const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    component: EstablishmentMainPageComponent,
  },
  {
    path: 'details/:establishmentId/:type',
    canActivate: [authGuard],
    component: EstablishmentDetailsPageComponent,
  },
  {
    path: 'details/:establishmentId/:type/:id',
    canActivate: [authGuard],
    component: EstablishmentDetailsPageComponent,
  },
  {
    path: 'management-details/:establishmentId',
    resolve: { establishmentData: MyEstablishmentResolver },
    canActivate: [authGuard],
    component: EstablishmentDetailsManagementComponent,
  },
  {
    path: 'nmwp-management-details/:establishmentId',
    resolve: { establishmentData: NMWPEstablishmentResolver },
    canActivate: [authGuard],
    component: NmwpEstablishmentDetailsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MyEstablishmentRoutingModule { }
