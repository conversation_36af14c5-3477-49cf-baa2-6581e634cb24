import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function internationalPhoneValidator(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    const value: string = control.value;

    if (!value) return null;

    // Exclude UAE numbers (e.g., +9715..., 9715..., etc.)
    const uaeRegex = /^(\+?9715)/;

    // Generic international format (e.g., +44xxxxxxxxx or 0044xxxxxxxxx)
    const internationalRegex = /^(\+|00)[1-9]\d{1,3}\d{6,14}$/;

    const isUae = uaeRegex.test(value);
    const isValidIntl = internationalRegex.test(value);

    return !isUae && isValidIntl ? null : { internationalPhone: true };
  };
}
