.custom-horizontal-stepper {
  position: relative !important;
  width: 100% !important; // Adjust width as needed

  .mat-step-header {
    display: flex !important;
    align-items: center !important;
    gap: 20px !important;
    width: 100% !important;
    position: relative !important; // For the line positioning
  }

  /* Make mat-step-icon a circle and hide the number */
  .mat-step-icon {
    background-color: $aegold-500 !important; // Custom color for the circle
    color: white !important;
    border-radius: 50% !important;
    width: 12px !important;
    height: 12px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0 !important; // Hide the number inside the circle
    transform: translateX(6px) !important;
  }

  .mat-step-label {
    display: grid !important;
    grid-template-columns: 1fr auto !important; // Left side (Date & Clock) | horizontal line and Status
    gap: 10px !important;
    align-items: center !important;
    width: 100% !important;
  }

  .step-content {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }

  .left-side {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    position: absolute !important;
    left: 0 !important;
  }

  .step-date {
    color: $aeblack-900 !important;
    font-size: 14px !important;
    // font-family: "Roboto" !important;
  }

  .step-clock {
    color: $aeblack-900 !important;
    font-size: 14px !important;
    // font-family: "Roboto" !important;
  }

  .step-status {
    color: $aeblack-900 !important;
    min-width: 109px !important;
    text-align: center !important;
  }

  /* Style the horizontal line between Date & Status */
  .mat-step-header .mat-stepper-horizontal-line::before {
    content: '' !important;
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 50% !important;
    /* Position the line in the middle */
    width: 5px !important;
    height: 100% !important;
    background-color: $aeblack-200 !important;
    /* Line color */
    transform: translateX(-50%) !important;
    /* Center the line */
  }

  // Ensuring all step content is always visible, even for inactive steps
  .mat-step-content {
    display: block !important;
  }

  .mat-stepper-horizontal .mat-step-header {
    display: flex !important;
    flex-direction: column !important; // Adjust for horizontal stepper
    align-items: flex-start !important;
    position: relative !important; // Ensure absolute positioning of the line works
  }

  .mat-stepper-horizontal-line::before {
    left: -.5px !important;
    border-left-width: 3px !important;
    border-left-color: $aeblack-200 !important;
  }

  .mat-horizontal-content-container {
    margin-left: calc(41px + 62px + 12px) !important;
  }

  .mat-horizontal-content-container {
    height: 0 !important;
  }
}

[dir=rtl] {
  .mat-horizontal-content-container {
    margin-right: 18px !important;
  }

  .mat-stepper-horizontal-line::before {
    right: 86px !important;
  }

  .custom-stepper {
    .left-side {
      right: 0 !important;
      left: auto !important;
    }

    // @media (min-width: 992px) {
    //   .step-content {
    //     justify-content: flex-end !important;
    //   }
    // }

    // .mat-step-icon {
    //   // transform: translateX(-77px) !important;
    //   transform: translateX(-10px) !important;
    // }
  }
}