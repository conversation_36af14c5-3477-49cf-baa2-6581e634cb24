import { Component, ViewChild } from '@angular/core';
import { Form, FormBuilder, FormGroup, MinLengthValidator, Validators } from '@angular/forms';
import { MatStepper } from '@angular/material/stepper';
import { TranslateService } from '@ngx-translate/core';
import { StepperService } from '../../../shared/services/stepper.service';
import { ToastrService } from 'ngx-toastr';
import { LanguageService } from '../../../shared/services/language.service';
import { SocialAidService } from '../services/social-aid.service';
import { ActivatedRoute, Route } from '@angular/router';
import { AuthService } from '../../../shared/services/auth.service';
import { customMobileValidator } from '../../../shared/validators/custom.mobile.validator';
import { customEmailValidator } from '../../../shared/validators/custom.email.validator';
import { SummaryStepsFullJourny } from '../../../shared/models/summry-steps-full-journey';
import { RequestFormComponent } from '../components/request-form/request-form.component';
import { PersonalInfoComponent } from '../components/personal-info/personal-info.component';
import { FamilyMembersInfoComponent } from '../components/family-members-info/family-members-info.component';
import { EducationInfoComponent } from '../components/education-info/education-info.component';
import { HousingInfoComponent } from '../components/housing-info/housing-info.component';
import { InflationInfoComponent } from '../components/inflation-info/inflation-info.component';
import { AttachmentsComponent } from '../components/attachments/attachments.component';
import { SummaryComponent } from '../components/summary/summary.component';
import { ServiceSteps } from '../enums/steps.enum';

@Component({
  selector: 'app-social-aid-stepper',
  templateUrl: './social-aid-stepper.component.html',
  styleUrl: './social-aid-stepper.component.scss',
})
export class SocialAidStepperComponent {
  @ViewChild('stepper') stepper: MatStepper;
  @ViewChild('ApplicantInfoComponentTemplate') RequestFormComponentTemplate: RequestFormComponent;
  @ViewChild('PersonalInfoComponentTemplate') PersonalInfoComponentTemplate: PersonalInfoComponent;
  @ViewChild('FamilyMembersComponentTemplate') FamilyMembersComponentTemplate: FamilyMembersInfoComponent;
  @ViewChild('EducationInfoComponentTemplate') EducationInfoComponentTemplate: EducationInfoComponent;
  @ViewChild('HousingInfoComponentTemplate') HousingInfoComponentTemplate: HousingInfoComponent;
  @ViewChild('InflactionInfoComponentTemplate') InflactionInfoComponentTemplate: InflationInfoComponent;
  @ViewChild('AttachmentsComponentTemplate') AttachmentsComponentTemplate: AttachmentsComponent;
  @ViewChild('SummaryComponentTemplate') SummaryComponentTemplate: SummaryComponent;

  stepperForm: FormGroup;
  messageTranslationPrefix: string = 'services.swp.';
  journeySteps: SummaryStepsFullJourny[];
  journeyIndexActiveStep: number;
  lastselectedTabIndex: unknown;
  isReturnForUpdate: boolean = false;


  ListAdditionalDoc: any;
  ListPersonalDocs: any;
  requestId: string | null;
  ContactId: string | null;
  isUaeNational: boolean = true;
  constructor(
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    protected translate: TranslateService,
    protected stepperService: StepperService,
    private toastr: ToastrService,
    protected lang: LanguageService,
    private socialAidService: SocialAidService,
    private authService: AuthService
  ) {
    this.stepperForm = this.formBuilder.group({
      requestForm: this.formBuilder.group({
        Category: [null, Validators.required],
        SubCategory: [null, Validators.required],
        IsActiveStudent: [null],
        ReceivedLocalSupport: [null],
        MilitaryServiceStatus: [null],
        Terminated: [null],
        ChildEligibilityforWomeninDifficulty: [[]],
        NumberOfChildren: [null],
        NumberOfChildrenLessThan25: [null],
        PursuingHigherEducation: [null],
        PursuingMilitaryService: [null],
        GuardianEmiratesID: [null],
      }),
      personalInfo: this.formBuilder.group({
        EmiratesID: [{ value: authService.getUserInfo()?.userInfo?.idn, disabled: true }, Validators.required],
        PassportNumber: [{ value: '', disabled: true }],
        IDNBackNumber: [{ value: '', disabled: true }],
        caseID: [{ value: '', disabled: true }],
        MaritalStatus: [{ value: '', disabled: true }, Validators.required],
        PreferredPhoneNumber: [{ value: '' }, [Validators.required, customMobileValidator(), Validators.minLength(12)]],
        alternativeNumber: [{ value: '', disabled: true }, Validators.required],
        PreferredEmail: ['', [Validators.required, customEmailValidator()]],
        AlternativeEmail: [{ value: '', disabled: true }, Validators.required],
        Occupations: ['', Validators.required],
        jobTitle: [''],
        Emirates: ['', Validators.required],
        Area: ['', Validators.required],
        Center: ['', Validators.required],
      }),
      familyMembersInfo: this.formBuilder.group({
        members: this.formBuilder.array([]),
      }),
      educationInfo: this.formBuilder.group({
        members: this.formBuilder.array([]),
      }),
      housingInfo: this.formBuilder.group({
        ApplyHousingAllowance: ['', Validators.required],
        ReceivingFederalLocalhousingsupport: [''],
        ReceivingHousingAllowanceFromEmployer: [''],
        FullOwnershipResidentialProperty: [''],
        IsUtilityBillIssuedForFullyOwnedProperty: [''],
        LivingSituation: [''],
        ReceivesHousingSupportFromHusband: [''],
      }),
      inflationInfo: this.formBuilder.group({
        ApplyInflationAllowance: ['', Validators.required],
        ApplyUtilityAllowance: [''],
        UtilityProvider: [''],
        UtilityAccountNumber: [''],
      }),
      attachmentsForm: this.formBuilder.group({
        // personalPhoto: ['', Validators.required],
      }),
      summaryForm: this.formBuilder.group({
        termsAndConditions: ['', Validators.requiredTrue],
        consent: ['', [Validators.required, Validators.requiredTrue]],
      }),
    });

    this.ContactId = localStorage.getItem('ContactId') ?? "";

  }

  ngOnInit() {
    this.socialAidService.lookupData$.subscribe((data) => {
    });

    const userInfo = this.authService.getUserInfo();
    if (userInfo?.userInfo?.nationalityEN?.toUpperCase() != "UAE") {
      this.isUaeNational = false;
    }
    if (!this.ContactId) {
      this.socialAidService.getSWPContactId(userInfo?.userInfo?.idn).subscribe((res) => {
        if (res.Status === 2000) {
          this.socialAidService.setContactId(res.data);
        }
      });
    }

    this.socialAidService.beneficiaryDetails$.subscribe(details => {
      // this.beneficiaryDetails = details;
    });

    this.socialAidService.getBeneficiaryDetails(userInfo?.userInfo?.idn);

  }


  goNext() {
    this.stepper.next();
  }
  goPrevious() {
    this.stepper.previous();
  }
  getDocumentsForRequest() {
    this.requestId = this.route.snapshot.paramMap.get('id');
    this.ContactId = localStorage.getItem('ContactId');
    if (this.requestId) {
      this.socialAidService.getRequestDocuments(this.requestId).subscribe(data => {
        if (data) {
          this.ListPersonalDocs = data?.Data?.ListPersonalDocs;
          this.ListAdditionalDoc = data?.Data?.ListAdditionalDoc;
        }
      });
    }
  }
  get requestForm() {
    return this.stepperForm.get('requestForm') as FormGroup;
  }

  get personalInfo() {
    return this.stepperForm.get("personalInfo") as FormGroup;
  }
  get familyMembers() {
    return this.stepperForm.get("familyMembersInfo") as FormGroup;
  }
  get systemValidation() {
    return this.stepperForm.get("systemValidation") as FormGroup;
  }
  get documentGeneration() {
    return this.stepperForm.get("documentGeneration") as FormGroup;
  }
  get educationInfo() {
    return this.stepperForm.get("educationInfo") as FormGroup;
  }

  get housingInfo() {
    return this.stepperForm.get("housingInfo") as FormGroup;
  }

  get inflationInfo() {
    return this.stepperForm.get("inflationInfo") as FormGroup;
  }

  get attachmentsForm() {
    return this.stepperForm.get('attachmentsForm') as FormGroup;
  }

  get summaryForm() {
    return this.stepperForm.get('summaryForm') as FormGroup;
  }

  get ServiceSteps() {
    return ServiceSteps;
  }

  get progress() {
    var progressToal = 0;
    if (this.requestForm.valid) {
      progressToal += 15;
    }
    if (this.personalInfo.valid) {
      progressToal += 15;
    }
    if (this.familyMembers.valid) {
      progressToal += 10;
    }
    if (this.housingInfo.valid) {
      progressToal += 10;
    }
    if (this.educationInfo.valid) {
      progressToal += 10;
    }
    if (this.inflationInfo.valid) {
      progressToal += 10;
    }
    if (this.attachmentsForm.valid) {
      progressToal += 20;
    }
    if (this.summaryForm.valid) {
      progressToal += 10;
    }
    return progressToal;
  }

  updateCurrentPortalStep = (index: number): void => {
    window.scrollTo(0, 0);
  }

  selectionChanged(event: any) {
    const currentStep = event.previouslySelectedIndex;
    if (currentStep === 0) {
      if (this.requestForm.invalid) {
        this.requestForm.markAllAsTouched();
        return;
      }
    }
    else if (currentStep === 1) {
      if (this.personalInfo.invalid) return
    }
    else if (currentStep === 2) {
      if (this.familyMembers.invalid) return
    }
    else if (currentStep === 3) {
      if (this.housingInfo.invalid) return
    }
    else if (currentStep === 4) {
      if (this.educationInfo.invalid) return
    }

    else if (currentStep === 5) {
      if (this.inflationInfo.invalid) return
    }
    else if (currentStep === 6) {
      if (this.attachmentsForm.invalid)
        return;
    }
    else if (currentStep === 7) {
      if (this.summaryForm.invalid) {
        this.summaryForm.markAllAsTouched();
        return;
      }
    }
  }

  goBack() {
    this.stepper.previous();
  }

  goForward() {
    this.stepper.next();
  }

  submit() {
    if (this.stepperForm.invalid) {
      this.stepperForm.markAllAsTouched();
      this.toastr.error('Invalid Form!', 'Failure');
    } else {
      this.toastr.success('Valid Form!', 'Success');
    }
  }
}
