#modal-help-fab{
    .modal-dialog{
        justify-content: center;
        @media only screen and (min-width: 992px) {
            transform: none !important;
            margin-right: 0;
        }
    }
    .modal-content{
        @media only screen and (min-width: 992px) {
            position: fixed;
            right: 0;
            top: 200px;
            border-radius: 16px 0 0 16px;
            [ng-reflect-dir=rtl] &, [dir=rtl] &{
                left: 0;
                right: auto;
                border-radius: 0 16px 16px 0;
            }
        }
        padding: 16px;
        max-width: 450px;
        border-radius: 12px;
        background: #F9F7ED;
        box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
        border: none;
        .modal-header{
            margin-bottom: 8px;
            padding: 0;
            border: none;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            button{
                margin: 0;
            }
            &-title{
                display: flex;
                flex-direction: row;
                gap: 8px;
            }
            h3{
                margin: 0;
                color: $aeblack-900;
                font-size: 18px;
                font-weight: 500;
                line-height: 28px;
            }
        }
        .modal-body{
            padding: 0;
            display: flex;
            gap: 16px;
            flex-direction: column;
            ul{
                margin: 0;
                padding: 0;
                border-radius: 8px;
                list-style: none;
                background-color: $white;
                li{
                    padding: 16px;
                    border-bottom: 1px solid $aeblack-100;
                    font-size: 14px;
                    font-weight: 700;
                    line-height: 20px;
                    &:last-child{
                        border: none;
                        border-radius: 0 0 8px 8px;
                    }
                    &:first-child{
                        border-radius: 8px 8px 0 0;
                    }
                    [ng-reflect-dir=rtl] &, [dir=rtl] &{
                        .helper__arrow-icon{
                            transform: rotate(180deg);
                            margin-left: 5px !important;
                            transition: 0.5s;
                        }
                    }
                    &:hover{
                        background-color: $aeblack-50;
                        .helper__arrow-icon{
                            margin: -5px !important;

                        }
                    }
                    .helper__arrow-icon{
                        margin-right: 5px !important;
                        transition: 0.5s;
                    }
                    a{
                        text-decoration: none;
                        color: $aeblack-900;
                        display: flex;
                        gap: 8px;
                        align-items: center;
                        justify-content: space-between;
                    }
                }
            }
        }
    }
}