import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Injector,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';

import { Lookup } from '../../../../shared/models/lookup.model';
import { SubmitType } from '../../models/submit-type';
import { Feedback, FileType } from '../../models/feedback';
import { NocRequestComponentBase } from '../../models/base/noc-request-component-base';
import { LegalFormTypesEnum } from '../../enums/legal-form-types-enum';

@Component({
  selector: 'app-npo-details',
  templateUrl: './npo-details.component.html',
  styleUrls: ['./npo-details.component.scss'],
})
export class NpoDetailsComponent
  extends NocRequestComponentBase
  implements OnInit, AfterViewInit, OnChanges {
  requestId: any;
  maxDate: Date;
  editRows: string[] = [];
  legalFormTypes: Lookup[];
  emirates: Lookup[];
  filteredLicensingAuthorities: Lookup[] = [];

  get fb(): any {
    return this.form.controls;
  }

  @Input() form: FormGroup;
  @Input() selectedLegalFormType: LegalFormTypesEnum | undefined;
  @Input() feedbackList: Feedback[] | undefined;
  @Input() isReturnForUpdate: boolean | undefined;
  @Input() isNotAllowedToEdit: boolean | undefined;
  @Input() currentEstablishment: any;
  @Output() next: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() previous: EventEmitter<boolean> = new EventEmitter<boolean>();
  constructor(injector: Injector, private modalService: NgbModal) {
    super(injector);
    this.messageTranslationPrefix =
      this.messageTranslationPrefix + 'forms.npoDetails.';

    const today = new Date();
    today.setHours(23, 59, 59, 999);
    this.maxDate = today;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['currentEstablishment'] &&
      changes['currentEstablishment']?.currentValue
    ) {
      this.fillNPOData();
    }

    if (
      changes['feedbackList']?.currentValue &&
      changes['feedbackList']?.currentValue?.length > 0 &&
      changes['isReturnForUpdate']?.currentValue === true
    ) {
      this.checkEditableFildes();
    }
  }

  fillNPOData = (): void => {
    this.form.disable();

    this.form
      .get('NpoNameAr')
      ?.setValue(this.currentEstablishment?.BasicInformationForm?.namear);
    this.form
      .get('NpoNameEn')
      ?.setValue(this.currentEstablishment?.BasicInformationForm?.name);

    this.form
      .get('NpoLegalForm')
      ?.setValue(
        this.legalFormTypes?.find(
          (_) =>
            _.ID === this.currentEstablishment?.BasicInformationForm?.npoform
        )
      );

    this.form
      .get('HeadquarterEmirate')
      ?.setValue(
        this.emirates?.find(
          (_) =>
            _.ID === this.currentEstablishment?.BasicInformationForm?.emirate
        )
      );

    this.form
      .get('LicensingEntity')
      ?.setValue(
        this.filteredLicensingAuthorities?.find(
          (_) =>
            _.ID ===
            this.currentEstablishment?.BasicInformationForm?.LicensingAuthority
        )
      );

    this.form
      .get('LicenseNumber')
      ?.setValue(
        this.currentEstablishment?.BasicInformationForm?.LicenseNumber
      );

    this.form
      .get('LicenseIssuanceDate')
      ?.setValue(this.currentEstablishment?.BasicInformationForm?.IssuanceDate);

    this.form
      .get('LicenseExpiryDate')
      ?.setValue(
        this.currentEstablishment?.BasicInformationForm?.Licenseexpirydate
      );
  };

  checkEditableFildes = (): void => {
    this.editRows = this.getUpdatedFildes(
      this.isReturnForUpdate ?? false,
      this.feedbackList,
      'NPODetails',
      this.fb
    );
  };

  ngAfterViewInit() {
    this.form.statusChanges.subscribe((status) => {
      if (status === 'VALID' && this.StepperService.IsAutoStep) {
        this.StepperService.setAutoStep(false);
        this.submit();
      }
    });
  }

  ngOnInit(): void {
    this.StepperService.formData$.subscribe((res) => {
      if (res && res.establishmentId) {
        this.form.get('EstablishmentId')?.setValue(res.establishmentId);
      }
    });

    this.NocRequestService.lookupData$.subscribe(
      (data) => {
        if (data) {
          this.emirates = data?.Emirates;
          this.legalFormTypes = data?.LegalForms;
          this.filteredLicensingAuthorities = data.LicensingAuthority;

          this.StepperService.requestData$.subscribe((_) => {

            if (_ && _.isFullDetails == true) {
              this.mapData(_?.NPODetails);
            } else if (_ && _.isFullDetails == false) {
              this.mapData(_?.NPODetails);
            }
          });
        }
      }
    );

    this.saveLegalForm();
  }

  isValidForm = (): boolean => {
    let result: boolean = Object.keys(this.form.controls).every(
      (controlName) => {
        const control = this.form.get(controlName);
        return control?.disabled || control?.valid;
      }
    );

    return result;
  };

  saveLegalForm = (): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'NPODetails',
      true
    );
    this.continueToNextStep(submitParams, false);
  };

  saveAsDraft = (isLazy: boolean = false): void => {
    const submitParams: SubmitType = this.createSubmitParams(
      'NPODetails',
      true
    );
    if (isLazy) this.savingLazyFormData(submitParams);
    else this.savingFormData(submitParams);
  };

  submit = (): void => {
    if (this.form.invalid) {
      this.handleFormError();
    } else {
      const submitParams: SubmitType = this.createSubmitParams(
        'NPODetails',
        false
      );
      this.handleSaveRequest(submitParams);
    }
  };

  private handleFormError(): void {
    this.form.markAllAsTouched();
    this.StepperService.scrollToError(this.ElementRef);
  }

  private createSubmitParams(key: string, isDraft: boolean): SubmitType {
    let object = this.getMappingObject;

    return {
      form: this.form,
      callBack: object,
      next: this.next,
      key: key,
      isDraft: isDraft,
    };
  }

  getMappingObject = (): any => {
    return {
      ApplicantEmirateId: this.AuthService?.getUserInfo()?.userInfo?.idn,
      NPOEntity: this.currentEstablishment?.Id,
      InsideOutsideUAE: this.selectedLegalFormType
      // NpoNameAr: this.fb?.NpoNameAr?.value ?? '',
      // NpoNameEn: this.fb?.NpoNameEn?.value ?? '',
      // NpoLegalForm: this.fb?.LegalFormId?.value?.ID ?? '',
      // NpoDeclarationDecisionLink:
      //   this.fb?.NpoDeclarationDecisionLink?.value ?? '',
      // MainCategory: this.fb?.MainCategory?.value ?? '',
      // HeadquarterEmirate: this.fb?.HeadquarterEmirate?.value?.ID ?? '',
      // LicensingEntity: this.fb?.LicensingEntity?.value?.ID ?? '',
      // LicenseNumber: this.fb?.LicenseNumber?.value ?? '',
      // LicenseIssuanceDate: this.fb?.LicenseIssuanceDate?.value ?? '',
      // LicenseExpiryDate: this.fb?.LicenseExpiryDate?.value ?? '',

    };
  };

  mapData = (data: any): void => {
    this.form.disable();
    if (!data) return;
  };
}
