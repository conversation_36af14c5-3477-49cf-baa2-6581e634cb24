import { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse, HttpResponse, HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { AuthService } from '../services/auth.service';
import { catchError, map, tap, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { NotifyService } from '../services/notify.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

  private activeRequestCount = 0;

  private activeRequests: Map<string, boolean> = new Map();

  constructor(
    private auth: AuthService,
    private router: Router,
    private notify: NotifyService,
    private spinner: NgxSpinnerService
  ) { }

  intercept(authReq: HttpRequest<any>, next: HttpHandler) {
    const urlKey = authReq.urlWithParams;

    var excludedUrls = ['/assets/i18n/en.json', '/assets/i18n/ar.json'];
    var externalUrls = ['https://translation.googleapis.com/language/translate/v2'];
    const currentUrl = authReq.url;
    const isExcluded = excludedUrls.some((url) => currentUrl.includes(url));
    const isExternal = externalUrls.some((url) => currentUrl.includes(url));

    if (isExternal) {
      return next.handle(authReq).pipe(
        tap((event: HttpEvent<any>) => {
          // console.log('Incoming external HTTP response', event);
        })
      );
    }

    if (!isExcluded) {
      this.increaseRequestCount();
    }
    const authToken = this.auth.getJwtToken();

    if (authToken) {
      authReq = authReq.clone({
        setHeaders: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      const isTokenValid = this.auth.isTokenValid(authToken);
      if (!isTokenValid) {
        this.auth.logout();
        this.router.navigate(['/login']);
        return throwError(() => 'Token expired');
      }
    }

    return next.handle(authReq).pipe(
      map((event) => {
        if (event instanceof HttpResponse) {
          this.decreaseRequestCount();
        }
        return event;
      }),
      catchError((error: HttpErrorResponse) => {
        this.decreaseRequestCount();
        if (error.error instanceof ErrorEvent) {
          console.error('An error occurred:', error.error.message);
        } else {
          if (error.status === 401) {
            this.auth.logout();
            this.router.navigate(['/login']);
          } else if (error.status === 0) {
            console.error('No internet connection');
            this.notify.showError('notify.error', 'notify.noInternet');

          } else {
            console.error(
              `Backend returned code ${error.status}, body was: ${error.error}`
            );
          }
        }
        return throwError(
          () => 'Something bad happened; please try again later.'
        );
      })
    );
  }

  private increaseRequestCount(): void {
    this.activeRequestCount++;
    if (this.activeRequestCount === 1) {
      this.spinner.show();
    }
  }

  private decreaseRequestCount(): void {
    if (this.activeRequestCount > 0) {
      this.activeRequestCount--;
    }
    if (this.activeRequestCount === 0) {
      this.spinner.hide();
    }
  }

}


