.status-container>div {
    border-radius: 24px;
    text-align: center;
    font-weight: 400;
    width: 100%;
}

.table-container {
    width: 100%;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    padding: 10px !important;
    // white-space: nowrap;
}

/* Works on Firefox */
* {
    scrollbar-width: none;
    // scrollbar-width: thin;
    // scrollbar-color: #f2eccf #ffffff;
}

/* Works on Chrome, Edge, and Safari */
*::-webkit-scrollbar {
    width: 12px;
}

*::-webkit-scrollbar-track {
    background: #ffffff;
}

*::-webkit-scrollbar-thumb {
    background-color: #f2eccf;
    border-radius: 20px;
    border: 3px solid #ffffff;
}


.application-overview {
    background: #fff !important;
    padding: 12px 24px !important;
    border-radius: 16px !important;
}

.total-count {
    @media (max-width:1024px) {
        display: none !important;
    }
}