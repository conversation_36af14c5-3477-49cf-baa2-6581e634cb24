.aegov-accordion {
  // background-color: $aegold-50;
  border-radius: 16px;
  padding: 0px;
  .accordion-item {
    background-color: $aegold-50;
    border: none;
    border-radius: 16px;
    .accordion-header {
      .accordion-button {
        display: flex;
        gap: 16px;
        border: none;
        background: none;
        outline: none;
        box-shadow: none;
        padding: 24px 16px;
        text-align: start !important;
        @media only screen and (min-width: 1024px) {
          padding: 24px 24px;
        }
        &:after {
          content: "\f078";
          font-family: "Font Awesome 6 Free";
          font-weight: 900;
          background: none;
          // color: $aegold-600;
          color: $aegold-500;
          [ng-reflect-dir="rtl"] &,
          [dir="rtl"] & {
            margin-right: auto;
            margin-left: initial;
          }
        }
        &:not(.collapsed)::after {
          content: "\f077";
          transform: rotate(0deg);
        }
      }
      i.header-icon {
        font-size: 40px;
        color: $aegreen-600;
      }
      svg {
        width: 40px;
        height: 40px;
        min-width: 40px;
        fill: $aegreen-600;
      }
      h3 {
        font-size: 20px !important;
        font-weight: 500 !important;
        line-height: 26px !important;
        color: $aeblack-800;
        margin: 0px !important;
      }
      p {
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;
        color: $aeblack-950;
        margin: 0px;
      }
      &.error {
        p,
        i.header-icon {
          color: $aered-600;
        }
        svg {
          fill: $aered-600;
        }
      }
    }
    .accordion-body {
      padding: 0px 16px 24px;
      @media only screen and (min-width: 1024px) {
        padding: 0px 24px 24px;
      }

      .aegov-alert {
        margin-bottom: 16px;
        @media only screen and (min-width: 1024px) {
          margin-bottom: 24px;
        }
      }

      .eligibility-data__item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 16px;
        background: white;
        border-radius: 8px;
        padding: 16px;
        @media only screen and (min-width: 1024px) {
          padding: 24px;
        }

        i {
          font-size: 40px;
          color: $aegreen-600;
        }
        svg {
          width: 40px;
          height: 40px;
          min-width: 40px;
          fill: $aegreen-600;
        }
        h4 {
          font-size: 20px;
          font-weight: 500;
          line-height: 26px;
          color: $aeblack-900;
          margin-bottom: 4px;
        }
        p {
          font-size: 16px;
          font-weight: 400;
          line-height: 22px;
          color: $aeblack-900;
          margin-bottom: 0px;
        }
        &.error {
          i,
          h4,
          p {
            color: $aered-600;
          }
          svg {
            fill: $aered-600;
          }
        }
      }
    }
  }
}

/*********************************/
mat-expansion-panel.mat-expansion-panel {
  margin-bottom: 16px;
  padding: 24px;
  background-color: $white;
  border-radius: 6px !important;
  border: 1px solid $aegold-400;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden;

  @media (max-width: 1023px) {
    padding: 12px !important;
    }


  .mat-expansion-indicator::after {
    color: $aegold-400;
  }

  .mat-expansion-panel-header {
    height: auto;
    line-height: 26px;
    padding: 0;
    font-size: 20px;
    color: $aeblack-800;
    font-weight: $font-medium;
  }
  .mat-expansion-panel-body {
    // margin-top: 35px;
    padding: 0;
    color: $aeblack-900;
    font-size: 15px;
    margin-top: 14px;
    
    @media(max-width:1024px){
      margin-top: 10px !important;
    }

    h1 {
      color: $aeblack-900;
      font-size: 15px;
    }

    // Apply spacing specifically to div:not(.not_card)
    & > div:not(.not_card) {
      border-radius: 8px;
    }

    // RTL-specific overrides
    :dir(rtl) &,
    &.rtl {
      & > .not_card {
        > .title_wrapper {
          margin: 24px 0 0 0 !important; // Ensure margin aligns with RTL requirements

          @media(max-width:1024px){
            margin: 14px 0 0 0 !important;
          }
        }
      }
    }

    & > .not_card {
      grid-column: span 2; // Make it span across the grid
      font-weight: $font-bold;

      > .title_wrapper {
        margin: 24px 0 0 0 !important;
        font-weight: 500;
        font-size: 18px;
        line-height: 28px;

        @media(max-width:1024px){
          margin: 14px 0 0 0 !important;
        }
      }
    }
  }
}


.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover {
  background: none !important;
}