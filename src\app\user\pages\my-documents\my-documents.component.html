<div class="container" *ngIf="auth.isAuthenticated()">
  <div class="container">
    <div class="btn-group" dir="ltr" (change)="handleChoice($event)" role="group"
      aria-label="Basic radio toggle button group">
      <input type="radio" class="btn-check" name="btnradio" id="btnCards" autocomplete="off" checked>
      <label class="btn btn-outline-primary" for="btnCards"><i class="fas fa-border-all"></i></label>
      <input type="radio" class="btn-check" name="btnradio" id="btnList" autocomplete="off">
      <label class="btn btn-outline-primary" for="btnList"><i class="fas fa-list"></i></label>
    </div>
  </div>
  <div class="row">
    <app-document-card *ngFor="let doc of documents" [label]="doc.DocumentNameEn" [document]="doc"
      [columns]="cols"></app-document-card>

  </div>
</div>
