import { LanguageService } from '../../../services/language.service';
import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { Lookup } from '../../../models/lookup.model';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { MatSelect } from '@angular/material/select';
import { ValidationService } from '../../../services/validation.service';
import { debounceTime, takeUntil, switchMap, Subject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { DataService } from '../../../services/data.service';

@Component({
  selector: 'app-select-search-api',
  templateUrl: './select-search-api.component.html',
  styleUrl: './select-search-api.component.scss',
  host: {
    '[class]': "'col-md-' + columns",
  },
})
export class SelectSearchApiComponent implements OnInit, OnChanges {
  @Input() label: string;
  @Input() placeholder: string = '';
  @Input() data: Lookup[] = [];
  @Input() control: FormControl;
  @Input() required: boolean = false;
  @Input() columns: number = 6;

  @Input() allowApiSearch: boolean = false;
  @Input() searchUrl: string | null = null;
  @Input() returnKey: string = 'data';

  @ViewChild('singleSelect', { static: true }) singleSelect: MatSelect;

  searchControl = new FormControl();

  filteredLookups: Lookup[];

  constructor(
    protected lang: LanguageService,
    public translate: TranslateService,
    private validationService: ValidationService,
    private dataService: DataService
  ) {
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this._placeholder = '';
    });
  }

  _placeholder: string = '';

  public get Placeholder() {
    if (this._placeholder) {
      return this._placeholder;
    }
    this._placeholder = this.placeholder
      ? this.placeholder
      : `${this.translate.instant('Please enter')}${this.translate.instant(
          this.label
        )}`;
    return this._placeholder;
  }

  private _onDestroy = new Subject<void>();

  ngOnInit(): void {
    this.initialize();
    if (!this.allowApiSearch) {
      this.searchControl.valueChanges.subscribe(
        (value) => (this.filteredLookups = this._filter(value))
      );
    } else {
      this.onSearchEmit();
    }
  }

  onSearchEmit() {
    this.searchControl.valueChanges.subscribe(val => {
      if(val && val !== ''){
        this.control.setValue('');
        this.dataService.get(`/${this.searchUrl}=${val}`).subscribe((res) => {
        this.filteredLookups = [];
        if (res[this.returnKey] && res[this.returnKey]?.length > 0) {
          (res[this.returnKey] as Array<any>).forEach((item$) => {
            this.filteredLookups.push({
              ID: item$?.ID,
              NameArabic: item$?.NameArabic,
              NameEnglish: item$?.NameEnglish,
              ParentID: item$?.ParentID,
              ExternalData: item$,
            });
          });
        }
      });
      }
    })
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data']) {
      this.initialize();
    }
  }

  initialize() {
    this.filteredLookups = this.data;
  }

  private _filter(value: string | Lookup): Lookup[] {
    if (!this.data) {
      return [];
    }
    const filterValue = typeof value === 'string' ? value.toLowerCase() : '';
    if (!this.data) {
      return [];
    }
    return this.data.filter(
      (lookup) =>
        lookup?.NameEnglish?.toLowerCase().includes(filterValue) ||
        lookup?.NameArabic?.toLowerCase().includes(filterValue)
    );
  }

  onSelectionChange(item: any) {
    this.control.setValue(item?.value);
  }

  hasError(errorType: string): boolean {
    return this.control.touched && this.control.hasError(errorType);
  }

  get errorMessage(): string | null {
    if (this.control && this.control.errors) {
      for (const key in this.control.errors) {
        if (this.control.errors.hasOwnProperty(key) && this.control.touched) {
          return this.validationService.getValidatorErrorMessage(
            key,
            this.control.errors[key]
          );
        }
      }
    }
    return null;
  }

  get isRequired() {
    if (this.control) {
      const validator = this.control.hasValidator(Validators.required);
      if (validator) {
        return true;
      }
    }
    return false;
  }
}
