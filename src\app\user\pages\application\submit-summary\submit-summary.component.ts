import { Component } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-submit-summary',
  templateUrl: './submit-summary.component.html',
  styleUrl: './submit-summary.component.scss'
})
export class SubmitSummaryComponent {
  isSuccess: boolean;
  applicationNumber: string;
  requestId: string
  serviceCatalogId: string
  isCaseParam: string;
  caseId: string;
  caseNumber: string;
  caseTitle: string;
  isCase: boolean;
  result: string | null;

  constructor(private router: Router, private route: ActivatedRoute) {
 

    this.route.paramMap.subscribe(params => {
      this.result = params.get('result');
      this.applicationNumber = params.get('id') ?? '';
    });

    this.route.queryParams.subscribe(params => {
  
      this.requestId = params['requestId'] ?? '';
      this.serviceCatalogId = params['serviceCatalogId'] ?? '';
      this.isCaseParam = params['isCase'] ?? '0';
      this.caseId = params['caseId'] ?? '';
      this.caseNumber = params['caseNumber'] ?? '';
      this.caseTitle = params['caseTitle'] ?? '';
      if (this.result?.toLocaleLowerCase() == 'success') {
        this.isSuccess = true;
      } else {
        this.isSuccess = false;
      }

      if (this.isCaseParam === '1') {
        this.isCase = true;
        this.applicationNumber = this.caseNumber;
      }
    });



  }
}
