#modal-eligibility-criteria{
    .modal-content{
        padding: 48px;
        border-radius: 16px;
        background: $white;
        box-shadow: 0px 20px 25px -5px rgba(27, 29, 33, 0.10), 0px 8px 10px -6px rgba(27, 29, 33, 0.10);
        border: none;
        .modal-header{
            margin-bottom: 8px;
            padding: 0;
            border: none;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            button{
                margin: 0;
            }
            .eligibility-criteria__title{
                color: $aeblack-800;
                // font-family: Inter;
                font-size: 32px;
                font-weight: 700;
                line-height: 38px;
                margin: 0;
            }
        }
        .modal-body{
            padding: 0;
            display: flex;
            flex-direction: column;
            .eligibility-criteria__tab{
                margin-bottom: 8px;
                .row{
                    gap: 16px;
                    margin-bottom: 16px;
                    @media (min-width: 992px) {
                        gap: 24px;
                        margin-bottom: 0;
                    }
                }
            }
            .eligibility-criteria__subtitle{
                color: $aeblack-800;
                // font-family: Inter;
                font-size: 26px;
                font-weight: 700;
                margin-bottom: 24px;
                line-height: 28px;
                display: none !important;
                @media (min-width: 992px) {
                    display: block !important;
                }
            }
            .eligibility-criteria__card{
                display: flex;
                padding: 24px;
                flex-direction: column;
                gap: 8px;
                flex: auto;
                border-radius: 16px;
                background: $aegold-100;
                margin-bottom: 0;
                @media (min-width: 992px) {
                    margin-bottom: 24px;
                    flex: 1 0 0;
                    gap: 16px;
                }
                &-header{
                    display: flex;
                    flex-direction: row;
                    gap: 8px;
                    align-items: center;
                    &-title{
                        color: $aeblack-900;
                        // font-family: Inter;
                        font-weight: 700;
                        margin: 0;
                        font-size: 20px;
                        line-height: 28px;
                        @media (min-width: 992px) {
                            font-size: 26px;
                            line-height: 30px;
                        }
                    }
                }
                    &-desc{
                        color: $aeblack-900;
                        font-weight: 400;
                        margin: 0;
                        font-size: 16px;
                        font-weight: 400;
                        line-height: 24px;
                        @media (min-width: 992px) { 
                            font-size: 18px;
                            line-height: 28px;
                        }
                    }
            }
            button{
                align-self: flex-end;
            }
            #pills-tab{
                display: flex !important;
                flex-direction: row;
                justify-content: start;
                overflow-x: auto;
                @media (min-width: 992px) {
                    display: none !important;
                }
                .nav-item{
                    margin: 8px !important;
                }
            }
            .tab-pane{
                @media (min-width: 992px) {
                    display: block !important;
                }
            }
        }
    }
}