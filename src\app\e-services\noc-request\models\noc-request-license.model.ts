interface Document {
  Id: string;
  DocumentType: string;
  DocumentTypeName: string;
  mimeType: string;
  FileName: string;
  Base64: string;
  SatusReason: string;
  SatusReasonCode: number;
}

interface UploadDocumentForm {
  Document: Document[];
}

interface NpoDetails {
  ApplicantEmirateId: string;
  NpoNameAr: string;
  NpoNameEn: string;
  NpoLegalForm: string;
  NpoDeclarationDecisionLink: string;
  MainCategory: string;
  HeadquarterEmirate: string;
  LicensingEntity: string;
  LicenseNumber: string;
  LicenseIssuanceDate: string; // ISO Date format
  LicenseExpiryDate: string; // ISO Date format
}

interface DonorDetails {
  DonorType: string;
  FullName: string;
  Nationality: string;
  PlaceOfBirth: string;
  CurrentResidence: string;
  Mobile: string;
  Email: string;
  PassportNumber: string;
  PassportType: string;
  PassportExpiryDate: string;
  JobTitle: string;
  Employer: string;
  CompanyName: string;
  Country: string;
  City: string;
  LicensingAuthority: string;
  CommercialLicenseNumber: string;
  LicenseNumber: string;
  Category: string;
  LicenseIssuanceDate: string;
  LicenseExpiryDate: string;
  EntityName: string;
  BusinessDomain: string;
  PhoneNumber: string;
  EmiratesId: string;
  DateOfBirth: string;
  NameAr: string;
  NameEn: string;
  UnifiedNumber: string;
  Emirate: string;
  EntityType: string;
  NpoNameAr: string;
  NpoNameEn: string;
  NpoLegalForm: string;
  LicensingEntity: string;
  DeclarationDecisionLink: string;
  HeadquartersEmirate: string;
}

interface TypeOfDonations {
  IsCashDonations: boolean;
  IsInKindDonations: boolean;
  CashDonationTypes: [];
  InKindDonationTypes: [];
  TotalCashDonations: string;
  TotalInKindDonations: string;
  GrandTotalAmount: string;
}

interface NpoBankAccountDetails {
  Bank: string;
  BankAccount: string;
  AccountOwnerNameEn: string;
  AccountOwnerNameAr: string;
  BranchName: string;
  BankAddress: string;
  IBANNumber: string;
  AccountType: string;
  AccountOpenDate: string;
  CurrencyType: number;
}

interface DonorBankAccountDetails {
  Country: string;
  BankName: string;
  AccountNumber: string;
  IBANNumber: string;
}

interface DonationPurpose {
  DonationPurpose: string;
}

interface DonationDateAndDuration {
  DonationDateOrDuration: string;
  DateOfReceivingDonations: string;
  FromDate: string;
  ToDate: string;
}

export interface NocRequestLicense {
  Id: string;
  StatusCode: number;
  SubmissionDate: string; // ISO Date format
  IsSubmitted: boolean;
  IsResubmitted: boolean;
  NpoDetails: NpoDetails;
  DonorDetails: DonorDetails;
  TypeOfDonations: TypeOfDonations;
  NpoBankAccountDetails: NpoBankAccountDetails;
  DonorBankAccountDetails: DonorBankAccountDetails;
  DonationPurpose: DonationPurpose;
  DonationDateAndDuration: DonationDateAndDuration;
  UploadDocumentForm: UploadDocumentForm;
  CurrentProcessStageConfigurationId: string;
}
