import { ComponentFactoryResolver, Injectable } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

@Injectable({
  providedIn: 'root'
})
export class ModalService {

  constructor(
    private modalService: NgbModal,
    private resolver: ComponentFactoryResolver
  ) {}

  openModal(component: any, data?: any): NgbModalRef {
    const modalRef = this.modalService.open(component);
    const componentFactory = this.resolver.resolveComponentFactory(component);
    const componentRef = modalRef.componentInstance.modalBody.createComponent(componentFactory);

    // Pass data to the dynamically created component if needed
    if (data) {
      Object.assign(componentRef.instance, data);
    }

    return modalRef;
  }
}
