@if(control) {
<mat-form-field floatLabel="always" appearance="outline" class="appFullWidth upload_wrapper" *ngIf="!file">
  <div [ngClass]="file ? 'align-items-center' : 'align-items-start'" class="row justify-content-center">
    <div
      [ngClass]="[isMobileScreen() ? 'col-md-12' : file ? 'col-md-12':'col-md-6', isMobileScreen() ? 'justify-content-start':'justify-content-end']">
      <h3>
        {{label | translate}} {{required?'*':''}}
      </h3>
      <p>
        {{description | translate}}
      </p>
    </div>
    <div class="d-flex align-items-center text-nowrap"
      [ngClass]="[file ? '': isMobileScreen() ? 'col-md-12' :'col-md-3', isMobileScreen() ? 'justify-content-start':'justify-content-end']">
      <input matInput [formControl]="control" style="display: none" />
      <input type="file" [formControl]="control" #fileUpload (change)="handleFileInput($event)" style="display: none" />
      @if(!file){
      <button  [disabled]="control.disabled" type="button" class="btn btn-primary upload-btn"
        (click)="fileUpload.click()">
        <svg width="20" height="20" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M15.5 9.87549V14.2505C15.5 14.582 15.3683 14.9 15.1339 15.1344C14.8995 15.3688 14.5815 15.5005 14.25 15.5005H1.75C1.41848 15.5005 1.10054 15.3688 0.866116 15.1344C0.631696 14.9 0.5 14.582 0.5 14.2505V9.87549C0.5 9.70973 0.565848 9.55076 0.683058 9.43355C0.800269 9.31634 0.95924 9.25049 1.125 9.25049C1.29076 9.25049 1.44973 9.31634 1.56694 9.43355C1.68415 9.55076 1.75 9.70973 1.75 9.87549V14.2505H14.25V9.87549C14.25 9.70973 14.3158 9.55076 14.4331 9.43355C14.5503 9.31634 14.7092 9.25049 14.875 9.25049C15.0408 9.25049 15.1997 9.31634 15.3169 9.43355C15.4342 9.55076 15.5 9.70973 15.5 9.87549ZM5.31719 4.69268L7.375 2.63409V9.87549C7.375 10.0413 7.44085 10.2002 7.55806 10.3174C7.67527 10.4346 7.83424 10.5005 8 10.5005C8.16576 10.5005 8.32473 10.4346 8.44194 10.3174C8.55915 10.2002 8.625 10.0413 8.625 9.87549V2.63409L10.6828 4.69268C10.8001 4.80996 10.9591 4.87584 11.125 4.87584C11.2909 4.87584 11.4499 4.80996 11.5672 4.69268C11.6845 4.5754 11.7503 4.41634 11.7503 4.25049C11.7503 4.08464 11.6845 3.92558 11.5672 3.8083L8.44219 0.683304C8.38414 0.625194 8.31521 0.579095 8.23934 0.547642C8.16346 0.516189 8.08213 0.5 8 0.5C7.91787 0.5 7.83654 0.516189 7.76066 0.547642C7.68479 0.579095 7.61586 0.625194 7.55781 0.683304L4.43281 3.8083C4.31554 3.92558 4.24965 4.08464 4.24965 4.25049C4.24965 4.41634 4.31554 4.5754 4.43281 4.69268C4.55009 4.80996 4.70915 4.87584 4.875 4.87584C5.04085 4.87584 5.19991 4.80996 5.31719 4.69268Z"
            fill="#ffffff" />
        </svg>
        {{ "Choose a file" | translate }}
      </button>
    }
    </div>
    <div *ngIf="isAdditional && !isSummary" class="col-md-1 d-flex justify-content-end"
      style="position: absolute; top: 0; right: 0;">
      <button (click)="deleteAttachmentType()" [ngStyle]="isMobileScreen()?{'top': '50px'} : {}" type="button"
        mat-icon-button color="warn">
        <i class="fas fa-times"></i>
      </button>
    </div>
  </div>

</mat-form-field>


@if (isPhoto) {
<div style="margin: 15px 0 !important;">
  <mat-hint>
    <svg width="15" height="15" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
        fill="#92722A" />
    </svg>

    <span *ngIf="control.touched && control.invalid">
      {{ "Please enter" | translate }}
      <strong>{{ label | translate }}</strong>
    </span>
    {{ "formatPhoto" | translate }}
  </mat-hint>
</div>
} @else {
<div style="margin: 15px 0 !important;">
  <mat-hint>
    <svg width="15" height="15" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 0C10.4288 0 7.91543 0.762437 5.77759 2.1909C3.63975 3.61935 1.97351 5.64968 0.989572 8.02512C0.0056327 10.4006 -0.251811 13.0144 0.249797 15.5362C0.751405 18.0579 1.98953 20.3743 3.80762 22.1924C5.6257 24.0105 7.94208 25.2486 10.4638 25.7502C12.9856 26.2518 15.5995 25.9944 17.9749 25.0104C20.3503 24.0265 22.3807 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9964 9.5533 24.6256 6.24882 22.1884 3.81163C19.7512 1.37445 16.4467 0.00363977 13 0ZM12.5 6C12.7967 6 13.0867 6.08797 13.3334 6.2528C13.58 6.41762 13.7723 6.65189 13.8858 6.92597C13.9994 7.20006 14.0291 7.50166 13.9712 7.79264C13.9133 8.08361 13.7704 8.35088 13.5607 8.56066C13.3509 8.77044 13.0836 8.9133 12.7926 8.97118C12.5017 9.02906 12.2001 8.99935 11.926 8.88582C11.6519 8.77229 11.4176 8.58003 11.2528 8.33335C11.088 8.08668 11 7.79667 11 7.5C11 7.10218 11.158 6.72064 11.4393 6.43934C11.7206 6.15804 12.1022 6 12.5 6ZM14 20C13.4696 20 12.9609 19.7893 12.5858 19.4142C12.2107 19.0391 12 18.5304 12 18V13C11.7348 13 11.4804 12.8946 11.2929 12.7071C11.1054 12.5196 11 12.2652 11 12C11 11.7348 11.1054 11.4804 11.2929 11.2929C11.4804 11.1054 11.7348 11 12 11C12.5304 11 13.0391 11.2107 13.4142 11.5858C13.7893 11.9609 14 12.4696 14 13V18C14.2652 18 14.5196 18.1054 14.7071 18.2929C14.8946 18.4804 15 18.7348 15 19C15 19.2652 14.8946 19.5196 14.7071 19.7071C14.5196 19.8946 14.2652 20 14 20Z"
        fill="#92722A" />
    </svg>

    <span *ngIf="control.touched && control.invalid">{{ "Please enter" | translate }}
      <strong>{{ label | translate }}</strong></span>
    {{ "format" | translate }}</mat-hint>
</div>
} @if (control.touched && control.invalid) {
<mat-error *ngIf="errorMessage !== null">{{ errorMessage }}</mat-error>
}
@if(file){
<div class="table-container d-block_mobile">
  <table class="my-table">
    <thead>
      <th translate class="align-middle">{{'Name' | translate}}</th>
      <th translate class="align-middle">{{'Type' | translate}}</th>
      <th translate class="align-middle">{{'Size' | translate}}</th>
      <th translate class="align-middle">{{'Action' | translate}}</th>
    </thead>
    <tr>
      <td>{{ file.name }}</td>
      <td>{{ file.type }}</td>
      <td>{{ file.size / 1024 | number : "1.2-2" }} KB</td>
      <td>
        @if(!control.disabled){
        <button type="button" *ngIf="file" class="btn text-danger" (click)="deleteFile()">
          <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
              fill="#92722A" />
          </svg>
        </button>
        }
      </td>
    </tr>
  </table>
</div>

<div class="figma-card-container">
  <div class="figma-card">
    @if(!control.disabled){
    <button mat-icon-button type="button" [matMenuTriggerFor]="menu" class="figma-actions-menu-trigger">
      <mat-icon>more_vert</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button type="button" *ngIf="file" class="btn text-danger" (click)="deleteFile()">
        <svg width="20" height="20" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M14.375 2.75H11.25V2.125C11.25 1.62772 11.0525 1.15081 10.7008 0.799175C10.3492 0.447544 9.87228 0.25 9.375 0.25H5.625C5.12772 0.25 4.65081 0.447544 4.29917 0.799175C3.94754 1.15081 3.75 1.62772 3.75 2.125V2.75H0.625C0.45924 2.75 0.300269 2.81585 0.183058 2.93306C0.0658481 3.05027 0 3.20924 0 3.375C0 3.54076 0.0658481 3.69973 0.183058 3.81694C0.300269 3.93415 0.45924 4 0.625 4H1.25V15.25C1.25 15.5815 1.3817 15.8995 1.61612 16.1339C1.85054 16.3683 2.16848 16.5 2.5 16.5H12.5C12.8315 16.5 13.1495 16.3683 13.3839 16.1339C13.6183 15.8995 13.75 15.5815 13.75 15.25V4H14.375C14.5408 4 14.6997 3.93415 14.8169 3.81694C14.9342 3.69973 15 3.54076 15 3.375C15 3.20924 14.9342 3.05027 14.8169 2.93306C14.6997 2.81585 14.5408 2.75 14.375 2.75ZM5 2.125C5 1.95924 5.06585 1.80027 5.18306 1.68306C5.30027 1.56585 5.45924 1.5 5.625 1.5H9.375C9.54076 1.5 9.69973 1.56585 9.81694 1.68306C9.93415 1.80027 10 1.95924 10 2.125V2.75H5V2.125ZM12.5 15.25H2.5V4H12.5V15.25ZM6.25 7.125V12.125C6.25 12.2908 6.18415 12.4497 6.06694 12.5669C5.94973 12.6842 5.79076 12.75 5.625 12.75C5.45924 12.75 5.30027 12.6842 5.18306 12.5669C5.06585 12.4497 5 12.2908 5 12.125V7.125C5 6.95924 5.06585 6.80027 5.18306 6.68306C5.30027 6.56585 5.45924 6.5 5.625 6.5C5.79076 6.5 5.94973 6.56585 6.06694 6.68306C6.18415 6.80027 6.25 6.95924 6.25 7.125ZM10 7.125V12.125C10 12.2908 9.93415 12.4497 9.81694 12.5669C9.69973 12.6842 9.54076 12.75 9.375 12.75C9.20924 12.75 9.05027 12.6842 8.93306 12.5669C8.81585 12.4497 8.75 12.2908 8.75 12.125V7.125C8.75 6.95924 8.81585 6.80027 8.93306 6.68306C9.05027 6.56585 9.20924 6.5 9.375 6.5C9.54076 6.5 9.69973 6.56585 9.81694 6.68306C9.93415 6.80027 10 6.95924 10 7.125Z"
            fill="#92722A" />
        </svg>
      </button>
    </mat-menu>
    }
    <div class="figma-card-content">
      <div class="figma-card-field">
        <span class="static-value">{{ "Name" | translate}}:</span>
        <span class="dynamic-value">{{ file.name }}</span>
      </div>
      <div class="figma-card-field">
        <span class="static-value">{{ 'Type' | translate }}:</span>
        <span class="dynamic-value">{{ file.type }}</span>
      </div>
      <div class="figma-card-field">
        <span class="static-value">{{ "Size" | translate}}:</span>
        <span class="dynamic-value">{{file.size }}</span>
      </div>
    </div>
  </div>
</div>
}

}